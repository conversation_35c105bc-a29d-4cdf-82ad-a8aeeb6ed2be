CREATE TABLE `app` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `name` varchar(128) COLLATE utf8mb4_bin NOT NULL COMMENT '应用名称',
  `code` varchar(128) COLLATE utf8mb4_bin NOT NULL COMMENT '应用代号(唯一标识)',
  `build_path` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '构建路径',
  `repo_addr` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '源码库地址',
  `status` int NOT NULL COMMENT '状态',
  `cmdb_id` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '对应的cmdb的 唯一Id',
  `project_id` int NOT NULL COMMENT '所属项目id',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `description` varchar(400) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `lang_name` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '语言名称',
  `lang_version` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '语言版本',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='应用信息表'

CREATE TABLE `app`.`app_preference` (
    `id` int NOT NULL AUTO_INCREMENT,
    `app_id` bigint NOT NULL COMMENT '应用id',
    `user_id` bigint NOT NULL COMMENT '用户id',
    `project_id` bigint NOT NULL COMMENT '项目id',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='应用收藏表';