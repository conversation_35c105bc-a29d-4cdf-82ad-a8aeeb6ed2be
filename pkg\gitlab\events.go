package gitlab

import (
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/tools/set"
	"52tt.com/cicd/pkg/tools/vec"
	"encoding/json"
	"fmt"
	"time"
)

type Action string

const (
	PushEvent         = "push"
	MergeEvent        = "merge_request"
	merge      Action = "merge"
)

func IsMergeAction(action string) bool {
	return action == string(merge)
}

func IsPushEvent(event string) bool {
	return event == PushEvent
}

func IsMergeEvent(event string) bool {
	return event == MergeEvent
}

func IsAutoEvent(event string) bool {
	return event == PushEvent || event == MergeEvent
}

type Project struct {
	Id                int    `json:"id"`
	Name              string `json:"name"`
	Description       string `json:"description"`
	WebUrl            string `json:"web_url"`
	AvatarUrl         string `json:"avatar_url"`
	GitSshUrl         string `json:"git_ssh_url"`
	GitHttpUrl        string `json:"git_http_url"`
	Namespace         string `json:"namespace"`
	VisibilityLevel   int    `json:"visibility_level"`
	PathWithNamespace string `json:"path_with_namespace"`
	DefaultBranch     string `json:"default_branch"`
	Homepage          string `json:"homepage"`
	Url               string `json:"url"`
	SshUrl            string `json:"ssh_url"`
	HttpUrl           string `json:"http_url"`
}

type Author struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type Commit struct {
	ID        string   `json:"id"`
	Message   string   `json:"message"`
	Title     string   `json:"title"`
	Timestamp string   `json:"timestamp"`
	URL       string   `json:"url"`
	Author    Author   `json:"author"`
	Added     []string `json:"added"`
	Modified  []string `json:"modified"`
	Removed   []string `json:"removed"`
}

type PushOptions struct {
}

type Repository struct {
	Name            string `json:"name"`
	URL             string `json:"url"`
	Description     string `json:"description"`
	Homepage        string `json:"homepage"`
	GitHttpUrl      string `json:"git_http_url"`
	GitSshUrl       string `json:"git_ssh_url"`
	VisibilityLevel int64  `json:"visibility_level"`
}

type GitPushEvent struct {
	ObjectKind        string      `json:"object_kind"`
	EventName         string      `json:"event_name"`
	Before            string      `json:"before"`
	After             string      `json:"after"`
	Ref               string      `json:"ref"`
	CheckoutSHA       string      `json:"checkout_sha"`
	Message           interface{} `json:"message"`
	UserID            int64       `json:"user_id"`
	UserName          string      `json:"user_name"`
	UserUsername      string      `json:"user_username"`
	UserEmail         string      `json:"user_email"`
	UserAvatar        string      `json:"user_avatar"`
	ProjectID         int         `json:"project_id"`
	Project           Project     `json:"project"`
	Commits           []Commit    `json:"commits"`
	TotalCommitsCount int64       `json:"total_commits_count"`
	PushOptions       PushOptions `json:"push_options"`
	Repository        Repository  `json:"repository"`
}

type Changes struct {
	MergeStatus MergeStatus `json:"merge_status"`
}

type MergeStatus struct {
	Previous string `json:"previous"`
	Current  string `json:"current"`
}

type ObjectAttributes struct {
	AssigneeID                  int64      `json:"assignee_id"`
	AuthorID                    int64      `json:"author_id"`
	CreatedAt                   string     `json:"created_at"`
	Description                 string     `json:"description"`
	ID                          int64      `json:"id"`
	Iid                         int64      `json:"iid"`
	MergeCommitSHA              string     `json:"merge_commit_sha"`
	MergeStatus                 string     `json:"merge_status"`
	MergeWhenPipelineSucceeds   bool       `json:"merge_when_pipeline_succeeds"`
	SourceBranch                string     `json:"source_branch"`
	SourceProjectID             int64      `json:"source_project_id"`
	StateID                     int64      `json:"state_id"`
	TargetBranch                string     `json:"target_branch"`
	TargetProjectID             int64      `json:"target_project_id"`
	TimeEstimate                int64      `json:"time_estimate"`
	Title                       string     `json:"title"`
	UpdatedAt                   string     `json:"updated_at"`
	URL                         string     `json:"url"`
	Source                      Project    `json:"Branch"`
	Target                      Project    `json:"target"`
	LastCommit                  LastCommit `json:"last_commit"`
	WorkInProgress              bool       `json:"work_in_progress"`
	TotalTimeSpent              int64      `json:"total_time_spent"`
	TimeChange                  int64      `json:"time_change"`
	AssigneeIDS                 []int64    `json:"assignee_ids"`
	State                       string     `json:"state"`
	BlockingDiscussionsResolved bool       `json:"blocking_discussions_resolved"`
	Action                      string     `json:"action"`
}

type GitUser struct {
	ID        int64  `json:"id"`
	Name      string `json:"name"`
	Username  string `json:"username"`
	AvatarURL string `json:"avatar_url"`
	Email     string `json:"email"`
}

type GitMergeEvent struct {
	ObjectKind       string           `json:"object_kind"`
	EventType        string           `json:"event_type"`
	User             GitUser          `json:"user"`
	Project          Project          `json:"project"`
	ObjectAttributes ObjectAttributes `json:"object_attributes"`
	Labels           []interface{}    `json:"labels"`
	Changes          Changes          `json:"changes"`
	Repository       Repository       `json:"repository"`
	Assignees        []interface{}    `json:"assignees"`
}

type LastCommit struct {
	ID        string    `json:"id"`
	Message   string    `json:"message"`
	Title     string    `json:"title"`
	Timestamp time.Time `json:"timestamp"`
	URL       string    `json:"url"`
	Author    struct {
		Name  string `json:"name"`
		Email string `json:"email"`
	} `json:"author"`
}

type GitEventResp struct {
	Name           string                 //事件名称
	UserID         int64                  //gitlab用户id
	Event          map[string]interface{} //本次事件
	GitHttpUrl     string                 //git仓库完整路径
	Email          string                 //触发者的邮箱
	UserName       string                 //本次事件的触发者
	ProjectID      int                    //项目ID (此处指gitlab项目)
	IID            int64                  //合并的IID
	SourceBranch   string                 // 源分支
	Branch         string                 //目标分支
	Status         string                 //合并状态
	Action         string                 //Mr事件的action
	gitClient      *Client
	Changes        []string
	CommitMsg      string //提交信息
	LastCommitHash string //最后一次提交的ID Hash
}

type EventHandler func([]byte) (*GitEventResp, error)

func (c *Client) GetGitlabEventMsg(gitEvents map[string]interface{}) (*GitEventResp, error) {
	eventName, ok := gitEvents["event_type"].(string)
	if !ok {
		eventName, _ = gitEvents["event_name"].(string)
	}
	if eventName != PushEvent && eventName != MergeEvent {
		errInfo := fmt.Sprintf("不符合条件的gitlab [%s] 事件", eventName)
		log.Errorf(errInfo)
		return nil, fmt.Errorf(errInfo)
	}
	data, err := json.Marshal(gitEvents)
	if err != nil {
		log.Debugf("gitlab自动触发构建参数序列化错误: %v", err)
		return nil, err
	}

	handleFunc := map[string]EventHandler{
		PushEvent:  c.processPushEvent,
		MergeEvent: c.processMergeEvent,
	}
	eventObj, err := handleFunc[eventName](data)
	if err != nil {
		errInfo := fmt.Errorf("获取 %s 事件的变更文件信息错误: %w", eventName, err)
		return nil, errInfo
	}
	eventObj.Name, eventObj.Event = eventName, gitEvents
	return eventObj, nil
}

func (c *Client) processPushEvent(data []byte) (*GitEventResp, error) {
	var event GitPushEvent
	commitMsg := ""
	if err := json.Unmarshal(data, &event); err != nil {
		errInfo := fmt.Errorf("反序列化gitlab的push事件信息错误: %v", err)
		log.Debug(errInfo.Error())
		return nil, err
	}
	ref := event.Ref
	gitPushEventObj := &GitEventResp{
		UserName:   event.UserName,
		UserID:     event.UserID,
		Email:      c.getEmail(event.UserID),
		GitHttpUrl: event.Project.GitHttpUrl,
		ProjectID:  event.ProjectID,
		Branch:     getBranch(ref),
		Changes:    getPushChangedFiles(event.Commits),
	}
	if len(event.Commits) > 0 {
		commitMsg = event.Commits[0].Message
	}
	gitPushEventObj.CommitMsg = commitMsg
	return gitPushEventObj, nil
}

func (c *Client) processMergeEvent(data []byte) (*GitEventResp, error) {
	var event GitMergeEvent
	commitMsg := ""
	if err := json.Unmarshal(data, &event); err != nil {
		errInfo := fmt.Errorf("反序列化gitlab的merge事件信息错误: %v", err)
		log.Debug(errInfo.Error())
		return nil, err
	}
	action := event.ObjectAttributes.Action
	gitHttpUrl := event.Project.GitHttpUrl
	iID := event.ObjectAttributes.Iid
	gitMergeEventObj := &GitEventResp{
		UserName:     event.User.Name,
		UserID:       event.User.ID,
		ProjectID:    event.Project.Id,
		IID:          iID,
		Email:        c.getEmail(event.User.ID),
		GitHttpUrl:   gitHttpUrl,
		SourceBranch: event.ObjectAttributes.SourceBranch,
		Branch:       event.ObjectAttributes.TargetBranch,
		Action:       action,
		Status:       event.ObjectAttributes.MergeStatus,
	}

	//The available values for object_attributes.action in the payload are:
	//open,close,reopen,update,approved,unapproved,approval,unapproval,merge
	sets := set.Of([]string{"open", "update", "merge", "reopen"})
	if !sets.Exists(action) {
		//非open,update,merge，则忽略
		return nil, fmt.Errorf("本次merge事件非open,update,merge,reopen动作")
	}
	files := c.getMergeChangedFiles(gitHttpUrl, iID)
	gitMergeEventObj.Changes = files
	if event.ObjectAttributes.Title != "" {
		commitMsg = event.ObjectAttributes.Title
	}
	if event.ObjectAttributes.LastCommit.ID != "" {
		gitMergeEventObj.LastCommitHash = event.ObjectAttributes.LastCommit.ID
	}
	gitMergeEventObj.CommitMsg = commitMsg
	return gitMergeEventObj, nil
}

func getPushChangedFiles(commits []Commit) []string {
	//sha := event.CheckoutSHA
	changes := make([]string, 0)
	for _, c := range commits {
		changes = append(changes, c.Added...)
		changes = append(changes, c.Modified...)
		changes = append(changes, c.Removed...)
	}
	uniqueChanges := vec.RemoveDuplicate(changes)
	return uniqueChanges
}
