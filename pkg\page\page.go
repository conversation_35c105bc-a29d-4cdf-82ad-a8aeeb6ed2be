package page

const (
	DefaultPageSize      = 10
	DefaultPage          = 1
	DefaultUnlimitedSize = 10000 // 无限大的 size，用于某些场景全量查询  这是个特别的常量
)

type EmptyObject struct{}
type EmptyList []EmptyObject

type PageModel struct {
	//页码
	Num int `form:"page" json:"page"`
	//每页显示记录数量
	Size int `form:"size" json:"size"`
	//SQL查询条件，group or order by
	Condition string `form:"condition" json:"condition"`
	//当前页的最后一个ID
	LastId int64 `form:"lastId" json:"lastId"`
	// 无限大的 size，用于某些场景全量查询
	unlimitedSize bool
}

type PQuery interface {
	PageNum() int
	SetPageNum(int)
	PageSize() int
	SetPageSize(int)
	PageCond() string
	LastID() int64
}

func (pm *PageModel) PageNum() int {
	if pm.Num == 0 {
		return DefaultPage
	}
	return pm.Num
}

func (pm *PageModel) SetPageNum(num int) {
	pm.Num = num
}

func (pm *PageModel) PageSize() int {
	if pm.Size == 0 {
		return DefaultPageSize
	}
	return pm.Size
}
func (pm *PageModel) SetPageSize(size int) {
	pm.Size = size
}

func (pm *PageModel) PageCond() string {
	return pm.Condition
}

func (pm *PageModel) LastID() int64 {
	return pm.LastId
}

func (pm *PageModel) UnlimitedSize() bool {
	return pm.unlimitedSize
}

func NewUnlimitedSizePageModel() PageModel {
	return PageModel{
		Num:           DefaultPage,
		Size:          DefaultUnlimitedSize,
		unlimitedSize: true,
	}
}

func PageOf(query PQuery, data interface{}, totalRecord int64) *Page {
	if data == nil {
		data = EmptyList{}
	}
	p := &Page{
		PageNum:     query.PageNum(),
		PageSize:    query.PageSize(),
		List:        data,
		TotalRecord: totalRecord,
	}
	return p
}

func Default(query PQuery) *Page {
	p := &Page{
		PageNum:     query.PageNum(),
		PageSize:    query.PageSize(),
		List:        EmptyList{},
		TotalRecord: 0,
	}
	return p
}

type Page struct {
	//页码
	PageNum int `json:"page" mapstructure:"page"`
	//每页显示记录数量
	PageSize int `json:"pageSize"`
	//数据
	List interface{} `json:"list"`
	//总记录数
	TotalRecord int64 `json:"totalRecord"`
}

func (p *Page) GetData() interface{} {
	return p.List
}

func (p *Page) GetTotal() int64 {
	return p.TotalRecord
}

type Paginator interface {
	GetData() interface{}
	GetTotal() int64
}

// TotalPage 计算总页数
func (p *Page) TotalPage() int {
	if p.PageSize == 0 {
		return 0
	}
	totalPage := p.TotalRecord / int64(p.PageSize)
	if p.TotalRecord%int64(p.PageSize) > 0 {
		totalPage++
	}
	return int(totalPage)
}

// Offset 计算偏移量
func (p *Page) Offset() int {
	return (p.PageNum - 1) * p.PageSize
}

// HandlePagination 分页处理函数
func (p *Page) HandlePagination(data []interface{}) *Page {
	// 计算分页信息
	pagination := &Page{
		PageNum:     p.PageNum,
		PageSize:    p.PageSize,
		TotalRecord: p.TotalRecord,
	}
	if len(data) == 0 {
		pagination.List = nil
		return pagination
	}
	if p.PageSize == 0 {
		p.PageSize = DefaultPageSize
	}
	if p.PageNum == 0 {
		p.PageNum = DefaultPage
	}
	// 计算最大页数
	maxPageNum := pagination.TotalPage()

	// 如果当前页码超过最大页数，设置当前页码为最大页数
	if pagination.PageNum > maxPageNum {
		pagination.PageNum = maxPageNum
	}
	// 计算起止索引
	start := pagination.Offset()
	end := start + pagination.PageSize
	if end > len(data) {
		end = len(data)
	}
	pagination.List = data[start:end]
	// 返回分页后的数据和分页对象
	return pagination
}
