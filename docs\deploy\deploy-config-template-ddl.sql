CREATE TABLE `deploy_config_template` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `config_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称',
  `config_type` tinyint NOT NULL DEFAULT '1' COMMENT '应用类型：1通用应用、2定时任务、3statefulset',
  `project_id` bigint NOT NULL COMMENT '项目id',
  `app_basic_config` json NOT NULL COMMENT '应用基础配置',
  `app_advanced_config` json NOT NULL COMMENT '应用高级配置',
  `trait_config` json NOT NULL COMMENT '运维特征',
  `created_by` bigint NOT NULL COMMENT '创建人id',
  `created_by_chinese_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人中文名',
  `created_by_employee_no` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人工号',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_applied` tinyint(1) NOT NULL COMMENT '是否应用',
  `public_config` json NOT NULL COMMENT '公共配置',
  `last_template_id` bigint NOT NULL COMMENT '上一次模版id'
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部署配置模版';
