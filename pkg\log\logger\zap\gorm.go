package zap

import (
	cctx "52tt.com/cicd/pkg/context"
	"context"
	"errors"
	"runtime"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm/logger"
)

func NewGORMLogger() logger.Interface {
	return newGORMLogger(newLogger())
}

func newGORMLogger(zl *zap.Logger) *gormLogger {
	return &gormLogger{
		zapLogger:                 zl,
		LogLevel:                  logger.Info,
		SlowThreshold:             200 * time.Millisecond,
		IgnoreRecordNotFoundError: false,
		SkipCallerLookup:          false,
	}
}

type gormLogger struct {
	zapLogger                 *zap.Logger
	LogLevel                  logger.LogLevel
	SlowThreshold             time.Duration
	IgnoreRecordNotFoundError bool
	SkipCallerLookup          bool
}

func (l gormLogger) LogMode(level logger.LogLevel) logger.Interface {
	return gormLogger{
		zapLogger:                 l.zapLogger,
		LogLevel:                  level,
		SlowThreshold:             l.SlowThreshold,
		IgnoreRecordNotFoundError: l.IgnoreRecordNotFoundError,
	}
}

// Info print info
func (l gormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel < logger.Info {
		return
	}
	l.getLogger(ctx).Sugar().Infof(msg, data...)
}

// Warn print warn messages
func (l gormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel < logger.Warn {
		return
	}
	l.getLogger(ctx).Sugar().Warnf(msg, data...)
}

// Error print error messages
func (l gormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel < logger.Error {
		return
	}
	l.getLogger(ctx).Sugar().Errorf(msg, data...)
}

// Trace print sql message
func (l gormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.LogLevel <= logger.Silent {
		return
	}

	zl := l.getLogger(ctx)
	elapsed := time.Since(begin)
	switch {
	case err != nil && l.LogLevel >= logger.Error && (!errors.Is(err, logger.ErrRecordNotFound) || !l.IgnoreRecordNotFoundError):
		sql, rows := fc()
		zl.Error("gorm error", zap.Error(err), zap.Duration("elapsed", elapsed), zap.Int64("rows", rows), zap.String("sql", sql))
	case elapsed > l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= logger.Warn:
		sql, rows := fc()
		zl.Warn("gorm slow query", zap.Duration("elapsed", elapsed), zap.Int64("rows", rows), zap.String("sql", sql))
	case l.LogLevel == logger.Info:
		sql, rows := fc()
		zl.Info("gorm", zap.Duration("elapsed", elapsed), zap.Int64("rows", rows), zap.String("sql", sql))
	}
}

const (
	gormPackage = "gorm.io/gorm"
)

func (l gormLogger) getLogger(ctx context.Context) *zap.Logger {
	info, _ := cctx.RequestInfoFromCtx(ctx)
	if info.UserID != 0 {
		l.zapLogger = l.zapLogger.With(zap.String("uid", strconv.Itoa(int(info.UserID))))
	}
	if info.ProjectID != 0 {
		l.zapLogger = l.zapLogger.With(zap.String("pid", strconv.Itoa(int(info.ProjectID))))
	}
	if info.ChineseName != "" {
		l.zapLogger = l.zapLogger.With(zap.String("cname", info.ChineseName))
	}
	if info.RequestID != "" {
		l.zapLogger = l.zapLogger.With(zap.String("reqid", info.RequestID))
	}

	if l.SkipCallerLookup {
		return l.zapLogger
	}

	for i := 2; i < 15; i++ {
		_, file, _, ok := runtime.Caller(i)

		switch {
		case !ok:
		case strings.HasSuffix(file, "_test.go"):
		case strings.Contains(file, gormPackage):
		default:
			return l.zapLogger.WithOptions(zap.AddCallerSkip(i - defaultCallerSkip - 1))
		}
	}
	return l.zapLogger
}
