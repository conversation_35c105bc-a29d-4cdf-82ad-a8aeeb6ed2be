package kafka

import (
	"strings"

	"52tt.com/cicd/pkg/tools/random"
	"github.com/IBM/sarama"
)

type Config struct {
	Name     string
	Brokers  string
	ClientID string

	Producer struct {
		Topic string
	}

	Consumer struct {
		Topics    string
		SubTopics string `mapstructure:"sub_topics" json:"sub_topics"`
		GroupID   string
		Offsets   string
	}

	SASL struct {
		Enable   bool
		User     string
		Password string
	}

	sc *sarama.Config
}

func (cfg Config) GetBrokers() []string {
	return strings.Split(cfg.Brokers, ",")
}

func (cfg Config) GetProducerTopic() string {
	return cfg.Producer.Topic
}

func (cfg Config) GetGroupID() string {
	return cfg.Consumer.GroupID
}

func (cfg Config) GetConsumerTopics() []string {
	return strings.Split(cfg.Consumer.Topics, ",")
}

func (cfg Config) GetSubTopics() []string {
	return strings.Split(cfg.Consumer.SubTopics, ",")
}

func (cfg Config) GetSaramaConfig() *sarama.Config {
	return cfg.sc
}

type Option func(*Config)

func ProducerTopic(topic string) Option {
	return func(config *Config) {
		config.Producer.Topic = topic
	}
}

func ConsumerTopics(topics []string) Option {
	return func(config *Config) {
		config.Consumer.Topics = strings.Join(topics, ",")
	}
}

func ConsumerGroupID(groupID string) Option {
	return func(config *Config) {
		config.Consumer.GroupID = groupID
	}
}

func EnableNetSASL(user, password string) Option {
	return func(config *Config) {
		config.SASL.Enable = true
		config.SASL.User = user
		config.SASL.Password = password
	}
}

func ClientID(clientID string) Option {
	return func(config *Config) {
		config.ClientID = clientID
	}
}

func Name(name string) Option {
	return func(config *Config) {
		config.Name = name
	}
}

func SetGroupIdAndName(name string) Option {
	return func(config *Config) {
		config.Consumer.GroupID = name
		config.Name = name + "-" + random.GenerateRandomAlphaNumericString(4)
	}
}

func Brokers(brokers []string) Option {
	return func(config *Config) {
		config.Brokers = strings.Join(brokers, ",")
	}
}

func WithConfig(cfg *Config) Option {
	return func(config *Config) {
		config.Name = cfg.Name
		config.Brokers = cfg.Brokers
		config.ClientID = cfg.ClientID
		config.Producer = cfg.Producer
		config.Consumer = cfg.Consumer
		config.SASL = cfg.SASL
	}
}

func NewSConfig() *sarama.Config {
	sc := sarama.NewConfig()
	// 生产者配置
	//
	// 确保写入所有的 kafka 要求的副本数
	sc.Producer.RequiredAcks = sarama.WaitForAll
	sc.Producer.Return.Successes = true
	sc.Producer.Return.Errors = true
	sc.ChannelBufferSize = 1 << 11

	// 消费者配置
	//
	// 要求从最早的消息进行消费, 避免分区扩容时候发生消息跳过的问题
	sc.Consumer.Offsets.Initial = sarama.OffsetOldest
	sc.Consumer.Return.Errors = true

	return sc
}

func NewConfig(opts ...Option) *Config {
	sc := NewSConfig()
	cfg := &Config{
		sc: sc,
	}

	for _, o := range opts {
		o(cfg)
	}

	if cfg.Name == "" {
		cfg.Name = "default-name"
	}
	if cfg.Brokers == "" {
		cfg.Brokers = "127.0.0.1:9092"
	}

	if len(cfg.Consumer.Topics) > 0 && cfg.Consumer.GroupID == "" {
		cfg.Consumer.GroupID = cfg.Name
	}

	if cfg.Consumer.Offsets == "latest" {
		cfg.sc.Consumer.Offsets.Initial = sarama.OffsetNewest
	}

	if cfg.SASL.Enable {
		cfg.sc.Net.SASL.Enable = true
		cfg.sc.Net.SASL.User = cfg.SASL.User
		cfg.sc.Net.SASL.Password = cfg.SASL.Password
	}
	if cfg.ClientID != "" {
		cfg.sc.ClientID = cfg.ClientID
	}

	return cfg
}
