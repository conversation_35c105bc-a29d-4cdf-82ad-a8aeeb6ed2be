package harbor

import (
	"fmt"
	"net/http"
	"net/url"
	"time"

	"52tt.com/cicd/pkg/httpclient"
)

type artifact struct {
	Host     string
	AuthName string
	AuthPsw  string
}

func NewArtifact(host, authName, authPsw string) artifact {
	return artifact{
		Host:     host,
		AuthName: authName,
		AuthPsw:  authPsw,
	}
}

type ArtifactInfo struct {
	ID            int                     `json:"id"`
	Type          string                  `json:"type"`
	MediaType     string                  `json:"media_type"`
	ManifestType  string                  `json:"manifest_media_type"`
	ProjectID     int                     `json:"project_id"`
	RepositoryID  int                     `json:"repository_id"`
	Digest        string                  `json:"digest"`
	Size          int64                   `json:"size"`
	PushTime      time.Time               `json:"push_time"`
	PullTime      time.Time               `json:"pull_time"`
	ExtraAttrs    map[string]interface{}  `json:"extra_attrs"`
	Annotations   map[string]string       `json:"annotations"`
	References    []Reference             `json:"references"`
	Tags          []Tag                   `json:"tags"`
	AdditionLinks map[string]AdditionLink `json:"addition_links"`
	Labels        []Label                 `json:"labels"`
}

type Reference struct {
	ParentID    int               `json:"parent_id"`
	ChildID     int               `json:"child_id"`
	ChildDigest string            `json:"child_digest"`
	Platform    *Platform         `json:"platform"`
	Annotations map[string]string `json:"annotations"`
	URLs        []string          `json:"urls"`
}

type Platform struct {
	Architecture string   `json:"architecture"`
	OS           string   `json:"os"`
	OSVersion    string   `json:"os.version"`
	OSFeatures   []string `json:"os.features"`
	Variant      string   `json:"variant"`
}

type Tag struct {
	ID           int       `json:"id"`
	RepositoryID int       `json:"repository_id"`
	ArtifactID   int       `json:"artifact_id"`
	Name         string    `json:"name"`
	PushTime     time.Time `json:"push_time"`
	PullTime     time.Time `json:"pull_time"`
	Immutable    bool      `json:"immutable"`
	Signed       bool      `json:"signed"`
}

type AdditionLink struct {
	HREF     string `json:"href"`
	Absolute bool   `json:"absolute"`
}

type Label struct {
	ID           int       `json:"id"`
	Name         string    `json:"name"`
	Description  string    `json:"description"`
	Color        string    `json:"color"`
	Scope        string    `json:"scope"`
	ProjectID    int       `json:"project_id"`
	CreationTime time.Time `json:"creation_time"`
	UpdateTime   time.Time `json:"update_time"`
}

func (a artifact) List(projectName, repositoryName string, page, size int) (objs []ArtifactInfo, err error) {
	objs = make([]ArtifactInfo, 0)
	urlPath := a.Host + "/api/v2.0/projects/" + projectName + "/repositories/" + url.PathEscape(url.PathEscape(repositoryName)) + "/artifacts"
	urlPath += fmt.Sprintf("?page=%d&page_size=%d", page, size)
	println(urlPath)
	req, err := http.NewRequest("GET", urlPath, nil)
	if err != nil {
		return
	}
	req.SetBasicAuth(a.AuthName, a.AuthPsw)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("GET artifact, got %s", resp.Status)
		return
	}
	err = httpclient.RespAsStruct(resp, "json", &objs)

	return
}

func (a artifact) Delete(projectName, repositoryName, reference string) (err error) {
	urlPath := a.Host + "/api/v2.0/projects/" + projectName + "/repositories/" + url.PathEscape(url.PathEscape(repositoryName)) + "/artifacts/" + reference
	println(urlPath)
	req, err := http.NewRequest("GET", urlPath, nil)
	if err != nil {
		return
	}
	req.SetBasicAuth(a.AuthName, a.AuthPsw)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode == http.StatusNotFound {
		return
	}
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("GET artifact, got %s", resp.Status)
		return
	}

	reqDel, err := http.NewRequest("DELETE", urlPath, nil)
	if err != nil {
		return
	}
	reqDel.SetBasicAuth(a.AuthName, a.AuthPsw)
	reqDel.Header.Set("X-Harbor-Csrf-Token", resp.Header.Get("X-Harbor-Csrf-Token"))

	respDel, err := http.DefaultClient.Do(reqDel)
	if err != nil {
		return
	}
	defer respDel.Body.Close()
	if respDel.StatusCode != http.StatusOK {
		err = fmt.Errorf("DELETE artifact, got %s", respDel.Status)
		return
	}

	return
}
