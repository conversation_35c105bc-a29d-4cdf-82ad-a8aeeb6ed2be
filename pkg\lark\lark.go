//go:generate mockgen -destination=lark_mock.go -package=lark -source=lark.go
package lark

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"

	"github.com/google/uuid"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
)

type Client struct {
	c *lark.Client
}

var _ Service = (*Client)(nil)

func NewClient(appId string, appSecret string) *Client {
	tr := &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}
	c := &http.Client{Transport: tr}
	client := lark.NewClient(appId, appSecret, lark.WithEnableTokenCache(true), lark.WithHttpClient(c))
	return &Client{c: client}
}

type Service interface {
	SendMsgToUsr(content, unionId, msgType string) (*larkim.CreateMessageResp, error)
	UrgentMessage(unionId, messageId string) error
	UpdateMsgCard(msgId string, content string) (*larkim.PatchMessageResp, error)
}

//取部门信息列表，带部门层级

func (c *Client) GetDepsListWithLevel(parentDepId string) (map[int][]*larkcontact.Department, error) {
	var topDeps []*larkcontact.Department
	var pageToken = ""
	resp, err := c.listDeps(parentDepId, &pageToken) //获取顶级部门
	if err != nil || len(resp.Items) == 0 {
		return nil, err
	}
	topDeps = append(topDeps, resp.Items...)
	if resp.PageToken != nil { //如果有分页，获取下几页的同级部门
		ds := c.getAllSameLevelDeps(parentDepId, resp.PageToken)
		topDeps = append(topDeps, ds...)
	}
	return c.traDepTree(topDeps), nil
}

//取部门信息列表

func (c *Client) GetDepsList(parentDepId string) ([]*larkcontact.Department, error) {
	var deps = make([]*larkcontact.Department, 0)
	depsWithLevel, err := c.GetDepsListWithLevel(parentDepId)
	if err != nil {
		return deps, err
	}
	for _, v := range depsWithLevel {
		for _, arr := range v {
			deps = append(deps, arr)
		}
	}
	return deps, nil
}

//获取某部门成员

func (c *Client) GetUsrsList(departmentId string) ([]*larkcontact.User, error) {
	var usrs []*larkcontact.User
	var pageToken = ""
	resp, err := c.listUsrs(departmentId, &pageToken)
	if err != nil || len(resp.Items) == 0 {
		return nil, err
	}
	usrs = append(usrs, resp.Items...)

	//如果有分页，获取下几页的成员
	if resp.PageToken != nil {
		ds := c.getAllSameLevelUsrs(departmentId, resp.PageToken)
		usrs = append(usrs, ds...)
	}
	return usrs, nil
}

//通过邮箱查找飞书用户union_id

func (c *Client) GetUserUnionIdByEmail(email string) (string, error) {
	req := larkcontact.NewBatchGetIdUserReqBuilder().
		UserIdType(`union_id`).
		Body(larkcontact.NewBatchGetIdUserReqBodyBuilder().
			Emails([]string{email}).
			Build()).
		Build()
	resp, err := c.c.Contact.User.BatchGetId(context.Background(), req, larkcore.WithTenantAccessToken(""))
	if err != nil {
		return "", err
	}
	if !resp.Success() {
		return "", fmt.Errorf("飞书服务端错误，code：%d, msg: %v, requestId: %s", resp.Code, resp.Msg, resp.RequestId())
	}

	unionId := resp.Data.UserList[0].UserId
	if unionId == nil {
		return "", nil
	}

	return *unionId, nil
}

//通过邮箱（50个以内）查找飞书用户id信息

func (c *Client) GetUsrsByEmails(email []string) ([]*larkcontact.UserContactInfo, error) {
	req := larkcontact.NewBatchGetIdUserReqBuilder().
		UserIdType(`union_id`).
		Body(larkcontact.NewBatchGetIdUserReqBodyBuilder().
			Emails(email).
			Build()).
		Build()
	resp, err := c.c.Contact.User.BatchGetId(context.Background(), req, larkcore.WithTenantAccessToken(""))
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		msg := fmt.Sprintf("飞书服务端错误，code：%d, msg:%v, requestId:%s", resp.Code, resp.Msg, resp.RequestId())
		return nil, fmt.Errorf(msg)
	}
	return resp.Data.UserList, nil
}

//通过union_id获该用户的飞书信息

func (c *Client) GetUsrInfoByUnionId(unionId string) (*larkcontact.User, error) {
	req := larkcontact.NewGetUserReqBuilder().
		UserId(unionId).
		UserIdType(`union_id`).
		DepartmentIdType(`open_department_id`).
		Build()
	resp, err := c.c.Contact.User.Get(context.Background(), req, larkcore.WithTenantAccessToken(""))
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		msg := fmt.Sprintf("飞书服务端错误，code：%d, msg:%v, requestId:%s", resp.Code, resp.Msg, resp.RequestId())
		return nil, fmt.Errorf(msg)
	}
	return resp.Data.User, nil
}

// SendMsgToUsr 发送飞书消息给特定用户
func (c *Client) SendMsgToUsr(content, unionId, msgType string) (*larkim.CreateMessageResp, error) {
	id := uuid.New()
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(`union_id`).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(unionId).
			MsgType(msgType).
			Content(content).
			Uuid(id.String()).
			Build()).
		Build()
	resp, err := c.c.Im.Message.Create(context.Background(), req)
	if err != nil {
		return resp, fmt.Errorf("发送飞书消息发生异常: %v", err)
	}
	if !resp.Success() {
		return resp, fmt.Errorf("发送消息飞书服务端错误: %v", resp.Msg)
	}
	return resp, nil
}

func (c *Client) UrgentMessage(unionId, messageId string) error {
	req := larkim.NewUrgentAppMessageReqBuilder().
		MessageId(messageId).
		UserIdType(`union_id`).
		UrgentReceivers(larkim.NewUrgentReceiversBuilder().
			UserIdList([]string{unionId}).
			Build()).
		Build()
	resp, err := c.c.Im.Message.UrgentApp(context.Background(), req)
	if err != nil {
		return fmt.Errorf("发送加急飞书消息发生异常: %v", err)
	}
	if !resp.Success() {
		return fmt.Errorf("发送加急消息飞书服务端错误: %v", resp.Msg)
	}
	return nil
}

//给定的父部门，返回其子部门

func (c *Client) listDeps(p string, t *string) (*larkcontact.ChildrenDepartmentRespData, error) {
	req := larkcontact.NewChildrenDepartmentReqBuilder().
		DepartmentId(p).
		UserIdType(`open_id`).
		DepartmentIdType(`open_department_id`).
		PageSize(50).
		PageToken(*t).
		Build()
	resp, err := c.c.Contact.Department.Children(context.Background(), req)
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		msg := fmt.Sprintf("飞书服务端错误，code：%d, msg:%v, requestId:%s", resp.Code, resp.Msg, resp.RequestId())
		return nil, fmt.Errorf(msg)
	}
	return resp.Data, nil
}

//给定的部门，返回其部门成员

func (c *Client) listUsrs(p string, t *string) (*larkcontact.ListUserRespData, error) {
	req := larkcontact.NewListUserReqBuilder().
		DepartmentId(p).
		UserIdType(`open_id`).
		DepartmentIdType(`open_department_id`).
		PageSize(50).
		PageToken(*t).
		Build()
	resp, err := c.c.Contact.User.List(context.Background(), req)
	if err != nil {
		return nil, err
	}
	if !resp.Success() {
		msg := fmt.Sprintf("飞书服务端错误，code：%d, msg:%v, requestId:%s", resp.Code, resp.Msg, resp.RequestId())
		return nil, fmt.Errorf(msg)
	}
	return resp.Data, nil
}

//返回同一父部门下剩余全部部门（分页）

func (c *Client) getAllSameLevelDeps(p string, t *string) []*larkcontact.Department {
	var deps []*larkcontact.Department
	for {
		if t != nil {
			break
		}
		resp, err := c.listDeps(p, t)
		if err != nil || len(resp.Items) == 0 {
			break
		}
		deps = append(deps, resp.Items...)
		t = resp.PageToken
	}
	return deps
}

//返回同一部门下剩余全部成员（分页）

func (c *Client) getAllSameLevelUsrs(p string, t *string) []*larkcontact.User {
	var usrs []*larkcontact.User
	for {
		if t != nil {
			break
		}
		resp, err := c.listUsrs(p, t)
		if err != nil || len(resp.Items) == 0 {
			break
		}
		usrs = append(usrs, resp.Items...)
		t = resp.PageToken
	}
	return usrs
}

//遍历顶级部门下的所有部门，及其子部门

func (c *Client) traDepTree(topDeps []*larkcontact.Department) map[int][]*larkcontact.Department {
	var stack = make(map[int]*larkcontact.Department, 0)  //栈
	var temp = make([]*larkcontact.Department, 0)         //操作组
	var list = make(map[int][]*larkcontact.Department, 0) //用于输出的部门列表
	var pageToken = ""                                    //分页token
	var level = 0                                         //部门层级
	for n, d := range topDeps {                           //顶级部门入栈
		stack[n] = d
	}

	for {
		if len(stack) == 0 { //空栈退出
			break
		}
		for k, v := range stack {
			list[level] = append(list[level], v)
			depC, err := c.listDeps(*v.OpenDepartmentId, &pageToken)
			if err != nil || len(depC.Items) == 0 {
				delete(stack, k)
				continue
			}
			temp = append(temp, depC.Items...)
			if depC.PageToken != nil { //如果有分页，获取下几页的同级部门
				ds := c.getAllSameLevelDeps(*v.OpenDepartmentId, depC.PageToken)
				temp = append(temp, ds...)
			}
			delete(stack, k)
		}
		for n, v := range temp { //压栈
			stack[n] = v
		}
		temp = temp[:0] //清空操作组
		level++
	}
	return list
}

func (c *Client) UpdateMsgCard(msgId string, content string) (*larkim.PatchMessageResp, error) {
	req := larkim.NewPatchMessageReqBuilder().
		Body(larkim.NewPatchMessageReqBodyBuilder().Content(content).Build()).MessageId(msgId).Build()
	resp, err := c.c.Im.Message.Patch(context.Background(), req, larkcore.WithTenantAccessToken(msgId))
	if err != nil {
		return nil, fmt.Errorf("更新消息--->\n%s\n--->出错:%v", resp, err)
	}
	if !resp.Success() {
		return nil, fmt.Errorf("更新消息飞书服务端错误:%v\n--->（可能原因:该用户已离职）", resp.Msg)
	}
	return resp, nil
}

type CardContent struct {
	BackGroundColor  string `mapstructure:"back_ground_color" json:"back_ground_color"`   //消息卡片背景颜色
	CardType         string `mapstructure:"card_type" json:"card_type"`                   //消息卡片名称
	TicketName       string `mapstructure:"ticket_name" json:"ticket_name"`               //消息卡片名称
	TicketType       string `mapstructure:"ticket_type" json:"ticket_type"`               //工单类型中文
	TicketNo         string `mapstructure:"ticket_no" json:"ticket_no"`                   //工单号
	Applicant        string `mapstructure:"applicant" json:"applicant"`                   //页面上创建这个工单的人
	ApplicationTime  string `mapstructure:"application_time" json:"application_time"`     //申请时间
	ApprovalNode     string `mapstructure:"approval_node" json:"approval_node"`           //当前事件的审批节点
	Approve          string `mapstructure:"approve" json:"approve"`                       //审批人
	ServiceName      string `mapstructure:"service_name" json:"service_name"`             //服务名
	Branch           string `mapstructure:"branch" json:"branch"`                         //运行分支
	Clusters         string `mapstructure:"clusters" json:"clusters"`                     //集群信息，多列
	Env              string `mapstructure:"env" json:"env"`                               //部署环境
	Detail           string `mapstructure:"detail" json:"detail"`                         //工单详情
	ReasonForChange  string `mapstructure:"reason_for_change" json:"reason_for_change"`   //变更原因
	OriginReason     string `mapstructure:"origin_reason" json:"origin_reason"`           //基准提测原因
	Result           string `mapstructure:"result" json:"result"`                         //审批结果
	ResultReason     string `mapstructure:"result_reason" json:"result_reason"`           //审批意见
	TicketId         string `mapstructure:"ticket_id" json:"ticket_id"`                   //工单ID
	UserId           string `mapstructure:"user_id" json:"user_id"`                       //审批人用户ID
	UserName         string `mapstructure:"user_name" json:"user_name"`                   //审批人用户名
	EmployeeNo       string `mapstructure:"employee_no" json:"employee_no"`               //审批人用户工号
	Status           string `mapstructure:"status" json:"status"`                         //构建状态
	Mention          string `mapstructure:"mention" json:"mention"`                       //构建提示信息
	PipelineName     string `mapstructure:"pipeline_name" json:"pipeline_name"`           //流水线名
	TriggerMode      string `mapstructure:"trigger_mode" json:"trigger_mode"`             //触发类型
	Trigger          string `mapstructure:"trigger" json:"trigger"`                       //触发人
	StartTime        string `mapstructure:"start_time" json:"start_time"`                 //开始构建时间
	EventTime        string `mapstructure:"event_time" json:"event_time"`                 //事件发生时间
	Stage            string `mapstructure:"stage" json:"stage"`                           //事件发生阶段（stage）
	Task             string `mapstructure:"task" json:"task"`                             //事件发生任务（task）
	ExpireDate       string `mapstructure:"expire_date" json:"expire_date"`               //工单处理期限
	TicketTypeEn     string `mapstructure:"ticket_type_en" json:"ticket_type_en"`         //工单类型
	IsRequiredReport string `mapstructure:"is_required_report" json:"is_required_report"` // 验收报告是否必填
	CheckReport      string `mapstructure:"check_report" json:"check_report"`             // 验收报告地址
	CanaryContent    string `mapstructure:"canary_content" json:"canary_content"`         // 金丝雀发布内容
}

//type Data struct {
//	TemplateId       string    `json:"template_id"`
//	TemplateVariable *Variable `json:"template_variable"`
//}

//type Variable struct {
//	CardType        string `json:"card_type"`        //消息卡片名称
//	TicketType      string `json:"ticket_type"`      //工单类型
//	TicketNo        string `json:"ticket_no"`        //工单号
//	Applicant       string `json:"applicant"`        //页面上创建这个工单的人
//	ApplicationTime string `json:"application_time"` //申请时间
//	ApprovalNode    string `json:"approval_node"`    //当前事件的审批节点
//	Approve         string `json:"approve"`          //审批人
//	ServiceName     string `json:"service_name"`     //服务名
//	Branch          string `json:"branch"`           //运行分支
//	Clusters        string `json:"clusters"`         //集群信息，多列
//	Env             string `json:"env"`              //部署环境
//	//Cluster         string `json:"cluster"`           //集群
//	//Namespace       string `json:"namespace"`         //命名空间
//	Detail          string `json:"detail"`            //工单详情
//	ReasonForChange string `json:"reason_for_change"` //变更原因
//	Result          string `json:"result"`            //审批结果
//	ResultReason    string `json:"result_reason"`     //审批意见
//	TicketId        string `json:"ticket_id"`         //工单ID
//	UserId          string `json:"user_id"`           //审批人用户ID
//	UserName        string `json:"user_name"`         //审批人用户名
//	EmployeeNo      string `json:"employee_no"`       //审批人用户工号
//	Status          string `json:"status"`            //构建状态
//	Mention         string `json:"mention"`           //构建提示信息
//	PipelineName    string `json:"pipeline_name"`     //流水线名
//	TriggerMode     string `json:"trigger_mode"`      //触发类型
//	Trigger         string `json:"trigger"`           //触发人
//	StartTime       string `json:"start_time"`        //开始构建时间
//	EventTime       string `json:"event_time"`        //事件发生时间
//	Stage           string `json:"stage"`             //事件发生阶段（stage）
//	Task            string `json:"task"`              //事件发生任务（task）
//	ExpireDate      string `json:"expire_date"`       //工单处理期限
//}
