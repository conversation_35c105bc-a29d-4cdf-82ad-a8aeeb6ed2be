package main

import (
	"context"
	"log"
	"strconv"
	"strings"
	"testing"

	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/event"
	"52tt.com/cicd/pkg/kafka"
)

func TestListConsumerGroups(t *testing.T) {
	// t.Skip("ingnore tests")
	mySender, err := event.NewSender(kafka.NewConfig(kafka.Name("test-sender"), kafka.Brokers(brokers), kafka.ProducerTopic(topic)))
	if err != nil {
		log.Fatalf("failed %v", err)
	}

	defer mySender.Close(context.Background())

	ctx := cctx.WithRequestInfo(context.Background(), &cctx.RequestInfo{
		RequestID: "requestID",
		UserInfo: cctx.UserInfo{
			UserID:      123,
			ProjectID:   321,
			ChineseName: "chineseName",
			Username:    "username",
			EmployeeNo:  "employeeNo",
			Roles:       strings.Split("roles", ","),
		},
	})

	for i := 0; i < count; i++ {
		e := event.NewPipelineRelatedEvent(map[string]interface{}{
			"id":      i,
			"message": "Hello my sender!",
		})

		e.SetID("============> " + strconv.Itoa(i))
		result := mySender.Send(ctx, e)
		if event.IsUndelivered(result) {
			log.Printf("failed to send: %v", result)
		} else {
			log.Printf("sent: %d, accepted: %t", i, event.IsACK(result))
		}
	}
}
