/*
 Navicat Premium Data Transfer

 Source Server         : cicd-test
 Source Server Type    : MySQL
 Source Server Version : 80022
 Source Host           : ************:3306
 Source Schema         : idehub

 Target Server Type    : MySQL
 Target Server Version : 80022
 File Encoding         : 65001

 Date: 11/06/2024 15:47:32
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for debug_log
-- ----------------------------
DROP TABLE IF EXISTS `debug_log`;
CREATE TABLE `debug_log`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `cluster` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署集群',
  `namespace` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署命名空间',
  `senv` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '子环境 2.0 名称',
  `app_id` bigint(0) NOT NULL COMMENT '应用id',
  `app_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `config_id` bigint(0) NOT NULL COMMENT '配置id',
  `status` tinyint(0) NOT NULL DEFAULT 1 COMMENT '部署结果: 0 未开始 1部署中、2成功、3失败',
  `user_id` bigint(0) NOT NULL COMMENT '操作人id',
  `pod_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'pod名称',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `traffic_mark_id` bigint(0) NULL DEFAULT NULL COMMENT '流量标记id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_app_id`(`app_id`) USING BTREE,
  INDEX `idx_operator_by`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '调试记录' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
