//go:generate mockgen -destination=rdlog_mock.go -package=rdlog -source=rdlog.go
package rdlog

import (
	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
	"context"
	"encoding/json"
	"fmt"
	"strings"
)

var _ Service = (*Client)(nil)

const (
	GetPodLogSearch = "/api/cls/search"
)

type Client struct {
	Host    string
	Token   string
	session httpclient.Session
}

func NewClient(host, token string) *Client {
	session := httpclient.NewSession(&httpclient.SessionOption{})
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": token,
	}
	session.SetHeaders(headers)
	client := &Client{
		Host:    host,
		Token:   token,
		session: session,
	}
	return client
}

type Service interface {
	// GetPodLog 从日志平台获取pod日志
	GetPodLog(ctx context.Context, rd SearchRdLog) (string, error)
}

func (c *Client) buildUrl(path string) string {
	newUrl := c.Host + fmt.Sprintf(path)
	return newUrl
}

func (c *Client) GetPodLog(ctx context.Context, rd SearchRdLog) (string, error) {
	url := c.buildUrl(GetPodLogSearch)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: rd})
	if err != nil {
		log.ErrorWithCtx(ctx, "获取日志平台pod日志序列化参数错误: %v", err)
		return "", err
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求日志平台pod日志错误: %v", err)
		return "", err
	}
	var res RdLog
	if err = httpResp.JsonToStruct(&res); err != nil {
		log.Errorf("结构化日志平台Response 失败: %v", err)
		return "", err
	}
	sb := &strings.Builder{}
	for i := len(res.Response.Results) - 1; i >= 0; i-- {
		l := res.Response.Results[i]
		var logJson LogJson
		if err := json.Unmarshal([]byte(l.LogJson), &logJson); err != nil {
			log.Errorf("parse logs from log platform failed ：%v, content: %s", err, l.LogJson)
			continue
		}

		sb.WriteString("\n")
		contents := []string{
			logJson.LogParseFailure,
			logJson.LogMsg,
			logJson.LogContent,
		}
		for _, content := range contents {
			if content != "" {
				str := strings.Replace(content, "\\\"", "\"", -1)
				sb.WriteString(str)
			}
		}
	}
	return sb.String(), nil
}

type SearchRdLog struct {
	TopicName string `json:"topic_name"`
	Query     string `json:"query"`
	From      int64  `json:"from"`
	To        int64  `json:"to"`
	Context   string `json:"context"`
	Sort      string `json:"sort"`
}

type RdLog struct {
	Response RdLogResponse `json:"Response"`
}

type RdLogResponse struct {
	Context  string         `json:"Context"`
	ListOver bool           `json:"ListOver"`
	Analysis bool           `json:"Analysis"`
	Results  []RdLogResults `json:"Results"`
}

type RdLogResults struct {
	LogJson string `json:"LogJson"`
}

type LogJson struct {
	Tag             Tag    `json:"__TAG__"`
	LogParseFailure string `json:"LogParseFailure"`
	LogMsg          string `json:"msg"`
	LogContent      string `json:"__CONTENT__"`
}

type Tag struct {
	ClusterId     string `json:"cluster_id"`
	ContainerName string `json:"container_name"`
	Namespace     string `json:"namespace"`
	PodName       string `json:"pod_name"`
}
