CREATE TABLE `pipeline` (
  `id` bigint NOT NULL AUTO_INCREMENT  COMMENT 'id',
  `name` varchar(128) COLLATE utf8mb4_bin NOT NULL COMMENT '流水线名称',
  `type` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '流水线类型',
  `app_name` varchar(256) COLLATE utf8mb4_bin NOT NULL COMMENT '应用名称',
  `language` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '语言类型',
  `repo_addr` varchar(256) COLLATE utf8mb4_bin NOT NULL COMMENT '仓库地址',
  `build_path` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT './' COMMENT '构建路径',
  `project_id` bigint NOT NULL COMMENT '关联项目ID',
  `app_id` bigint NOT NULL COMMENT '关联服务ID',
  `trigger_type` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '触发事件类型',
  `trigger_by_change` tinyint NOT NULL COMMENT '是否变更触发',
  `target_branch_type` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '目标分支类型',
  `target_branch_content` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '目标分支内容',
  `template_id` bigint NOT NULL COMMENT '关联流水线模板ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `config` json NOT NULL COMMENT '流水线自定义配置',
  `last_run_id` bigint DEFAULT NULL COMMENT '最近一次流水线ID',
  `pipeline_group_id` bigint NOT NULL COMMENT '流水线组ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_template_id` (`template_id`),
  KEY `idx_last_run_id` (`last_run_id`) USING BTREE,
  KEY `idx_pipeline_group_id` (`pipeline_group_id`) USING BTREE,
  KEY `idx_project_id` (`project_id`) USING BTREE,
  KEY `idx_app_id` (`app_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='流水线信息表';

alter table pipeline.pipeline
    add column `is_auto_merge` tinyint not null default 0 comment '是否自动合并分支';