CREATE TABLE `deploy_change_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `env` tinyint NOT NULL COMMENT '部署环境: 1开发、2测试、3灰度、4生产',
  `env_target` tinyint NOT NULL COMMENT '部署环境目标：1基准环境、2子环境',
  `cluster` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署集群',
  `namespace` varchar(63) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署命名空间',
  `app_id` bigint NOT NULL COMMENT '应用id',
  `app_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'APP名字',
  `config_id` bigint NOT NULL COMMENT '配置id',
  `config_type` tinyint NOT NULL COMMENT '配置类型',
  `config_version` int NOT NULL COMMENT '配置版本',
  `metadata_id` bigint NOT NULL COMMENT '元信息id',
  `is_current` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否当前运行',
  `description` varchar(256) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '部署结果: 1部署中、2成功、3失败',
  `artifact_version` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '制品版本',
  `task_run_id` bigint NOT NULL COMMENT 'task_run_id',
  `subtask_run_id` bigint NOT NULL default 0 COMMENT 'pipeline_run_subtask 子任务id',
  `image_url` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '镜像地址',
  `operator_by` bigint NOT NULL COMMENT '操作人id',
  `operator_by_chinese_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人中文名',
  `operator_by_employee_no` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人工号',
  `operated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='变更记录';

-- 子环境 2.0 名称
alter table deploy_change_log add column senv varchar(63) not null default '' comment '子环境 2.0 名称';

--- 变更类型
alter table deploy_change_log add column action tinyint not null default 0 comment '变更类型: 1部署、2回滚、3下线';

--- 构建分支
alter table deploy_change_log add column branch varchar(128) DEFAULT "" COMMENT '构建分支';