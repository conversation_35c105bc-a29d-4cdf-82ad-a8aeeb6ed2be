create table if not exists image
(
    id                 bigint auto_increment comment 'id' primary key,
    identity_name      varchar(64) collate utf8mb4_bin  not null comment '镜像识别名称',
    address            varchar(128) collate utf8mb4_bin not null comment '镜像访问地址',
    visible_project_id bigint                           not null comment '目标服务器',
    created_at         datetime                         not null comment '创建时间',
    updated_at         datetime                         not null comment '更新时间',
    operator_id        bigint                           not null comment '操作人id',
    operator_no        varchar(128)                     not null comment '操作人工号',
    operator_cname     varchar(128)                     not null comment '操作人中文名',
    constraint uniq_identity_name unique (identity_name)
) engine = InnoDB default charset = utf8mb4 collate = utf8mb4_general_ci comment '镜像管理表';


CREATE TABLE `image_project` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
     `image_id` bigint NOT NULL COMMENT '镜像id',
     `project_id` bigint NOT NULL COMMENT '项目组id',
     `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`)
) COMMENT='镜像项目关联表';

-- 刷新数据
INSERT INTO image_project(image_id, project_id,created_at,updated_at)
SELECT id, visible_project_id,updated_at,updated_at
FROM image