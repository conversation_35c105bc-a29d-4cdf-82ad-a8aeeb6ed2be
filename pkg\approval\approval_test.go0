package approval

import (
	"context"
	"net/http"
	"testing"

	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type ApprovalTestSuite struct {
	log.Suite
	ctrl           *gomock.Controller
	session        *httpclient.MockSession
	approvalClient Service
}

func TestApprovalSuite(t *testing.T) {
	suite.Run(t, new(ApprovalTestSuite))
}

func (s *ApprovalTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.session = httpclient.NewMockSession(s.ctrl)
	s.approvalClient = NewClient("https://approvalclient.com")
	s.approvalClient.(*Client).session = s.session
}

func (s *ApprovalTestSuite) TestTicketApprovedCallback_InvalidReq() {
	expectErr := errors.New("invalid request")

	err := s.approvalClient.TicketApprovedCallback(context.Background(), nil)

	assert.Equal(s.T(), expectErr.Error(), err.Error())
}

func (s *ApprovalTestSuite) TestTicketApprovedCallback_SessionPostError() {
	// given
	expectErr := errors.New("test error")
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(nil, expectErr)

	// when
	err := s.approvalClient.TicketApprovedCallback(context.Background(), []string{"tekton-01"})

	// then
	assert.True(s.T(), errors.Is(err, expectErr))
}

func (s *ApprovalTestSuite) TestTicketApprovedCallback_Failed() {
	// given
	httpResp := &http.Response{
		StatusCode: 400,
	}
	resp := &httpclient.Response{Response: httpResp}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(resp, nil)

	// when
	err := s.approvalClient.TicketApprovedCallback(context.Background(), []string{"tekton-01"})

	// then
	assert.Contains(s.T(), err.Error(), "request Approval ticket callback failed with")
}

func (s *ApprovalTestSuite) TestTicketApprovedCallback_Success() {
	// given
	httpResp := &http.Response{
		StatusCode: 200,
	}
	resp := &httpclient.Response{Response: httpResp}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(resp, nil)

	// when
	err := s.approvalClient.TicketApprovedCallback(context.Background(), []string{"tekton-01"})

	// then
	assert.NoError(s.T(), err)
}

func (s *ApprovalTestSuite) TestDeploy_SessionPostError() {
	// given
	expectErr := errors.New("test error")
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(nil, expectErr)

	// when
	opt := DeployOption{
		TektonName: "tekton-01",
		AppName:    "app-01",
		Namespace:  "default",
		Cluster:    "test-cluster-01",
		HelmRepo:       "https://helmrepo.com",
		Version:   "v1.0.0",
		Manifest: "manifest",
		AppId: 1,
	}
	err := s.approvalClient.Deploy(context.Background(), &opt)

	// then
	assert.True(s.T(), errors.Is(err, expectErr))
}

func (s *ApprovalTestSuite) TestDeploy_HTTPError() {
	// given
	httpResp := &http.Response{
		StatusCode: 400,
	}
	resp := &httpclient.Response{Response: httpResp}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(resp, nil)

	// when
	opt := DeployOption{
		TektonName: "tekton-01",
		AppName:    "app-01",
		Namespace:  "default",
		Cluster:    "test-cluster-01",
		HelmRepo:       "https://helmrepo.com",
		Version:   "v1.0.0",
		Manifest: "manifest",
		AppId: 1,
	}
	err := s.approvalClient.Deploy(context.Background(), &opt)

	// then
	assert.Contains(s.T(), err.Error(), "request Approval deploy failed with")
}

func (s *ApprovalTestSuite) TestDeploy_Success() {
	// given
	httpResp := &http.Response{
		StatusCode: 200,
	}
	resp := &httpclient.Response{Response: httpResp}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(resp, nil)

	// when
	opt := DeployOption{
		TektonName: "tekton-01",
		AppName:    "app-01",
		Namespace:  "default",
		Cluster:    "test-cluster-01",
		HelmRepo:       "https://helmrepo.com",
		Version:   "v1.0.0",
		Manifest: "manifest",
		AppId: 1,
	}
	err := s.approvalClient.Deploy(context.Background(), &opt)

	// then
	assert.NoError(s.T(), err)
}

func (s *ApprovalTestSuite) TestInokeVirtualTask_SessionPostError() {
	// given
	expectErr := errors.New("test error")
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(nil, expectErr)

	// when
	err := s.approvalClient.InvokeVirtualTask(context.Background(), "test-label")

	// then
	assert.True(s.T(), errors.Is(err, expectErr))
}

func (s *ApprovalTestSuite) TestInvokeVirtualTask_HTTPError() {
	// given
	httpResp := &http.Response{
		StatusCode: 400,
	}
	resp := &httpclient.Response{Response: httpResp}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(resp, nil)

	// when
	err := s.approvalClient.InvokeVirtualTask(context.Background(), "test-label")

	// then
	assert.Contains(s.T(), err.Error(), "request Approval virtualTask failed with")
}

func (s *ApprovalTestSuite) TestInvokeVirtualTask_Success() {
	// given
	httpResp := &http.Response{
		StatusCode: 200,
	}
	resp := &httpclient.Response{Response: httpResp}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(resp, nil)

	// when
	err := s.approvalClient.InvokeVirtualTask(context.Background(), "test-label")

	// then
	assert.NoError(s.T(), err)
}
