CREATE TABLE `deploy`.`migrate_task` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `project_id` bigint NOT NULL COMMENT '项目id',
    `origin_cluster` varchar(256) NOT NULL COMMENT '源集群名称',
    `origin_namespace` varchar(64) NOT NULL COMMENT '源命名空间名称',
    `target_cluster` varchar(256) NOT NULL COMMENT '目标集群名称',
    `target_namespace` varchar(64) NOT NULL COMMENT '目标命名空间名称',
    `created_by` bigint NOT NULL COMMENT '创建人',
    `created_by_chinese_name` varchar(64) NOT NULL COMMENT '创建人中文名',
    `created_by_employee_no` varchar(64) NOT NULL COMMENT '创建人工号',
    `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='迁移任务表';

CREATE TABLE `deploy`.`migrate_task_app` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
     `task_id` bigint NOT NULL COMMENT '迁移任务id',
     `project_id` bigint NOT NULL COMMENT '项目id',
     `app_id` bigint NOT NULL COMMENT '服务id',
     `app_name` varchar(128) NOT NULL COMMENT '服务名称',
     `change_log_id` bigint NOT NULL COMMENT '变更记录id',
     `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='迁移任务服务表';