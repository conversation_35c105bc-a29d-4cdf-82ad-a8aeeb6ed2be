CREATE TABLE `app_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `app_id` bigint NOT NULL COMMENT '应用id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='应用负责人信息表';
