-- 迭代7 的SQL变更记录  预计发生产之前，统一提单。如果已自行提单，请备注下
alter table traffic_mark
    add column `origin` bigint not null default 0 comment '跨组织标记的来源';

alter table pipeline.pipeline_group
    add column `is_auto_merge` tinyint not null default 0 comment '是否自动合并分支';

alter table pipeline.pipeline
    add column `is_auto_merge` tinyint not null default 0 comment '是否自动合并分支';

alter table pipeline.pipeline_run
    add column `is_auto_merge` tinyint not null default 0 comment '是否自动合并分支';