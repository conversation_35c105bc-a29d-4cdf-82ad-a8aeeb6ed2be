package aggregate

import (
	"52tt.com/cicd/pkg/cloud"
)

type fullAggClient struct {
	cloud.GRPCClient
	cloud.DeployGRPCClient
	cloud.HTTPClient
	cloud.Service
}

type AggCfg struct {
	HTTPTarget       string
	GRPCTarget       string
	DeployGRPCTarget string
	Token            string
}

func NewAggClient(cfg *AggCfg) (AggClient, error) {
	a := &fullAggClient{}

	gCli, err := cloud.NewGRPCClient(cfg.GRPCTarget, cfg.Token)
	if err != nil {
		return nil, err
	}
	a.GRPCClient = gCli

	dgCli, err := cloud.NewDeployGRPCClient(cfg.DeployGRPCTarget, cfg.Token)
	if err != nil {
		return nil, err
	}
	a.DeployGRPCClient = dgCli

	a.HTTPClient = cloud.NewHTTPClient(cfg.HTTPTarget, cfg.Token)
	a.Service = cloud.NewServiceClient(cfg.HTTPTarget, cfg.Token)
	return a, nil
}
