- name: cicd-notify
  serviceType: deployment
  containers:
    - name: cicd-notify
      dev:
        gitUrl: ""
        image: cr.ttyuyin.com/devops/golang-dlv:1.22-qjf
        shell: ""
        workDir: /home/<USER>
        storageClass: ""
        devContainerName: cicd-notify
        resources:
          limits:
            memory: 4Gi
            cpu: "3"
          requests:
            memory: 2Gi
            cpu: "2"
        persistentVolumeDirs: []
        command:
          run:
            - go run ./services/notify/cmd/main.go --s=./services/notify/etc
          debug:
            - dlv debug ./services/notify/cmd/main.go --listen :9043 --headless=true --api-version=2 --accept-multiclient
        debug:
          remoteDebugPort: 9043
          language: go
        hotReload: false
        sync:
          type: send
          mode: pattern
          filePattern:
            - .
          ignoreFilePattern:
            - .git
            - .github
            - .vscode
            - node_modules
            - ci
        env:
          - name: qqq
            value: zzz
        portForward: []
        sidecarImage: cr.ttyuyin.com/tt_oss/nocalhost-sidecar:syncthing
