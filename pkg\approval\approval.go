//go:generate mockgen -destination=approval_mock.go -package=approval -source=approval.go
package approval

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
)

var _ Service = (*Client)(nil)

type Client struct {
	Host    string
	session httpclient.Session
}

func NewClient(host string) *Client {
	session := httpclient.NewSession(&httpclient.SessionOption{})
	client := &Client{
		Host:    host,
		session: session,
	}
	return client
}

type Service interface {
	// TicketApprovedCallback ticket approve 审批成功回调Tekton
	TicketApprovedCallback(ctx context.Context, tektonNameList []string, deployConfigs []DeployConfig) error
	// Deploy 部署接口
	Deploy(context.Context, *DeployOption) error
	// InvokeVirtualTask 触发变更集虚拟节点运行
	InvokeVirtualTask(ctx context.Context, label string) error
	// SimpleDeploy 短流程发布
	SimpleDeploy(context.Context, *DeployOption) error
	// FakeDeployPass 触发tekton任务success
	FakeDeployPass(ctx context.Context, names []string) error
	// FakeDeploySkip 触发tekton任务 skip
	FakeDeploySkip(ctx context.Context, pipelineTektonName, currTaskName string, skipTask []string) error
}

// DeployConfig 工单审批通过传递相关的部署配置信息
type DeployConfig struct {
	TicketTektonName string `json:"ticketTektonName"`
	FilePath         string `json:"filePath"`    // 指定配置文件的写入路径 /configs/appName/pipelineRunID/deploy-config.json
	FileContent      []byte `json:"fileContent"` // 配置文件文本内容
}

type ticketApprovedReq struct {
	TektonNameList []string       `json:"name_list"`
	DeployConfigs  []DeployConfig `json:"deploy_configs"`
}

type fakeDeployPassReq struct {
	Names []string `json:"names"`
}

type FakeDeploySkipReq struct {
	Name         string   `json:"name"`
	SkipTasks    []string `json:"skipTasks"`
	CurrTaskName string   `json:"currTaskName"`
}

// TicketApprovedCallback ticket approve 审批成功回调Tekton
// tektonNameList: 需要回调的tekton任务名称列表(requried)
// deployConfig: 部署配置信息(optional)
func (c *Client) TicketApprovedCallback(ctx context.Context, tektonNameList []string, deployConfig []DeployConfig) error {
	if len(tektonNameList) == 0 {
		return errors.New("TicketApprovedCallback invalid request")
	}
	url := c.buildUrl("ticket")
	req := ticketApprovedReq{
		TektonNameList: tektonNameList,
	}
	if len(deployConfig) > 0 {
		req.DeployConfigs = deployConfig
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		return errors.Wrap(err, "construct Approval ticket callback request failed")
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		return errors.Wrap(err, "request Approval ticket callback failed")
	}
	if httpResp.StatusCode == 200 {
		log.InfoWithCtx(ctx, "request Approval ticket callback success")
		return nil
	} else {
		return errors.Errorf("request Approval ticket callback failed with %d, http response: %s", httpResp.StatusCode, httpResp.String())
	}
}

type DeployOption struct {
	TektonName      string `json:"-"`
	AppName         string `json:"appName"`
	Namespace       string `json:"namespace"`
	Cluster         string `json:"cluster"`
	HelmRepo        string `json:"helmRepo"`
	Version         string `json:"version"`
	Manifest        string `json:"manifest"`
	AppId           int64  `json:"appId"`
	ArgoAppName     string `json:"argoAppName"`
	NeedArgoReplace bool   `json:"needArgoReplace"` // 是否需要使用 ArgoReplace 命令
	PipelineRunName string `json:"pipelineRunName"`
	ChangeLogID     string `json:"changeLogId"`
	DeployType      string `json:"deployType"`
	PrSubtaskID     int64  `json:"prSubtaskID"` // 判断是否流水线部署，不走流水线的部署设置为0
	Timeout         int64  `json:"timeout"`     // 部署超时时长
}

func (c *Client) Deploy(ctx context.Context, opt *DeployOption) error {
	url := c.buildUrl("deploy/" + opt.TektonName)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: opt})
	if err != nil {
		return errors.Wrap(err, "construct Approval deploy request failed")
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		return errors.Wrap(err, "request Approval deploy failed")
	}
	if httpResp.StatusCode == 200 {
		log.InfoWithCtx(ctx, "request Approval deploy success")
		return nil
	} else {
		return errors.Errorf("request Approval deploy failed with %d, http response: %s", httpResp.StatusCode, httpResp.String())
	}
}

// SimpleDeploy 短流程发布
func (c *Client) SimpleDeploy(ctx context.Context, opt *DeployOption) error {
	url := c.buildUrl("simple-deploy")
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: opt})
	if err != nil {
		return errors.Wrap(err, "construct Approval simple deploy request failed")
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		return errors.Wrap(err, "request Approval simple deploy failed")
	}
	if httpResp.StatusCode == 200 {
		log.InfoWithCtx(ctx, "request Approval simple deploy success")
		return nil
	} else {
		return errors.Errorf("request Approval simple deploy failed with %d, http response: %s", httpResp.StatusCode, httpResp.String())
	}
}

// 触发变更集虚拟节点运行
func (c *Client) InvokeVirtualTask(ctx context.Context, label string) error {
	url := c.buildUrl("virtualTask/" + label)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{})
	if err != nil {
		return errors.Wrap(err, "construct Approval virtualTask request fail")
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		return errors.Wrap(err, "request Approval virtualTask failed")
	}
	if httpResp.StatusCode == 200 {
		log.InfoWithCtx(ctx, "request Approval virtualTask success")
		return nil
	} else {
		return errors.Errorf("request Approval virtualTask failed with %d, http response: %s", httpResp.StatusCode, httpResp.String())
	}
}

// FakeDeployPass 触发tekton任务success
func (c *Client) FakeDeployPass(ctx context.Context, names []string) error {
	url := c.buildUrl("fakeDeploy/pass")
	req := fakeDeployPassReq{
		Names: names,
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		return errors.Wrap(err, "construct Fake deploy pass request failed")
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		return errors.Wrap(err, "request Approval Fake deploy pass failed")
	}
	if httpResp.StatusCode == 200 {
		log.InfoWithCtx(ctx, "request Approval Fake deploy pass success")
		return nil
	} else {
		return errors.Errorf("request Approval Fake deploy pass failed with %d, http response: %s", httpResp.StatusCode, httpResp.String())
	}
}

func (c *Client) FakeDeploySkip(ctx context.Context, pipelineTektonName, currTaskName string, skipTask []string) error {
	url := c.buildUrl("fakeDeploy/skip")

	req := FakeDeploySkipReq{
		Name:         pipelineTektonName,
		SkipTasks:    skipTask,
		CurrTaskName: currTaskName,
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		return errors.Wrap(err, "construct Fake deploy skip request failed")
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		return errors.Wrap(err, "request Approval Fake deploy skip failed")
	}
	if httpResp.StatusCode == 200 {
		log.InfoWithCtx(ctx, "request Approval Fake deploy skip success")
		return nil
	} else {
		return errors.Errorf("request Approval Fake deploy skip failed with %d, http response: %s", httpResp.StatusCode, httpResp.String())
	}
}

func (c *Client) buildUrl(path string) string {
	return fmt.Sprintf("%s%s", c.Host, path)
}
