package route

import (
	"fmt"

	"github.com/gin-gonic/gin"
)

// 此处路由功能，负责注册每个open api接口的路由

type Route interface {
	Register(route *gin.RouterGroup)
}

type Router struct {
	rootRouter *gin.RouterGroup
}

func alreadyRegister(routes []gin.RouteInfo, route string) bool {
	for _, item := range routes {
		if item.Path == route {
			return true
		}
	}
	return false
}

func NewRoute(group *gin.Engine, route string) *Router {
	routeList := group.Routes()
	exists := alreadyRegister(routeList, route)
	if exists {
		panic(fmt.Sprintf("route %s already exists, cannot duplicate register!", route))
	}
	rootRoute := group.Group(route)
	return &Router{
		rootRouter: rootRoute,
	}
}

func (router *Router) Register(routes ...func(*gin.RouterGroup)) {
	for _, route := range routes {
		route(router.rootRouter)
	}
}
func (router *Router) Use(middleware ...gin.HandlerFunc) {
	router.rootRouter.Use(middleware...)
}
