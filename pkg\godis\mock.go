package godis

import (
	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"strings"
)

const redisErrName = "RedisErr"

type Suite struct {
	suite.Suite

	Cli       *redis.Client
	MiniRedis *miniredis.Miniredis
}

func (suite *Suite) SetupTest() {
	suite.ResetRedis()
}

func (suite *Suite) BeforeTest(suiteName, testName string) {
	if strings.Contains(testName, redisErrName) {
		suite.MiniRedis.Close()
	}
}

func (suite *Suite) ResetRedis() {
	suite.MiniRedis = miniredis.RunT(suite.T())
	suite.Cli = redis.NewClient(&redis.Options{
		Addr: suite.MiniRedis.Addr(),
	})

	Rdb = suite.Cli
}
