alter table `deploy`.`ticket`
    add column `is_stage_check` tinyint default 0 comment '是否预发布验证';

alter table `deploy`.`ticket_story`
    add column `is_dev_test` tinyint comment '是否研发自测 1:是',
    add column `is_stage_check` tinyint default 0 comment '是否预发布验证 1:是',
    add column `dev_ser_rela` json comment '需求与服务关联';



-- 工单 需求 流水线运行 关联关系
CREATE TABLE `deploy`.`ticket_story_run` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `story_run_id` bigint NOT NULL COMMENT '需求流水线运行表id',
  `ticket_story_id` bigint NOT NULL COMMENT '工单需求补充表id',
  `pipeline_id`       bigint          NOT NULL COMMENT '流水线id',
  `pipeline_run_number` bigint NOT NULL COMMENT '流水线运行记录号',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_run` (`story_run_id`) USING BTREE,
  KEY `idx_ticket` (`ticket_story_id`) USING BTREE,
  KEY `idx_pipe_run` (`pipeline_id`,`pipeline_run_number`) USING BTREE
) ENGINE=InnoDB COMMENT='工单 需求 流水线运行 关联关系';


-- 需求和流水线运行关联
CREATE TABLE `deploy`.`story_change_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
  -- 需求数据
  `story_id` varchar(64) NOT NULL default '' COMMENT '需求id',
  `iteration_id` varchar(32) NOT NULL default '' COMMENT '迭代id',
  `workspace_id` varchar(32) NOT NULL default '' COMMENT 'Tapd项目id',
  `story_name` varchar(256) NOT NULL default '' COMMENT '需求标题',
  `origin_status` varchar(128) NOT NULL COMMENT '源来状态',
  `target_status` varchar(128) NOT NULL COMMENT '目标状态',
  `is_failed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1:修改失败了',
  `failed_msg` varchar(512) NOT NULL DEFAULT '' COMMENT '失败消息',
  -- 公共部分
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_story` (`story_id`) USING BTREE
) ENGINE=InnoDB COMMENT='需求状态变更记录表';