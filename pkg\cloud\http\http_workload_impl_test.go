package http

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/require"

	"52tt.com/cicd/pkg/cloud/http/core"
)

func TestWorkloadHTTPClient_GetReadyReplicas(t *testing.T) {
	t.Skip()
	host := os.Getenv("CLOUD_HOST")
	token := os.Getenv("CLOUD_TOKEN")
	client := NewWorkloadHTTPClient(host, token)
	resp, err := client.GetReadyReplicas(context.Background(), &core.GetReadyReplicasRequest{
		Cluster:      "k8s-hw-bj-zt-test-turbo",
		Namespace:    "media",
		Kind:         "StatefulSet",
		ResourceName: "agora-streaming-uki-s2",
	})

	require.NoError(t, err)
	t.Logf("%+v", resp)
}
