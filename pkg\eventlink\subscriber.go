package eventlink

import (
	"context"

	"github.com/tidwall/sjson"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
)

func (c *eventLinkImpl) DelSB(ctx context.Context, cluster, ns, name string) (err error) {
	_, err = c.cloudClient.Delete(ctx, &constack.DeleteRequest{
		Cluster:   cluster,
		Namespace: ns,
		Name:      name,
		GroupVersionResource: &constack.GroupVersionResource{
			Group:    "event.tt.io",
			Version:  "v1alpha2",
			Resource: "subscribers",
		},
	})
	if err != nil {
		return
	}
	return
}

func (c *eventLinkImpl) UpdSB(ctx context.Context, cluster, ns, name string, cols map[string]string) (err error) {
	if len(cols) == 0 {
		return
	}
	// AnyResource 必须先查，在全量属性更新
	crd, err := c.cloudClient.Get(ctx, &constack.GetRequest{
		Cluster:   cluster,
		Namespace: ns,
		Name:      name,
		GroupVersionResource: &constack.GroupVersionResource{
			Group:    "event.tt.io",
			Version:  "v1alpha2",
			Resource: "subscribers",
		},
	})

	if err != nil {
		return
	}

	strRaw := crd.Data

	for k, v := range cols {
		path := "spec." + k
		strRaw, err = sjson.SetRaw(strRaw, path, v)
		if err != nil {
			return
		}
	}

	_, err = c.cloudClient.Update(ctx, &constack.UpdateRequest{
		Cluster:   cluster,
		Namespace: ns,
		Data:      strRaw,
	})
	if err != nil {
		return
	}
	return
}
