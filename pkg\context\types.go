package context

import (
	"fmt"
	"strings"
)

type (
	userinfoKey      struct{}
	requestInfoKey   struct{}
	token<PERSON>ey         struct{}
	qwTrafficMarkKey struct{}
)

var (
	emptyUserinfo    UserInfo
	emptyRequestInfo RequestInfo
)

type UserInfo struct {
	UserID      int64
	Username    string
	ChineseName string
	EmployeeNo  string
	Roles       []string
	ProjectID   int64
}

func (ui *UserInfo) String() string {
	return fmt.Sprintf("UserInfo:{ uid:%d username:%s displayName:%s(%s) project:%d roles:%v }",
		ui.UserID, ui.Username, ui.ChineseName, ui.EmployeeNo, ui.ProjectID, ui.Roles)
}

func (ui *UserInfo) RolesString() string {
	return strings.Join(ui.Roles, ",")
}

type RequestInfo struct {
	UserInfo
	RequestID string
}

func (ri *RequestInfo) String() string {
	return fmt.Sprintf("RequestInfo:{ uid:%d username:%s displayName:%s(%s) project:%d roles:%v requestID:%s }",
		ri.UserID, ri.Username, ri.ChineseName, ri.EmployeeNo, ri.ProjectID, ri.Roles, ri.RequestID)
}
