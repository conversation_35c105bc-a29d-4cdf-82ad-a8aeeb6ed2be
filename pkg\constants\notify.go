package constants

type MessageType string

const (
	TEXT        MessageType = "text"        //文本
	POST        MessageType = "post"        //富文本
	IMAGE       MessageType = "image"       //图片
	INTERACTIVE MessageType = "interactive" //消息卡片
	SHARE_CHAT  MessageType = "share_chat"  //分享群名片
	SHARE_USER  MessageType = "share_user"  //分享个人名片
	AUDIO       MessageType = "audio"       //语音
	MEDIA       MessageType = "media"       //视频
	FILE        MessageType = "file"        //文件
	STICKER     MessageType = "sticker"     //表情包
)

type DeployEnvDisplay string

const (
	LarkOriginEnv      DeployEnvDisplay = "基准环境"
	LarkStagingEnv     DeployEnvDisplay = "预发布环境"
	LarkCanaryEnv      DeployEnvDisplay = "金丝雀环境"
	LarkSubEnv2        DeployEnvDisplay = "子环境2.0"
	LarkSubEnv         DeployEnvDisplay = "子环境1.0"
	LarkSubEnvironment DeployEnvDisplay = "子环境"
)

func (f DeployEnvDisplay) String() string {
	return string(f)
}

func GetEnvDescribe(envTarget string, senv string) string {
	if senv == CANARY {
		return LarkCanaryEnv.String()
	} else if senv == STAGING {
		return LarkStagingEnv.String()
	} else if envTarget == ORIGIN.String() {
		return LarkOriginEnv.String()
	} else if envTarget == SUB.String() {
		return LarkSubEnv.String()
	} else if envTarget == SUB_V2.String() {
		return LarkSubEnv2.String()
	}
	return ""
}

func ToMessageDesc(n int32) MessageType {
	switch n {
	case 0:
		return TEXT
	case 1:
		return POST
	case 2:
		return IMAGE
	case 3:
		return INTERACTIVE
	case 4:
		return SHARE_CHAT
	case 5:
		return SHARE_USER
	case 6:
		return AUDIO
	case 7:
		return MEDIA
	case 8:
		return FILE
	case 9:
		return STICKER
	default:
		return ""
	}
}

type FeishuTemplate string

const (
	APPROVALTICKET               FeishuTemplate = "approvalTicket"               // 审批过后 发送给提单人的飞书模板
	TESTACCEPTANCETICKET         FeishuTemplate = "testAcceptanceTicket"         // 测试验收审批模板
	UPGRADETICKET                FeishuTemplate = "upgradeTicket"                // 升级审批模板
	UPDATETICKET                 FeishuTemplate = "updateTicket"                 // 更新审批模板
	TESTACCEPTANCEAPPROVALTICKET FeishuTemplate = "testAcceptanceApprovalTicket" // 测试验收审批后发送给提单人的飞书模板
	TESTACCEPTANCEUPDATETICKET   FeishuTemplate = "testAcceptanceUpdateTicket"   // 测试验收更新审批模板
	ROUTEAPPLYTICKET             FeishuTemplate = "routeApplyTicket"             // 路由申请审批模板
	ROUTEAPPLYUPDATETICKET       FeishuTemplate = "routeApplyUpdateTicket"       // 路由申请审批后更新审批模板
	EVENTLINKAPPLYTICKET         FeishuTemplate = "eventLinkApplyTicket"         // eventlink申请审批模板
	EVENTLINKAPPLYUPDATETICKET   FeishuTemplate = "eventLinkApplyUpdateTicket"   // eventlink申请审批后更新审批模板
)

func (f FeishuTemplate) String() string {
	return string(f)
}

const UrlRegex = `^(http|https|lark)://[a-zA-Z0-9]+(.[a-zA-Z0-9/]+)+([a-zA-Z0-9-._?,'+/\~:#[]@!$&amp;*])*$`

type EnumCanaryPolicyDisplay string

const (
	LarkTRAFFIC EnumCanaryPolicyDisplay = "流量比例"
	// LarkClientVersion 业务含义变更为使用发布计划策略
	LarkClientVersion EnumCanaryPolicyDisplay = "使用发布计划策略"
)

func (f EnumCanaryPolicyDisplay) String() string {
	return string(f)
}

func GetCanaryPolicyLarkDisplay(policy string) string {
	if policy == PolicyTraffic.String() {
		return LarkTRAFFIC.String()
	} else if policy == PolicyClientVersion.String() {
		return LarkClientVersion.String()
	}
	return "-"
}
