{"config": {"wide_screen_mode": true, "update_multi": true}, "elements": [{"tag": "div", "text": {"content": "➤𝙎𝙚𝙣𝙩𝙞𝙣𝙖𝙡︰\n*❗您有待处理的审批任务，请及时处理*", "tag": "lark_md"}}, {"tag": "hr"}, {"tag": "div", "text": {"content": "🟢**基本信息**", "tag": "lark_md"}}, {"tag": "markdown", "content": "工单类型：${ticket_type}\n工单ID：　${ticket_no}\n变更原因：${reason_for_change}\n申请人：　<font color='red'>${applicant}</font>\n申请时间：*${application_time}*\n审批节点：${approval_node}\n审批人：　<font color='red'>${approve}</font>"}, {"tag": "markdown", "content": "🟢**部署信息**"}, {"tag": "markdown", "content": "服务名：　${service_name}\n运行分支：${branch}\n部署环境：${env}\n${clusters}"}, {"tag": "markdown", "content": "[>>工单入口<<](${detail})", "text_align": "left"}, {"tag": "div", "text": {"content": "请于<font color='red'>${expire_date}</font>前尽快处理工单", "tag": "lark_md"}}, {"tag": "action", "actions": [{"tag": "button", "text": {"tag": "plain_text", "content": "通过"}, "type": "primary", "value": {"is_approved": "1", "ticket_id": "${ticket_id}", "user_id": "${user_id}", "user_name": "${user_name}", "employee_no": "${employee_no}", "card_type": "${card_type}", "ticket_type": "${ticket_type}", "ticket_no": "${ticket_no}", "applicant": "${applicant}", "application_time": "${application_time}", "approval_node": "${approval_node}", "approve": "${approve}", "service_name": "${service_name}", "branch": "${branch}", "env": "${env}", "cluster": "${cluster}", "namespace": "${namespace}", "detail": "${detail}", "reason_for_change": "${reason_for_change}"}}, {"tag": "button", "text": {"tag": "plain_text", "content": "驳回"}, "type": "danger", "value": {"is_approved": "0", "ticket_id": "${ticket_id}", "user_id": "${user_id}", "user_name": "${user_name}", "employee_no": "${employee_no}", "card_type": "${card_type}", "ticket_type": "${ticket_type}", "ticket_no": "${ticket_no}", "applicant": "${applicant}", "application_time": "${application_time}", "approval_node": "${approval_node}", "approve": "${approve}", "service_name": "${service_name}", "branch": "${branch}", "env": "${env}", "cluster": "${cluster}", "namespace": "${namespace}", "detail": "${detail}", "reason_for_change": "${reason_for_change}"}}]}], "header": {"template": "green", "title": {"content": "CICD${card_type}", "tag": "plain_text"}}}