package engine

import (
	"52tt.com/cicd/pkg/config"
	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/engine/middleware"
	"52tt.com/cicd/pkg/godis"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/log/hook"
	"52tt.com/cicd/pkg/log/legacy"
	"52tt.com/cicd/pkg/log/logger/zap"
	"52tt.com/cicd/pkg/route"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type LocalEngine struct {
	Engine *gin.Engine
	router *route.Router
	c      config.Configuration
}

func NewEngine(c config.Configuration, option *EngineOptions) *LocalEngine {
	mode := "debug"
	basePrefix := "/"
	if option != nil {
		basePrefix = option.BasePath
		mode = option.Mode
	}
	gin.SetMode(mode)
	local := gin.New()
	local.Use(middleware.RequestID())
	// 设置日志格式为JSON格式
	logrusLogger := logrus.New()
	logrusLogger.SetFormatter(&logrus.JSONFormatter{DisableHTMLEscape: true, TimestampFormat: legacy.TimePattern})
	local.Use(gin.LoggerWithWriter(logrusLogger.Writer()))

	router := route.NewRoute(local, basePrefix)

	localEngine := &LocalEngine{
		Engine: local,
		router: router,
		c:      c,
	}

	return localEngine
}

func (local *LocalEngine) Init() error {
	if err := local.InitLog(); err != nil {
		return err
	}

	if err := db.InitDB(local.c.GetDbConfig(), db.Logger(zap.NewGORMLogger())); err != nil {
		return err
	}
	if err := godis.InitRedis(local.c.GetRedisConfig()); err != nil {
		return err
	}
	return nil
}

func (local *LocalEngine) InitLog() error {
	if err := log.InitLog(local.c.GetLogConfig()); err != nil {
		return err
	}

	// 注册hook，打印上下文请求信息
	log.RegisterHook(hook.ExtraFieldFromContext)

	// 临时方案，使用旧的配置项设置日志等级
	lv, err := log.ParseLevel(local.c.GetLogConfig().Loglevel)
	if err != nil {
		return err
	}
	log.SetLevel(lv)
	return nil
}

func (local *LocalEngine) Use(middlewares ...gin.HandlerFunc) {
	local.router.Use(middlewares...)
}

func (local *LocalEngine) Register(routes ...func(*gin.RouterGroup)) {
	local.router.Register(routes...)
}

func (local *LocalEngine) Route(routeName string) *route.Router {
	router := route.NewRoute(local.Engine, routeName)
	return router
}
