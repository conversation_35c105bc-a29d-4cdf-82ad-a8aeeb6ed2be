package constants

type NhctlOpRecordClientType string
type NhctlOpRecordIdeType string
type NhctlOpRecordSystemType string
type NhctlOpRecordActionType string
type NhctlOpRecordResultCodeType int

const (
	NhctlOpRecordClientTypeUnknown   NhctlOpRecordClientType = "unknown"
	NhctlOpRecordClientTypeNocalhost NhctlOpRecordClientType = "nocalhost"
	NhctlOpRecordClientTypeJetDev    NhctlOpRecordClientType = "jetDev"

	NhctlOpRecordIdeTypeUnknown NhctlOpRecordIdeType = "unknown"
	NhctlOpRecordIdeTypeVsCode  NhctlOpRecordIdeType = "vscode"
	NhctlOpRecordIdeTypeGoland  NhctlOpRecordIdeType = "goland"

	NhctlOpRecordSystemTypeUnknown NhctlOpRecordSystemType = "unknown"
	NhctlOpRecordSystemTypeMac     NhctlOpRecordSystemType = "mac"
	NhctlOpRecordSystemTypeWindows NhctlOpRecordSystemType = "windows"
	NhctlOpRecordSystemTypeLinux   NhctlOpRecordSystemType = "linux"

	NhctlOpRecordActionUnknown             NhctlOpRecordActionType = "unknown"
	NhctlOpRecordActionStartDevMode        NhctlOpRecordActionType = "startDevMode"
	NhctlOpRecordActionStartRemoteRun      NhctlOpRecordActionType = "remoteRun"
	NhctlOpRecordActionStartRemoteDebug    NhctlOpRecordActionType = "remoteDebug"
	NhctlOpRecordActionStartRemoteTerminal NhctlOpRecordActionType = "remoteTerminal"
	NhctlOpRecordActionEndDevMode          NhctlOpRecordActionType = "endDevMode"

	NhctlOpRecordResultCodeUnknown    NhctlOpRecordResultCodeType = -1
	NhctlOpRecordResultCodeSuccessful NhctlOpRecordResultCodeType = 0
	NhctlOpRecordResultCodeFailed     NhctlOpRecordResultCodeType = 1
)
