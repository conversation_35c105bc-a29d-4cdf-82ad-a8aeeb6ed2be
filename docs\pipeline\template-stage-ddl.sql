CREATE TABLE `stage` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '阶段名称中文',
  `template_id` bigint NOT NULL COMMENT '关联流水线模板ID',
  `task_sequence` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联任务ID顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '阶段类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='流水线模板阶段表';
