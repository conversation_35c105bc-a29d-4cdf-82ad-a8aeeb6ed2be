//go:generate mockgen -destination=event_link_mock.go -package=eventlink -source=event_link.go
package eventlink

import (
	"context"

	"52tt.com/cicd/pkg/cloud/aggregate"
)

// EventLink CRD 增删改查
type EventLink interface {
	// CreatCB ConsumerBinding 增
	CreatCB(ctx context.Context, cluster, ns string, req ConsumerBinding) (err error)
	// DelCB ConsumerBinding 删
	DelCB(ctx context.Context, cluster, ns, name string) (err error)
	// GetCB ConsumerBinding 查
	GetCB(ctx context.Context, cluster, ns, name string) (data string, resp ConsumerBinding, has bool, err error)

	// DelSB Subscriber 删
	DelSB(ctx context.Context, cluster, ns, name string) (err error)
	// UpdSB Subscriber 改
	UpdSB(ctx context.Context, cluster, ns, name string, cols map[string]string) (err error)
}

type eventLinkImpl struct {
	cloudClient aggregate.AggClient
}

func NewEventLink(cloudClient aggregate.AggClient) EventLink {
	return &eventLinkImpl{cloudClient: cloudClient}
}
