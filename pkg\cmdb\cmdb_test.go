package cmdb

import (
	"52tt.com/cicd/pkg/tools/random"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"

	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
	apperr "52tt.com/cicd/services/app/pkg/error"
	gomock "github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestClient_GetModulesTree(t *testing.T) {
	t.Skip("nil error")
	client := &Client{
		Host:  "http://*************:5000/api/jsonrpc/",
		Token: "zkFJd720KfR8Mx382TDDcB1fPwr5x0pWT5YHSn9zbKCqG3idPn6rLSzkMykKru0o",
	}
	resp, err := client.GetModulesTree(context.Background())
	if err != nil || resp.Err != nil {
		t.Error(err)
	}
	data, err := json.<PERSON>(resp)
	if err != nil {
		t.Error(err)
	}
	fmt.Println(string(data))
	assert.NoError(t, resp, nil)
}

func TestClient_GetApp(t *testing.T) {
	t.Skip("timeout and nil pointer dereference")
	client := &Client{
		Host:  "http://*************:5000/api/jsonrpc/",
		Token: "zkFJd720KfR8Mx382TDDcB1fPwr5x0pWT5YHSn9zbKCqG3idPn6rLSzkMykKru0o",
	}
	ids := []string{
		"5f6c790cc820857cf861b240",
	}
	apps, err := client.GetApps(context.Background(), ids)
	if err != nil {
		t.Error(err)
	}
	assert.Equal(t, 1, len(apps), "expect result count equal one")
}

type CmdbSuite struct {
	log.Suite
	ctrl            *gomock.Controller
	session         *httpclient.MockSession
	cmdbServiceTest Service
}

func (s *CmdbSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.session = httpclient.NewMockSession(s.ctrl)
	s.cmdbServiceTest = NewClient("http://cmdbclietn.com", "cmdb-token")
	s.cmdbServiceTest.(*Client).session = s.session
}

func TestCmdbServiceSuite(t *testing.T) {
	suite.Run(t, new(CmdbSuite))
}

func (s *CmdbSuite) TestCreateApp_SessionError() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"id": 0, "jsonrpc": "2.0" "result": {"id": "testid"}}`),
	}
	app := &App{
		Name: "test",
	}
	expectErr := fmt.Errorf("test error")
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, expectErr)
	_, err := s.cmdbServiceTest.CreateApp(context.Background(), app)
	assert.Equal(s.T(), expectErr, err)
}

func (s *CmdbSuite) TestCreateApp_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"id": 0, "jsonrpc": "2.0", "result": {"id": "testid"}}`),
	}

	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)
	id, err := s.cmdbServiceTest.CreateApp(context.Background(), &App{
		Name: "test",
	})
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), "testid", id)
}

func (s *CmdbSuite) TestGetAlias_AppIdExist() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"id": 0, "jsonrpc": "2.0", "error": {"code": -101, "message": "test error"}}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)

	alias, err := s.cmdbServiceTest.GetAlias(context.Background(), &App{Name: "test"})
	assert.Equal(s.T(), "", alias)
	assert.Equal(s.T(), err, apperr.ErrAppExisted)
}

func (s *CmdbSuite) TestGetAlias_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"id": 0, "jsonrpc": "2.0", "result": {"app_alias": "testalias"}}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)

	alias, err := s.cmdbServiceTest.GetAlias(context.Background(), &App{Name: "test"})
	assert.Equal(s.T(), "testalias", alias)
	assert.NoError(s.T(), err)
}

func (s *CmdbSuite) TestUpdateApp_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"id": 0, "jsonrpc": "2.0", "result": {"id": "testid"}}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)

	_, err := s.cmdbServiceTest.UpdateApp(context.Background(), &App{Name: "test"})
	assert.NoError(s.T(), err)
}

func (s *CmdbSuite) TestGetApp_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`
        {
            "id": 0,
            "jsonrpc": "2.0",
            "result": [
              {
                "_business_label_l1_obj_id": "61b6f4816c6797a6a292cf4e",
                "_business_label_l2_obj_id": "62afd9e20e5f732827ff1b26",
                "_business_label_l3_obj_id": "62afd9e20e5f732827ff1b27",
                "_business_label_l4_obj_id": null,
                "app_alias": "sd.sdqd.sdAndroid.applogic",
                "app_class": "default",
                "app_create_time": null,
                "business_label_l1_name": "声洞",
                "business_label_l2_name": "声洞前端",
                "business_label_l3_name": "声洞Android",
                "business_label_l4_name": null,
                "description": "描述",
                "develop_user_list": [
                  {
                    "email": "<EMAIL>",
                    "name": "lichao1",
                    "real_name": "李超"
                  }
                ],
                "id": "5f72fa57415ece87465d0b86",
                "is_bundled": null,
                "language": "cpp",
                "level": "p1",
                "name": "applogic",
                "ops_user_list": [
                  {
                    "email": "<EMAIL>",
                    "name": "liuzhimin",
                    "phone": null,
                    "real_name": "刘智敏"
                  }
                ]
              }
            ]
          }
`)}

	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)

	apps, err := s.cmdbServiceTest.GetApps(context.Background(), []string{"5f72fa57415ece87465d0b86"})
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), 1, len(apps))
	assert.Equal(s.T(), "5f72fa57415ece87465d0b86", apps[0].ID)
	assert.Equal(s.T(), "applogic", apps[0].Name)
}

func (s *CmdbSuite) TestGetModulesTree() {
	httResp := &httpclient.Response{
		Body: []byte(`
        {
            "id": 0,
            "jsonrpc": "2.0",
            "result": {
                "alias": null,
                "id": "5f72fa57415ece87465d0b86",
                "level": "p1",
                "name": "applogic",
                "v0": null,
                "v202206": null,
                "children": [
                {
                    "alias": null,
                    "children": [
                      {
                        "alias": null,
                        "children": [],
                        "id": "6295b61d8cdbcb1d5da34b24",
                        "level": 3,
                        "name": "声岛官网-test",
                        "v0": true,
                        "v202206": true
                      }
                    ],
                    "id": "6295b60d33c435526ce91229",
                    "level": 2,
                    "name": "声岛",
                    "v0": true,
                    "v202206": true
                }
                ]
            }
        }
`)}

	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httResp, nil)

	_, err := s.cmdbServiceTest.GetModulesTree(context.Background())
	assert.NoError(s.T(), err)
}

func (s *CmdbSuite) TestGetModelList_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`
        {
			"code": 0,
			"count": 1,
			"items": [
				{
					"private_id": "123"
				}
			]
		}
`)}

	s.session.EXPECT().Get(gomock.Any(), gomock.Any()).Return(httpResp, nil)

	models, err := s.cmdbServiceTest.GetModelList(context.Background(), "kubernetes", map[string]string{"name": "k8s-tc-bj-test"})
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), 1, len(models))
	assert.Equal(s.T(), "123", models[0]["private_id"].(string))
}

func (s *CmdbSuite) TestGetModelList_Error() {
	httpResp := &httpclient.Response{
		Body: []byte(`
        {
			"code": -1,
			"error": "未找到"
		}
`)}

	s.session.EXPECT().Get(gomock.Any(), gomock.Any()).Return(httpResp, nil)

	_, err := s.cmdbServiceTest.GetModelList(context.Background(), "kubernetes", map[string]string{"name": "k8s-tc-bj-test"})
	assert.Equal(s.T(), "获取模型实例数据列表失败: 未找到", err.Error())
}

func (s *CmdbSuite) TestBatchUpdateApp_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"id": 0, "jsonrpc": "2.0", "result": {"status": true, "apps": [{"cmdb_id": "abc", "alias": "aa.bb.cc"}]}}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)

	resp, err := s.cmdbServiceTest.BatchUpdateApp(context.Background(), &BatchUpdateCmdbApp{
		Apps:            []App{{ID: random.GenerateRandomAlphaNumericString(6)}},
		DevelopUserList: []string{"zhangsan"},
	})
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), "aa.bb.cc", resp.Result.Apps[0].Alias)
}

func (s *CmdbSuite) TestBatchUpdateApp_Update_Error() {
	wantErr := errors.New("patch apps failed")
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(nil, wantErr)

	_, err := s.cmdbServiceTest.BatchUpdateApp(context.Background(), &BatchUpdateCmdbApp{
		Apps:            []App{{ID: random.GenerateRandomAlphaNumericString(6)}},
		DevelopUserList: []string{"zhangsan"},
	})
	assert.Equal(s.T(), wantErr.Error(), err.Error())
}

func (s *CmdbSuite) TestBatchUpdateApp_RespToStruct_Error() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"id": 0, "jsonrpc": "2.0", "result": {"status": true}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)

	_, err := s.cmdbServiceTest.BatchUpdateApp(context.Background(), &BatchUpdateCmdbApp{
		Apps:            []App{{ID: random.GenerateRandomAlphaNumericString(6)}},
		DevelopUserList: []string{"zhangsan"},
	})
	assert.Equal(s.T(), "unexpected end of JSON input", err.Error())
}

func (s *CmdbSuite) TestBatchUpdateApp_UpdateResp_Error() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"id": 0, "jsonrpc": "2.0", "error": {"code": -1, "message": "_business_label_obj_id not exist"}}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)

	_, err := s.cmdbServiceTest.BatchUpdateApp(context.Background(), &BatchUpdateCmdbApp{
		Apps:            []App{{ID: random.GenerateRandomAlphaNumericString(6)}},
		DevelopUserList: []string{"zhangsan"},
	})
	assert.Equal(s.T(), "请求CMDB接口批量修改应用错误: _business_label_obj_id not exist", err.Error())
}
