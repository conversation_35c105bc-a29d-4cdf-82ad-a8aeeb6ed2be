package http

import (
	"context"
	"errors"

	"52tt.com/cicd/pkg/cloud/http/core"
	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
)

// https://q9jvw0u5f5.feishu.cn/wiki/wikcn4wuf4LoKd1gpDFO8D5tOFh
const (
	clusterList          = "/cicd/cluster/list"
	clusterNamespaceList = "/cicd/cluster/namespace/list"
)

func NewClusterNamespaceHTTPClient(host, token string) core.ClusterNamespaceClient {
	session := buildSession(token)
	client := NewClusterNamespaceHTTPClientWithSession(host, token, session)
	return client
}

func NewClusterNamespaceHTTPClientWithSession(host, token string, session httpclient.Session) core.ClusterNamespaceClient {
	return &cNamespaceHTTPClient{
		Host:    host,
		Token:   token,
		session: session,
	}
}

type cNamespaceHTTPClient struct {
	Host    string
	Token   string
	session httpclient.Session
}

func (c *cNamespaceHTTPClient) ListCluster(ctx context.Context, in *core.ListClusterRequest) (*core.ListClusterResponse, error) {
	url := buildURL(c.Host, clusterList)

	log.DebugWithCtx(ctx, "[ListCluster] req=%+v", in)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: in})
	if err != nil {
		log.ErrorWithCtx(ctx, "[ListCluster] init cloud ceq, error: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[ListCluster] send req list cluster, error: %v, env=%v, emails=%v", err, in.Env, in.Emails)
		return nil, err
	}

	var out core.ListClusterResponse
	if err = httpResp.JsonToStruct(&out); err != nil {
		log.ErrorWithCtx(ctx, "[ListCluster] parse response, error: %v", err)
		return nil, err
	}
	if out.Code != 0 {
		log.ErrorWithCtx(ctx, "[ListCluster] list cluster failed, return code is not zero, req=%+v, resp=%+v", in, out)
		return nil, errors.New(out.Message)
	}

	log.DebugWithCtx(ctx, "[ListCluster] resp=%+v", out)
	return &out, nil
}

func (c *cNamespaceHTTPClient) ListClusterNamespace(ctx context.Context, in *core.ListClusterNamespaceRequest) (*core.ListClusterNamespaceResponse, error) {
	url := buildURL(c.Host, clusterNamespaceList)

	log.DebugWithCtx(ctx, "[ListClusterNamespace] req=%+v", in)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: in})
	if err != nil {
		log.ErrorWithCtx(ctx, "[ListClusterNamespace] init cloud ceq, error: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[ListClusterNamespace] send req list cluster namespace, error: %v, cluster=%v", err, in.Cluster)
		return nil, err
	}

	var out core.ListClusterNamespaceResponse
	if err = httpResp.JsonToStruct(&out); err != nil {
		log.ErrorWithCtx(ctx, "[ListClusterNamespace] parse response, error: %v", err)
		return nil, err
	}
	if out.Code != 0 {
		log.ErrorWithCtx(ctx, "[ListClusterNamespace] list cluster namespace failed, return code is not zero, req=%+v, resp=%+v", in, out)
		return nil, errors.New(out.Message)
	}

	log.DebugWithCtx(ctx, "[ListClusterNamespace] resp=%+v", out)
	return &out, nil
}
