package clean_job

import (
	"context"
	"strings"
	"time"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/protocol/deploy"
	"52tt.com/cicd/services/schedule/internal/dao"
	"52tt.com/cicd/services/schedule/internal/model"
)

type CleanCDLogs struct {
	depolyRpc deploy.DeployServiceClient
}

func NewCleanCDLogs(rpc deploy.DeployServiceClient) *CleanCDLogs {
	return &CleanCDLogs{
		depolyRpc: rpc,
	}
}

func (job *CleanCDLogs) Run() {
	log.Info("🐲🐲🐲 CleanCDLogs start.")
	repo := dao.NewDeployRunRepo()
	prjRepo := dao.NewAppPrjRepo()

	prjs, err := prjRepo.ListProjects(context.Background(), &model.ListProjectsReq{
		UserGroup: "TT",
	})
	if err != nil {
		log.Errorf("CleanCDLogs ListProjects Err %v", err)
		return
	}
	isCurrent := true
	for _, prj := range prjs {
		log.Infof("CleanCDLogs Clean %s ,非提测的子环境 部署记录", prj.Name)
		// 遍历所有90天前的测试环境 子环境部署记录
		cdlogs, err := repo.FindChangeLogs(context.Background(), model.ListChangeLogReq{
			IsCurrent:      &isCurrent,
			ProjectId:      prj.Id,
			Env:            constants.EnumEnvTesting,
			EnvTarget:      constants.EnumEnvTargetSubV2,
			OperatedBefore: time.Now().Add(-90 * 24 * time.Hour),
		})
		if err != nil {
			log.Errorf("CleanCDLogs FindChangeLogs Err %v", err)
			continue
		}

		for _, cdlog := range cdlogs {
			if strings.HasSuffix(cdlog.Senv, "-qa") {
				continue
			}

			log.Infof("CleanCDLogs OfflineChain  %d cluster:%s namespace:%s env:%s  ,%s ", cdlog.ID, cdlog.Cluster, cdlog.Namespace, cdlog.Senv, cdlog.Description)
			_, err = job.depolyRpc.OfflineChain(context.Background(), &deploy.OfflineChainReq{
				ReleaseId: cdlog.ID,
				AutoOperator: &deploy.AutoOperator{
					Id:          0,
					ChineseName: "system",
					EmployeeNo:  "T0000",
				},
				Desc: "自动下线,90天前的非提测的子环境",
			})
			if err != nil {
				log.Errorf("CleanCDLogs OfflineChain Err %v", err)
				continue
			}
		}
	}
	log.Info("🐘🐘🐘 CleanCDLogs end...")
}
