-- 用户服务部署配置表
CREATE TABLE `idehub`.`idehub_user_appconfig` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
   `user_id` bigint NOT NULL COMMENT '用户id',
   `app_id` bigint  NOT NULL COMMENT '应用id',
   `config` json COMMENT '部署配置',
   `nhctl_config` longtext NOT NULL COMMENT 'nhctl配置',
   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`),
   KEY `idx_app_id` (`app_id`) USING BTREE,
   KEY `idx_user_id` (`user_id`) USING BTREE,
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户服务部署配置表';

-- 调试记录
CREATE TABLE `idehub`.`debug_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `cluster` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署集群',
    `namespace` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署命名空间',
    `senv` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '子环境 2.0 名称',
    `app_id` bigint NOT NULL COMMENT '应用id',
    `app_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
    `config_id` bigint NOT NULL COMMENT '配置id',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '部署结果: 0 未开始 1部署中、2成功、3失败',
    `operator_by` bigint NOT NULL COMMENT '操作人id',
    `pod_name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'pod名称',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_app_id` (`app_id`) USING BTREE,
    KEY `idx_operator_by` (`operator_by`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='调试记录';

-- 用户k8s配置
CREATE TABLE `idehub`.`user_kubeconfig` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `cluster` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署集群',
    `namespace` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署命名空间',
    `user_id` bigint NOT NULL COMMENT '用户id',
    `content` longtext NOT NULL COMMENT 'k8s配置内容',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY `uniq_user_cluster` (`user_id`,`cluster`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户k8s配置';
