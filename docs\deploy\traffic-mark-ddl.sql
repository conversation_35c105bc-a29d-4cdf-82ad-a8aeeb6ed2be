CREATE TABLE `traffic_mark`
(
    `id`           bigint                                  NOT NULL AUTO_INCREMENT COMMENT 'id',
    `name`         varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
    `env`          tinyint                                 NOT NULL COMMENT '部署环境: 1开发、2测试、3灰度、4生产',
    `project_id`   bigint                                  NOT NULL COMMENT '项目id',
    `is_cross`     tinyint(1)                              NOT NULL DEFAULT 0 COMMENT '是否跨应用',
    `created_at`   timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='流量标记';

alter table traffic_mark
    add column `origin` bigint not null default 0 comment '跨组织标记的来源';