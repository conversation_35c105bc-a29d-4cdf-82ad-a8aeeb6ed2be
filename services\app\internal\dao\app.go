//go:generate mockgen -destination=app_mock.go -package=dao -source=app.go
package dao

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/app/internal/model"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var _ AppRepository = (*AppRepo)(nil)

type AppRepository interface {
	Insert(context.Context, *App) error
	Delete(*App) error
	FindAll(ctx context.Context, query *model.AppQuery, total *int64) ([]App, error)
	List(ctx context.Context, query *model.AppQuery, data interface{}) error
	FindAppsByUserAndProject(ctx context.Context, userId, projectId int64) ([]App, error)
	InsertAppOwner(context.Context, []AppUser) error
	FindAppBy(ctx context.Context, projectId int64, name string) (*App, error)
	FindApp(ctx context.Context, id int64) (*App, error)
	FindRepoByUrl(ctx context.Context, host string) (*Repo, error)
	DeleteAllAppOwners(ctx context.Context, appIds []int64) error
	UpdateApp(ctx context.Context, app *App, cols ...string) error
	FindAppBasicInfoById(ctx context.Context, id int64) (*App, error)
	FindAllAppBasicInfo(ctx context.Context, query *model.AppQuery) ([]App, error)
	FindAppsByIds(ctx context.Context, ids []int64, projectId int64) ([]App, error)
	FindProjectAppsByLanguage(ctx context.Context, query *model.ProjectAppsParam) ([]App, error)
	FindUserAppPreferences(ctx context.Context, projectId, userId int64) ([]AppPreference, error)
	CreateAppPreference(ctx context.Context, preference model.AppPreference) error
	DeleteAppPreference(ctx context.Context, preference model.AppPreference) error
	BatchUpdateAppCols(ctx context.Context, apps []App, cols ...string) error
	SearchApps(ctx context.Context, name string) ([]App, error)
	GetUserApps(ctx context.Context, query model.UserAppsReq) ([]App, error)
	GetUserPreferenceApps(ctx context.Context, query model.UserAppsReq) ([]App, error)
	GetProjectsApps(ctx context.Context, query model.ProjectsAppsReq) ([]App, error)
	ListAppEventlink(ctx context.Context, appId int64) ([]AppEventlink, error)
	GetAppEventlink(ctx context.Context, appId int64, env constants.EnvType) (*AppEventlink, error)
	CreateAppEventlink(ctx context.Context, eventlink *AppEventlink) error
	UpdateAppEventlink(ctx context.Context, eventlink *AppEventlink) error
	BatchUpdateAppEventlink(ctx context.Context, appId int64, updateFields map[string]interface{}) error
	FindAppsByName(ctx context.Context, parms model.FindAppsReq) ([]App, error)
}

// App 应用信息表
type App struct {
	db.BaseModel      `mapstructure:",squash"`
	Name              string         `gorm:"column:name;NOT NULL" json:"name"` // 应用名称
	Code              string         `gorm:"column:code;NOT NULL" json:"code"` // 应用代号(唯一标识)
	BuildPath         string         `gorm:"column:build_path"`                // 构建路径
	LangName          string         `gorm:"column:lang_name"`                 // 应用语言名称
	LangVersion       string         `gorm:"column:lang_version"`              // 应用语言版本
	RepoAddr          string         `gorm:"column:repo_addr;NOT NULL"`        // 源码库地址
	Status            int            `gorm:"column:status;NOT NULL"`           // 状态
	CmdbId            string         `gorm:"column:cmdb_id;NOT NULL"`          // 对应的cmdb的 唯一Id
	ProjectID         int64          `gorm:"column:project_id;NOT NULL"`       // 所属项目id
	Description       string         `gorm:"column:description"`               // 备注
	MatchLabels       db.DbStrMap    `gorm:"column:match_labels"`              // 服务发布时k8s Deployment的 selector MatchLabels
	ServiceLabels     db.DbStrMap    `gorm:"column:service_labels"`            // 服务发布时k8s Service的 selector
	StandLabelEnvs    string         `gorm:"column:stand_label_envs"`          // 使用标准化Labels部署的环境配置。英文,分隔(dev,testing,preview,production)
	SenvStatus        string         `gorm:"column:senv_status"`               // 是否支持子环境2.0，enabled：支持，disabled：不支持
	NeedExternalRoute bool           `gorm:"column:need_external_route"`       // 是否需要对外路由
	EventlinkType     string         `gorm:"column:eventlink_type"`            // eventlink类型，多个使用,分隔
	CmdbInfo          datatypes.JSON `json:"cmdbInfo" gorm:"column:cmdb_info"` // 应用在CMDB的信息，定时同步
	Users             []AppUser      `gorm:"foreignKey:AppId"`
	Project           *Project       `gorm:"foreignKey:project_id"`
}

func (app *App) TableName() string {
	return "app"
}

// Repo 仓库信息表
type Repo struct {
	ID          int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                        // id
	Name        string    `gorm:"column:name" json:"name"`                                               // 仓库名称
	RepoType    string    `gorm:"column:repo_type" json:"repoType"`                                      // 仓库类型(git,harbor,nexus)
	AuthType    string    `gorm:"column:auth_type" json:"authType"`                                      // 认证类型(token,密码)
	Url         string    `gorm:"column:url" json:"url"`                                                 // 目标服务器
	Port        string    `gorm:"column:port" json:"port"`                                               // 默认端口
	Username    string    `gorm:"column:username" json:"username"`                                       // 用户名
	Password    string    `gorm:"column:password" json:"password"`                                       // 密码
	AccessToken string    `gorm:"column:access_token" json:"accessToken"`                                // 访问Token
	CaCert      string    `gorm:"column:ca_cert" json:"caCert"`                                          // ca证书
	CreatedAt   time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"createdAt"` // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"updatedAt"` // 修改时间
	Remark      string    `gorm:"column:remark" json:"remark"`                                           // 备注
	Status      int       `gorm:"column:status;NOT NULL" json:"status"`                                  // 状态0连接不上,1正常连接
	IsDeleted   int       `gorm:"column:is_deleted;default:1" json:"isDeleted"`                          // 是否已删除(逻辑删除),0已删除,1未删除
}

func (repo *Repo) TableName() string {
	return "repo"
}

type AppRepo struct {
	db *gorm.DB
}

func NewAppRepository(db *gorm.DB) AppRepository {
	return &AppRepo{db: db}
}

// AppUser 应用用户表
type AppUser struct {
	db.BaseModel
	AppId  int64 `gorm:"column:app_id;NOT NULL"`
	UserId int64 `gorm:"column:user_id;NOT NULL"`
}

func (app *AppUser) TableName() string {
	return "app_user"
}

// AppPreference 应用收藏表
type AppPreference struct {
	db.BaseModel
	AppId     int64 `gorm:"column:app_id;NOT NULL"`
	UserId    int64 `gorm:"column:user_id;NOT NULL"`
	ProjectId int64 `gorm:"column:project_id;NOT NULL"`
	App       *App  `gorm:"foreignKey:app_id"`
}

func (m *AppPreference) TableName() string {
	return "app_preference"
}

type AppEventlink struct {
	db.BaseModel
	AppID        int64             `gorm:"column:app_id;NOT NULL"`
	AppName      string            `gorm:"column:app_name;NOT NULL"`
	ProjectID    int64             `gorm:"column:project_id;NOT NULL"`
	Env          constants.EnvType `gorm:"column:env;NOT NULL"`
	ConsumerType string            `gorm:"column:consumer_type;NOT NULL"`
	ProducerType string            `gorm:"column:producer_type;NOT NULL"`
}

func (m *AppEventlink) TableName() string {
	return "app_event_link"
}

func (appRepo *AppRepo) Insert(ctx context.Context, app *App) error {
	tx := db.CtxTX(ctx, appRepo.db)
	if err := tx.Clauses(clause.Insert{Modifier: "IGNORE"}).Create(app).Error; err != nil {
		log.ErrorWithCtx(ctx, "持久化应用信息错误: %v", err)
		return err
	}
	if app.ID == 0 {
		log.ErrorWithCtx(ctx, "该应用[%s]已经存在", app.Name)
		return nil
	}
	return nil
}

func (appRepo *AppRepo) InsertAppOwner(ctx context.Context, value []AppUser) error {
	tx := db.CtxTX(ctx, appRepo.db)
	if err := tx.Create(value).Error; err != nil {
		log.ErrorWithCtx(ctx, "持久化应用负责人信息错误: %v", err)
		return err
	}
	return nil
}

func (appRepo *AppRepo) Delete(app *App) error {
	if err := db.Model(&App{}).Delete("id=?", app.ID).Error; err != nil {
		log.Errorf("删除应用信息错误: %v", err)
		return err
	}
	return nil
}

func (appRepo *AppRepo) FindAll(ctx context.Context, query *model.AppQuery, total *int64) ([]App, error) {
	var appList []App
	tx := db.Model(&App{}).Where("project_id = ?", query.ProjectId)
	if query.Name != "" {
		if query.NameMatch == constants.EXACT {
			tx = tx.Where("BINARY name = ?", query.Name)
		} else {
			tx = tx.Where("name like ?", fmt.Sprintf("%%%s%%", query.Name))
		}
	}
	if query.OwnerId != 0 {
		tx = tx.Joins("left join app_user on app_id = app.id").Where("user_id = ?", query.OwnerId)
	}
	if query.SenvStatus == "empty" {
		tx = tx.Where("senv_status = ?", "")
	} else if query.SenvStatus != "" {
		tx = tx.Where("senv_status = ?", query.SenvStatus)
	}
	// example:SELECT * FROM `app` WHERE project_id = 1 ORDER BY FIELD(id,4,5) desc,id desc LIMIT 10
	if len(query.OrderByAppPreferenceIds) > 0 {
		fieldOrder := "FIELD(`app`.id, " + strings.Join(query.OrderByAppPreferenceIds, ",") + ") DESC"
		tx = tx.Order(fieldOrder)
	}
	if err := tx.Count(total).Scopes(db.Paginate(query)).
		Order("`app`.id DESC").Preload("Users").Find(&appList).Error; err != nil {
		log.ErrorWithCtx(ctx, "按条件查询应用信息出错: %v", err)
		return nil, fmt.Errorf("按条件查询应用信息出错：%w", err)
	}
	return appList, nil
}

func (appRepo *AppRepo) List(ctx context.Context, query *model.AppQuery, data interface{}) error {
	tx := db.Model(&App{}).Where("project_id = ?", query.ProjectId)
	if query.Name != "" {
		tx = tx.Where("name like ?", fmt.Sprintf("%%%s%%", query.Name))
	}
	if err := tx.Find(data).Error; err != nil {
		log.ErrorWithCtx(ctx, "按条件查询应用信息出错: %v", err)
		return fmt.Errorf("按条件查询应用信息出错：%w", err)
	}
	return nil
}

func (appRepo *AppRepo) FindAllAppBasicInfo(ctx context.Context, query *model.AppQuery) ([]App, error) {
	var appList []App
	tx := db.Model(&App{})
	if query.ProjectId != 0 {
		tx = tx.Where("project_id = ?", query.ProjectId)
	}
	if query.Name != "" {
		tx = tx.Where("name like ?", fmt.Sprintf("%%%s%%", query.Name))
	}
	if err := tx.Order("id DESC").Find(&appList).Error; err != nil {
		log.ErrorWithCtx(ctx, "按条件查询应用基本信息出错: %v", err)
		return nil, fmt.Errorf("按条件查询应用基本信息出错：%w", err)
	}
	return appList, nil
}

func (appRepo *AppRepo) FindAppsByUserAndProject(ctx context.Context, userId, projectId int64) ([]App, error) {
	var apps []App
	tx := db.Model(&App{}).
		Where("project_id = ?", projectId).
		Joins("left join app_user on app_id = app.id").
		Where("user_id = ?", userId)
	if err := tx.Find(&apps).Error; err != nil {
		return nil, fmt.Errorf("按条件查应用信息出错：%w", err)
	}
	return apps, nil
}

func (appRepo *AppRepo) FindAppBy(ctx context.Context, projectId int64, name string) (*App, error) {
	var data App
	tx := db.Model(&App{}).Select("id", "name").Where("project_id = ? and name = ?", projectId, name).
		Find(&data)
	if err := tx.Error; err != nil {
		log.ErrorWithCtx(ctx, "按条件查询应用信息出错: %v", err)
		return nil, fmt.Errorf("按条件查询应用信息出错：%w", err)
	}
	return &data, nil
}

func (appRepo *AppRepo) FindApp(ctx context.Context, id int64) (*App, error) {
	// todo 这个接口要改名为 FindAppWithPreload
	var app App
	err := db.Preload("Users").Model(&App{}).First(&app, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.ErrorWithCtx(ctx, "查询应用信息出错，应用未找到: %v", err)
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "查询应用信息出错: %v", err)
		return nil, fmt.Errorf("查询应用信息出错：%w", err)
	}
	return &app, nil
}

func (appRepo *AppRepo) FindRepoByUrl(ctx context.Context, host string) (*Repo, error) {
	var rp Repo
	if err := db.Model(Repo{}).Where("url = ? and is_deleted = ?", host, 0).Find(&rp).Error; err != nil {
		return nil, err
	}
	return &rp, nil
}

func (appRepo *AppRepo) DeleteAllAppOwners(ctx context.Context, appIds []int64) error {
	tx := db.CtxTX(ctx, appRepo.db)
	if err := tx.Where("app_id in (?)", appIds).Delete(&AppUser{}).Error; err != nil {
		log.ErrorWithCtx(ctx, "删除应用负责人信息错误: %v", err)
		return err
	}
	return nil
}

func (appRepo *AppRepo) UpdateApp(ctx context.Context, app *App, cols ...string) error {
	tx := db.CtxTX(ctx, appRepo.db)
	tx = tx.Model(&App{}).Where("id = ?", app.ID)
	if len(cols) > 0 {
		tx = tx.Select(cols)
	} else {
		tx = tx.Select("*")
	}

	if err := tx.Omit("id", "created_at").Updates(app).Error; err != nil {
		log.ErrorWithCtx(ctx, "更新应用信息错误: %v", err)
		return err
	}
	return nil
}

func (appRepo *AppRepo) FindAppBasicInfoById(ctx context.Context, id int64) (*App, error) {
	// todo 这个接口可以改名为 FindApp
	var data App
	tx := db.Model(&App{}).Preload("Project").Where("id = ?", id).First(&data)
	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "按条件查询应用信息出错: %v", err)
		return nil, fmt.Errorf("按条件查询应用信息出错：%w", err)
	}
	return &data, nil
}

func (appRepo *AppRepo) FindAppsByIds(ctx context.Context, ids []int64, projectId int64) ([]App, error) {
	var value []App
	tx := db.Model(&App{}).Preload("Users").Where("id in ?", ids)
	if projectId != 0 {
		tx = tx.Where("project_id = ?", projectId)
	}
	if err := tx.Order("id DESC").Find(&value).Error; err != nil {
		log.ErrorWithCtx(ctx, "根据应用id查询应用列表错误: %v", err)
		return nil, err
	}
	return value, nil
}

func (appRepo *AppRepo) FindProjectAppsByLanguage(ctx context.Context, query *model.ProjectAppsParam) ([]App, error) {
	var apps []App
	tx := db.Model(&App{}).Where("project_id = ?", query.ID).
		Where("lang_name = ?", query.Language)
	if err := tx.Order("id DESC").Find(&apps).Error; err != nil {
		log.ErrorWithCtx(ctx, "按条件查询应用基本信息出错: %v", err)
		return nil, fmt.Errorf("按条件查询应用基本信息出错：%w", err)
	}
	return apps, nil
}

func (appRepo *AppRepo) findAppPreference(ctx context.Context, preference model.AppPreference) (*AppPreference, error) {
	var appPreference AppPreference
	err := appRepo.db.WithContext(ctx).Model(&AppPreference{}).Where("project_id = ? and app_id = ? and user_id = ?", preference.ProjectID, preference.AppID, preference.UserID).First(&appPreference).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &appPreference, nil
}

func (appRepo *AppRepo) FindUserAppPreferences(ctx context.Context, projectId, userId int64) ([]AppPreference, error) {
	var appPreference []AppPreference
	err := appRepo.db.WithContext(ctx).Model(&AppPreference{}).Joins("left join app on app_preference.app_id = app.id").Where("app_preference.project_id = ? and app_preference.user_id = ?", projectId, userId).Order("app.id").Find(&appPreference).Error
	if err != nil {
		return nil, err
	}
	return appPreference, nil
}

func (appRepo *AppRepo) CreateAppPreference(ctx context.Context, preference model.AppPreference) error {
	appPreference, err := appRepo.findAppPreference(ctx, preference)
	if err != nil {
		return err
	}
	obj := AppPreference{
		ProjectId: preference.ProjectID,
		AppId:     preference.AppID,
		UserId:    preference.UserID,
	}
	if appPreference == nil || appPreference.ID == 0 {
		if err := appRepo.db.WithContext(ctx).Create(&obj).Error; err != nil {
			return err
		}
	}
	return nil
}

func (appRepo *AppRepo) DeleteAppPreference(ctx context.Context, preference model.AppPreference) error {
	if err := db.Model(&AppPreference{}).Where("project_id = ? and app_id = ? and user_id = ?", preference.ProjectID, preference.AppID, preference.UserID).Delete(&AppPreference{}).Error; err != nil {
		log.ErrorWithCtx(ctx, "删除应用收藏信息错误: %v", err)
		return err
	}
	return nil
}

func (appRepo *AppRepo) BatchUpdateAppCols(ctx context.Context, apps []App, cols ...string) error {
	if len(apps) == 0 {
		return nil
	}

	if len(cols) > 0 {
		cols = append(cols, "updated_at")
	}

	batchUpdateFn := func(tx *gorm.DB) error {
		return tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns(cols),
		}).Omit(clause.Associations).Create(&apps).Error
	}

	tx := db.CtxTX(ctx, appRepo.db)
	if tx == nil {
		return appRepo.db.Transaction(func(tx *gorm.DB) error {
			return batchUpdateFn(tx)
		})
	} else {
		return batchUpdateFn(tx)
	}
}

func (appRepo *AppRepo) SearchApps(ctx context.Context, name string) ([]App, error) {
	tx := db.CtxTX(ctx, appRepo.db)
	var apps []App
	if err := tx.Model(&App{}).Where("BINARY name like ?", fmt.Sprintf("%%%s%%", name)).Find(&apps).Error; err != nil {
		log.ErrorWithCtx(ctx, "search apps by name %s error: %v", name, err)
		return nil, err
	}
	return apps, nil
}

func (appRepo *AppRepo) GetUserApps(ctx context.Context, query model.UserAppsReq) ([]App, error) {
	tx := db.CtxTX(ctx, appRepo.db)
	var apps []App
	tx = tx.Model(&App{}).Preload("Project")
	if query.AppName != "" {
		tx = tx.Where("BINARY name like ?", fmt.Sprintf("%%%s%%", query.AppName))
	}
	if len(query.ProjectIDs) > 0 {
		tx = tx.Where("project_id in (?)", query.ProjectIDs)
	}
	if err := tx.Model(&App{}).Order("project_id, name").Find(&apps).Error; err != nil {
		log.ErrorWithCtx(ctx, "get user apps error: %v, query: %+v", err, query)
		return nil, err
	}
	return apps, nil
}

func (appRepo *AppRepo) GetUserPreferenceApps(ctx context.Context, query model.UserAppsReq) ([]App, error) {
	tx := db.CtxTX(ctx, appRepo.db)
	var apps []App
	tx = tx.Model(&App{}).Preload("Project")
	if query.AppName != "" {
		tx = tx.Where("BINARY name like ?", fmt.Sprintf("%%%s%%", query.AppName))
	}
	if len(query.ProjectIDs) > 0 {
		tx = tx.Where("project_id in (?)", query.ProjectIDs)
	}
	if query.UserID != 0 {
		tx = tx.Where("id in (select app_id from app_preference WHERE user_id = ?)", query.UserID)
	}
	if err := tx.Model(&App{}).Order("project_id, name").Find(&apps).Error; err != nil {
		log.ErrorWithCtx(ctx, "get user apps error: %v, query: %+v", err, query)
		return nil, err
	}
	return apps, nil
}

func (appRepo *AppRepo) GetProjectsApps(ctx context.Context, query model.ProjectsAppsReq) ([]App, error) {
	tx := db.CtxTX(ctx, appRepo.db)
	var apps []App
	tx = tx.Model(&App{}).Preload("Project")
	if query.AppName != "" {
		tx = tx.Where("BINARY name like ?", fmt.Sprintf("%%%s%%", query.AppName))
	}
	if len(query.ProjectIDs) > 0 {
		tx = tx.Where("project_id in (?)", query.ProjectIDs)
	}
	if err := tx.Model(&App{}).Order("project_id, name").Find(&apps).Error; err != nil {
		log.ErrorWithCtx(ctx, "get projects apps error: %v, query: %+v", err, query)
		return nil, err
	}
	return apps, nil
}

func (appRepo *AppRepo) ListAppEventlink(ctx context.Context, appId int64) ([]AppEventlink, error) {
	tx := db.CtxTX(ctx, appRepo.db)
	var eventlinks []AppEventlink
	if err := tx.Model(&AppEventlink{}).Where("app_id = ?", appId).Find(&eventlinks).Error; err != nil {
		log.ErrorWithCtx(ctx, "[ListAppEventlink] get app eventlink config by appId[%d] error: %v", appId, err)
		return nil, err
	}
	return eventlinks, nil
}

func (appRepo *AppRepo) GetAppEventlink(ctx context.Context, appId int64, env constants.EnvType) (*AppEventlink, error) {
	tx := db.CtxTX(ctx, appRepo.db)
	var eventlink AppEventlink
	if err := tx.Model(&AppEventlink{}).Where("app_id = ? and env = ?", appId, env).First(&eventlink).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "[GetAppEventlink] get app eventlink config by appId[%d] error: %v", appId, err)
		return nil, err
	}
	return &eventlink, nil
}

func (appRepo *AppRepo) CreateAppEventlink(ctx context.Context, eventlink *AppEventlink) error {
	tx := db.CtxTX(ctx, appRepo.db)
	if err := tx.Create(eventlink).Error; err != nil {
		log.ErrorWithCtx(ctx, "[CreateAppEventlink] create app eventlink config error: %v", err)
		return err
	}
	return nil
}

func (appRepo *AppRepo) UpdateAppEventlink(ctx context.Context, eventlink *AppEventlink) error {
	tx := db.CtxTX(ctx, appRepo.db)
	if err := tx.Model(&AppEventlink{}).Where("id = ?", eventlink.ID).Updates(map[string]interface{}{
		"consumer_type": eventlink.ConsumerType,
		"producer_type": eventlink.ProducerType,
	}).Error; err != nil {
		log.ErrorWithCtx(ctx, "[UpdateAppEventlink] update app eventlink config error: %v", err)
		return err
	}
	return nil
}

func (appRepo *AppRepo) BatchUpdateAppEventlink(ctx context.Context, appId int64, updateFields map[string]interface{}) error {
	tx := db.CtxTX(ctx, appRepo.db)
	if err := tx.Model(&AppEventlink{}).Where("app_id = ?", appId).Updates(updateFields).Error; err != nil {
		log.ErrorWithCtx(ctx, "[BatchUpdateAppEventlink] update app eventlink config error: %v", err)
		return err
	}
	return nil
}

func (appRepo *AppRepo) FindAppsByName(ctx context.Context, parms model.FindAppsReq) (objs []App, err error) {
	objs = make([]App, 0)
	tx := db.CtxTX(ctx, appRepo.db)
	tx = tx.Select("app.*").Preload("Project").Where("app.name = ?", parms.AppName)
	if parms.ProjectID > 0 {
		tx = tx.Where("app.project_id = ?", parms.ProjectID)
	}
	if parms.ProjectName != "" {
		tx = tx.Joins("left join project on app.project_id = project.id").Where("project.name like ?", fmt.Sprintf("%%%s%%", parms.ProjectName))
	}

	err = tx.Find(&objs).Error

	return
}
