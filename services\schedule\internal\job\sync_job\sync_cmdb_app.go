package sync_job

import (
	"context"

	"52tt.com/cicd/pkg/log"
	pbapp "52tt.com/cicd/protocol/app"
)

type SyncCMDBAppJob struct {
	appRpc pbapp.AppServiceClient
}

func NewSyncCMDBAppJob(appRpc pbapp.AppServiceClient) *SyncCMDBAppJob {
	return &SyncCMDBAppJob{appRpc: appRpc}
}

// Run 执行同步CMDB应用信息的定时任务
// 该任务会调用应用服务的SyncCMDBInfo方法，同步所有应用的CMDB信息到本地数据库
func (job *SyncCMDBAppJob) Run() {
	log.Info("SyncCMDBAppJob start.")

	// 调用应用服务的SyncCMDBInfo方法同步CMDB信息
	_, err := job.appRpc.SyncCMDBInfo(context.Background(), &pbapp.AppByNameReq{})
	if err != nil {
		log.Errorf("SyncCMDBAppJob failed: %v", err)
	} else {
		log.Info("SyncCMDBAppJob success.")
	}
}
