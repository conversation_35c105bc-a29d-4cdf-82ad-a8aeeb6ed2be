CREATE TABLE `approval_flow_node` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
  `name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '审批节点名称',
  `is_assigned` tinyint DEFAULT 0 COMMENT '多个审批人时是否指定审批人,1指定,0不指定',
  `approval_flow_id` bigint NOT NULL COMMENT '审批流id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `approvers` json DEFAULT NULL COMMENT '审批人ID数组',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `approval_flow_id_index` (`approval_flow_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批流节点表';

