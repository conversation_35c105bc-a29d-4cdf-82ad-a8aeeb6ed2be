package config

import (
	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/kafka"
	"52tt.com/cicd/pkg/log"
	"github.com/redis/go-redis/v9"
)

type Http struct {
	Port    int
	Timeout string
}
type Rpc struct {
	Port    int
	Timeout string
}

type Application struct {
	Name        string
	Http        *Http
	Rpc         *Rpc
	Version     string
	Mode        string
	Description string
}

// Registry client注册中心(默认使用直连的方式,例如127.0.0.1:8080)
type Registry struct {
	Url             string
	AppRpcUrl       string `mapstructure:"app_rpc_url"`
	IamRpcUrl       string `mapstructure:"iam_rpc_url"`
	NotifyRpcUrl    string `mapstructure:"notify_rpc_url"`
	DeployRpcUrl    string `mapstructure:"deploy_rpc_url"`
	PipelineRpcUrl  string `mapstructure:"pipeline_rpc_url"`
	StatisticRpcUrl string `mapstructure:"statistic_rpc_url"`
	ToolsRpcUrl     string `mapstructure:"tools_rpc_url"`
}

type Default struct {
	App   *Application   `mapstructure:"app"`
	Db    *db.Config     `mapstructure:"database"`
	Log   *log.Config    `mapstructure:"logger"`
	Ris   *Registry      `mapstructure:"registry"`
	Redis *redis.Options `mapstructure:"redis"`
	Kafka *kafka.Config  `mapstructure:"kafka"`
}

func (c *Default) GetAppConfig() *Application {
	return c.App
}

func (c *Default) GetDbConfig() *db.Config {
	return c.Db
}

func (c *Default) GetLogConfig() *log.Config {
	return c.Log
}
func (c *Default) GetRegistry() *Registry {
	return c.Ris
}

func (c *Default) GetRedisConfig() *redis.Options {
	return c.Redis
}

func (c *Default) GetKafkaConfig() *kafka.Config {
	return c.Kafka
}

func (c *Default) InitConfig(fns ...func(*Default) error) error {
	for _, fn := range fns {
		err := fn(c)
		if err != nil {
			return err
		}
	}
	return nil
}
