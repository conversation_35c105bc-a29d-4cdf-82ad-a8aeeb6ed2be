package kafka

import (
	"sync"

	"52tt.com/cicd/pkg/log"
	"github.com/IBM/sarama"
)

type Producer struct {
	name     string
	topic    string
	producer sarama.AsyncProducer
	wg       sync.WaitGroup
}

func NewProducer(cfg *Config) (*Producer, error) {
	producer, err := sarama.NewAsyncProducer(cfg.GetBrokers(), cfg.GetSaramaConfig())
	if err != nil {
		log.Errorf("Failed to create kafka producer to broker %+v : %+v", cfg.GetBrokers(), err)
		return nil, err
	}

	ep := &Producer{
		name:     cfg.Name,
		topic:    cfg.GetProducerTopic(),
		producer: producer,
	}

	ep.printPushMsgDeal()
	return ep, nil
}

func (ep *Producer) printPushMsgDeal() {
	ep.wg.Add(2)

	go func() {
		defer func() {
			ep.wg.Done()
		}()
		for success := range ep.producer.Successes() {
			if success != nil {
				log.Infof("[%s] Sent message ---> [%d:%d]%s success", ep.name, success.Partition, success.Offset, success.Key)
			}
		}
	}()

	go func() {
		defer func() {
			ep.wg.Done()
		}()
		for err := range ep.producer.Errors() {
			if err != nil {
				log.Errorf("Failed to send message %v, err %+v", err.Msg, err.Err)
			}
		}
	}()
}

func (ep *Producer) publish(key string, value []byte) {
	msg := &sarama.ProducerMessage{
		Topic: ep.topic,
		Key:   sarama.StringEncoder(key),
		Value: sarama.ByteEncoder(value),
	}
	ep.producer.Input() <- msg
}

func (ep *Producer) Close() {
	ep.producer.AsyncClose()
	ep.wg.Wait()
}

func (ep *Producer) Publish(key string, value []byte) {
	ep.publish(key, value)
}

func (ep *Producer) PublishAny(key string, value any, fn ValueConverter) {
	bytes, err := fn(value)
	if err != nil {
		log.Errorf("marshal data failed", "err is ", err.Error())
		return
	}

	ep.publish(key, bytes)
}

type ValueConverter func(value any) ([]byte, error)
