package constants

// EnumEnv 环境枚举类型
type EnumEnv int8

func (e EnumEnv) Value() int8 {
	return int8(e)
}

// String 返回枚举值的字符串，内容为 EnvType 定义的字符串
func (e EnumEnv) String() string {
	return e.ToEnvType().String()
}

func (e EnumEnv) ToEnvType() EnvType {
	v, ok := enumEnvStringMapping[e]
	if ok {
		return v
	}
	return EnvType("unknown")
}

var enumEnvStringMapping = map[EnumEnv]EnvType{
	EnumEnvDev:        DEV,
	EnumEnvTesting:    TESTING,
	EnumEnvPreview:    PREVIEW,
	EnumEnvProduction: PRODUCTION,
}

const (
	// EnumEnvUnknown 未知环境
	EnumEnvUnknown EnumEnv = 0
	// EnumEnvDev 开发环境
	EnumEnvDev EnumEnv = 1
	// EnumEnvTesting 测试环境
	EnumEnvTesting EnumEnv = 2
	// EnumEnvPreview 预发环境
	EnumEnvPreview EnumEnv = 3
	// EnumEnvProduction 生产环境
	EnumEnvProduction EnumEnv = 4
)
