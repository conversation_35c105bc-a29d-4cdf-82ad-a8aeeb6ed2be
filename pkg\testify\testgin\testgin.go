package testgin

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"

	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/engine/middleware"
)

var mockCtxInfo = &cctx.RequestInfo{
	UserInfo: cctx.UserInfo{
		UserID:      1,
		Username:    "username",
		ChineseName: "cname",
		EmployeeNo:  "T001",
		Roles:       []string{"admin"},
		ProjectID:   1,
	},
	RequestID: "reqid",
}

func mockAuthInfo() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		cctx.SetHeaderRequestInfo(ctx, mockCtxInfo)
	}
}

type Suite struct {
	suite.Suite
	engine *gin.Engine
	ctrl   *gomock.Controller
}

func (s *Suite) TearDownTest() {
	s.ctrl.Finish()
}

func (s *Suite) Setup() {
	gin.SetMode(gin.ReleaseMode)

	s.ctrl = gomock.NewController(s.T())
	s.engine = gin.New()
	s.engine.Use(gin.Recovery(), mockAuthInfo(), middleware.AuthContext())
}

func (s *Suite) Context() context.Context {
	return cctx.WithRequestInfo(context.Background(), mockCtxInfo)
}

func (s *Suite) Controller() *gomock.Controller {
	return s.ctrl
}

func (s *Suite) RouterGroup() *gin.RouterGroup {
	return &s.engine.RouterGroup
}

func (s *Suite) Engine() *gin.Engine {
	return s.engine
}

func (s *Suite) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	s.engine.ServeHTTP(w, r)
}

func (s *Suite) NewRequest(method, url string, body io.Reader) (*http.Request, error) {
	return http.NewRequest(method, url, body)
}

func (s *Suite) MockCallHTTPWithStruct(method, url string, body interface{}) (*httptest.ResponseRecorder, error) {
	req, _ := json.Marshal(body)
	return s.MockCallHTTPInterface(method, url, bytes.NewBuffer(req))
}

func (s *Suite) MockCallHTTPInterface(method, url string, body io.Reader) (*httptest.ResponseRecorder, error) {
	req, err := s.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}

	resp := httptest.NewRecorder()
	s.ServeHTTP(resp, req)

	return resp, nil
}

func Run(t *testing.T, s suite.TestingSuite) {
	suite.Run(t, s)
}
