package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"52tt.com/cicd/pkg/constants"
	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/log"
	pbapp "52tt.com/cicd/protocol/app"
	pbiam "52tt.com/cicd/protocol/iam"
	"52tt.com/cicd/services/pipeline/internal/client"
	"52tt.com/cicd/services/pipeline/internal/conf"
	"52tt.com/cicd/services/pipeline/internal/dao"
	"52tt.com/cicd/services/pipeline/internal/model"
	bizerr "52tt.com/cicd/services/pipeline/pkg/errors"
	"52tt.com/cicd/services/pipeline/pkg/tekton/utils"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	"golang.org/x/sync/errgroup"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/selection"
)

func makeEmptyApprovalRequestTask(name string) *v1beta1.PipelineTask {
	task := v1beta1.PipelineTask{
		Name: name,
		TaskRef: &v1beta1.TaskRef{
			Kind:       "ApprovalRequest",
			APIVersion: "approval.tekton.dev/v1beta1",
		},
		Params: v1beta1.Params{},
	}
	return &task
}

func NewDefaultTaskBuilder(prs *dao.PipelineRunStage, prt *dao.PipelineRunTask) *DefaultTaskBuilder {
	b := &DefaultTaskBuilder{
		prs: prs,
		prt: prt,
	}

	b.init()
	return b
}

type DefaultTaskBuilder struct {
	env string
	// prs is current stage
	prs *dao.PipelineRunStage
	// prt is current task
	prt *dao.PipelineRunTask
	// task is the Builder result
	task *v1beta1.PipelineTask
}

func (b *DefaultTaskBuilder) init() {
	b.task = makeEmptyApprovalRequestTask(b.Name())
}

func (b *DefaultTaskBuilder) TaskType() string {
	return b.prt.Type
}

func (b *DefaultTaskBuilder) TaskID() int64 {
	return b.prt.ID
}

func (b *DefaultTaskBuilder) StageID() int64 {
	return b.prs.ID
}

func (b *DefaultTaskBuilder) Name() string {
	return makeCustomTaskName(b.TaskType(), b.StageID(), b.TaskID())
}

func (b *DefaultTaskBuilder) SetEnv(env string) {
	b.env = env
}

func (b *DefaultTaskBuilder) CustomTaskType() string {
	customTaskConfig := conf.GetCustomTaskTypeConfig()
	tt := customTaskConfig[b.TaskType()]
	return tt
}

func (b *DefaultTaskBuilder) SetRunAfter(runAfter []string) {
	if len(runAfter) == 0 {
		return
	}
	b.task.RunAfter = runAfter
}

func (b *DefaultTaskBuilder) Build(ctx context.Context) (*v1beta1.PipelineTask, error) {
	typeName := b.CustomTaskType()
	b.task.Params = []v1beta1.Param{
		{Name: "type", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: typeName}},
		{Name: "content", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "{}"}},
	}
	b.task.WhenExpressions = []v1beta1.WhenExpression{
		{Input: fmt.Sprintf("$(params.%s)", constants.GenerateSkipParamName(b.TaskID())), Operator: selection.In, Values: []string{"false"}},
	}

	return b.task, nil
}

func NewCustomShellTaskBuilder(prs *dao.PipelineRunStage, prt *dao.PipelineRunTask, pr *dao.PipelineRun, tektonPR *v1beta1.PipelineRun, requestDate string) *CustomShellTaskBuilder {
	return &CustomShellTaskBuilder{
		DefaultTaskBuilder: *NewDefaultTaskBuilder(prs, prt),
		pr:                 pr,
		tektonPR:           tektonPR,
		requestDate:        requestDate,
	}
}

type CustomShellTaskBuilder struct {
	DefaultTaskBuilder
	// pr is current pipeline run
	pr          *dao.PipelineRun
	tektonPR    *v1beta1.PipelineRun
	requestDate string
}

func (b *CustomShellTaskBuilder) Build(ctx context.Context) (*v1beta1.PipelineTask, error) {
	b.task.TaskRef = &v1beta1.TaskRef{
		Name: "custom-shell-script",
	}

	var customShell model.CustomShell
	if err := b.prt.UnmarshalConfig(&customShell); err != nil {
		log.ErrorWithCtx(ctx, "CustomShellTaskBuilder build *v1beta1.PipelineTask, pr[%d] task[%d] UnmarshalConfig error: %v", b.pr.ID, b.prt.ID, err)
		return nil, bizerr.ErrConfigUnmarshal
	}

	shellCommand := utils.ApplyScriptParams(customShell.ExecShell, *b.pr)

	// 获取CI部分 生成 & 推送镜像 任务的 结果信息
	for _, stage := range b.pr.Stages {
		pullImgTask, has := lo.Find(stage.Tasks, func(task dao.PipelineRunTask) bool {
			return task.Type == string(constants.TASK_GENERATE_PUSH_IMAGE)
		})
		// log.Infof("CustomShellTaskBuilder stage:%d  %s", stage.ID, stage.Type)

		if has {
			imageUrl := pullImgTask.GetImageUrlOnTR()
			// log.Infof("CustomShellTaskBuilder pullImgTask:%d  %s  imageUrl :%s \n  result:%s", pullImgTask.ID, pullImgTask.Type, imageUrl, pullImgTask.Result)
			if imageUrl != "" {
				shellCommand = utils.Replace(shellCommand, "$imageName", imageUrl)
				break
			}
		}
	}

	b.task.Params = []v1beta1.Param{
		{Name: "contents", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: shellCommand}},
		{Name: "ticketInfoFile", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "ticket-info.json"}}, // 自定义 shell 前置工单审批后的数据(具体数据结构需要看 ticket 的逻辑)
	}
	if customShell.ImageAddress != "" {
		b.task.Params = append(b.task.Params, v1beta1.Param{
			Name: "image", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: customShell.ImageAddress},
		})
	}
	// mount same workspace with pipelinerun
	b.tektonPR.Spec.Workspaces = []v1beta1.WorkspaceBinding{
		{Name: "share-data", VolumeClaimTemplate: &corev1.PersistentVolumeClaim{
			ObjectMeta: metav1.ObjectMeta{
				Annotations: map[string]string{
					string(constants.EnvKey):         b.env,
					string(constants.RepoAddressKey): b.pr.RepoAddress,
					string(constants.RequestDateKey): b.requestDate,
					string(constants.PipelineIdKey):  strconv.FormatInt(b.pr.PipelineId, 10),
					string(constants.BuildNumberKey): strconv.FormatInt(b.pr.BuildNumber, 10),
					string(constants.CsiVersionKey):  "v2",
					string(constants.BranchKey):      b.pr.Branch,
				},
			},
			Spec: corev1.PersistentVolumeClaimSpec{
				AccessModes:      []corev1.PersistentVolumeAccessMode{"ReadWriteMany"},
				StorageClassName: utils.StringPointer("nfs-client"),
				Resources: corev1.VolumeResourceRequirements{
					Requests: corev1.ResourceList{
						corev1.ResourceStorage: resource.MustParse("1Gi"),
					},
				},
			},
		}},
		{Name: "deployConfig", PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{ClaimName: "pvc-nfs"}, SubPath: fmt.Sprintf("config/%s-%d/%d/%d", b.pr.Pipeline.AppName, b.pr.Pipeline.AppID, b.pr.PipelineId, b.pr.BuildNumber)},
	}
	// task ref need workspace named output, we define share-data as output
	b.task.Workspaces = []v1beta1.WorkspacePipelineTaskBinding{
		{Name: "output", Workspace: "share-data"},
		{Name: "deployConfig", Workspace: "deployConfig"},
	}
	b.task.WhenExpressions = []v1beta1.WhenExpression{
		{Input: fmt.Sprintf("$(params.%s)", constants.GenerateSkipParamName(b.TaskID())), Operator: selection.In, Values: []string{"false"}},
	}
	return b.task, nil
}

func NewAPIAutoTestTaskBuilder(prs *dao.PipelineRunStage, prt *dao.PipelineRunTask, pr *dao.PipelineRun) *APIAutoTestTaskBuilder {
	return &APIAutoTestTaskBuilder{
		DefaultTaskBuilder: *NewDefaultTaskBuilder(prs, prt),
		pr:                 pr,
	}
}

type APIAutoTestTaskBuilder struct {
	DefaultTaskBuilder
	pr *dao.PipelineRun
}

func (b *APIAutoTestTaskBuilder) fillApiTestParams(ctx context.Context, deployTask *dao.PipelineRunTask, apiTestParams *apiTestCustomParams) error {
	if deployTask.Type == string(constants.TASK_AUTOMATION_DEPLOY) {
		// 自动化部署任务从主任务 config 获取数据
		var deployConf model.AutomationDeploy
		if err := deployTask.UnmarshalConfig(&deployConf); err != nil {
			log.ErrorWithCtx(ctx, "APIAutoTestTaskBuilder unmarshal Task %d config %+v failed: %s", deployTask.ID, deployTask.Config, err)
			return errors.Wrapf(bizerr.ErrConfigUnmarshal, "apiAutomation Task get deploy task config failed %s", err)
		}
		log.InfoWithCtx(ctx, "APIAutoTestTaskBuilder fill param with Task %d config %+v", deployTask.ID, deployConf)
		if deployConf.EnvTarget != constants.ORIGIN.String() {
			if deployConf.TrafficMark != "" {
				apiTestParams.SubEnvInfo = &subEnvInfo{SubEnvName: deployConf.TrafficMark}
			}
		}
	} else {
		// Notice 现在只有两种任务：1.部署基准(不需要传递流量标记) 2.部署子环境
		if deployTask.Type != string(constants.TASK_DEPLOY_SUB) {
			log.InfoWithCtx(ctx, "APIAutoTestTaskBuilder fill param Type %s no need pass to approval", deployTask.Type)
			return nil
		}
		// 因为工单会改动集群命名空间和子环境，所以这里只传递主任务 ID 给 Approval。
		apiTestParams.RunTaskID = deployTask.ID
	}
	return nil
}

func (b *APIAutoTestTaskBuilder) Build(ctx context.Context) (*v1beta1.PipelineTask, error) {
	// 自动化测试设置成fakeDeploy类型
	b.task.Params = []v1beta1.Param{
		{Name: "type", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ApprovalDeployTypeFakeDeploy}},
		{Name: "content", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "{}"}},
	}
	b.task.WhenExpressions = []v1beta1.WhenExpression{
		{Input: fmt.Sprintf("$(params.%s)", constants.GenerateSkipParamName(b.TaskID())), Operator: selection.In, Values: []string{"false"}},
	}

	return b.task, nil
}

func handleCallGetInfo(ctx context.Context, appID int64, userID int64) (*pbapp.APP, *pbiam.UserResponse, error) {
	var (
		appResp  *pbapp.APP
		userResp *pbiam.UserResponse
	)

	eg, gCtx := errgroup.WithContext(context.Background())
	callCtx := cctx.WithUserinfo(gCtx, cctx.GetUserinfo(ctx))
	eg.Go(func() error {
		var err error
		req := &pbapp.AppParam{Id: appID}
		appResp, err = client.AppClient().GetApp(callCtx, req)
		return err
	})
	eg.Go(func() error {
		var err error
		req := &pbiam.UserParam{UserId: userID}
		userResp, err = client.UserClient().GetUserById(callCtx, req)
		return err
	})

	if err := eg.Wait(); err != nil {
		log.ErrorWithCtx(ctx, "调用其它服务获取服务名和用户邮箱，发生异常: %v", err)
		return nil, nil, err
	}

	return appResp, userResp, nil
}

type subEnvInfo struct {
	SubEnvName string `json:"sub_env_name,omitempty"`
}

type triggerUser struct {
	Email   string `json:"email"`
	Account string `json:"account,omitempty"`
}

type apiTestCustomParams struct {
	ProjectID          string       `json:"project_id"`
	PlanID             string       `json:"plan_id"`
	SubEnvInfo         *subEnvInfo  `json:"sub_env_info,omitempty"`
	XAccount           string       `json:"x_account"`
	Threshold          float64      `json:"threshold"`
	QualityPlatformUrl string       `json:"execute_plan_url"`
	RunTaskID          int64        `json:"run_task_id,omitempty"`
	TriggerUser        *triggerUser `json:"trigger_user"`
	Services           []string     `json:"services"`
}

func NewDeployTaskBuilder(prs *dao.PipelineRunStage, prt *dao.PipelineRunTask, pr *dao.PipelineRun, prePRT *dao.PipelineRunTask, isRetry bool) *DeployTaskBuilder {
	return &DeployTaskBuilder{
		DefaultTaskBuilder: *NewDefaultTaskBuilder(prs, prt),
		pr:                 pr,
		prePRT:             prePRT,
		isRetry:            isRetry,
	}
}

type DeployTaskBuilder struct {
	DefaultTaskBuilder
	isRetry bool
	pr      *dao.PipelineRun
	prePRT  *dao.PipelineRunTask
}

func (b *DeployTaskBuilder) Build(ctx context.Context) (*v1beta1.PipelineTask, error) {
	var deployConfig model.AutomationDeploy
	if err := b.prt.UnmarshalConfig(&deployConfig); err != nil {
		log.ErrorWithCtx(ctx, "DeployTaskBuilder build *v1beta1.PipelineTask, pr[%d] task[%d] UnmarshalConfig error: %v", b.pr.ID, b.prt.ID, err)
		return nil, bizerr.ErrConfigUnmarshal
	}

	typeName := "manualDeploy"
	if b.isRetry && b.prePRT.GetType().IsDeployTask() {
		typeName = "autoDeploy"
	} else if constants.TriggerModeType(deployConfig.TriggerMode) == constants.AUTO {
		typeName = "autoDeploy"
	}

	log.DebugWithCtx(ctx, "DeployTaskBuilder build *v1beta1.PipelineTask, pr[%d] task[%d] customTask type is %s", b.pr.ID, b.prt.ID, typeName)
	b.task.Params = []v1beta1.Param{
		{Name: "type", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: typeName}},
		{Name: "content", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "{}"}},
	}
	b.task.WhenExpressions = []v1beta1.WhenExpression{
		{Input: fmt.Sprintf("$(params.%s)", constants.GenerateSkipParamName(b.TaskID())), Operator: selection.In, Values: []string{"false"}},
	}

	return b.task, nil
}

func NewFakeDeployTaskBuilder(prs *dao.PipelineRunStage, prt *dao.PipelineRunTask, pr *dao.PipelineRun) *FakeDeployTaskBuilder {
	return &FakeDeployTaskBuilder{
		DefaultTaskBuilder: *NewDefaultTaskBuilder(prs, prt),
		pr:                 pr,
	}
}

// FakeDeployTaskBuilder 是一个类似审批类型的任务，但是不会真正执行部署操作。
type FakeDeployTaskBuilder struct {
	DefaultTaskBuilder
	pr *dao.PipelineRun
}

func (b *FakeDeployTaskBuilder) Build(ctx context.Context) (*v1beta1.PipelineTask, error) {

	b.task.Params = []v1beta1.Param{
		{Name: "type", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: ApprovalDeployTypeFakeDeploy}},
		{Name: "content", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "{}"}},
	}

	// this task will not skip by default
	// 为了支持跳过任务，Approval 服务需要动态设置skip 参数。
	b.task.WhenExpressions = []v1beta1.WhenExpression{
		{Input: fmt.Sprintf("$(params.%s)", constants.GenerateSkipParamName(b.TaskID())), Operator: selection.In, Values: []string{"false"}},
	}

	return b.task, nil
}

const (
	ApprovalDeployTypeMultiCluster  = "multiCluster"  // 多云部署
	ApprovalDeployTypeSingleCluster = "singleCluster" // 单云部署
	ApprovalDeployTypeFakeDeploy    = "fakeDeploy"    // 兼容所有部署

)

type DeployContent struct {
	ApprovalDeployType string `json:"approvalDeployType"`
	Params             any    `json:"params"`
}

func (c *DeployContent) MarshalJsonString() string {
	b, _ := json.Marshal(c)
	return string(b)
}

func UnmarshalDeployContent(s string) (*DeployContent, error) {
	var c DeployContent
	err := json.Unmarshal([]byte(s), &c)
	if err != nil {
		return nil, err
	}
	return &c, nil
}

func (c *DeployContent) UnmarshalToSubTaskRunContent() (*SubTaskRunContent, error) {
	var subContent SubTaskRunContent
	if c.ApprovalDeployType != ApprovalDeployTypeMultiCluster {
		return nil, fmt.Errorf("invalid approval deploy type: %s", c.ApprovalDeployType)
	}
	obj := c.Params.(map[string]any)
	objByte, _ := json.Marshal(obj)
	_ = json.Unmarshal(objByte, &subContent)
	return &subContent, nil
}

type SubTaskRunContent struct {
	SubTaskID     int64  `json:"subTaskID"`
	Name          string `json:"name"`
	TaskRunID     int64  `json:"taskRunID"`
	PipelineRunID int64  `json:"pipelineRunID"`
}

func NewMultiCloudDeployTaskBuilder(prs *dao.PipelineRunStage, prt *dao.PipelineRunTask, mct *dao.PipelineRunSubtask, approvalTypeName string) *MultiCloudDeployTaskBuilder {
	b := &MultiCloudDeployTaskBuilder{
		DefaultTaskBuilder: DefaultTaskBuilder{
			prs: prs,
			prt: prt,
		},
		mct:              mct,
		approvalTypeName: approvalTypeName,
	}
	b.init()
	return b
}

type MultiCloudDeployTaskBuilder struct {
	DefaultTaskBuilder

	mct              *dao.PipelineRunSubtask
	approvalTypeName string
}

func (b *MultiCloudDeployTaskBuilder) init() {
	b.task = makeEmptyApprovalRequestTask(b.Name())
}

func (b *MultiCloudDeployTaskBuilder) Name() string {
	return makeSubTaskRunName(b.TaskType(), b.StageID(), b.TaskID(), b.mct.ID)
}

func (b *MultiCloudDeployTaskBuilder) Build(ctx context.Context) (*v1beta1.PipelineTask, error) {
	content := buildApprovalSubtaskContent(b.mct)
	b.task.Params = []v1beta1.Param{
		{Name: "type", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: b.approvalTypeName}},
		{Name: "content", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: content}},
	}

	return b.task, nil
}

func buildApprovalSubtaskContent(prst *dao.PipelineRunSubtask) string {
	c := &DeployContent{
		ApprovalDeployType: ApprovalDeployTypeMultiCluster,
		Params: &SubTaskRunContent{
			SubTaskID:     prst.ID,
			Name:          prst.Name,
			TaskRunID:     prst.PipelineRunTaskId,
			PipelineRunID: prst.PipelineRunID,
		},
	}

	return c.MarshalJsonString()
}

func NewVirtualTaskBuilder(prs *dao.PipelineRunStage, prt *dao.PipelineRunTask, changeSetRunTask *dao.ChangeSetRunTask) *VirtualTaskBuilder {
	b := &VirtualTaskBuilder{
		DefaultTaskBuilder: *NewDefaultTaskBuilder(prs, prt),
		ChangeSetRunTask:   changeSetRunTask,
	}
	b.init()
	return b
}

type VirtualTaskBuilder struct {
	DefaultTaskBuilder
	ChangeSetRunTask *dao.ChangeSetRunTask
}

func (b *VirtualTaskBuilder) init() {
	b.task.Name = b.Name()
}

func (b *VirtualTaskBuilder) Name() string {
	return b.ChangeSetRunTask.TektonLabel
}

func (b *VirtualTaskBuilder) TaskType() string {
	return b.ChangeSetRunTask.Type.String()
}

func (b *VirtualTaskBuilder) Build(ctx context.Context) (*v1beta1.PipelineTask, error) {
	typeName := b.CustomTaskType()
	if len(typeName) == 0 {
		return nil, errors.New("virtualtask type is empty")
	}
	b.task.Params = []v1beta1.Param{
		{Name: "type", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: typeName}},
		{Name: "content", Value: v1beta1.ParamValue{Type: v1beta1.ParamTypeString, StringVal: "{}"}},
	}

	return b.task, nil
}

func (b *VirtualTaskBuilder) CustomTaskType() string {
	customTaskConfig := conf.GetCustomTaskTypeConfig()
	tt := customTaskConfig[b.TaskType()]
	return tt
}
