package bag

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNew(t *testing.T) {
	ids1 := []int64{1, 2, 3}
	ids2 := []int64{2, 3, 4}

	b := New[int64]()
	b.Add(int64(6))
	b.Adds(ids1...)
	b.Adds(ids2...)

	result := []int64{1, 2, 3, 4, 6}
	assert.ElementsMatch(t, b.Items(), result)
	assert.Equal(t, b.<PERSON>mpty(), false)
	assert.Equal(t, b.<PERSON>(), len(result))
}

func TestDifference(t *testing.T) {
	b1 := New[int64]()
	b2 := New[int64]()

	b1.Adds([]int64{1, 2, 3, 4, 5}...)
	b2.Adds([]int64{2, 3, 5}...)

	result := Difference(b1, b2)
	assert.ElementsMatch(t, result, []int64{1, 4})
}

func TestDifference2(t *testing.T) {
	ids1 := []int64{1, 2, 3}
	ids2 := []int64{2, 3, 4}

	result := Difference(New[int64]().Adds(ids1...), New[int64]().Adds(ids2...))
	assert.ElementsMatch(t, result, []int64{1})
}
