package page

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_Page(t *testing.T) {
	// 示例数据
	data := []interface{}{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11}

	// 分页参数
	a := Page{PageNum: 12, PageSize: 3, TotalRecord: int64(len(data))}

	// 处理分页
	pagination := a.HandlePagination(data)
	fmt.Printf("当前页: %d, 每页条数: %d, 总页数: %d\n", pagination.PageNum, pagination.PageSize, pagination.TotalPage())
	fmt.Println("分页后的数据:%=+v", pagination.List)
	// 断言
	assert.Equal(t, 4, pagination.PageNum, "页码不正确")
	assert.Equal(t, 3, pagination.PageSize, "每页条数不正确")
	assert.Equal(t, 4, pagination.TotalPage(), "总页数不正确")
	assert.Equal(t, []interface{}{10, 11}, pagination.List, "分页后的数据不正确")
}
