package main

import (
	"context"
	"os/signal"
	"syscall"
	"testing"

	"52tt.com/cicd/pkg/kafka"
	"52tt.com/cicd/pkg/log"
)

func kfkMsgCB(msg kafka.Event) (err error, b bool) {

	log.Infof("Kafka msg callback  [%s] type=%s  msgUid=%s SubscribeEvents %v ;", msg.EventTime, msg.Types, msg.MsgId, msg.Datas)

	return
}

var (
	brokers = []string{"************:9092"}
	topics  = []string{"pipeline-test"}
)

func TestUserServiceInit(t *testing.T) {
	s := kafka.NewConsumer(brokers[0], "sentinel-event-test", "deploy-ser-Rs46", "cicd-cia", true, kfkMsgCB)
	// s := kafka.NewConsumer(brokers[0], "cicd-pipeline-event-test", "pipeline-receive-pipeline", "cicd-cia", true, kfkMsgCB)
	log.Infof("start consumer")
	s.Start()

	ctx, stop := signal.NotifyContext(context.Background(),
		syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	defer stop()

	<-ctx.Done()

	log.Infof("stop consumer")
	s.Stop()
}
