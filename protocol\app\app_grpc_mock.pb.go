// Code generated by MockGen. DO NOT EDIT.
// Source: ./protocol/app/app_grpc.pb.go

// Package app is a generated GoMock package.
package app

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockAppServiceClient is a mock of AppServiceClient interface.
type MockAppServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockAppServiceClientMockRecorder
}

// MockAppServiceClientMockRecorder is the mock recorder for MockAppServiceClient.
type MockAppServiceClientMockRecorder struct {
	mock *MockAppServiceClient
}

// NewMockAppServiceClient creates a new mock instance.
func NewMockAppServiceClient(ctrl *gomock.Controller) *MockAppServiceClient {
	mock := &MockAppServiceClient{ctrl: ctrl}
	mock.recorder = &MockAppServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAppServiceClient) EXPECT() *MockAppServiceClientMockRecorder {
	return m.recorder
}

// CreateOrUpdateAppEventlink mocks base method.
func (m *MockAppServiceClient) CreateOrUpdateAppEventlink(ctx context.Context, in *CreateOrUpdateAppEventlinkReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateOrUpdateAppEventlink", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrUpdateAppEventlink indicates an expected call of CreateOrUpdateAppEventlink.
func (mr *MockAppServiceClientMockRecorder) CreateOrUpdateAppEventlink(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateAppEventlink", reflect.TypeOf((*MockAppServiceClient)(nil).CreateOrUpdateAppEventlink), varargs...)
}

// GetApp mocks base method.
func (m *MockAppServiceClient) GetApp(ctx context.Context, in *AppParam, opts ...grpc.CallOption) (*APP, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApp", varargs...)
	ret0, _ := ret[0].(*APP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApp indicates an expected call of GetApp.
func (mr *MockAppServiceClientMockRecorder) GetApp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApp", reflect.TypeOf((*MockAppServiceClient)(nil).GetApp), varargs...)
}

// GetAppBranchList mocks base method.
func (m *MockAppServiceClient) GetAppBranchList(ctx context.Context, in *AppParam, opts ...grpc.CallOption) (*AppBranchList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppBranchList", varargs...)
	ret0, _ := ret[0].(*AppBranchList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppBranchList indicates an expected call of GetAppBranchList.
func (mr *MockAppServiceClientMockRecorder) GetAppBranchList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppBranchList", reflect.TypeOf((*MockAppServiceClient)(nil).GetAppBranchList), varargs...)
}

// GetAppByName mocks base method.
func (m *MockAppServiceClient) GetAppByName(ctx context.Context, in *AppByNameReq, opts ...grpc.CallOption) (*APP, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppByName", varargs...)
	ret0, _ := ret[0].(*APP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppByName indicates an expected call of GetAppByName.
func (mr *MockAppServiceClientMockRecorder) GetAppByName(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppByName", reflect.TypeOf((*MockAppServiceClient)(nil).GetAppByName), varargs...)
}

// GetAppDeployMsg mocks base method.
func (m *MockAppServiceClient) GetAppDeployMsg(ctx context.Context, in *GetDeployMsgReq, opts ...grpc.CallOption) (*GetDeployMsgResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppDeployMsg", varargs...)
	ret0, _ := ret[0].(*GetDeployMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppDeployMsg indicates an expected call of GetAppDeployMsg.
func (mr *MockAppServiceClientMockRecorder) GetAppDeployMsg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppDeployMsg", reflect.TypeOf((*MockAppServiceClient)(nil).GetAppDeployMsg), varargs...)
}

// GetAppEventlink mocks base method.
func (m *MockAppServiceClient) GetAppEventlink(ctx context.Context, in *GetAppEventlinkReq, opts ...grpc.CallOption) (*GetAppEventlinkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppEventlink", varargs...)
	ret0, _ := ret[0].(*GetAppEventlinkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppEventlink indicates an expected call of GetAppEventlink.
func (mr *MockAppServiceClientMockRecorder) GetAppEventlink(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppEventlink", reflect.TypeOf((*MockAppServiceClient)(nil).GetAppEventlink), varargs...)
}

// GetAppInfo mocks base method.
func (m *MockAppServiceClient) GetAppInfo(ctx context.Context, in *GetAppInfoReq, opts ...grpc.CallOption) (*GetAppInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppInfo", varargs...)
	ret0, _ := ret[0].(*GetAppInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppInfo indicates an expected call of GetAppInfo.
func (mr *MockAppServiceClientMockRecorder) GetAppInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppInfo", reflect.TypeOf((*MockAppServiceClient)(nil).GetAppInfo), varargs...)
}

// GetAppListByIds mocks base method.
func (m *MockAppServiceClient) GetAppListByIds(ctx context.Context, in *AppsReq, opts ...grpc.CallOption) (*AppList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppListByIds", varargs...)
	ret0, _ := ret[0].(*AppList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppListByIds indicates an expected call of GetAppListByIds.
func (mr *MockAppServiceClientMockRecorder) GetAppListByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppListByIds", reflect.TypeOf((*MockAppServiceClient)(nil).GetAppListByIds), varargs...)
}

// GetUserApps mocks base method.
func (m *MockAppServiceClient) GetUserApps(ctx context.Context, in *GetUserAppsReq, opts ...grpc.CallOption) (*AppList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserApps", varargs...)
	ret0, _ := ret[0].(*AppList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserApps indicates an expected call of GetUserApps.
func (mr *MockAppServiceClientMockRecorder) GetUserApps(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserApps", reflect.TypeOf((*MockAppServiceClient)(nil).GetUserApps), varargs...)
}

// GetUserPreferenceApps mocks base method.
func (m *MockAppServiceClient) GetUserPreferenceApps(ctx context.Context, in *GetUserAppsReq, opts ...grpc.CallOption) (*AppList, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPreferenceApps", varargs...)
	ret0, _ := ret[0].(*AppList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPreferenceApps indicates an expected call of GetUserPreferenceApps.
func (mr *MockAppServiceClientMockRecorder) GetUserPreferenceApps(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPreferenceApps", reflect.TypeOf((*MockAppServiceClient)(nil).GetUserPreferenceApps), varargs...)
}

// SearchAppByProjectIdAndName mocks base method.
func (m *MockAppServiceClient) SearchAppByProjectIdAndName(ctx context.Context, in *SearchAppParam, opts ...grpc.CallOption) (*APP, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchAppByProjectIdAndName", varargs...)
	ret0, _ := ret[0].(*APP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAppByProjectIdAndName indicates an expected call of SearchAppByProjectIdAndName.
func (mr *MockAppServiceClientMockRecorder) SearchAppByProjectIdAndName(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAppByProjectIdAndName", reflect.TypeOf((*MockAppServiceClient)(nil).SearchAppByProjectIdAndName), varargs...)
}

// SyncCMDBInfo mocks base method.
func (m *MockAppServiceClient) SyncCMDBInfo(ctx context.Context, in *AppByNameReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SyncCMDBInfo", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncCMDBInfo indicates an expected call of SyncCMDBInfo.
func (mr *MockAppServiceClientMockRecorder) SyncCMDBInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncCMDBInfo", reflect.TypeOf((*MockAppServiceClient)(nil).SyncCMDBInfo), varargs...)
}

// MockAppServiceServer is a mock of AppServiceServer interface.
type MockAppServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockAppServiceServerMockRecorder
}

// MockAppServiceServerMockRecorder is the mock recorder for MockAppServiceServer.
type MockAppServiceServerMockRecorder struct {
	mock *MockAppServiceServer
}

// NewMockAppServiceServer creates a new mock instance.
func NewMockAppServiceServer(ctrl *gomock.Controller) *MockAppServiceServer {
	mock := &MockAppServiceServer{ctrl: ctrl}
	mock.recorder = &MockAppServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAppServiceServer) EXPECT() *MockAppServiceServerMockRecorder {
	return m.recorder
}

// CreateOrUpdateAppEventlink mocks base method.
func (m *MockAppServiceServer) CreateOrUpdateAppEventlink(arg0 context.Context, arg1 *CreateOrUpdateAppEventlinkReq) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateAppEventlink", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrUpdateAppEventlink indicates an expected call of CreateOrUpdateAppEventlink.
func (mr *MockAppServiceServerMockRecorder) CreateOrUpdateAppEventlink(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateAppEventlink", reflect.TypeOf((*MockAppServiceServer)(nil).CreateOrUpdateAppEventlink), arg0, arg1)
}

// GetApp mocks base method.
func (m *MockAppServiceServer) GetApp(arg0 context.Context, arg1 *AppParam) (*APP, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApp", arg0, arg1)
	ret0, _ := ret[0].(*APP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApp indicates an expected call of GetApp.
func (mr *MockAppServiceServerMockRecorder) GetApp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApp", reflect.TypeOf((*MockAppServiceServer)(nil).GetApp), arg0, arg1)
}

// GetAppBranchList mocks base method.
func (m *MockAppServiceServer) GetAppBranchList(arg0 context.Context, arg1 *AppParam) (*AppBranchList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppBranchList", arg0, arg1)
	ret0, _ := ret[0].(*AppBranchList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppBranchList indicates an expected call of GetAppBranchList.
func (mr *MockAppServiceServerMockRecorder) GetAppBranchList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppBranchList", reflect.TypeOf((*MockAppServiceServer)(nil).GetAppBranchList), arg0, arg1)
}

// GetAppByName mocks base method.
func (m *MockAppServiceServer) GetAppByName(arg0 context.Context, arg1 *AppByNameReq) (*APP, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppByName", arg0, arg1)
	ret0, _ := ret[0].(*APP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppByName indicates an expected call of GetAppByName.
func (mr *MockAppServiceServerMockRecorder) GetAppByName(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppByName", reflect.TypeOf((*MockAppServiceServer)(nil).GetAppByName), arg0, arg1)
}

// GetAppDeployMsg mocks base method.
func (m *MockAppServiceServer) GetAppDeployMsg(arg0 context.Context, arg1 *GetDeployMsgReq) (*GetDeployMsgResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppDeployMsg", arg0, arg1)
	ret0, _ := ret[0].(*GetDeployMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppDeployMsg indicates an expected call of GetAppDeployMsg.
func (mr *MockAppServiceServerMockRecorder) GetAppDeployMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppDeployMsg", reflect.TypeOf((*MockAppServiceServer)(nil).GetAppDeployMsg), arg0, arg1)
}

// GetAppEventlink mocks base method.
func (m *MockAppServiceServer) GetAppEventlink(arg0 context.Context, arg1 *GetAppEventlinkReq) (*GetAppEventlinkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppEventlink", arg0, arg1)
	ret0, _ := ret[0].(*GetAppEventlinkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppEventlink indicates an expected call of GetAppEventlink.
func (mr *MockAppServiceServerMockRecorder) GetAppEventlink(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppEventlink", reflect.TypeOf((*MockAppServiceServer)(nil).GetAppEventlink), arg0, arg1)
}

// GetAppInfo mocks base method.
func (m *MockAppServiceServer) GetAppInfo(arg0 context.Context, arg1 *GetAppInfoReq) (*GetAppInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppInfo", arg0, arg1)
	ret0, _ := ret[0].(*GetAppInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppInfo indicates an expected call of GetAppInfo.
func (mr *MockAppServiceServerMockRecorder) GetAppInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppInfo", reflect.TypeOf((*MockAppServiceServer)(nil).GetAppInfo), arg0, arg1)
}

// GetAppListByIds mocks base method.
func (m *MockAppServiceServer) GetAppListByIds(arg0 context.Context, arg1 *AppsReq) (*AppList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppListByIds", arg0, arg1)
	ret0, _ := ret[0].(*AppList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppListByIds indicates an expected call of GetAppListByIds.
func (mr *MockAppServiceServerMockRecorder) GetAppListByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppListByIds", reflect.TypeOf((*MockAppServiceServer)(nil).GetAppListByIds), arg0, arg1)
}

// GetUserApps mocks base method.
func (m *MockAppServiceServer) GetUserApps(arg0 context.Context, arg1 *GetUserAppsReq) (*AppList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserApps", arg0, arg1)
	ret0, _ := ret[0].(*AppList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserApps indicates an expected call of GetUserApps.
func (mr *MockAppServiceServerMockRecorder) GetUserApps(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserApps", reflect.TypeOf((*MockAppServiceServer)(nil).GetUserApps), arg0, arg1)
}

// GetUserPreferenceApps mocks base method.
func (m *MockAppServiceServer) GetUserPreferenceApps(arg0 context.Context, arg1 *GetUserAppsReq) (*AppList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPreferenceApps", arg0, arg1)
	ret0, _ := ret[0].(*AppList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPreferenceApps indicates an expected call of GetUserPreferenceApps.
func (mr *MockAppServiceServerMockRecorder) GetUserPreferenceApps(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPreferenceApps", reflect.TypeOf((*MockAppServiceServer)(nil).GetUserPreferenceApps), arg0, arg1)
}

// SearchAppByProjectIdAndName mocks base method.
func (m *MockAppServiceServer) SearchAppByProjectIdAndName(arg0 context.Context, arg1 *SearchAppParam) (*APP, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAppByProjectIdAndName", arg0, arg1)
	ret0, _ := ret[0].(*APP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAppByProjectIdAndName indicates an expected call of SearchAppByProjectIdAndName.
func (mr *MockAppServiceServerMockRecorder) SearchAppByProjectIdAndName(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAppByProjectIdAndName", reflect.TypeOf((*MockAppServiceServer)(nil).SearchAppByProjectIdAndName), arg0, arg1)
}

// SyncCMDBInfo mocks base method.
func (m *MockAppServiceServer) SyncCMDBInfo(arg0 context.Context, arg1 *AppByNameReq) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncCMDBInfo", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncCMDBInfo indicates an expected call of SyncCMDBInfo.
func (mr *MockAppServiceServerMockRecorder) SyncCMDBInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncCMDBInfo", reflect.TypeOf((*MockAppServiceServer)(nil).SyncCMDBInfo), arg0, arg1)
}

// mustEmbedUnimplementedAppServiceServer mocks base method.
func (m *MockAppServiceServer) mustEmbedUnimplementedAppServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAppServiceServer")
}

// mustEmbedUnimplementedAppServiceServer indicates an expected call of mustEmbedUnimplementedAppServiceServer.
func (mr *MockAppServiceServerMockRecorder) mustEmbedUnimplementedAppServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAppServiceServer", reflect.TypeOf((*MockAppServiceServer)(nil).mustEmbedUnimplementedAppServiceServer))
}

// MockUnsafeAppServiceServer is a mock of UnsafeAppServiceServer interface.
type MockUnsafeAppServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAppServiceServerMockRecorder
}

// MockUnsafeAppServiceServerMockRecorder is the mock recorder for MockUnsafeAppServiceServer.
type MockUnsafeAppServiceServerMockRecorder struct {
	mock *MockUnsafeAppServiceServer
}

// NewMockUnsafeAppServiceServer creates a new mock instance.
func NewMockUnsafeAppServiceServer(ctrl *gomock.Controller) *MockUnsafeAppServiceServer {
	mock := &MockUnsafeAppServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAppServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAppServiceServer) EXPECT() *MockUnsafeAppServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAppServiceServer mocks base method.
func (m *MockUnsafeAppServiceServer) mustEmbedUnimplementedAppServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAppServiceServer")
}

// mustEmbedUnimplementedAppServiceServer indicates an expected call of mustEmbedUnimplementedAppServiceServer.
func (mr *MockUnsafeAppServiceServerMockRecorder) mustEmbedUnimplementedAppServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAppServiceServer", reflect.TypeOf((*MockUnsafeAppServiceServer)(nil).mustEmbedUnimplementedAppServiceServer))
}
