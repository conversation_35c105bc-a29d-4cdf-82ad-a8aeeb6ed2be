package scope

import (
	"gorm.io/gorm"
)

type Paginator interface {
	PageNum() int
	SetPageNum(int)
	PageSize() int
	SetPageSize(int)
	PageCond() string
	LastID() int64
	UnlimitedSize() bool
}

const (
	defaultPageSize = 10
	defaultPage     = 1
)

func Paginate(query Paginator) func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		pageNum := query.PageNum()
		if pageNum == 0 {
			pageNum = defaultPage
			query.SetPageNum(pageNum)
		}
		pageSize := query.PageSize()
		if (pageSize > 500 || pageSize <= 0) && !query.UnlimitedSize() {
			pageSize = defaultPageSize
			query.SetPageSize(pageSize)
		}

		offset := (pageNum - 1) * pageSize
		tx = tx.Offset(offset).Limit(pageSize)

		cond := query.PageCond()
		if cond != "" {
			tx = tx.Order(cond)
		}

		lastID := query.LastID()
		if lastID != 0 {
			tx = tx.Where("id > ?", lastID)
		}
		return tx
	}
}
