package cloud

import (
	"52tt.com/cicd/pkg/log"
	"context"
	"crypto/tls"
	continuousdeployment "golang.ttyuyin.com/genproto/quwanapis/cloud/continuousdeployment/v1alpha"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

type dgClient struct {
	continuousdeployment.ContinuousDeploymentServiceClient
	rc credentials.PerRPCCredentials
}

var _ DeployGRPCClient = (*dgClient)(nil)

func NewDeployGRPCClient(url, token string) (DeployGRPCClient, error) {
	g := &dgClient{
		rc: newPerRPCCredential(token),
	}
	conn, err := grpc.DialContext(context.Background(), url, grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{MinVersion: tls.VersionTLS12})), grpc.WithChainUnaryInterceptor(g.withToken), grpc.WithChainStreamInterceptor(g.withStreamToken))
	if err != nil {
		log.Errorf("failed to dial grpc, err: %v, target: %s", err, url)
		return nil, err
	}
	log.Infof("dial grpc success, target: %s", url)
	g.ContinuousDeploymentServiceClient = continuousdeployment.NewContinuousDeploymentServiceClient(conn)
	return g, nil
}

var _ grpc.UnaryClientInterceptor = (*dgClient)(nil).withToken

var _ grpc.StreamClientInterceptor = (*dgClient)(nil).withStreamToken

func (g *dgClient) withToken(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	return invoker(ctx, method, req, reply, cc, append(opts, grpc.PerRPCCredentials(g.rc))...)
}
func (g *dgClient) withStreamToken(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn, method string, streamer grpc.Streamer, opts ...grpc.CallOption) (grpc.ClientStream, error) {
	return streamer(ctx, desc, cc, method, append(opts, grpc.PerRPCCredentials(g.rc))...)
}
