package cloud

import (
	"52tt.com/cicd/pkg/constants"
	"context"
	"encoding/json"
	"fmt"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	vs "istio.io/api/networking/v1beta1"
	"istio.io/client-go/pkg/apis/networking/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"os"
	"testing"

	"github.com/stretchr/testify/require"

	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type CloudSuite struct {
	log.Suite
	ctrl             *gomock.Controller
	session          *httpclient.MockSession
	cloudServiceTest Service
}

func (s *CloudSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.session = httpclient.NewMockSession(s.ctrl)
	s.cloudServiceTest = NewServiceClient("https://cloudclient.com", "cloud-token")
	s.cloudServiceTest.(*httpClient).session = s.session
}

func TestCloudServiceSuite(t *testing.T) {
	suite.Run(t, new(CloudSuite))
}

func (s *CloudSuite) TestGetClusterList_SessionError() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"code": 0, "message":"success", "data": {"clusterList": []}}}`),
	}
	expectErr := fmt.Errorf("test error")
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, expectErr)
	_, err := s.cloudServiceTest.GetClusterList([]string{"<EMAIL>", "<EMAIL>"}, "production")
	assert.Equal(s.T(), expectErr, err)
}

func (s *CloudSuite) TestGetClusterList_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"code": 0, "message":"success", "data": {"clusterList": ["test-cluster-01", "test-cluster-02"]}}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)
	clusters, err := s.cloudServiceTest.GetClusterList([]string{"<EMAIL>", "<EMAIL>"}, "production")
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), 2, len(clusters))
	assert.Equal(s.T(), "test-cluster-01", clusters[0])
}

func (s *CloudSuite) TestGetClusterList_Failed() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"code": 1, "message":"fail", "data": {}}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)
	clusters, err := s.cloudServiceTest.GetClusterList([]string{"<EMAIL>", "<EMAIL>"}, "production")
	assert.Equal(s.T(), "查询容器云集群列表失败: fail", err.Error())
	assert.Nil(s.T(), clusters)
}

func (s *CloudSuite) TestGetNamespaceList_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"code": 0, "message":"success", "data": {"namespaceList": [{"namespace": "xxx-namespace-01", "trafficMarking": "trafficMarking", "describe": "xxx"}]}}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)
	namespaces, err := s.cloudServiceTest.GetNamespaceList("test-cluster-01", []string{"<EMAIL>", "<EMAIL>"}, "origin")
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), 1, len(namespaces))
	assert.Equal(s.T(), "xxx-namespace-01", namespaces[0].Namespace)
}

func (s *CloudSuite) TestGetSubTrafficMarking_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"code": 0, "message":"success", "data": {"namespace": "default", "trafficMarking": "default"}}`),
	}
	s.session.EXPECT().Get(gomock.Any(), gomock.Any()).Return(httpResp, nil)
	trafficMark, err := s.cloudServiceTest.GetSubTrafficMarking("test-cluster-01", "default")
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), "default", trafficMark)
}

func (s *CloudSuite) TestGetClusterResources_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"code": 0, "message":"success", "data": {"list": ["istio-ca-root-cert-xxx"], "page": 1, "size": 2}}`),
	}
	s.session.EXPECT().Get(gomock.Any(), gomock.Any()).Return(httpResp, nil)
	resp, err := s.cloudServiceTest.GetClusterResources("test-cluster-01", "default", "kind", "search", 1, 2)
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), "istio-ca-root-cert-xxx", resp.List[0])
}

func (s *CloudSuite) TestCreateRoute_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"code": 0, "message":"success", "data": {}}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)
	resp, err := s.cloudServiceTest.CreateRoute("test-cluster-01", "default", "service")
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), true, resp)
}

func (s *CloudSuite) TestCopyClusterResource_Success() {
	httpResp := &httpclient.Response{
		Body: []byte(`{"code": 0, "message":"success", "data": {}}`),
	}
	s.session.EXPECT().Post(gomock.Any(), gomock.Any()).Return(httpResp, nil)
	resp, err := s.cloudServiceTest.CopyClusterResource("test-cluster-01", "default", "service", []Resource{{"istio-ca-root-cert-xxx", []string{"test-cluster-"}}})
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), true, resp)
}

// 从牵星获取kubeconfig
func TestGetKubeconfig_Success(t *testing.T) {
	client, err := NewGRPCClient("cloud.ttyuyin.com:8100", "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663")
	if err != nil {
		return
	}

	config, err := client.GetConfig(context.TODO(), &constack.GetConfigRequest{
		Username: "qiujunfeng",
		Cluster:  "k8s-tc-bj-cicd-test",
	})
	if err != nil {
		panic(err)
	}

	// 解密
	cipher, err := DecryptCipher(config.Kubeconfig)
	if err != nil {
		return
	}
	fmt.Println(cipher)
}

func TestDecryptCipher_Success(t *testing.T) {
	data, err := DecryptCipher("")
	if err != nil {
		panic(err)
	}

	fmt.Println(data)
}

func TestCloudService_OldInterface(t *testing.T) {
	t.Skip()
	host := os.Getenv("CLOUD_HOST")
	token := os.Getenv("CLOUD_TOKEN")
	client := NewServiceClient(host, token)

	resp, err := client.GetNamespaceList("k8s-tc-bj-cicd-test", []string{"<EMAIL>"}, "")
	require.NoError(t, err)
	t.Logf("%+v", resp)
}

func TestGetAnyResource(t *testing.T) {
	//t.Skip()
	client, err := NewGRPCClient("alpha-cloud.ttyuyin.com:8100", "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663")
	if err != nil {
		return
	}
	req := &constack.GetRequest{
		Cluster:              "k8s-tc-bj-1-test",
		Namespace:            "cicd",
		Name:                 "wc",
		GroupVersionResource: &constack.GroupVersionResource{Group: "networking.istio.io", Version: "v1beta1", Resource: "sidecars"},
	}
	getSidecar, err := client.Get(context.TODO(), req)
	if err != nil {
		panic(err)
	}
	t.Logf("%+v", getSidecar)
	realSidecar := &v1beta1.Sidecar{}
	err = json.Unmarshal([]byte(getSidecar.Data), realSidecar)
	t.Logf("%+v", realSidecar)
	if err != nil {
		return
	}
	fmt.Println(realSidecar.Spec.Egress)
	for _, egress := range realSidecar.Spec.Egress {
		for _, host := range egress.Hosts {
			fmt.Println(host)
		}
	}
}

type SidecarDetail struct {
	metav1.TypeMeta `json:",inline"`
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              SidecarSpec `json:"spec,omitempty"`
}

type SidecarSpec struct {
	Egress []*SidecarEgress `json:"egress,omitempty"`
	// +optional
	WorkloadSelector *SidecarWorkloadSelector `json:"workloadSelector,omitempty"`
}

type SidecarWorkloadSelector struct {
	Labels map[string]string `json:"labels,omitempty"`
}

type SidecarEgress struct {
	Hosts []string `json:"hosts,omitempty"`
}

func TestCreateAnyResource(t *testing.T) {
	t.Skip()
	client, err := NewGRPCClient("alpha-cloud.ttyuyin.com:8100", "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663")
	if err != nil {
		return
	}
	sidecar := SidecarDetail{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Sidecar",
			APIVersion: "networking.istio.io/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "cpp-test",
			Namespace: "cicd",
			Labels: map[string]string{
				string(constants.ResourceLogoLableKey): string(constants.ResourceLogoLableValue),
			},
		},
		Spec: SidecarSpec{
			Egress: []*SidecarEgress{
				{
					Hosts: []string{"istio-ecosystem/*", "external/*"},
				},
			},
			WorkloadSelector: &SidecarWorkloadSelector{
				Labels: map[string]string{
					"app": "cpp-test",
				},
			},
		},
	}
	data, err := json.Marshal(sidecar)
	if err != nil {
		panic(err)
	}
	fmt.Println(string(data))
	req := &constack.CreateRequest{
		Cluster:   "k8s-tc-bj-1-test",
		Namespace: "cicd",
		Data:      string(data),
	}
	fmt.Println(req)
	_, err = client.Create(context.TODO(), req)
	if err != nil {
		panic(err)
	}
}

func TestUpdateAnyResource(t *testing.T) {
	t.Skip()
	client, err := NewGRPCClient("alpha-cloud.ttyuyin.com:8100", "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663")
	if err != nil {
		return
	}
	sidecar := v1beta1.Sidecar{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Sidecar",
			APIVersion: "networking.istio.io/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "cpp-test",
			Namespace: "cicd",
			Labels: map[string]string{
				string(constants.ResourceLogoLableKey): string(constants.ResourceLogoLableValue),
			},
		},
		Spec: vs.Sidecar{
			Egress: []*vs.IstioEgressListener{
				{
					Hosts: []string{"istio-ecosystem-test/*", "external-test/*"},
				},
			},
			WorkloadSelector: &vs.WorkloadSelector{
				Labels: map[string]string{
					"app": "cpp-test",
				},
			},
		},
	}
	data, err := json.Marshal(sidecar)
	if err != nil {
		panic(err)
	}
	fmt.Println(string(data))
	req := &constack.UpdateRequest{
		Cluster:   "k8s-tc-bj-1-test",
		Namespace: "cicd",
		Data:      string(data),
	}
	_, err = client.Update(context.TODO(), req)
	if err != nil {
		panic(err)
	}
}

func TestSyncUnifiedClusterSidecar(t *testing.T) {
	t.Skip()
	client, err := NewGRPCClient("alpha-cloud.ttyuyin.com:8100", "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663")
	if err != nil {
		return
	}
	req := &constack.SyncUnifiedClusterSidecarReq{
		Cluster:   "k8s-tc-bj-1-test",
		Namespace: "cicd",
		Name:      "demo-sidecar",
	}
	resp, err := client.SyncUnifiedClusterSidecar(context.Background(), req)
	if err != nil {
		panic(err)
	}
	for _, r := range resp.Result {
		fmt.Printf("cluster: %s, namespace: %s, name: %s, status: %s, message: %s\n", r.Cluster, r.Namespace, r.Name, r.Status, r.Message)
	}
}
