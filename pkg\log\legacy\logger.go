package legacy

import (
	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/context"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"time"
)

const TimePattern = "2006-01-02 15:04:05.000"

var Logger *zap.Logger
var SugarLogger *zap.SugaredLogger

// 自定义时间输出格式
var customTimeEncoder = func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(fmt.Sprintf("[%s]", t.Format(TimePattern)))
}

// 自定义日志级别显示
var customLevelEncoder = func(level zapcore.Level, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(fmt.Sprintf("[%s]", level.CapitalString()))
}

func init() {
	_ = InitLog(&Config{
		Logname:    "/tmp/default.log",
		Loglevel:   "info",
		Maxsize:    100,
		Maxbackups: 1,
		Maxage:     1,
		Compress:   false,
	})
}

func InitLog(logConfig *Config) error {
	// get logger writer syncer
	fileOutput := getLoggerWriter(logConfig)

	// get logger encoder
	consoleEncoder := getConsoleEncoder()
	consoleOutput := zapcore.Lock(os.Stdout)

	// logger level
	var zLevel zapcore.Level
	switch logConfig.Loglevel {
	case "info":
		zLevel = zapcore.InfoLevel
	case "debug":
		zLevel = zapcore.DebugLevel
	case "error":
		zLevel = zapcore.ErrorLevel
	case "fatal":
		zLevel = zapcore.FatalLevel
	default:
		zLevel = zapcore.WarnLevel
	}
	atomicLevel := zap.NewAtomicLevel()
	atomicLevel.SetLevel(zLevel)
	enableLog := zap.LevelEnablerFunc(func(level zapcore.Level) bool {
		return level >= zLevel
	})
	var zapCores []zapcore.Core

	// zap core
	zapCores = append(zapCores, zapcore.NewCore(consoleEncoder, fileOutput, enableLog))
	zapCores = append(zapCores, zapcore.NewCore(consoleEncoder, consoleOutput, enableLog))

	core := zapcore.NewTee(zapCores...)
	Logger = zap.New(core).WithOptions(zap.AddCaller(), zap.AddCallerSkip(1))
	SugarLogger = Logger.Sugar()

	return nil
}

func getConsoleEncoder() zapcore.Encoder {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = customTimeEncoder
	encoderConfig.EncodeLevel = customLevelEncoder
	return zapcore.NewConsoleEncoder(encoderConfig)
}

func getLoggerWriter(logConfig *Config) zapcore.WriteSyncer {
	lumberJackLogger := lumberjack.Logger{
		Filename:   logConfig.Logname,
		MaxSize:    logConfig.Maxage,
		MaxAge:     logConfig.Maxage,
		MaxBackups: logConfig.Maxbackups,
		LocalTime:  false,
		Compress:   logConfig.Compress,
	}
	return zapcore.AddSync(&lumberJackLogger)
}

func Debug(format string) {
	SugarLogger.Debug(format)
}

func Info(format string) {
	SugarLogger.Info(format)
}

func Warn(format string) {
	SugarLogger.Warn(format)
}

func Error(format string) {
	SugarLogger.Error(format)
}

func Fatal(format string) {
	SugarLogger.Fatal(format)
}

func Debugf(format string, args ...interface{}) {
	SugarLogger.Debugf(format, args...)
}

func Infof(format string, args ...interface{}) {
	SugarLogger.Infof(format, args...)
}

func Warnf(format string, args ...interface{}) {
	SugarLogger.Warnf(format, args...)
}

func Errorf(format string, args ...interface{}) {
	SugarLogger.Errorf(format, args...)
}

func Fatalf(format string, args ...interface{}) {
	SugarLogger.Fatalf(format, args...)
}

func InitMockLog(mockLogger *zap.SugaredLogger) {
	SugarLogger = mockLogger
}

type logInfo struct {
	time    string
	userId  int64
	oType   constants.OperationType
	oObject constants.OperationObjects
	id      int64
}

func LogOperation(ctx *gin.Context, oType constants.OperationType, object constants.OperationObjects, id int64) {
	info := logInfo{
		time:    time.Now().Format(TimePattern),
		userId:  context.GetUseridFromGin(ctx),
		oType:   oType,
		oObject: object,
		id:      id,
	}
	SugarLogger.Infof("操作时间：%s, 操作人：ID:%d, 操作类型：%s, 操作对象：%s-ID:%d",
		info.time, info.userId, info.oType, info.oObject, info.id)
}
