-- 发布计划添加金丝雀策略和流量控制计划字段
alter table `deploy`.`deploy_plan`
    add column `canary_policy` varchar(256) default "CLIENT_VERSION" comment '金丝雀策略',
    add column `traffic_control_plan` json comment '流量控制计划',
    add column `traffic_mark_name` varchar(64) default "quwan-default-canary-client-version-traffic-mark" comment '流量标记名称';

-- 发布计划记录
alter table `deploy`.`deploy_plan_record`
    alter column `status` SET DEFAULT 'UNRELATED';

-- 客户端版本号表
CREATE TABLE `deploy`.`deploy_plan_version` (
    `id`           bigint                                  NOT NULL AUTO_INCREMENT COMMENT 'id',
    `deploy_plan_id` bigint                                NOT NULL COMMENT '发布计划id',
    `version`      varchar(128) NOT NULL COMMENT '版本号',
    `created_at`   timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_deploy_plan_id` (`deploy_plan_id`)
) ENGINE=InnoDB  COMMENT='发布计划版本号';


CREATE TABLE `deploy`.`plan_run` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  -- 发布计划快照 数据
  `deploy_plan_id` bigint NOT NULL COMMENT '所属发布计划id',
  `deploy_plan_record_id` bigint NOT NULL COMMENT '发布计划记录id',
  `time_type` varchar(64)  NOT NULL default '' COMMENT '放量计划时间类型',
  `plan_run_cfg` json comment '运行的整体配置快照',
  `traffic_control_cfg` json comment '流量控制计划配置快照',
  -- 计划作用的数据
  `pipeline_run_id` bigint NOT NULL COMMENT '流水线运行id',
  `ticket_task_run_id` bigint NOT NULL COMMENT '工单任务运行id',
  -- 计划运行数据
  `need_run` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:不需要运行，未激活 1:需要运行',
  `status` varchar(64)  NOT NULL default 'INITIAL' COMMENT '状态 INITIAL:初始化,RUNNING:运行中, SUCCESS:成功, FAILED:失败',
  `time_start` bigint NOT NULL DEFAULT 0 COMMENT '执行开始时间戳(s)',
  `time_end` bigint NOT NULL DEFAULT 0 COMMENT '执行结束时间戳(s)',
  `time_interval` int(10) NOT NULL DEFAULT 0 COMMENT '执行时长(S秒)',
  -- 公共部分
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1:删除',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_pipeline_run` (`pipeline_run_id`) USING BTREE,
  KEY `idx_deploy_plan` (`deploy_plan_id`) USING BTREE,
  KEY `idx_del` (`is_del`) USING BTREE,
  KEY `idx_run` (`need_run`,`status`) USING BTREE
) ENGINE=InnoDB COMMENT='放量计划自动运行表';


CREATE TABLE `deploy`.`plan_node_run` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `plan_run_id` bigint NOT NULL COMMENT '所属放量计划运行id',
  `action` varchar(64)  NOT NULL default '' COMMENT '动作类型: 调整流量比例、通过金丝雀、部署基准等',
  `node_run_cfg` json  comment '节点运行配置快照',
  -- 计划运行数据
  `need_run` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:不需要运行，未激活 1:需要运行',
  `status` varchar(64)  NOT NULL default 'INITIAL' COMMENT '状态 INITIAL:初始化,WAITING:待调度, RUNNING:运行中, SUCCESS:成功, FAILED:失败',
  `time_start` bigint NOT NULL DEFAULT 0 COMMENT '执行开始时间戳(s)',
  `time_end` bigint NOT NULL DEFAULT 0 COMMENT '执行结束时间戳(s)',
  `time_interval` int(10) NOT NULL DEFAULT 0 COMMENT '执行时长(S秒)',
  `time_schedule` bigint NOT NULL DEFAULT 0 COMMENT '任务被调度到的时间戳(s)',

  `run_input` text COMMENT '本次执行入参数据Json,为了方便排查问题',
  `run_output` text COMMENT '本次执行出参数据Json,为了方便排查问题',
  `run_remark` text COMMENT '执行描述',
  `run_times` int(10) NOT NULL DEFAULT 0 COMMENT '执行(重试)次数',
  -- 公共部分
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1:删除',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_run` (`plan_run_id`) USING BTREE,
  KEY `idx_del` (`is_del`) USING BTREE,
  KEY `idx_run` (`need_run`,`status`) USING BTREE
) ENGINE=InnoDB COMMENT='放量计划自动运行表';

CREATE TABLE `pipeline`.`pipeline_run_operate_log` (
    `id`                    bigint                                  NOT NULL AUTO_INCREMENT COMMENT 'id',
    `action`                varchar(64)                             NOT NULL COMMENT '操作动作',
    `pipeline_run_id`       bigint                                  NOT NULL COMMENT '流水线运行id',
    `task_run_id`           bigint                                  NOT NULL COMMENT '任务运行id',
    `task_run_type`         varchar(64)                             NOT NULL COMMENT '任务运行类型',
    `operator`              bigint                                  NOT NULL COMMENT '操作人',
    `operator_chinese_name` varchar(128) NOT NULL COMMENT '操作人中文名',
    `operator_employee_no`  varchar(128) NOT NULL COMMENT '操作人工号',
    `created_at`            timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`            timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_pipeline_run_id` (`pipeline_run_id`),
    KEY `idx_task_run_id` (`task_run_id`)
) ENGINE=InnoDB COMMENT='流水线运行操作日志表';

-- 增加发布计划取消人信息
alter table `deploy`.`plan_run`
    add column `canceled_by_chinese_name` varchar(256) default "" comment '取消人中文名',
    add column `canceled_by_employee_no` varchar(256) default "" comment '取消人工号',
    add column `canceled_at` timestamp comment '取消时间'

-- 增加发布计划客户端 马甲包信息
alter table `deploy`.`deploy_plan_version`
    add column `req_cli_type` bigint default NULL comment '客户端类型header',
    add column `req_market_id` bigint default NULL comment '马甲包类型header',
