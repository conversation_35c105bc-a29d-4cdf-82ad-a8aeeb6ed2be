CREATE TABLE `pipeline_run_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `pipeline_run_stage_id` bigint NOT NULL COMMENT '流水线运行阶段id',
  `name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `type` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务类型',
  `status` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务运行状态',
  `started_time` timestamp NULL DEFAULT NULL COMMENT '任务开始时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '任务完成时间',
  `config` json NOT NULL COMMENT '运行任务全量配置信息',
  `result` json DEFAULT NULL COMMENT '任务结果',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `tekton_name` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'tektonCRDName',
  `tekton_namespace` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT'k8snamespace',
  `task_id` int DEFAULT NULL COMMENT 'task id',
  `pod_name` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'taskrun pod name',
  `pipeline_run_id` bigint DEFAULT NULL COMMENT 'pipeline run id',
  PRIMARY KEY (`id`),
  KEY `pipeline_run_task_pipeline_run_stage_id_index` (`pipeline_run_stage_id`),
  KEY `pipeline_run_task_task_id_index` (`task_id`),
  KEY `pipeline_run_task_pipeline_run_id_index` (`pipeline_run_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='流水线运行任务表';

-- 是否实际运行的克隆记录
alter table `pipeline`.`pipeline_run_task` add column `is_clone` tinyint not null default 0 comment '是否实际运行的克隆记录';