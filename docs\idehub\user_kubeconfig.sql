/*
 Navicat Premium Data Transfer

 Source Server         : cicd-test
 Source Server Type    : MySQL
 Source Server Version : 80022
 Source Host           : ************:3306
 Source Schema         : idehub

 Target Server Type    : MySQL
 Target Server Version : 80022
 File Encoding         : 65001

 Date: 11/06/2024 15:48:07
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for user_kubeconfig
-- ----------------------------
DROP TABLE IF EXISTS `user_kubeconfig`;
CREATE TABLE `user_kubeconfig`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `cluster` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署集群',
  `user_id` bigint(0) NOT NULL COMMENT '用户id',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'k8s配置内容',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_user_cluster`(`user_id`, `cluster`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户k8s配置' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
