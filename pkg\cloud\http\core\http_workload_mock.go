// Code generated by MockGen. DO NOT EDIT.
// Source: http_workload.go

// Package core is a generated GoMock package.
package core

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockWorkloadClient is a mock of WorkloadClient interface.
type MockWorkloadClient struct {
	ctrl     *gomock.Controller
	recorder *MockWorkloadClientMockRecorder
}

// MockWorkloadClientMockRecorder is the mock recorder for MockWorkloadClient.
type MockWorkloadClientMockRecorder struct {
	mock *MockWorkloadClient
}

// NewMockWorkloadClient creates a new mock instance.
func NewMockWorkloadClient(ctrl *gomock.Controller) *MockWorkloadClient {
	mock := &MockWorkloadClient{ctrl: ctrl}
	mock.recorder = &MockWorkloadClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWorkloadClient) EXPECT() *MockWorkloadClientMockRecorder {
	return m.recorder
}

// GetReadyReplicas mocks base method.
func (m *MockWorkloadClient) GetReadyReplicas(ctx context.Context, in *GetReadyReplicasRequest) (*GetReadyReplicasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReadyReplicas", ctx, in)
	ret0, _ := ret[0].(*GetReadyReplicasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReadyReplicas indicates an expected call of GetReadyReplicas.
func (mr *MockWorkloadClientMockRecorder) GetReadyReplicas(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadyReplicas", reflect.TypeOf((*MockWorkloadClient)(nil).GetReadyReplicas), ctx, in)
}
