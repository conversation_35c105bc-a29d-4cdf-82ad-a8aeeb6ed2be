/*
 Navicat Premium Data Transfer

 Source Server         : cicd-test
 Source Server Type    : MySQL
 Source Server Version : 80022
 Source Host           : ************:3306
 Source Schema         : idehub

 Target Server Type    : MySQL
 Target Server Version : 80022
 File Encoding         : 65001

 Date: 02/08/2024 17:17:48
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for nhctl_op_record
-- ----------------------------
DROP TABLE IF EXISTS `nhctl_op_record`;
CREATE TABLE `nhctl_op_record`  (
                                    `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `client` varchar(64)  NOT NULL DEFAULT '' COMMENT '客户端类型',
                                    `ide` varchar(64) NOT NULL DEFAULT '' COMMENT 'ide类型',
                                    `system` varchar(64) NOT NULL DEFAULT '' COMMENT '操作系统类型',
                                    `plugin_version` varchar(64)  NOT NULL DEFAULT '' COMMENT '插件版本',
                                    `nhctl_version` varchar(64)  NOT NULL DEFAULT '' COMMENT 'nhctl版本',
                                    `user_id` bigint(0) NULL DEFAULT -1 COMMENT '用户id',
                                    `app_id` bigint(0) NOT NULL DEFAULT -1 COMMENT 'appId',
                                    `cluster` varchar(64) NOT NULL DEFAULT '' COMMENT '服务所在的集群',
                                    `namespace` varchar(64)  NOT NULL DEFAULT '' COMMENT '服务所在的命名空间',
                                    `action` varchar(64)  NOT NULL DEFAULT '' COMMENT '执行的动作',
                                    `cmd` varchar(255)  NOT NULL DEFAULT '' COMMENT '执行的命令',
                                    `result_code` bigint(0) NOT NULL DEFAULT -1 COMMENT '执行的结果',
                                    `elasped_time` bigint(0) NULL DEFAULT NULL COMMENT '执行的耗时(单位为秒)',
                                    `extra_data` json NULL COMMENT '拓展字段(额外信息)',
                                    `created_at` timestamp(0) NULL DEFAULT NULL COMMENT '创建时间',
                                    `updated_at` timestamp(0) NULL DEFAULT NULL COMMENT '更新时间',
                                    `unique_identification` varchar(64)  NULL DEFAULT NULL COMMENT '唯一标识,用于记录单次操作',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT 'nhctl上报操作表' ROW_FORMAT = Dynamic ;

SET FOREIGN_KEY_CHECKS = 1;
