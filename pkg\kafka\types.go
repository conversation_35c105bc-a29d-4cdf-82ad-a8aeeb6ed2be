package kafka

import (
	"encoding/json"
	"time"

	cctx "52tt.com/cicd/pkg/context"
)

type Event struct {
	EventTime time.Time        `json:"eventTime"`
	Types     string           `json:"types"`
	Sources   string           `json:"source"`
	Datas     any              `json:"data"`
	MsgId     string           `json:"msgId"`
	TktEvtId  string           `json:"tktEvtId"`
	ReqInfo   cctx.RequestInfo `json:"reqInfo"`
}

func (e *Event) SetID(msgId string) {
	e.MsgId = msgId
}

func (e *Event) SetType(types string) {
	e.Types = types
}

func (e *Event) SetSource(source string) {
	e.Sources = source
}

// contentType JSON  Only
func (e *Event) SetData(contentType string, data any) (err error) {
	e.Datas = data
	return
}

func (e *Event) SetDatas(data any) {
	e.Datas = data
}

func (e *Event) ID() (msgId string) {
	return e.MsgId
}

func (e *Event) Type() (types string) {
	return e.Types
}

func (e *Event) Source() (source string) {
	return e.Sources
}

func (e *Event) String() (str string) {
	return
}

func (e *Event) Time() time.Time {
	return e.EventTime
}

// contentType JSON  Only
// func (e Event) Data() (data any) {
// 	return e.data
// }

func (e *Event) Data() []byte {
	b, err := json.Marshal(e.Datas)
	if err != nil {
		return make([]byte, 0)
	}
	return b
}

// DataAs attempts to populate the provided data object with the event payload.
// obj should be a pointer type.
func (e *Event) DataAs(obj interface{}) error {
	if obj == nil {
		return nil
	}
	data := e.Data()
	if len(data) == 0 {
		// No data.
		return nil
	}
	return json.Unmarshal(data, obj)
}
