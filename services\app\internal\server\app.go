package server

import (
	"context"

	"52tt.com/cicd/pkg/constants"
	"github.com/golang/protobuf/ptypes/empty"

	"52tt.com/cicd/services/app/internal/model"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/tools/vec"
	pbapp "52tt.com/cicd/protocol/app"
	"52tt.com/cicd/services/app/internal/dao"
	"52tt.com/cicd/services/app/internal/service"
	"github.com/mitchellh/mapstructure"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type AppServer struct {
	pbapp.UnimplementedAppServiceServer
	as     service.AppService
	ar     dao.AppRepository
	pr     dao.ProjectRepository
	cfgSvc service.ConfigManagerService
}

func NewAppServer(as service.AppService, ar dao.AppRepository, pr dao.ProjectRepository, cfgSvc service.ConfigManagerService) *AppServer {
	return &AppServer{
		as:     as,
		ar:     ar,
		pr:     pr,
		cfgSvc: cfgSvc,
	}
}

func (a *AppServer) GetApp(ctx context.Context, param *pbapp.AppParam) (*pbapp.APP, error) {
	info, err := a.as.GetAppBasicInfo(ctx, param.Id)
	if err != nil {
		return &pbapp.APP{}, status.Errorf(codes.Internal, "查找服务信息失败，%v", err)
	}
	if info == nil {
		return &pbapp.APP{}, nil
	}
	result := pbapp.APP{}
	err = mapstructure.Decode(info, &result)
	if err != nil {
		return &pbapp.APP{}, status.Errorf(codes.Internal, "类型转换失败，%v", err)
	}
	return &result, nil
}

func (a *AppServer) SearchAppByProjectIdAndName(ctx context.Context, param *pbapp.SearchAppParam) (*pbapp.APP, error) {
	result := pbapp.APP{}
	app, err := a.ar.FindAppBy(ctx, param.ProjectId, param.Name)
	if err != nil {
		return nil, err
	}
	if app != nil {
		err = mapstructure.Decode(app, &result)
	}
	return &result, nil
}

func (a *AppServer) GetAppBranchList(ctx context.Context, param *pbapp.AppParam) (*pbapp.AppBranchList, error) {
	branches, err := a.as.GetBranchList(ctx, param.Id, param.BranchSearch, param.Regex)
	if err != nil {
		return nil, err
	}
	return &pbapp.AppBranchList{BranchList: branches}, nil
}

func (a *AppServer) GetAppListByIds(ctx context.Context, appsReq *pbapp.AppsReq) (*pbapp.AppList, error) {
	var (
		apps []dao.App
		err  error
	)
	if len(appsReq.GetId()) > 0 {
		apps, err = a.as.GetAppListByIds(ctx, appsReq.GetId(), appsReq.GetProjectId())
	} else {
		apps, err = a.ar.FindAllAppBasicInfo(ctx, &model.AppQuery{ProjectId: appsReq.GetProjectId()})
	}
	resp := &pbapp.AppList{}
	if err != nil {
		return resp, err
	}

	var appList []*pbapp.APP
	for _, app := range apps {
		appItem := pbapp.APP{
			Id:          app.ID,
			Name:        app.Name,
			BuildPath:   app.BuildPath,
			RepoAddr:    app.RepoAddr,
			LangName:    app.LangName,
			LangVersion: app.LangVersion,
			ProjectID:   app.ProjectID,
			Description: app.Description,
			Owners: vec.MapTo(app.Users, func(u dao.AppUser) int64 {
				return u.UserId
			}),
		}
		appList = append(appList, &appItem)
	}
	resp.Apps = appList
	return resp, nil
}

func (a *AppServer) GetAppDeployMsg(ctx context.Context, param *pbapp.GetDeployMsgReq) (resp *pbapp.GetDeployMsgResp, errIn error) {
	var app *model.AppResp
	var dyCfgs []*pbapp.DynamicConfig

	log.InfoWithCtx(ctx, "GetAppDeployMsg 获取服务[%d] 基本信息a.as.GetApp", param.GetId())
	app, errIn = a.as.GetApp(ctx, param.GetId())
	if errIn != nil {
		log.ErrorWithCtx(ctx, "GetAppDeployMsg 获取服务[%d]部署信息，发生异常: %v", param.GetId(), errIn)
		return
	}

	log.InfoWithCtx(ctx, "GetAppDeployMsg 获取服务[%d] 项目信息a.as.GetAppPrj", param.GetId())
	appPrj, errIn := a.as.GetAppPrj(ctx, param.GetId())
	if errIn != nil {
		log.ErrorWithCtx(ctx, "GetAppDeployMsg GetAppPrj 服务[%d] 发生异常: %v", param.GetId(), errIn)
		return
	}

	log.InfoWithCtx(ctx, "GetAppDeployMsg 获取服务[%d] 动态配置信息a.cfgSvc.FindAppDyCfgs ", param.GetId())
	appDyCfgs, errIn := a.cfgSvc.FindAppDyCfgs(context.Background(), &model.FindAppDyCfgsReq{
		AppID:     param.GetId(),
		EnvType:   constants.EnvType(param.GetEnvType()),
		ProjectID: appPrj.ProjectID,
		UserGroup: appPrj.UserGroup,
	})
	if errIn != nil {
		log.ErrorWithCtx(ctx, "GetAppDeployMsg FindAppDyCfgs 服务[%d] 发生异常: %v", param.GetId(), errIn)
		return
	}
	for _, cfg := range appDyCfgs {
		dyCfgs = append(dyCfgs, &pbapp.DynamicConfig{
			Id:       cfg.ID,
			FileName: cfg.Name,
			EnvType:  cfg.Env.String(),
			ApolloNs: cfg.ApolloNamespace,
			IsGlobal: cfg.IsGlobal,
		})
	}

	if app == nil {
		log.ErrorWithCtx(ctx, "GetAppDeployMsg 服务[%d]基本信息不存在", param.GetId())
		return nil, status.Errorf(codes.Internal, "服务[%d]基本信息不存在", param.GetId())
	}

	log.InfoWithCtx(ctx, "GetAppDeployMsg 获取服务[%d] ... End ", param.GetId())

	resp = &pbapp.GetDeployMsgResp{
		Level:          app.Level,
		MatchLabels:    app.MatchLabels,
		ServiceLabels:  app.ServiceLabels,
		ProjectId:      app.ProjectID,
		CmdbId:         app.CmdbID,
		LangName:       app.LangName,
		AppName:        app.Name,
		StandLabelEnvs: app.StandLabelEnvs,
		DynamicConfigs: dyCfgs,
	}
	return
}

func (a *AppServer) GetAppInfo(ctx context.Context, req *pbapp.GetAppInfoReq) (*pbapp.GetAppInfoResp, error) {
	app, err := a.ar.FindAppBasicInfoById(ctx, req.GetAppId())
	if err != nil {
		log.ErrorWithCtx(ctx, "获取服务[%d]基本信息，发生异常: %v", req.GetAppId(), err)
		return nil, status.Errorf(codes.Internal, "获取服务基本信息发生异常: %v", err)
	}
	if app == nil {
		log.ErrorWithCtx(ctx, "服务[%d]基本信息不存在", req.GetAppId())
		return nil, status.Errorf(codes.Internal, "服务基本信息不存在")
	}
	var basicInfo pbapp.APP
	err = mapstructure.Decode(app, &basicInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "服务[%d]基本信息处理发生异常: %v", req.GetAppId(), err)
		return nil, status.Errorf(codes.Internal, "服务基本信息处理发生异常: %v", err)
	}

	project, err := a.pr.FindProjectByID(ctx, app.ProjectID)
	if err != nil {
		log.ErrorWithCtx(ctx, "获取服[%d]务项目信息，发生异常: %v", req.GetAppId(), err)
		return nil, status.Errorf(codes.Internal, "获取服务项目信息发生异常: %v", err)
	}
	if project == nil {
		log.ErrorWithCtx(ctx, "服务[%d]项目信息不存在", req.GetAppId())
		return nil, status.Errorf(codes.Internal, "服务项目信息不存在")
	}
	projectInfo := pbapp.GetAppInfoResp_ProjectInfo{
		Id:                       project.ID,
		Name:                     project.Name,
		Type:                     project.Type.String(),
		Description:              project.Description,
		Identity:                 project.Identity(),
		CanaryDeployNotifyStatus: project.CanaryDeployNotifyStatus,
	}

	resp := &pbapp.GetAppInfoResp{
		App:     &basicInfo,
		Project: &projectInfo,
	}
	return resp, nil
}

func (a *AppServer) GetUserApps(ctx context.Context, req *pbapp.GetUserAppsReq) (*pbapp.AppList, error) {
	query := model.UserAppsReq{
		UserID:     req.GetUserId(),
		ProjectIDs: req.GetProjectIds(),
		AppName:    req.GetAppName(),
	}
	apps, err := a.ar.GetUserApps(ctx, query)
	resp := &pbapp.AppList{}
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetUserApps] get user apps error: %v, query: %+v", err, query)
		return resp, err
	}
	var allApps []*pbapp.APP
	for _, app := range apps {
		allApps = append(allApps, &pbapp.APP{
			Id:          app.ID,
			Name:        app.Name,
			BuildPath:   app.BuildPath,
			RepoAddr:    app.RepoAddr,
			LangName:    app.LangName,
			LangVersion: app.LangVersion,
			ProjectID:   app.ProjectID,
			Description: app.Description,
			CmdbId:      app.CmdbId,
			ProjectName: app.Project.Name,
		})
		log.DebugWithCtx(ctx, "[GetUserApps] get user app: %+v, project: %+v", app, app.Project)
	}
	resp.Apps = allApps
	return resp, nil
}

func (a *AppServer) GetUserPreferenceApps(ctx context.Context, req *pbapp.GetUserAppsReq) (*pbapp.AppList, error) {
	query := model.UserAppsReq{
		UserID:     req.GetUserId(),
		ProjectIDs: req.GetProjectIds(),
		AppName:    req.GetAppName(),
	}
	apps, err := a.ar.GetUserPreferenceApps(ctx, query)
	resp := &pbapp.AppList{}
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetUserPreferenceApps] get user preference apps error: %v, query: %+v", err, query)
		return resp, err
	}
	var allApps []*pbapp.APP
	for _, app := range apps {
		allApps = append(allApps, &pbapp.APP{
			Id:          app.ID,
			Name:        app.Name,
			BuildPath:   app.BuildPath,
			RepoAddr:    app.RepoAddr,
			LangName:    app.LangName,
			LangVersion: app.LangVersion,
			ProjectID:   app.ProjectID,
			Description: app.Description,
			CmdbId:      app.CmdbId,
			ProjectName: app.Project.Name,
		})
	}
	resp.Apps = allApps
	return resp, nil
}

func (a *AppServer) CreateOrUpdateAppEventlink(ctx context.Context, req *pbapp.CreateOrUpdateAppEventlinkReq) (*empty.Empty, error) {
	env := constants.EnvType(req.GetEnv())
	eventlink, err := a.ar.GetAppEventlink(ctx, req.GetAppId(), env)
	if err != nil {
		log.ErrorWithCtx(ctx, "[CreateOrUpdateAppEventlink] 获取服务Eventlink信息失败: %v", err)
		return &empty.Empty{}, err
	}
	if eventlink != nil {
		eventlink.ConsumerType = req.GetConsumerType()
		eventlink.ProducerType = req.GetProducerType()
		if err := a.ar.UpdateAppEventlink(ctx, eventlink); err != nil {
			log.ErrorWithCtx(ctx, "更新服务事件关联信息失败: %v", err)
			return &empty.Empty{}, err
		}
	} else {
		eventlink = &dao.AppEventlink{
			AppID:        req.GetAppId(),
			AppName:      req.GetAppName(),
			ProjectID:    req.GetProjectId(),
			ConsumerType: req.GetConsumerType(),
			ProducerType: req.GetProducerType(),
			Env:          env,
		}
		if err := a.ar.CreateAppEventlink(ctx, eventlink); err != nil {
			log.ErrorWithCtx(ctx, "创建服务事件关联信息失败: %v", err)
			return &empty.Empty{}, err
		}
	}
	return &empty.Empty{}, nil
}

func (a *AppServer) GetAppEventlink(ctx context.Context, req *pbapp.GetAppEventlinkReq) (*pbapp.GetAppEventlinkResp, error) {
	env := constants.EnvType(req.GetEnv())
	eventlink, err := a.ar.GetAppEventlink(ctx, req.GetAppId(), env)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetAppEventlink] 获取服务Eventlink信息失败: %v", err)
		return &pbapp.GetAppEventlinkResp{}, err
	}
	if eventlink == nil {
		return &pbapp.GetAppEventlinkResp{
			AppId: req.GetAppId(),
			Env:   req.GetEnv(),
		}, nil
	}
	return &pbapp.GetAppEventlinkResp{
		AppId:        req.GetAppId(),
		AppName:      eventlink.AppName,
		ProjectId:    eventlink.ProjectID,
		Env:          req.GetEnv(),
		ConsumerType: eventlink.ConsumerType,
		ProducerType: eventlink.ProducerType,
	}, nil
}

func (a *AppServer) GetAppByName(ctx context.Context, req *pbapp.AppByNameReq) (rst *pbapp.APP, errRPC error) {
	rst = &pbapp.APP{}
	app, err := a.ar.FindAppsByName(ctx, model.FindAppsReq{
		AppName:     req.GetName(),
		ProjectName: req.GetProjectName(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AppService FindAppWithPrjs Err: %v", err)
		return
	}

	if len(app) > 0 {
		rst = &pbapp.APP{
			Id:          app[0].ID,
			Name:        app[0].Name,
			BuildPath:   app[0].BuildPath,
			RepoAddr:    app[0].RepoAddr,
			LangName:    app[0].LangName,
			LangVersion: app[0].LangVersion,
			ProjectID:   app[0].ProjectID,
			Description: app[0].Description,
			CmdbId:      app[0].CmdbId,
			SenvStatus:  app[0].SenvStatus,
		}
		if app[0].Project != nil {
			rst.ProjectName = app[0].Project.Name
		}
	}

	return
}

func (a *AppServer) SyncCMDBInfo(ctx context.Context, req *pbapp.AppByNameReq) (rst *empty.Empty, errRPC error) {

	return
}
