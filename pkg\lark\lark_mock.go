// Code generated by MockGen. DO NOT EDIT.
// Source: lark.go

// Package lark is a generated GoMock package.
package lark

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// SendMsgToUsr mocks base method.
func (m *MockService) SendMsgToUsr(content, unionId, msgType string) (*larkim.CreateMessageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMsgToUsr", content, unionId, msgType)
	ret0, _ := ret[0].(*larkim.CreateMessageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendMsgToUsr indicates an expected call of SendMsgToUsr.
func (mr *MockServiceMockRecorder) SendMsgToUsr(content, unionId, msgType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsgToUsr", reflect.TypeOf((*MockService)(nil).SendMsgToUsr), content, unionId, msgType)
}

// UpdateMsgCard mocks base method.
func (m *MockService) UpdateMsgCard(msgId, content string) (*larkim.PatchMessageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMsgCard", msgId, content)
	ret0, _ := ret[0].(*larkim.PatchMessageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMsgCard indicates an expected call of UpdateMsgCard.
func (mr *MockServiceMockRecorder) UpdateMsgCard(msgId, content interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMsgCard", reflect.TypeOf((*MockService)(nil).UpdateMsgCard), msgId, content)
}

// UrgentMessage mocks base method.
func (m *MockService) UrgentMessage(unionId, messageId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UrgentMessage", unionId, messageId)
	ret0, _ := ret[0].(error)
	return ret0
}

// UrgentMessage indicates an expected call of UrgentMessage.
func (mr *MockServiceMockRecorder) UrgentMessage(unionId, messageId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UrgentMessage", reflect.TypeOf((*MockService)(nil).UrgentMessage), unionId, messageId)
}
