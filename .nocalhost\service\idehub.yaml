- name: cicd-idehub
  serviceType: deployment
  containers:
    - name: cicd-idehub
      dev:
        gitUrl: ""
        image: cr.ttyuyin.com/tt_oss/tt-golang-dev:latest
        shell: ""
        workDir: /home/<USER>
        storageClass: ""
        devContainerName: cicd-idehub
        resources:
          limits:
            memory: 4096Mi
            cpu: "3"
          requests:
            memory: 128Mi
            cpu: "1"
        persistentVolumeDirs: []
        command:
          run:
            - go run ./services/idehub/cmd/main.go --s=./services/idehub/etc
          debug:
            - dlv
            - --headless
            - --log
            - --listen :9043
            - --api-version 2
            - --accept-multiclient
            - debug
            - ./services/idehub/cmd/main.go
            - --
            - --s=./services/idehub/etc
        debug:
          remoteDebugPort: 9043
          language: go
        hotReload: false
        sync:
          type: send
          mode: pattern
          filePattern:
            - .
          ignoreFilePattern:
            - .git
            - .github
            - .vscode
            - node_modules
        env:
          - name: MY_IMAGE_VERSION
            value: V20240423144420-5f5ece907
          - name: TT_Sentinel_CD_AT
            value: "2024-04-23 14:47:26.551639"
          - name: www
            value: tt.com
          - name: tt_BizDevOps
            value: $(MY_APP_NAME)-tt.com
          - name: hello
            value: world
          - name: qqq
            value: zzz
        portForward: []
        sidecarImage: cr.ttyuyin.com/tt_oss/nocalhost-sidecar:syncthing
