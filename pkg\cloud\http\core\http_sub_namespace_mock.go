// Code generated by MockGen. DO NOT EDIT.
// Source: http_sub_namespace.go

// Package core is a generated GoMock package.
package core

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockSubNamespaceClient is a mock of SubNamespaceClient interface.
type MockSubNamespaceClient struct {
	ctrl     *gomock.Controller
	recorder *MockSubNamespaceClientMockRecorder
}

// MockSubNamespaceClientMockRecorder is the mock recorder for MockSubNamespaceClient.
type MockSubNamespaceClientMockRecorder struct {
	mock *MockSubNamespaceClient
}

// NewMockSubNamespaceClient creates a new mock instance.
func NewMockSubNamespaceClient(ctrl *gomock.Controller) *MockSubNamespaceClient {
	mock := &MockSubNamespaceClient{ctrl: ctrl}
	mock.recorder = &MockSubNamespaceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSubNamespaceClient) EXPECT() *MockSubNamespaceClientMockRecorder {
	return m.recorder
}

// CreateSubNamespace mocks base method.
func (m *MockSubNamespaceClient) CreateSubNamespace(ctx context.Context, in *CreateSubNamespaceRequest) (*CreateSubNamespaceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubNamespace", ctx, in)
	ret0, _ := ret[0].(*CreateSubNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubNamespace indicates an expected call of CreateSubNamespace.
func (mr *MockSubNamespaceClientMockRecorder) CreateSubNamespace(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubNamespace", reflect.TypeOf((*MockSubNamespaceClient)(nil).CreateSubNamespace), ctx, in)
}

// GetSubNamespace mocks base method.
func (m *MockSubNamespaceClient) GetSubNamespace(ctx context.Context, in *GetSubNamespaceRequest) (*GetSubNamespaceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubNamespace", ctx, in)
	ret0, _ := ret[0].(*GetSubNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubNamespace indicates an expected call of GetSubNamespace.
func (mr *MockSubNamespaceClientMockRecorder) GetSubNamespace(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubNamespace", reflect.TypeOf((*MockSubNamespaceClient)(nil).GetSubNamespace), ctx, in)
}
