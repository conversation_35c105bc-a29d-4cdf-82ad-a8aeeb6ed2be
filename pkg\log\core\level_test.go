package core

import (
	"github.com/stretchr/testify/require"
	"testing"
)

func TestLevel(t *testing.T) {
	lv := Level(0)

	require.Equal(t, "info", lv.String())
	require.Equal(t, "INFO", lv.CapitalString())

	lvByte, err := lv.MarshalText()
	require.NoError(t, err)
	require.ElementsMatch(t, []byte("info"), lvByte)
	require.NoError(t, lv.UnmarshalText(lvByte))
	require.Error(t, lv.UnmarshalText([]byte("invalid")))

	level, err := ParseLevel("info")
	require.NoError(t, err)
	require.Equal(t, InfoLevel, level)
}
