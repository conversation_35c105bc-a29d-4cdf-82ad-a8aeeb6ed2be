package constants

type FlowType string

const (
	RaiseTestApproval     FlowType = "RAISE_TEST"     //部署测试审批
	GrayUpgradeApproval   FlowType = "GRAY_UPGRADE"   //部署灰度审批
	UpgradeApproval       FlowType = "UPGRADE"        //部署生产审批
	GeneralOnlineApproval FlowType = "GENERAL_ONLINE" // 仅修改配置-正常上线审批流
	UrgentOnlineApproval  FlowType = "URGENT_ONLINE"  // 仅修改配置-紧急上线审批流
)

func (t FlowType) String() string {
	return string(t)
}

func (t FlowType) IsOnlineApproval() bool {
	return t == GeneralOnlineApproval || t == UrgentOnlineApproval
}

type FlowKind string

const (
	DefaultApproval FlowKind = "DEFAULT_APPROVAL" // 默认审批流
	BaseApproval    FlowKind = "BASE_APPROVAL"    // 基准提测审批流
)
