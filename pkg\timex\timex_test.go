package timex

import (
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/stretchr/testify/require"
)

func TestDefaultTimeFunc(t *testing.T) {
	now := time.Now()
	MockTimeFunc(now)

	require.Equal(t, now.String(), DefaultTimeFunc().String())
}

func TestFormat(t *testing.T) {
	now := time.Date(2024, time.January, 17, 9, 0, 0, 0, time.UTC)
	tcs := []struct {
		name   string
		target time.Time
		want   string
	}{
		{
			name:   "target is zero",
			target: time.Time{},
			want:   "",
		},
		{
			name:   "target is utc time",
			target: now,
			want:   "2024-01-17 17:00:00 星期三 CST",
		},
		{
			name:   "target is timestamppb.New(now).AsTime()",
			target: timestamppb.New(now).AsTime(),
			want:   "2024-01-17 17:00:00 星期三 CST",
		},
	}

	for _, tc := range tcs {
		t.Run(tc.name, func(t *testing.T) {
			actual := Format(tc.target)
			require.Equal(t, tc.want, actual)
		})
	}
}

func TestToYearMonth(t *testing.T) {
	utcTime := time.Date(2023, time.June, 27, 10, 30, 0, 0, time.UTC)

	str := ToYearMonth(utcTime)

	require.Equal(t, "2023-06", str)
}

func TestToPointer(t *testing.T) {
	tcs := []struct {
		name   string
		target time.Time
		want   func() *time.Time
	}{
		{
			name:   "target is zero",
			target: time.Time{},
			want:   func() *time.Time { return nil },
		},
		{
			name:   "target is not zero",
			target: time.Now(),
			want: func() *time.Time {
				now := DefaultTimeFunc()
				return &now
			},
		},
	}

	for _, tc := range tcs {
		t.Run(tc.name, func(t *testing.T) {
			MockTimeFunc(tc.target)
			actual := ToPointer(tc.target)
			want := tc.want()
			require.Equal(t, want, actual)
		})
	}
}
