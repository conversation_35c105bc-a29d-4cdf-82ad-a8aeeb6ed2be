package db

import (
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type Config struct {
	Username       string
	Password       string
	Host           string
	Port           int
	Dbname         string
	Maxlifetime    string `mapstructure:"max_life_time"`
	Idleconnection int    `mapstructure:"idle_connection"`
	Openconnection int    `mapstructure:"open_connection"`
	Parsetime      bool   `mapstructure:"parse_time"`

	gormConfig *gormConfig
}

type gormConfig = gorm.Config

type Option func(config *Config)

func Host(host string) Option {
	return func(config *Config) {
		config.Host = host
	}
}

func Port(port int) Option {
	return func(config *Config) {
		config.Port = port
	}
}

func Username(username string) Option {
	return func(config *Config) {
		config.Username = username
	}
}

func Password(passwd string) Option {
	return func(config *Config) {
		config.Password = passwd
	}
}

func Database(dbName string) Option {
	return func(config *Config) {
		config.Dbname = dbName
	}
}

func Logger(l logger.Interface) Option {
	return func(config *Config) {
		if config.gormConfig == nil {
			config.gormConfig = &gormConfig{}
		}
		config.gormConfig.Logger = l
	}
}

func NewConfig(opts ...Option) *Config {
	cfg := &Config{
		Username:       "rd_dev",
		Password:       "",
		Host:           "127.0.0.1",
		Port:           3306,
		Dbname:         "test",
		Maxlifetime:    "30m",
		Idleconnection: 30,
		Openconnection: 30,
		Parsetime:      true,
	}

	for _, opt := range opts {
		opt(cfg)
	}
	return cfg
}
