// Code generated by MockGen. DO NOT EDIT.
// Source: app.go

// Package service is a generated GoMock package.
package service

import (
	context "context"
	http "net/http"
	reflect "reflect"

	page "52tt.com/cicd/pkg/page"
	deploy "52tt.com/cicd/protocol/deploy"
	dao "52tt.com/cicd/services/app/internal/dao"
	model "52tt.com/cicd/services/app/internal/model"
	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockAppService is a mock of AppService interface.
type MockAppService struct {
	ctrl     *gomock.Controller
	recorder *MockAppServiceMockRecorder
}

// MockAppServiceMockRecorder is the mock recorder for MockAppService.
type MockAppServiceMockRecorder struct {
	mock *MockAppService
}

// NewMockAppService creates a new mock instance.
func NewMockAppService(ctrl *gomock.Controller) *MockAppService {
	mock := &MockAppService{ctrl: ctrl}
	mock.recorder = &MockAppServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAppService) EXPECT() *MockAppServiceMockRecorder {
	return m.recorder
}

// AppCanDel mocks base method.
func (m *MockAppService) AppCanDel(ctx *gin.Context, appIds []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AppCanDel", ctx, appIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// AppCanDel indicates an expected call of AppCanDel.
func (mr *MockAppServiceMockRecorder) AppCanDel(ctx, appIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AppCanDel", reflect.TypeOf((*MockAppService)(nil).AppCanDel), ctx, appIds)
}

// BatchUpdateApp mocks base method.
func (m *MockAppService) BatchUpdateApp(ctx context.Context, req model.BatchUpdateParam) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateApp", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateApp indicates an expected call of BatchUpdateApp.
func (mr *MockAppServiceMockRecorder) BatchUpdateApp(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateApp", reflect.TypeOf((*MockAppService)(nil).BatchUpdateApp), ctx, req)
}

// CancelAppPreference mocks base method.
func (m *MockAppService) CancelAppPreference(ctx context.Context, req model.AppPreference) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelAppPreference", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelAppPreference indicates an expected call of CancelAppPreference.
func (mr *MockAppServiceMockRecorder) CancelAppPreference(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelAppPreference", reflect.TypeOf((*MockAppService)(nil).CancelAppPreference), ctx, req)
}

// CheckApp mocks base method.
func (m *MockAppService) CheckApp(ctx context.Context, app model.AppBase) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckApp", ctx, app)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckApp indicates an expected call of CheckApp.
func (mr *MockAppServiceMockRecorder) CheckApp(ctx, app interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckApp", reflect.TypeOf((*MockAppService)(nil).CheckApp), ctx, app)
}

// CheckAppNameExisted mocks base method.
func (m *MockAppService) CheckAppNameExisted(ctx context.Context, projectId int64, appName string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAppNameExisted", ctx, projectId, appName)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAppNameExisted indicates an expected call of CheckAppNameExisted.
func (mr *MockAppServiceMockRecorder) CheckAppNameExisted(ctx, projectId, appName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAppNameExisted", reflect.TypeOf((*MockAppService)(nil).CheckAppNameExisted), ctx, projectId, appName)
}

// CheckAppOwner mocks base method.
func (m *MockAppService) CheckAppOwner(ctx context.Context, userId, appId int64) (*model.AppOwner, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAppOwner", ctx, userId, appId)
	ret0, _ := ret[0].(*model.AppOwner)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAppOwner indicates an expected call of CheckAppOwner.
func (mr *MockAppServiceMockRecorder) CheckAppOwner(ctx, userId, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAppOwner", reflect.TypeOf((*MockAppService)(nil).CheckAppOwner), ctx, userId, appId)
}

// CreateAppPreference mocks base method.
func (m *MockAppService) CreateAppPreference(ctx context.Context, req model.AppPreference) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppPreference", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppPreference indicates an expected call of CreateAppPreference.
func (mr *MockAppServiceMockRecorder) CreateAppPreference(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppPreference", reflect.TypeOf((*MockAppService)(nil).CreateAppPreference), ctx, req)
}

// DeleteApp mocks base method.
func (m *MockAppService) DeleteApp(ctx *gin.Context, appId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteApp", ctx, appId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteApp indicates an expected call of DeleteApp.
func (mr *MockAppServiceMockRecorder) DeleteApp(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteApp", reflect.TypeOf((*MockAppService)(nil).DeleteApp), ctx, appId)
}

// FindAppWithPrjs mocks base method.
func (m *MockAppService) FindAppWithPrjs(ctx context.Context, req model.FindAppsReq) ([]model.AppWithProject, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAppWithPrjs", ctx, req)
	ret0, _ := ret[0].([]model.AppWithProject)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAppWithPrjs indicates an expected call of FindAppWithPrjs.
func (mr *MockAppServiceMockRecorder) FindAppWithPrjs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAppWithPrjs", reflect.TypeOf((*MockAppService)(nil).FindAppWithPrjs), ctx, req)
}

// GetApp mocks base method.
func (m *MockAppService) GetApp(ctx context.Context, appId int64) (*model.AppResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApp", ctx, appId)
	ret0, _ := ret[0].(*model.AppResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApp indicates an expected call of GetApp.
func (mr *MockAppServiceMockRecorder) GetApp(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApp", reflect.TypeOf((*MockAppService)(nil).GetApp), ctx, appId)
}

// GetAppBasicInfo mocks base method.
func (m *MockAppService) GetAppBasicInfo(ctx context.Context, appId int64) (*model.AppDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppBasicInfo", ctx, appId)
	ret0, _ := ret[0].(*model.AppDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppBasicInfo indicates an expected call of GetAppBasicInfo.
func (mr *MockAppServiceMockRecorder) GetAppBasicInfo(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppBasicInfo", reflect.TypeOf((*MockAppService)(nil).GetAppBasicInfo), ctx, appId)
}

// GetAppBasicInfoList mocks base method.
func (m *MockAppService) GetAppBasicInfoList(ctx context.Context, query *model.AppQuery) ([]*model.AppDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppBasicInfoList", ctx, query)
	ret0, _ := ret[0].([]*model.AppDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppBasicInfoList indicates an expected call of GetAppBasicInfoList.
func (mr *MockAppServiceMockRecorder) GetAppBasicInfoList(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppBasicInfoList", reflect.TypeOf((*MockAppService)(nil).GetAppBasicInfoList), ctx, query)
}

// GetAppEventlinkConfig mocks base method.
func (m *MockAppService) GetAppEventlinkConfig(ctx context.Context, req *model.AppEventlinkReq) (*model.AppEventlinkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppEventlinkConfig", ctx, req)
	ret0, _ := ret[0].(*model.AppEventlinkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppEventlinkConfig indicates an expected call of GetAppEventlinkConfig.
func (mr *MockAppServiceMockRecorder) GetAppEventlinkConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppEventlinkConfig", reflect.TypeOf((*MockAppService)(nil).GetAppEventlinkConfig), ctx, req)
}

// GetAppListByIds mocks base method.
func (m *MockAppService) GetAppListByIds(ctx context.Context, ids []int64, projectId int64) ([]dao.App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppListByIds", ctx, ids, projectId)
	ret0, _ := ret[0].([]dao.App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppListByIds indicates an expected call of GetAppListByIds.
func (mr *MockAppServiceMockRecorder) GetAppListByIds(ctx, ids, projectId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppListByIds", reflect.TypeOf((*MockAppService)(nil).GetAppListByIds), ctx, ids, projectId)
}

// GetAppPrj mocks base method.
func (m *MockAppService) GetAppPrj(ctx context.Context, appId int64) (*model.AppBasic, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppPrj", ctx, appId)
	ret0, _ := ret[0].(*model.AppBasic)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppPrj indicates an expected call of GetAppPrj.
func (mr *MockAppServiceMockRecorder) GetAppPrj(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppPrj", reflect.TypeOf((*MockAppService)(nil).GetAppPrj), ctx, appId)
}

// GetBranchList mocks base method.
func (m *MockAppService) GetBranchList(ctx context.Context, appId int64, search, regex string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBranchList", ctx, appId, search, regex)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBranchList indicates an expected call of GetBranchList.
func (mr *MockAppServiceMockRecorder) GetBranchList(ctx, appId, search, regex interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBranchList", reflect.TypeOf((*MockAppService)(nil).GetBranchList), ctx, appId, search, regex)
}

// GetCmdbData mocks base method.
func (m *MockAppService) GetCmdbData(ctx context.Context, originalReq *http.Request, url string) (any, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCmdbData", ctx, originalReq, url)
	ret0, _ := ret[0].(any)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCmdbData indicates an expected call of GetCmdbData.
func (mr *MockAppServiceMockRecorder) GetCmdbData(ctx, originalReq, url interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCmdbData", reflect.TypeOf((*MockAppService)(nil).GetCmdbData), ctx, originalReq, url)
}

// GetDeployConfigBy mocks base method.
func (m *MockAppService) GetDeployConfigBy(ctx context.Context, para model.AppDeployConfigsPara) (*deploy.GetAppConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeployConfigBy", ctx, para)
	ret0, _ := ret[0].(*deploy.GetAppConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeployConfigBy indicates an expected call of GetDeployConfigBy.
func (mr *MockAppServiceMockRecorder) GetDeployConfigBy(ctx, para interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeployConfigBy", reflect.TypeOf((*MockAppService)(nil).GetDeployConfigBy), ctx, para)
}

// GetDeployPlanApps mocks base method.
func (m *MockAppService) GetDeployPlanApps(ctx context.Context, req model.ProjectsAppsReq) ([]model.AppBasic, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeployPlanApps", ctx, req)
	ret0, _ := ret[0].([]model.AppBasic)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeployPlanApps indicates an expected call of GetDeployPlanApps.
func (mr *MockAppServiceMockRecorder) GetDeployPlanApps(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeployPlanApps", reflect.TypeOf((*MockAppService)(nil).GetDeployPlanApps), ctx, req)
}

// GetModuleTree mocks base method.
func (m *MockAppService) GetModuleTree(ctx context.Context, nodeId string) (map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetModuleTree", ctx, nodeId)
	ret0, _ := ret[0].(map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetModuleTree indicates an expected call of GetModuleTree.
func (mr *MockAppServiceMockRecorder) GetModuleTree(ctx, nodeId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetModuleTree", reflect.TypeOf((*MockAppService)(nil).GetModuleTree), ctx, nodeId)
}

// ListApp mocks base method.
func (m *MockAppService) ListApp(ctx context.Context, pQuery *model.AppQuery) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListApp", ctx, pQuery)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListApp indicates an expected call of ListApp.
func (mr *MockAppServiceMockRecorder) ListApp(ctx, pQuery interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListApp", reflect.TypeOf((*MockAppService)(nil).ListApp), ctx, pQuery)
}

// NewApp mocks base method.
func (m *MockAppService) NewApp(ctx context.Context, app *model.App) (*model.CreateAppResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewApp", ctx, app)
	ret0, _ := ret[0].(*model.CreateAppResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewApp indicates an expected call of NewApp.
func (mr *MockAppServiceMockRecorder) NewApp(ctx, app interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewApp", reflect.TypeOf((*MockAppService)(nil).NewApp), ctx, app)
}

// PageApp mocks base method.
func (m *MockAppService) PageApp(ctx context.Context, pQuery *model.AppQuery) (page.Paginator, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PageApp", ctx, pQuery)
	ret0, _ := ret[0].(page.Paginator)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageApp indicates an expected call of PageApp.
func (mr *MockAppServiceMockRecorder) PageApp(ctx, pQuery interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageApp", reflect.TypeOf((*MockAppService)(nil).PageApp), ctx, pQuery)
}

// SearchAppsByUser mocks base method.
func (m *MockAppService) SearchAppsByUser(ctx context.Context, userID int64, search model.AppSearchReq) ([]model.AppSearchResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAppsByUser", ctx, userID, search)
	ret0, _ := ret[0].([]model.AppSearchResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAppsByUser indicates an expected call of SearchAppsByUser.
func (mr *MockAppServiceMockRecorder) SearchAppsByUser(ctx, userID, search interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAppsByUser", reflect.TypeOf((*MockAppService)(nil).SearchAppsByUser), ctx, userID, search)
}

// SetWebhookAndAddSysUserForAppRepo mocks base method.
func (m *MockAppService) SetWebhookAndAddSysUserForAppRepo(ctx context.Context, repoAddr string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWebhookAndAddSysUserForAppRepo", ctx, repoAddr)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWebhookAndAddSysUserForAppRepo indicates an expected call of SetWebhookAndAddSysUserForAppRepo.
func (mr *MockAppServiceMockRecorder) SetWebhookAndAddSysUserForAppRepo(ctx, repoAddr interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWebhookAndAddSysUserForAppRepo", reflect.TypeOf((*MockAppService)(nil).SetWebhookAndAddSysUserForAppRepo), ctx, repoAddr)
}

// SyncCMDBInfo mocks base method.
func (m *MockAppService) SyncCMDBInfo(ctx context.Context, appId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncCMDBInfo", ctx, appId)
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncCMDBInfo indicates an expected call of SyncCMDBInfo.
func (mr *MockAppServiceMockRecorder) SyncCMDBInfo(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncCMDBInfo", reflect.TypeOf((*MockAppService)(nil).SyncCMDBInfo), ctx, appId)
}

// UpdateApp mocks base method.
func (m *MockAppService) UpdateApp(ctx context.Context, appReq model.UpdateAppReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateApp", ctx, appReq)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateApp indicates an expected call of UpdateApp.
func (mr *MockAppServiceMockRecorder) UpdateApp(ctx, appReq interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApp", reflect.TypeOf((*MockAppService)(nil).UpdateApp), ctx, appReq)
}

// UpdateAppSenvStatus mocks base method.
func (m *MockAppService) UpdateAppSenvStatus(ctx context.Context, app model.AppSenvStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAppSenvStatus", ctx, app)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAppSenvStatus indicates an expected call of UpdateAppSenvStatus.
func (mr *MockAppServiceMockRecorder) UpdateAppSenvStatus(ctx, app interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppSenvStatus", reflect.TypeOf((*MockAppService)(nil).UpdateAppSenvStatus), ctx, app)
}
