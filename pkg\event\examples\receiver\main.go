package main

import (
	"context"
	"fmt"
	"log"

	"52tt.com/cicd/pkg/event"

	"52tt.com/cicd/pkg/kafka"
)

var (
	brokers = []string{"************:9092"}
	topics  = []string{"pipeline-dev"}
)

func main() {
	myReceiver, err := event.NewReceiver(kafka.NewConfig(kafka.Name("test-sender"), kafka.Brokers(brokers), kafka.ConsumerTopics(topics)))
	if err != nil {
		log.Fatalf("failed %v", err)
	}

	defer myReceiver.Close(context.Background())

	log.Printf("will listen consuming topic test-topic\n")
	myReceiver.StartReceiver(context.Background())
	if err != nil {
		log.Fatalf("failed to start receiver: %s", err)
	} else {
		log.Printf("receiver stopped\n")
	}
}

func receive(ctx context.Context, event event.Event) {
	fmt.Printf("%s", event)
}
