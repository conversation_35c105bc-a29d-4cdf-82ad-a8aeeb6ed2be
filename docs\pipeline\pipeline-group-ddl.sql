CREATE TABLE `pipeline_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '流水线组名称',
  `type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '流水线组类型',
  `trigger_type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发事件类型',
  `trigger_by_change` tinyint NOT NULL COMMENT '是否变更触发',
  `target_branch_type` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '目标分支类型',
  `target_branch_content` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '目标分支内容',
  `project_id` bigint NOT NULL COMMENT '关联项目id',
  `template_id` bigint NOT NULL COMMENT '流水线组关联模板id',
  `app_ids` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '流水线组关联应用id列表',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `language` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '语言',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`) USING BTREE,
  KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='流水线组信息表';

alter table pipeline.pipeline_group
    add column `is_auto_merge` tinyint not null default 0 comment '是否自动合并分支';

alter table pipeline.pipeline_group
    add column `is_one_running` tinyint not null default 0 comment '同时仅允许一条流水线记录运行中';
