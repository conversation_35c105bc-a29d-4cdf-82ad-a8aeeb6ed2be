package rest

import (
	"context"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/mcp/internal/service/run_ser"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mitchellh/mapstructure"
)

func (ctl *ttCloudMCPController) ToolsRunPipeline() mcp.Tool {
	return mcp.NewTool("run_pipeline",
		mcp.WithDescription("运行{项目组}里某{应用}下的{流水线}"),
		mcp.WithString("projectName", mcp.Required(), mcp.Description("项目组名称")),
		mcp.WithString("appName", mcp.Required(), mcp.Description("应用或服务名称")),
		mcp.WithString("pipelineName", mcp.Required(), mcp.Description("待运行的流水线名称")),
		mcp.WithString("branch", mcp.Required(), mcp.Description("运行选中的代码分支")),
		mcp.WithString("userEmail", mcp.Description("触发用户的邮箱，可选")),
	)
}

func (ctl *ttCloudMCPController) RunPipeline(ctx context.Context, request mcp.CallToolRequest) (res *mcp.CallToolResult, errOut error) {
	var req run_ser.RunPipelineReq
	err := mapstructure.Decode(request.Params.Arguments, &req)
	if err != nil {
		res = mcp.NewToolResultError("RunPipelineReq Decode Err")
		return
	}

	err = run_ser.RunAgg.RunPipeline(req)
	if err != nil {
		log.Errorf("ttCloudMCPController RunPipeline err: %v", err)
		res = mcp.NewToolResultError(err.Error())
		return
	}

	res = mcp.NewToolResultText("RunPipeline Success")

	return
}
