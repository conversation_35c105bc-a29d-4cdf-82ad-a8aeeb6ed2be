package config

import (
	"fmt"
	"strings"

	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/log"
	"github.com/fsnotify/fsnotify"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
)

type Configuration interface {
	GetAppConfig() *Application
	GetDbConfig() *db.Config
	GetLogConfig() *log.Config
	GetRegistry() *Registry
	GetRedisConfig() *redis.Options
}

// NewConfig Init config
func NewConfig(path string, value interface{}) error {
	viper.AddConfigPath(path)
	viper.SetConfigName("config")
	viper.SetConfigType("toml")
	viper.AutomaticEnv()
	replacer := strings.NewReplacer(".", "_")
	viper.SetEnvKeyReplacer(replacer)

	if err := viper.ReadInConfig(); err != nil {
		fmt.Printf("Couldn't load config: %s", err)
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			fmt.Printf("Config file are not found error: %s", err)
			return err
		}
		return err
	}

	if err := viper.Unmarshal(value); err != nil {
		fmt.Printf("Nonserialized config into struct error: %s", err)
		return err
	}

	viper.OnConfigChange(func(e fsnotify.Event) {
		fmt.Printf("config file changed: %s", e.Name)
		if err := viper.Unmarshal(value); err != nil {
			fmt.Printf("Nonserialized config into struct error: %s", err)
		}
	})
	viper.WatchConfig()
	return nil
}
