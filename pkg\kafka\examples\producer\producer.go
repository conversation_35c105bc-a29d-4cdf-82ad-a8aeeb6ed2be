package main

import (
	"encoding/json"

	"52tt.com/cicd/pkg/kafka"
	"52tt.com/cicd/pkg/log"
)

var (
	brokers = []string{"************:9092"}
	topic   = "pipeline-test"
)

type EventMsg struct {
	Detail string
	ID     uint64
}

type Service struct {
	producer *kafka.Producer

	isSync bool
}

func (s *Service) PushEvent() error {
	payload := &EventMsg{
		Detail: "payload-detail",
		ID:     1,
	}

	s.producer.Publish("test3", []byte("hello world async"))
	s.producer.PublishAny("test4", payload, func(value any) ([]byte, error) {
		return json.Marshal(payload)
	})

	return nil
}

func (s *Service) Stop() {
	s.producer.Close()
}

func NewService(cfg *kafka.Config) *Service {
	s := &Service{}

	p, err := kafka.NewProducer(cfg)
	if err != nil {
		log.Errorf("NewEventProducer fail: %v", err)
	}
	s.producer = p

	return s
}

func main() {
	cfg := kafka.NewConfig(kafka.Name("test-producer"), kafka.Brokers(brokers), kafka.ProducerTopic(topic))

	s := NewService(cfg)

	if err := s.PushEvent(); err != nil {
		log.Errorf("err %v\n", err)
	}

	// stop
	s.Stop()
}
