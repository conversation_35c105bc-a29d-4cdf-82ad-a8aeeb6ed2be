package server

import (
	"context"

	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/log"
	"google.golang.org/grpc"
)

func UnaryServerInterceptor(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp any, err error) {
	// 将md转为普通的ctx传递下去
	ri := cctx.GetRequestInfoFromIncomingCtx(ctx)
	newCtx := cctx.WithRequestInfo(ctx, ri)
	tkn := cctx.GetTokenFromIncomingCtx(ctx)
	if tkn != "" {
		newCtx = cctx.WithToken(newCtx, tkn)
	}
	mark := cctx.GetTrafficMarkFromCtx(ctx)
	if mark != "" {
		newCtx = cctx.WithTrafficMark(newCtx, mark)
	}

	log.InfoWithCtx(newCtx, "gRPC received Request: %+v - From: %+v", req, info.FullMethod)

	return handler(newCtx, req)
}
