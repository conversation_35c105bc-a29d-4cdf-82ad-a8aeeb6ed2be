package safego

import (
	"52tt.com/cicd/pkg/log"
	"runtime/debug"
)

var testGoImpl TestGo

type TestGo struct {
	ignoreGo bool
}

// IgnoreGo 在单测里面要执行这个，用来忽略 go
func IgnoreGo() {
	testGoImpl.ignoreGo = true
}

// RecoverGo 不忽略 go
func RecoverGo() {
	testGoImpl.ignoreGo = false
}

// 带panic处理的协程
func Recover(cleanups ...func()) {
	for _, cleanup := range cleanups {
		cleanup()
	}
	if r := recover(); r != nil {
		log.Errorf("goroutine panicked with error: %v, stack:%s\n", r, string(debug.Stack()))
	}
}

func Go(fn func()) {
	if testGoImpl.ignoreGo {
		// 如果忽略，啥事不干，直接 return， 这样的话，单测的时候，就可以执行到
		return
	}
	go RunSafe(fn)
}

func RunSafe(fn func()) {
	defer Recover()
	fn()
}
