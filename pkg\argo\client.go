package argo

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"golang.org/x/net/context/ctxhttp"
	"golang.org/x/oauth2"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"52tt.com/cicd/pkg/log"
)

// ErrArgoCDClientNotReady ArgoCD 客户端未初始化, 以防 Panic 原地爆炸
var ErrArgoCDClientNotReady = fmt.Errorf("argoCD client not ready")

type client struct {
	hc        *http.Client
	o2hc      *http.Client
	opt       *Options
	authToken string
}

type Options struct {
	*SessionRequest
	ServerAddr          string
	DefaultAppNamespace string
	DefaultAppProject   string
}

var _ ArgoClient = (*client)(nil)

func NewClient(opt *Options) ArgoClient {
	c, _ := newClient(opt)
	return c
}

func newClient(opt *Options) (*client, error) {
	if opt == nil {
		return nil, fmt.Errorf("argoCD client options is empty")
	}
	if opt.DefaultAppNamespace == "" {
		opt.DefaultAppNamespace = defaultAppNamespace
	}
	if opt.DefaultAppProject == "" {
		opt.DefaultAppProject = defaultProject
	}
	c := &client{
		hc: &http.Client{
			Transport: &http.Transport{
				Proxy:                 http.ProxyFromEnvironment,
				ForceAttemptHTTP2:     true,
				MaxIdleConns:          100,
				IdleConnTimeout:       90 * time.Second,
				TLSHandshakeTimeout:   10 * time.Second,
				ExpectContinueTimeout: 1 * time.Second,
				TLSClientConfig:       &tls.Config{InsecureSkipVerify: true /*同一个集群就不需要校验了, 除非有人黑入集群了*/},
			},
		},
		opt: opt,
	}
	_ = c.initOauth2Hc()
	return c, nil
}

func (c *client) initOauth2Hc() error {
	if c == nil {
		return ErrArgoCDClientNotReady
	}
	if err := c.refreshOauth2Hc(); err != nil {
		log.Errorf("argoCD client init oauth2 http client error: %v", err)
		return err
	}
	version, _ := c.getVersion(context.Background())
	log.Infof("argoCD version: %v", version)
	// 直接摆烂每个小时刷新一次 ArgoCD 的 oauth2 的 client 就好了, 不需要解析 JWT 的 Claims 去获取 ExpireTime 来指定时间刷新
	go func() {
		for range time.NewTicker(time.Hour).C {
			if err := c.refreshOauth2Hc(); err != nil {
				log.Errorf("argoCD client refresh oauth2 http client error: %v", err)
			}
		}
	}()
	return nil
}

func (c *client) refreshOauth2Hc() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	token, err := c.getSessionToken(ctx)
	if err != nil {
		return err
	}
	oauth2WithClientOption := context.WithValue(context.Background(), oauth2.HTTPClient, c.hc)
	c.o2hc = oauth2.NewClient(oauth2WithClientOption, oauth2.StaticTokenSource(&oauth2.Token{AccessToken: token, TokenType: "Bearer"}))
	return nil
}

// 获取 Token
func (c *client) getSessionToken(ctx context.Context) (string, error) {
	body, _ := json.Marshal(c.opt.SessionRequest)
	resp, err := ctxhttp.Post(ctx, c.hc, c.opt.ServerAddr+"/api/v1/session", "application/json", bytes.NewReader(body))
	if err != nil {
		return "", err
	}
	defer func() { _ = resp.Body.Close() }()
	var respData = new(sessionResponse)
	if err := json.NewDecoder(resp.Body).Decode(respData); err != nil {
		return "", err
	}
	return respData.Token, nil
}

// 获取版本
func (c *client) getVersion(ctx context.Context) (string, error) {
	resp, err := ctxhttp.Get(ctx, c.o2hc, c.opt.ServerAddr+"/api/version")
	if err != nil {
		return "", err
	}
	defer func() { _ = resp.Body.Close() }()
	infoData, _ := io.ReadAll(resp.Body)
	return string(infoData), nil
}

// 列出 argo application, alias of argocd app list
func (c *client) listApplication(ctx context.Context) (any, error) {
	resp, err := ctxhttp.Get(ctx, c.o2hc, c.opt.ServerAddr+"/api/v1/applications")
	if err != nil {
		return "", err
	}
	defer func() { _ = resp.Body.Close() }()
	infoData, _ := io.ReadAll(resp.Body)
	return string(infoData), nil
}

// DeleteApplication 删除 argo application, alias of argocd app delete
func (c *client) DeleteApplication(ctx context.Context, req *ApplicationDeleteRequest) (*ApplicationResponse, error) {
	if c == nil {
		return nil, ErrArgoCDClientNotReady
	}
	u, err := url.Parse(c.opt.ServerAddr + "/api/v1/applications/" + req.Name)
	if err != nil {
		return nil, err
	}
	q := u.Query()
	q.Set("cascade", fmt.Sprintf("%v", req.Cascade))
	q.Set("propagationPolicy", req.PropagationPolicy)
	appNamespace := req.AppNamespace
	if appNamespace == "" {
		appNamespace = c.opt.DefaultAppNamespace
	}
	q.Set("appNamespace", appNamespace)
	appProject := req.Project
	if appProject == "" {
		appProject = c.opt.DefaultAppProject
	}
	q.Set("project", appProject)
	u.RawQuery = q.Encode()
	httpRequest, _ := http.NewRequestWithContext(ctx, http.MethodDelete, u.String(), nil)
	resp, err := ctxhttp.Do(ctx, c.o2hc, httpRequest)
	if err != nil {
		return nil, err
	}
	defer func() { _ = resp.Body.Close() }()

	// 如果正常删除就直接返回结果
	if resp.StatusCode == http.StatusOK {
		_, _ = io.Copy(io.Discard, resp.Body)
		return &ApplicationResponse{}, nil
	}

	// 不正常就解析一下错误
	respData, _ := io.ReadAll(resp.Body)
	errWithCode := &struct {
		Code codes.Code `json:"code"`
	}{}
	_ = json.Unmarshal(respData, errWithCode)
	return nil, status.Error(errWithCode.Code, string(respData))
}
