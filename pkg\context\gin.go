package context

import (
	"context"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

const (
	requestInfoGinKey = "requestInfoGinKey"
)

func UseridFromGinHeader(ctx *gin.Context) string {
	return ctx.GetHeader(UseridHeaderKey)
}

func UsernameFromGinHeader(ctx *gin.Context) string {
	return ctx.GetHeader(UsernameHeaderKey)
}

func ChineseNameFromGinHeader(ctx *gin.Context) string {
	return ctx.GetHeader(ChineseNameHeaderKey)
}

func EmployeeNoFromGinHeader(ctx *gin.Context) string {
	return ctx.GetHeader(EmployeeNoHeaderKey)
}

func RolesFromGinHeader(ctx *gin.Context) string {
	return ctx.GetHeader(RolesHeaderKey)
}

func ProjectIdFromGinHeader(ctx *gin.Context) string {
	return ctx.GetHeader(ProjectIdHeaderKey)
}

func TokenFromGinHeader(ctx *gin.Context) string {
	return ctx.GetHeader(AuthorizationHeaderKey)
}

func TrafficMarkFromGinHeader(ctx *gin.Context) string {
	return ctx.GetHeader(trafficMarkKey)
}

func RequestIDFromGinHeader(ctx *gin.Context) string {
	return ctx.GetHeader(RequestIDHeaderKey)
}

func OpenapiTokenFromGinHeader(ctx *gin.Context) string {
	return ctx.GetHeader(OpenapiTokenHeaderKey)
}

func RequestInfoFromGinHeader(ctx *gin.Context) *RequestInfo {
	var (
		uid int64
		pid int64
	)

	uid, _ = strconv.ParseInt(UseridFromGinHeader(ctx), 10, 64)
	pid, _ = strconv.ParseInt(ProjectIdFromGinHeader(ctx), 10, 64)

	ri := &RequestInfo{
		UserInfo: UserInfo{
			UserID:      uid,
			Username:    UsernameFromGinHeader(ctx),
			ChineseName: ChineseNameFromGinHeader(ctx),
			EmployeeNo:  EmployeeNoFromGinHeader(ctx),
			Roles:       strings.Split(RolesFromGinHeader(ctx), ","),
			ProjectID:   pid,
		},
		RequestID: RequestIDFromGinHeader(ctx),
	}
	return ri
}

func RequestInfoFromGinCtx(ctx *gin.Context) (*RequestInfo, bool) {
	ri, ok := ctx.Get(requestInfoGinKey)
	if ok {
		return ri.(*RequestInfo), true
	}

	return &emptyRequestInfo, false
}

// SetRequestInfo set request info to gin context
// 先叫这个名字吧，目测只有bff会用到一次
func SetRequestInfo(ctx *gin.Context, ri *RequestInfo) {
	ctx.Set(requestInfoGinKey, ri)
	// 兼容之前的，后续再移除
	ctx.Set(useridKey, strconv.FormatInt(ri.UserID, 10))
	ctx.Set(usernameKey, ri.Username)
	ctx.Set(chineseNameKey, ri.ChineseName)
	ctx.Set(employeeNoKey, ri.EmployeeNo)
	ctx.Set(rolesKey, ri.RolesString())
	ctx.Set(projectIdKey, strconv.FormatInt(ri.ProjectID, 10))
	ctx.Set(requestIDKey, ri.RequestID)
}

// SetHeaderRequestInfo set request info to http request header
// 抽出一个函数，在写单测的时候有部分需要在头部设置信息的场景，可以使用这个函数
func SetHeaderRequestInfo(ctx *gin.Context, ri *RequestInfo) {
	ctx.Request.Header.Set(UseridHeaderKey, strconv.FormatInt(ri.UserID, 10))
	ctx.Request.Header.Set(UsernameHeaderKey, ri.Username)
	ctx.Request.Header.Set(ChineseNameHeaderKey, ri.ChineseName)
	ctx.Request.Header.Set(EmployeeNoHeaderKey, ri.EmployeeNo)
	ctx.Request.Header.Set(RolesHeaderKey, strings.Join(ri.Roles, ","))
	ctx.Request.Header.Set(ProjectIdHeaderKey, strconv.FormatInt(ri.ProjectID, 10))
	ctx.Request.Header.Set(RequestIDHeaderKey, ri.RequestID)
}

// ForwardHeaderRequestInfo forward request info from gin context to http request header
func ForwardHeaderRequestInfo(ctx *gin.Context) {
	ri, ok := RequestInfoFromGinCtx(ctx)
	if ok {
		SetHeaderRequestInfo(ctx, ri)
	}
}

func NewCtxFromGin(ctx *gin.Context) context.Context {
	ri, ok := RequestInfoFromGinCtx(ctx)
	if !ok {
		ri = RequestInfoFromGinHeader(ctx)
	}

	newCtx := WithRequestInfo(context.Background(), ri)
	return WithToken(newCtx, TokenFromGinHeader(ctx))
}

func SetTrafficMark(ctx *gin.Context) {
	mark := TrafficMarkFromGinHeader(ctx)
	if mark != "" {
		ctx.Set(trafficMarkKey, mark)
	}
}
