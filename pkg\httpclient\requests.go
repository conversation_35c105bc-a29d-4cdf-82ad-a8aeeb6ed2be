package httpclient

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	reflect "reflect"
	"time"
)

type RequestOption interface {
	applyToRequest(*Request) error
}

type RequestWithJson struct {
	Data any
}

func (r RequestWithJson) applyToRequest(req *Request) error {
	data, err := json.Marshal(r.Data)
	if err != nil {
		return err
	}
	buf := bytes.NewBuffer(data)
	req.BodyData = buf
	req.Headers["Content-Type"] = "application/json"
	req.Json = data
	return nil
}

type RequestWithMapParams map[string]string

func (r RequestWithMapParams) applyToRequest(req *Request) error {
	req.Params = r
	return nil
}

type RequestWithStrcutParams struct {
	Data any
}

// TODO: Reqeust and Response performance optimization

func (r RequestWithStrcutParams) applyToRequest(req *Request) error {
	structValue := reflect.ValueOf(r.Data)
	structType := reflect.TypeOf(r.Data)
	for i := 0; i < structValue.NumField(); i++ {
		field := structValue.Field(i)
		fieldName := structType.Field(i).Name
		fieldTag := structType.Field(i).Tag
		fieldValue := fmt.Sprintf("%v", field.Interface())
		if !field.CanInterface() {
			return fmt.Errorf("struct field %s can't interface", fieldName)
		}
		var paramName string
		if fieldTag.Get("query") != "" {
			paramName = fieldTag.Get("query")
		} else {
			paramName = fieldName
		}
		req.Params[paramName] = fieldValue
	}
	return nil
}

type RequestWithInsecure bool

func (r RequestWithInsecure) applyToRequest(req *Request) error {
	skip := bool(r)
	req.InsecureSkipVerify = &skip
	return nil
}

// Request as a wrapper of http.Request, and provide some useful methods.
// Request has some option as the same to Session. Request's option will override Session's option, eg InsecureSkipVerify.
type Request struct {
	*http.Request
	Headers  map[string]string
	Params   map[string]string // URL parameters to append to URL
	BodyData *bytes.Buffer
	Json     []byte
	// Files   map[string]http.File
	// Auth map[string]string
	// Cookies map[string]string
	// Hooks   map[string]func()
	InsecureSkipVerify *bool
}

func newDefaultRequest() *Request {
	skip := false
	return &Request{
		Headers:            make(map[string]string),
		Params:             make(map[string]string),
		BodyData:           new(bytes.Buffer),
		Json:               nil,
		InsecureSkipVerify: &skip,
	}
}

func (r *Request) IsSkipVerify() bool {
	if r.InsecureSkipVerify != nil && *r.InsecureSkipVerify {
		return true
	}
	return false
}

func NewRequest(url string, opt ...RequestOption) (*Request, error) {
	defaultRequest := newDefaultRequest()
	for _, o := range opt {
		err := o.applyToRequest(defaultRequest)
		if err != nil {
			return nil, err
		}
	}

	// create http.Request from Request
	req, err := http.NewRequest("GET", url, defaultRequest.BodyData)
	if err != nil {
		return nil, err
	}
	// set headers
	for key, value := range defaultRequest.Headers {
		req.Header.Set(key, value)
	}
	// set url parameters
	if len(defaultRequest.Params) > 0 {
		q := req.URL.Query()
		for key, value := range defaultRequest.Params {
			q.Add(key, value)
		}
		req.URL.RawQuery = q.Encode()
	}
	defaultRequest.Request = req
	return defaultRequest, nil
}

type Response struct {
	*http.Response
	Body []byte
}

func (r *Response) IsJson() bool {
	return r.Response.Header.Get("Content-Type") == "application/json"
}

func (r *Response) Content() ([]byte, error) {
	return r.Body, nil
}

func (r *Response) String() string {
	return string(r.Body)
}

func (r *Response) JsonToStruct(target any) error {
	return json.Unmarshal(r.Body, target)
}

func (r *Response) JsonToMap(target *map[string]any) error {
	err := json.Unmarshal(r.Body, &target)
	if err != nil {
		return err
	}
	return nil
}

// Session will keep cookies, configuration between http requests.
type Session interface {
	Get(ctx context.Context, request *Request) (*Response, error)
	Post(ctx context.Context, request *Request) (*Response, error)
	Put(ctx context.Context, request *Request) (*Response, error)
	Delete(ctx context.Context, request *Request) (*Response, error)

	// SetHeaders set headers for session level. It won't affect the headers of request level.
	// If key is already exists in request's headers, it will be ignore.
	SetHeaders(headers map[string]string)
}

type SessionOption struct {
	Timeout            time.Duration
	InsecureSkipVerify *bool
	RetryTimes         int
	BackOffTime        time.Duration
}

// session use multiple http.Client to support session level behavior.
type session struct {
	headers            map[string]string // for session level headers management
	client             *http.Client
	insecureClient     *http.Client
	request            *Request
	response           Response
	insecureSkipVerify bool
	timeout            time.Duration
	retryTimes         int
	backOff            time.Duration
}

func NewSession(opt *SessionOption) Session {
	session := &session{
		client: &http.Client{
			Timeout: opt.Timeout,
		},
		insecureClient: &http.Client{
			Timeout: opt.Timeout,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
		},
		timeout:    opt.Timeout,
		backOff:    opt.BackOffTime,
		retryTimes: opt.RetryTimes,
	}
	if opt.InsecureSkipVerify != nil && *opt.InsecureSkipVerify {
		session.insecureSkipVerify = true
	}
	return session
}

// getClient should consider session and reqeust options to return a suitable http.Client
func (s *session) getClient() *http.Client {
	if s.request.IsSkipVerify() {
		return s.insecureClient
	}

	if s.insecureSkipVerify {
		return s.insecureClient
	}
	return s.client
}

func (s *session) send(ctx context.Context, req *Request) (Response, error) {
	// merge last request and current request

	// create http.Request from Request
	newHttpRequest := req.Request.Clone(ctx)
	req.Request = newHttpRequest
	s.request = req

	// merge headers, cookies
	for key, value := range s.headers {
		if v := newHttpRequest.Header.Get(key); v == "" {
			newHttpRequest.Header.Set(key, value)
		}
	}

	// send http request
	retries := 3
	if s.retryTimes > 0 {
		retries = s.retryTimes
	}
	var (
		resp *http.Response
		err  error
		r    Response
		body []byte
	)
	for retries >= 0 {
		resp, err = s.getClient().Do(newHttpRequest)
		if err != nil || (resp != nil && resp.StatusCode >= 500) {
			retries -= 1
		} else {
			break
		}
		time.Sleep(s.backOff)
	}
	r = Response{Response: resp}
	if err != nil {
		return r, err
	}
	if resp != nil {
		defer resp.Body.Close()

		// TODO: may be it's not a good idea to read all body here.
		// Consider to use singleton pattern to read body at Response's method.
		body, err = io.ReadAll(resp.Body)
		if err != nil {
			return r, err
		}
		r.Body = body
	}
	s.response = r
	return r, nil
}

func (s *session) SetHeaders(headers map[string]string) {
	s.headers = headers
}

func (s *session) Get(ctx context.Context, request *Request) (*Response, error) {
	request.Request.Method = "GET"
	resp, err := s.send(ctx, request)
	return &resp, err
}

func (s *session) Post(ctx context.Context, request *Request) (*Response, error) {
	request.Request.Method = "POST"
	resp, err := s.send(ctx, request)
	return &resp, err
}

func (s *session) Put(ctx context.Context, request *Request) (*Response, error) {
	request.Request.Method = "PUT"
	resp, err := s.send(ctx, request)
	return &resp, err
}

func (s *session) Delete(ctx context.Context, request *Request) (*Response, error) {
	request.Request.Method = "DELETE"
	resp, err := s.send(ctx, request)
	return &resp, err
}

func (s *session) Any(ctx context.Context, request *Request) (*Response, error) {
	resp, err := s.send(ctx, request)
	return &resp, err
}
