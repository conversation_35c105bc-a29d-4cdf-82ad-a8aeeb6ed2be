package sentinel

import (
	"math/rand"
	"time"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/migrate/internal/dao"
	"52tt.com/cicd/services/migrate/pkg/cim/cicd_ser/models"
	"golang.org/x/sync/errgroup"
)

type pplRunnerEn struct {
}

var TestInfoArr = [11]TestInfo{
	{AppId: 1670, AppName: "sentinel-test-001", TargetBranch: "release/tt_rel_prod", PipelineId: 53614},
	{AppId: 1671, AppName: "sentinel-test-002", TargetBranch: "release/tt_rel_prod", PipelineId: 53615},
	{AppId: 1672, AppName: "sentinel-test-003", TargetBranch: "release/tt_rel_prod", PipelineId: 53616},
	{AppId: 1674, AppName: "sentinel-test-004", TargetBranch: "prod", PipelineId: 53617},
	{AppId: 1677, AppName: "sentinel-test-006", TargetBranch: "release-v13", PipelineId: 53619},
	{AppId: 1678, AppName: "sentinel-test-007", TargetBranch: "release-v13", PipelineId: 53620},
	{AppId: 1679, AppName: "sentinel-test-008", TargetBranch: "release-v13", PipelineId: 53621},
	{AppId: 1680, AppName: "sentinel-test-009", TargetBranch: "master", PipelineId: 53622},
	{AppId: 1681, AppName: "sentinel-test-010", TargetBranch: "master", PipelineId: 53623},
	{AppId: 1682, AppName: "sentinel-test-011", TargetBranch: "master", PipelineId: 53624},
	{AppId: 1683, AppName: "sentinel-test-012", TargetBranch: "master", PipelineId: 53625},
}

var backdoor = time.Date(1988, 12, 22, 19, 30, 0, 0, time.Local)

func (en *pplRunnerEn) BatchRun(cnt int) (err error) {
	runParmsCh := make(chan TestInfo, 10)

	go func() {
		for i := 0; i < cnt; i++ {
			randIdx := rand.Intn(11)
			runParmsCh <- TestInfoArr[randIdx]
		}
		close(runParmsCh)
	}()

	eg := &errgroup.Group{}
	for i := 0; i < 5; i++ {
		eg.Go(func() (errIn error) {
			for item := range runParmsCh {
				errf := cicdSer.RunPpl(models.RunPplReq{
					PplID:        item.PipelineId,
					TargetBranch: item.TargetBranch,
				})
				if errf != nil {
					log.Errorf("运行流水线失败：%v", errf)
					continue
				}
				println("-------->随机运行流水线：", item.AppName)
				time.Sleep(time.Second * 10)
			}
			return
		})
	}

	err = eg.Wait()

	return
}

func (en *pplRunnerEn) BatchCancel() (err error) {
	for _, item := range TestInfoArr {
		en.cancelPplAllRuns(item.PipelineId)
	}

	return
}

func (en *pplRunnerEn) cancelPplAllRuns(pplId int) (err error) {
	rst, err := cicdSer.PplRunList(models.PplRunReq{
		CmnReq:     models.CmnReq{Size: 99},
		PipelineId: pplId,
	})
	if err != nil {
		return
	}

	for _, item := range rst.List {
		log.Infof("流水线运行信息：%d,%d,%s,%s ", item.ID, item.BuildNumber, item.Status, item.TriggerByChineseName)
		errIn := cicdSer.CancelPplRun(item.ID)
		if errIn != nil {
			log.Errorf("取消流水线失败：%v", errIn)
		}
	}

	return
}

func (en *pplRunnerEn) list(pplId int) (data any, err error) {
	data, _, err = dao.RepoFac.PipelineRunRepo.Find(dao.ParmsPplr{PipelineId: pplId})
	return
}

func (en *pplRunnerEn) watchStats(pplRunId int) (vm PipeRunVm, e error) {
	vm, e = WatchStats(func() (data PipeRunVm, finish bool, err error) {
		taskRun, has, errIn := dao.RepoFac.PipelineRunRepo.GetById(pplRunId)
		if errIn != nil {
			err = errIn
			return
		}
		if !has {
			data.Status = "taskRunId Not Fund"
			finish = true
			return
		}
		switch taskRun.Status {
		case constants.SUCCESSFUL, constants.FAILED:
			data.Status = string(taskRun.Status)
			finish = true
			return
		}
		return
	})

	if e == TimeOutErr {
		vm.Status = e.Error()
		e = nil
	}

	return
}

func (en *pplRunnerEn) svip(pplRunId int) (data any, err error) {
	obj, has, err := dao.RepoFac.PipelineRunRepo.GetById(pplRunId)
	if err != nil {
		return
	}
	if !has {
		data = "PipelineRunId Not Fund"
		return
	}

	obj.TektonAt = backdoor

	_, err = dao.RepoFac.PipelineRunRepo.Update(obj, "TektonAt")

	return
}

func (en *pplRunnerEn) clearHarborCache() (data any, err error) {

	return
}
