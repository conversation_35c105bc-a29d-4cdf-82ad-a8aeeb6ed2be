//go:generate mockgen -destination=app_mock.go -package=service -source=app.go
package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"52tt.com/cicd/pkg/cmdb"
	"52tt.com/cicd/pkg/constants"
	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/gitlab"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/page"
	"52tt.com/cicd/pkg/tools"
	"52tt.com/cicd/pkg/tools/encryption"
	"52tt.com/cicd/pkg/tools/set"
	"52tt.com/cicd/pkg/tools/vec"
	pbdep "52tt.com/cicd/protocol/deploy"
	pbiam "52tt.com/cicd/protocol/iam"
	pbpipeline "52tt.com/cicd/protocol/pipeline"
	"52tt.com/cicd/services/app/internal/conf"
	"52tt.com/cicd/services/app/internal/dao"
	"52tt.com/cicd/services/app/internal/model"
	appErr "52tt.com/cicd/services/app/pkg/error"
	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	"gorm.io/gorm"
)

const (
	appAvailable   = 1
	appUnAvailable = 0
)

type AppService interface {
	NewApp(ctx context.Context, app *model.App) (*model.CreateAppResp, error)
	PageApp(ctx context.Context, pQuery *model.AppQuery) (page.Paginator, error)
	ListApp(ctx context.Context, pQuery *model.AppQuery) ([]string, error)
	GetModuleTree(ctx context.Context, nodeId string) (map[string]interface{}, error)
	CheckAppNameExisted(ctx context.Context, projectId int64, appName string) (bool, error)
	CheckApp(ctx context.Context, app model.AppBase) error
	GetApp(ctx context.Context, appId int64) (*model.AppResp, error)
	GetAppPrj(ctx context.Context, appId int64) (*model.AppBasic, error)
	GetBranchList(ctx context.Context, appId int64, search string, regex string) ([]string, error)
	UpdateApp(ctx context.Context, appReq model.UpdateAppReq) error
	GetAppBasicInfo(ctx context.Context, appId int64) (*model.AppDetail, error)
	GetAppBasicInfoList(ctx context.Context, query *model.AppQuery) ([]*model.AppDetail, error)
	GetAppListByIds(ctx context.Context, ids []int64, projectId int64) ([]dao.App, error)
	CheckAppOwner(ctx context.Context, userId int64, appId int64) (*model.AppOwner, error)
	SetWebhookAndAddSysUserForAppRepo(ctx context.Context, repoAddr string) error
	GetDeployConfigBy(ctx context.Context, para model.AppDeployConfigsPara) (*pbdep.GetAppConfigResp, error)
	CreateAppPreference(ctx context.Context, req model.AppPreference) error
	CancelAppPreference(ctx context.Context, req model.AppPreference) error
	BatchUpdateApp(ctx context.Context, req model.BatchUpdateParam) error
	SearchAppsByUser(ctx context.Context, userID int64, search model.AppSearchReq) ([]model.AppSearchResult, error)
	UpdateAppSenvStatus(ctx context.Context, app model.AppSenvStatus) error
	GetDeployPlanApps(ctx context.Context, req model.ProjectsAppsReq) ([]model.AppBasic, error)
	GetCmdbData(ctx context.Context, originalReq *http.Request, url string) (any, error)
	GetAppEventlinkConfig(ctx context.Context, req *model.AppEventlinkReq) (*model.AppEventlinkResp, error)
	FindAppWithPrjs(ctx context.Context, req model.FindAppsReq) (objs []model.AppWithProject, err error)
	DeleteApp(ctx *gin.Context, appId int64) (err error)
	AppCanDel(ctx *gin.Context, appIds []int64) (err error)
	SyncCMDBInfo(ctx context.Context, appId int64) error
}

type AppSvc struct {
	appRepo           dao.AppRepository
	projectRepo       dao.ProjectRepository
	cfgMngRepo        dao.ConfigManagerRepository
	userClient        pbiam.UserServiceClient
	pbPipeline        pbpipeline.PipelineServiceClient
	cmdbClient        cmdb.Service
	configClient      pbdep.DeployConfigServiceClient
	projectUserClient pbiam.ProjectUserServiceClient
	deployClient      pbdep.DeployServiceClient
}

func NewAppService(userClient pbiam.UserServiceClient,
	appRepo dao.AppRepository,
	projectRepo dao.ProjectRepository,
	cfgMngRepo dao.ConfigManagerRepository,
	cmdbClient cmdb.Service,
	pbPipeline pbpipeline.PipelineServiceClient,
	configClient pbdep.DeployConfigServiceClient,
	projectUserClient pbiam.ProjectUserServiceClient,
	deployClient pbdep.DeployServiceClient) *AppSvc {
	return &AppSvc{
		userClient:        userClient,
		appRepo:           appRepo,
		projectRepo:       projectRepo,
		pbPipeline:        pbPipeline,
		cmdbClient:        cmdbClient,
		configClient:      configClient,
		projectUserClient: projectUserClient,
		deployClient:      deployClient,
		cfgMngRepo:        cfgMngRepo,
	}
}

func (appSvc *AppSvc) GetAppBasicInfoList(ctx context.Context, query *model.AppQuery) ([]*model.AppDetail, error) {
	appList, err := appSvc.appRepo.FindAllAppBasicInfo(ctx, query)
	if err != nil {
		log.ErrorWithCtx(ctx, "查询应用基本信息列表发生错误： %v", err)
		return nil, err
	}
	var appDetailList []*model.AppDetail
	for _, app := range appList {
		appDetail := &model.AppDetail{
			ID:          app.ID,
			ProjectID:   app.ProjectID,
			Name:        app.Name,
			LangName:    app.LangName,
			LangVersion: app.LangVersion,
			RepoAddr:    app.RepoAddr,
			BuildPath:   app.BuildPath,
		}
		appDetailList = append(appDetailList, appDetail)
	}
	return appDetailList, nil
}

func (appSvc *AppSvc) GetBranchList(ctx context.Context, appId int64, search string, regex string) ([]string, error) {
	app, err := appSvc.appRepo.FindApp(ctx, appId)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据应用ID[%d]查询应用出错:%v", appId, err)
		return nil, fmt.Errorf("根据应用ID[%d]查询应用出错", appId)
	}
	if app == nil || app.RepoAddr == "" {
		log.ErrorWithCtx(ctx, "应用[%d]不存在或者应用的代码库有误", appId)
		return nil, nil
	}
	urlRaw, err := url.Parse(app.RepoAddr)
	if err != nil {
		return nil, err
	}
	host := fmt.Sprintf("%s://%s", urlRaw.Scheme, urlRaw.Host)
	repository, err := appSvc.appRepo.FindRepoByUrl(ctx, host)
	if err != nil {
		return nil, err
	}
	gitClient := gitlab.NewClient(repository.Url, repository.AccessToken)
	branchList, err := gitClient.GetBranches(app.RepoAddr, search, regex)
	if err != nil {
		log.ErrorWithCtx(ctx, "查询应用ID[%d]代码库分支列表出错:%v", appId, err)
		return nil, nil
	}
	return branchList, nil
}

func (appSvc *AppSvc) NewApp(ctx context.Context, app *model.App) (*model.CreateAppResp, error) {
	cmdbApp := cmdb.App{
		Name:                 app.Name,
		BusinessLabelL1ObjId: app.FirstBizModuleId,
		BusinessLabelL2ObjId: app.SecondBizModuleId,
		BusinessLabelL3ObjId: app.ThirdBizModuleId,
		BusinessLabelL4ObjId: app.FourthBizModuleId,
	}
	alias, err := appSvc.cmdbClient.GetAlias(ctx, &cmdbApp)
	if err != nil {
		return nil, err
	}
	app.Code = alias

	//获取userid应用负责人信息列表
	userList, err := appSvc.getProjectOwners(ctx, app.ProjectID, app.Owners)
	if err != nil {
		return nil, err
	}
	userNameList := tools.MapTo(userList, func(user *pbiam.User) string {
		return user.Username
	})
	createApp := cmdb.App{
		Name:                 app.Name,
		Level:                app.Level,
		AppClass:             app.AppClass,
		IsBundled:            app.IsBundled,
		Language:             app.LangName,
		BusinessLabelL1ObjId: app.FirstBizModuleId,
		BusinessLabelL2ObjId: app.SecondBizModuleId,
		BusinessLabelL3ObjId: app.ThirdBizModuleId,
		BusinessLabelL4ObjId: app.FourthBizModuleId,
		DevelopUserList:      userNameList,
		Category:             "normal",
		Description:          app.Description,
		AppCreateTime:        int(time.Now().Unix()),
	}
	cmdbId, err := appSvc.cmdbClient.CreateApp(ctx, &createApp)
	if err != nil {
		return nil, err
	}
	app.CmdbId = cmdbId

	appData := appSvc.genAppData(app)
	if err := db.Transaction(func(tx *gorm.DB) error {
		txCtx := db.CtxWithTX(ctx, tx)
		if err := appSvc.appRepo.Insert(txCtx, &appData); err != nil {
			return err
		}
		appOwners := tools.MapTo(userList, func(user *pbiam.User) dao.AppUser {
			return dao.AppUser{
				AppId:  appData.ID,
				UserId: user.Id,
			}
		})
		if err := appSvc.appRepo.InsertAppOwner(txCtx, appOwners); err != nil {
			return err
		}
		return nil
	}); err != nil {
		errInfo := fmt.Sprintf("新增应用信息错误: %v", err)
		log.Errorf(errInfo)
		return nil, fmt.Errorf(errInfo)
	}
	return &model.CreateAppResp{
		ID: appData.ID,
	}, nil
}

func (appSvc *AppSvc) SetWebhookAndAddSysUserForAppRepo(ctx context.Context, repoAddr string) error {
	gitlabConfig := conf.AppConfig.Gitlab
	accessToken, err := encryption.Decrypt(gitlabConfig.AdminToken)
	if err != nil {
		log.ErrorWithCtx(ctx, "解密gitlab管理员token出错:%v", err)
		return err
	}

	gitClient := gitlab.NewClient(gitlabConfig.Url, accessToken)

	project, err := gitClient.GetProject(repoAddr)
	if err != nil {
		return err
	}
	err = gitClient.AddWebhook(project.ID, gitlabConfig.PipelineCallback)
	if err != nil {
		log.ErrorWithCtx(ctx, "代码库添加webhook出错:%v", err)
		return err
	}
	err = gitClient.AddSysUser(project.ID, gitlabConfig.UserId)
	if err != nil {
		log.ErrorWithCtx(ctx, "代码库添加用户sysUser出错:%v", err)
		return err
	}
	return nil
}

func (appSvc *AppSvc) genAppData(app *model.App) dao.App {
	path := app.BuildPath
	if path == "" {
		//默认build path路径
		path = "./"
	}
	appData := dao.App{
		Name:              app.Name,
		Code:              app.Code,
		BuildPath:         path,
		LangName:          app.LangName,
		LangVersion:       app.LangVersion,
		RepoAddr:          app.RepoAddr,
		Status:            appAvailable,
		CmdbId:            app.CmdbId,
		ProjectID:         app.ProjectID,
		Description:       app.Description,
		NeedExternalRoute: app.NeedExternalRoute,
		EventlinkType:     strings.Join(app.EventlinkType, ","),
	}
	return appData
}

func (appSvc *AppSvc) PageApp(ctx context.Context, pQuery *model.AppQuery) (page.Paginator, error) {
	var total int64
	// 查询用户收藏的应用id，排在前面
	userInfo := cctx.GetUserinfo(ctx)
	preferences, err := appSvc.appRepo.FindUserAppPreferences(ctx, pQuery.ProjectId, userInfo.UserID)
	if err != nil {
		return nil, err
	}
	var appPreferenceIds []string
	if len(preferences) > 0 {
		appPreferenceIds = tools.MapTo(preferences, func(a dao.AppPreference) string {
			return fmt.Sprintf("%d", a.AppId)
		})
		pQuery.OrderByAppPreferenceIds = appPreferenceIds
	}
	appList, err := appSvc.appRepo.FindAll(ctx, pQuery, &total)
	if err != nil {
		log.ErrorWithCtx(ctx, "查询应用列表发生错误： %v", err)
		return nil, err
	}
	owners := tools.FlatSlice(tools.MapTo(appList, func(app dao.App) []int64 {
		return tools.MapTo(app.Users, func(apu dao.AppUser) int64 {
			return apu.UserId
		})
	}))

	userQuery := pbiam.UserIdsQuery{Id: set.Of(owners).Slice()}
	//获取userid应用负责人信息列表
	userList, err := appSvc.userClient.GetUserListByIds(ctx, &userQuery)
	if err != nil {
		return nil, err
	}
	appIds := tools.MapTo(appList, func(app dao.App) int64 {
		return app.ID
	})
	result, err := appSvc.pbPipeline.GetPipelineCountByAppIds(ctx, &pbpipeline.PipelineCountReq{AppIds: appIds})
	if err != nil {
		return nil, err
	}
	var AppDetails []model.AppDetail
	for _, app := range appList {
		detail := genApp(app, userList.Users)
		detail.PipelineSize = result.CountMap[app.ID]
		// 加个收藏标签给前端
		if len(appPreferenceIds) > 0 {
			isAppInAppPreference := tools.Any(appPreferenceIds, func(appPreferenceStrId string) bool {
				appPreferenceId, _ := strconv.ParseInt(appPreferenceStrId, 10, 64)
				return app.ID == appPreferenceId
			})
			if isAppInAppPreference {
				detail.IsPreference = true
			}
		}
		detail.EventlinkType = genEventlinkType(app.EventlinkType)
		AppDetails = append(AppDetails, detail)
	}
	pg := page.PageOf(pQuery, &AppDetails, total)
	return pg, nil
}

func genEventlinkType(eventlinkType string) []string {
	eventlinkTypes := make([]string, 0)
	if eventlinkType != "" {
		eventlinkTypes = strings.Split(eventlinkType, ",")
	}
	return eventlinkTypes
}

func (appSvc *AppSvc) ListApp(ctx context.Context, pQuery *model.AppQuery) ([]string, error) {
	var appList []dao.App
	if err := appSvc.appRepo.List(ctx, pQuery, &appList); err != nil {
		log.ErrorWithCtx(ctx, "查询应用列表发生错误： %v", err)
		return nil, err
	}
	apps := vec.MapTo(appList, func(app dao.App) string {
		return app.Name
	})
	return apps, nil
}

func (appSvc *AppSvc) GetModuleTree(ctx context.Context, nodeId string) (map[string]interface{}, error) {
	var modelTree map[string]interface{}
	var moduleResp *cmdb.CmdbModelTreeResponse
	var err error
	if nodeId == "" {
		moduleResp, err = appSvc.cmdbClient.GetModulesTree(ctx)
	} else {
		moduleResp, err = appSvc.cmdbClient.GetModulesSubTree(ctx, nodeId)
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "查询应用业务模块错误: %v", err)
		return modelTree, err
	}
	return moduleResp.Result, nil
}

func genApp(app dao.App, users []*pbiam.User) model.AppDetail {
	owners := tools.MapTo(app.Users, func(usr dao.AppUser) []string {
		return tools.MapTo(tools.Filter(users,
			func(entity *pbiam.User) bool {
				return usr.UserId == entity.Id
			}),
			func(pbUser *pbiam.User) string {
				return fmt.Sprintf("%s / %s", pbUser.ChineseName, pbUser.EmployeeNo)
			})
	})
	appInst := model.AppDetail{
		ID:                app.ID,
		Name:              app.Name,
		Code:              app.Code,
		Owners:            tools.FlatSlice(owners),
		BuildPath:         app.BuildPath,
		RepoAddr:          app.RepoAddr,
		LangName:          app.LangName,
		LangVersion:       app.LangVersion,
		Status:            app.Status,
		CmdbId:            app.CmdbId,
		ProjectID:         app.ProjectID,
		Description:       app.Description,
		SenvStatus:        app.SenvStatus,
		NeedExternalRoute: app.NeedExternalRoute,
	}

	return appInst
}

func (appSvc *AppSvc) CheckAppNameExisted(ctx context.Context, projectId int64, appName string) (bool, error) {
	app, err := appSvc.appRepo.FindAppBy(ctx, projectId, appName)
	if err != nil {
		log.ErrorWithCtx(ctx, "check app name existed failed %v", err)
		return false, err
	}
	if reflect.DeepEqual(app, &dao.App{}) {
		return false, nil
	}
	return true, nil
}

func (appSvc *AppSvc) CheckApp(ctx context.Context, app model.AppBase) error {
	// check repo addr
	if !strings.HasSuffix(app.RepoAddr, ".git") || !strings.Contains(app.RepoAddr, "gitlab.ttyuyin.com") {
		return appErr.ErrRepoAddrInvalid
	}

	// check app name match k8s ServiceName rules
	appNameReg := regexp.MustCompile(`^[a-z]([-a-z0-9]*[a-z0-9])?$`)
	if !appNameReg.MatchString(app.Name) {
		return appErr.ErrAppNameInvalid
	}

	// check Project exist
	project, err := appSvc.projectRepo.FindProjectByID(ctx, app.ProjectID)
	if err != nil || project == nil {
		return appErr.ErrProjectNotFound
	}

	return nil
}

func (appSvc *AppSvc) CheckAppOwner(ctx context.Context, userId int64, appId int64) (*model.AppOwner, error) {
	var notExists model.AppOwner
	app, err := appSvc.appRepo.FindApp(ctx, appId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.ErrorWithCtx(ctx, "check app owner failed: %v", err)
			return nil, appErr.ErrAppNotFound
		}
		return nil, err
	}
	if app == nil {
		return nil, appErr.ErrAppNotFound
	}

	for _, user := range app.Users {
		if user.UserId == userId {
			return &model.AppOwner{model.AppOwnerExists}, nil
		}
	}
	return &notExists, nil
}

// GetProjectOwners will return users who are belong to the project and in the usersId list.
// Error will be returned if any user in usersId is not belong to the project.
func (appSvc *AppSvc) getProjectOwners(ctx context.Context, projectId int64, usersId []int64) ([]*pbiam.User, error) {
	// get all users of the project
	resp, err := appSvc.userClient.GetUserOfProject(ctx, &pbiam.UserSearch{ProjectId: projectId})
	if err != nil {
		return nil, err
	}
	// check if all users in usersId are belong to the project
	projectUsers := make(map[int64]*pbiam.User)
	for _, user := range resp.Users {
		projectUsers[user.Id] = user
	}
	users := make([]*pbiam.User, 0, len(usersId))
	for _, userId := range usersId {
		if user, ok := projectUsers[userId]; ok {
			users = append(users, user)
		} else {
			return nil, fmt.Errorf("%d %w", userId, appErr.ErrUserRemoved)
		}
	}
	return users, nil
}

func (appSvc *AppSvc) GetApp(ctx context.Context, appId int64) (*model.AppResp, error) {
	app, err := appSvc.appRepo.FindApp(ctx, appId)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, appErr.ErrAppNotFound
	}
	// get app from cmdb
	log.InfoWithCtx(ctx, "GetAppDeployMsg  服务[%d] 根据cmdbId[%s]查询应用信息appSvc.cmdbClient.GetApps", appId, app.CmdbId)
	// 创建带90秒超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()

	cmdbApps, err := appSvc.cmdbClient.GetApps(timeoutCtx, []string{app.CmdbId})
	if err != nil {
		log.ErrorWithCtx(ctx, "get cmdb app [%s] failed %v", app.CmdbId, err)
		err = fmt.Errorf("cmdbClient.GetApps Err %w", err)
		return nil, err
	}

	if len(cmdbApps) == 0 {
		log.ErrorWithCtx(ctx, "服务[%v]cmdbId[%v]错误", app.Name, app.CmdbId)
		return nil, fmt.Errorf("该服务cmdbId错误")
	}
	cmdbApp := cmdbApps[0]

	// get app owners
	var owners []int64
	for _, user := range app.Users {
		owners = append(owners, user.UserId)
	}

	return &model.AppResp{
		ID:                  app.ID,
		Name:                app.Name,
		Owners:              owners,
		BuildPath:           app.BuildPath,
		RepoAddr:            app.RepoAddr,
		FirstBizModuleId:    cmdbApp.BusinessLabelL1ObjId,
		SecondBizModuleId:   cmdbApp.BusinessLabelL2ObjId,
		ThirdBizModuleId:    cmdbApp.BusinessLabelL3ObjId,
		FirstBizModuleName:  cmdbApp.BusinessLabelL1Name,
		SecondBizModuleName: cmdbApp.BusinessLabelL2Name,
		ThirdBizModuleName:  cmdbApp.BusinessLabelL3Name,
		Level:               cmdbApp.Level,
		LangName:            app.LangName,
		LangVersion:         app.LangVersion,
		ProjectID:           app.ProjectID,
		Description:         app.Description,
		MatchLabels:         app.MatchLabels,
		ServiceLabels:       app.ServiceLabels,
		CmdbID:              app.CmdbId,
		StandLabelEnvs:      app.StandLabelEnvs,
		NeedExternalRoute:   app.NeedExternalRoute,
		EventlinkType:       genEventlinkType(app.EventlinkType),
	}, nil
}

func (appSvc *AppSvc) GetAppPrj(ctx context.Context, appId int64) (app *model.AppBasic, err error) {
	obj, err := appSvc.appRepo.FindAppBasicInfoById(ctx, appId)
	if err != nil {
		return
	}
	app = &model.AppBasic{
		ID:          obj.ID,
		Name:        obj.Name,
		ProjectID:   obj.ProjectID,
		BuildPath:   obj.BuildPath,
		RepoAddr:    obj.RepoAddr,
		ProjectName: obj.Project.Name,
		UserGroup:   obj.Project.UserGroup,
	}
	return
}

func (appSvc *AppSvc) UpdateApp(ctx context.Context, appReq model.UpdateAppReq) error {
	// get app from db
	app, err := appSvc.appRepo.FindApp(ctx, appReq.ID)
	if err != nil {
		// internal error
		return err
	}
	if app == nil {
		return appErr.ErrAppNotFound
	}
	//check if the language has changed when the pipeline exists
	if strings.TrimSpace(app.LangName) != strings.TrimSpace(appReq.LangName) {
		p := &pbpipeline.Pipeline{AppId: app.ID}
		pipes, err := appSvc.pbPipeline.GetPipelineByAppId(ctx, p)
		if err != nil {
			log.ErrorWithCtx(ctx, "根据appId[%d]查询流水线失败: %v", app.ID, err)
			return err
		}
		if len(pipes.Pipelines) > 0 {
			return appErr.ErrLanguageNoMatchPipeline
		}
	}
	// get app from cmdb
	cmdbApps, err := appSvc.cmdbClient.GetApps(ctx, []string{app.CmdbId})
	if err != nil {
		log.ErrorWithCtx(ctx, "更新应用获取 CMDB 应用数据失败 %v", err)
		return err
	}
	cApp := cmdbApps[0]
	log.DebugWithCtx(ctx, "CMDB 应用数据 %s", cApp)
	// compare cmdb app Bizmod and appReq Bizmod
	var shouldChangeBizMod bool
	if cApp.BusinessLabelL1ObjId != appReq.FirstBizModuleId ||
		cApp.BusinessLabelL2ObjId != appReq.SecondBizModuleId ||
		cApp.BusinessLabelL3ObjId != appReq.ThirdBizModuleId {
		shouldChangeBizMod = true
		log.InfoWithCtx(ctx, "应用业务模块发生变化 UpdateApp %s %s %s", appReq.FirstBizModuleId, appReq.SecondBizModuleId, appReq.ThirdBizModuleId)
	}

	var alias string
	if shouldChangeBizMod {
		// call cmdb verify app in uniqure in bizmodule relationship
		aliasReq := &cmdb.App{
			BusinessLabelL1ObjId: appReq.FirstBizModuleId,
			BusinessLabelL2ObjId: appReq.SecondBizModuleId,
			BusinessLabelL3ObjId: appReq.ThirdBizModuleId,
			Name:                 appReq.Name,
		}
		alias, err = appSvc.cmdbClient.GetAlias(ctx, aliasReq)
		if err != nil {
			if errors.Is(err, appErr.ErrAppExisted) {
				log.ErrorWithCtx(ctx, "更新应用失败 %v", err)
			} else {
				log.ErrorWithCtx(ctx, "更新应用cmdb失败 %v", err)
			}
			return err
		}
		log.InfoWithCtx(ctx, "获取到新的应用别名 %s", alias)
	} else {
		alias = app.Code
	}

	// get user info for cmdb patch
	//获取userid应用负责人信息列表
	userList, err := appSvc.getProjectOwners(ctx, appReq.ProjectID, appReq.Owners)
	if err != nil {
		return err
	}
	userNameList := tools.MapTo(userList, func(user *pbiam.User) string {
		return user.Username
	})
	// generate cmdb patch request data
	var updateCmdbApp *cmdb.App
	if shouldChangeBizMod {
		updateCmdbApp = &cmdb.App{
			ID:                   app.CmdbId,
			DevelopUserList:      userNameList,
			BusinessLabelL1ObjId: appReq.FirstBizModuleId,
			BusinessLabelL2ObjId: appReq.SecondBizModuleId,
			BusinessLabelL3ObjId: appReq.ThirdBizModuleId,
		}
	} else {
		updateCmdbApp = &cmdb.App{
			ID:              app.CmdbId,
			DevelopUserList: userNameList,
		}
	}
	log.InfoWithCtx(ctx, "更新 cmdb 应用 %s", updateCmdbApp)
	if _, err = appSvc.cmdbClient.UpdateApp(ctx, updateCmdbApp); err != nil {
		log.ErrorWithCtx(ctx, "更新应用cmdb失败 %v", err)
		return err
	}

	var owners []dao.AppUser
	for _, u := range userList {
		owners = append(owners, dao.AppUser{
			AppId:  app.ID,
			UserId: u.Id,
		})
	}
	app.Code = alias
	if appReq.BuildPath == "" {
		app.BuildPath = "./"
	} else {
		app.BuildPath = appReq.BuildPath
	}
	oldEventlinkType := genEventlinkType(app.EventlinkType)
	deleteEventlinkType := set.Of(oldEventlinkType).Difference(set.Of(appReq.EventlinkType)).Slice()
	log.InfoWithCtx(ctx, "删除的事件类型 %s", deleteEventlinkType)
	updatedFields := make(map[string]interface{}, 0)
	for _, eventlink := range deleteEventlinkType {
		if eventlink != "" {
			key := fmt.Sprintf("%s_type", eventlink)
			updatedFields[key] = ""
		}
	}
	app.LangName = appReq.LangName
	app.LangVersion = appReq.LangVersion
	app.RepoAddr = appReq.RepoAddr
	app.Description = appReq.Description
	app.NeedExternalRoute = appReq.NeedExternalRoute
	app.EventlinkType = strings.Join(appReq.EventlinkType, ",")
	pipelineAppMsg := pbpipeline.PipelineAppReq{
		AppName:         app.Name,
		AppId:           app.ID,
		BuildPath:       app.BuildPath,
		RepoAddr:        app.RepoAddr,
		Language:        app.LangName,
		LanguageVersion: app.LangVersion,
	}
	if err := db.Transaction(func(tx *gorm.DB) error {
		txCtx := db.CtxWithTX(ctx, tx)
		if err = appSvc.appRepo.DeleteAllAppOwners(txCtx, []int64{app.ID}); err != nil {
			return err
		}
		if err = appSvc.appRepo.InsertAppOwner(txCtx, owners); err != nil {
			return err
		}
		if err = appSvc.appRepo.UpdateApp(txCtx, app); err != nil {
			return err
		}
		if len(updatedFields) > 0 {
			if err = appSvc.appRepo.BatchUpdateAppEventlink(txCtx, app.ID, updatedFields); err != nil {
				return err
			}
		}
		if _, err = appSvc.pbPipeline.UpdatePipelineAppMsg(ctx, &pipelineAppMsg); err != nil {
			return err
		}
		return nil
	}); err != nil {
		errInfo := fmt.Sprintf("更新应用信息错误: %v", err)
		log.ErrorWithCtx(ctx, errInfo)
		return fmt.Errorf(errInfo)
	}
	return nil
}

func (appSvc *AppSvc) GetAppBasicInfo(ctx context.Context, appId int64) (*model.AppDetail, error) {
	app, err := appSvc.appRepo.FindAppBasicInfoById(ctx, appId)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, nil
	}
	detail := model.AppDetail{}
	err = mapstructure.Decode(app, &detail)
	return &detail, nil
}

func (appSvc *AppSvc) GetAppListByIds(ctx context.Context, ids []int64, projectId int64) ([]dao.App, error) {
	apps, err := appSvc.appRepo.FindAppsByIds(ctx, ids, projectId)
	return apps, err
}

func (appSvc *AppSvc) GetDeployConfigBy(ctx context.Context, para model.AppDeployConfigsPara) (*pbdep.GetAppConfigResp, error) {
	req := &pbdep.GetAppConfigReq{
		AppId:     para.ID,
		Namespace: para.Namespace,
		Cluster:   para.Cluster,
		EnvTarget: para.EnvTarget,
		Senv:      para.Senv,
		Env:       para.Env,
	}
	config, err := appSvc.configClient.GetAppConfig(ctx, req)
	if err != nil {
		return nil, err
	}
	return config, nil
}

func (appSvc *AppSvc) CreateAppPreference(ctx context.Context, req model.AppPreference) error {
	app, err := appSvc.appRepo.FindApp(ctx, req.AppID)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAppPreference 查询应用失败 %v", err)
		return err
	}
	if app == nil {
		log.ErrorWithCtx(ctx, "CreateAppPreference 查询应用失败 %v", appErr.ErrAppNotFound)
		return appErr.ErrAppNotFound
	}
	if err = appSvc.appRepo.CreateAppPreference(ctx, req); err != nil {
		log.ErrorWithCtx(ctx, "CreateAppPreference 创建应用偏好失败 %v", err)
		return err
	}
	return nil
}

func (appSvc *AppSvc) CancelAppPreference(ctx context.Context, req model.AppPreference) error {
	app, err := appSvc.appRepo.FindApp(ctx, req.AppID)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelAppPreference 查询应用失败 %v", err)
		return err
	}
	if app == nil {
		log.ErrorWithCtx(ctx, "CancelAppPreference 查询应用失败 %v", appErr.ErrAppNotFound)
		return appErr.ErrAppNotFound
	}
	if err = appSvc.appRepo.DeleteAppPreference(ctx, req); err != nil {
		log.ErrorWithCtx(ctx, "CancelAppPreference 取消应用偏好失败 %v", err)
		return err
	}
	return nil
}

func (appSvc *AppSvc) BatchUpdateApp(ctx context.Context, req model.BatchUpdateParam) error {
	// 大于50个返回
	appIdLen := len(req.AppIDs)
	if appIdLen <= 0 || appIdLen > 50 {
		return appErr.ErrAppNum
	}
	apps, err := appSvc.appRepo.FindAppsByIds(ctx, req.AppIDs, req.ProjectID)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchUpdateApp Get Apps failed: %v", err)
		return err
	}
	projectApps := make(map[int64]dao.App)
	for _, app := range apps {
		projectApps[app.ID] = app
	}
	for _, appId := range req.AppIDs {
		if _, ok := projectApps[appId]; !ok {
			return fmt.Errorf("%d %w", appId, appErr.ErrAppNotFound)
		}
	}

	switch req.Type {
	case "owners":
		return appSvc.batchUpdateAppOwners(ctx, req, apps)
	case "bizModule":
		return appSvc.batchUpdateAppBizModule(ctx, req, apps)
	case "repoAddr":
		return appSvc.batchUpdateAppRepoAddr(ctx, req, apps)
	}

	return nil
}

func (appSvc *AppSvc) batchUpdateAppOwners(ctx context.Context, req model.BatchUpdateParam, apps []dao.App) error {
	// 批量更新研发负责人
	// 获取userid应用负责人信息列表
	userList, err := appSvc.getProjectOwners(ctx, req.ProjectID, req.Owners)
	if err != nil {
		return err
	}
	userNameList := tools.MapTo(userList, func(user *pbiam.User) string {
		return user.Username
	})

	// generate cmdb patch_apps request content
	updateApps := tools.MapTo(apps, func(app dao.App) cmdb.App {
		return cmdb.App{
			ID: app.CmdbId,
		}
	})
	updateCmdbApps := &cmdb.BatchUpdateCmdbApp{
		Apps:            updateApps,
		DevelopUserList: userNameList,
	}
	log.InfoWithCtx(ctx, "[batchUpdateAppOwners] request content %s", updateCmdbApps)
	if _, err = appSvc.cmdbClient.BatchUpdateApp(ctx, updateCmdbApps); err != nil {
		log.ErrorWithCtx(ctx, "[batchUpdateAppOwners] request cmdb update error %v", err)
		return err
	}

	// generate app owners list
	var owners []dao.AppUser
	for _, u := range userList {
		for _, app := range apps {
			owners = append(owners, dao.AppUser{
				AppId:  app.ID,
				UserId: u.Id,
			})
		}
	}
	if err := db.Transaction(func(tx *gorm.DB) error {
		txCtx := db.CtxWithTX(ctx, tx)
		if err = appSvc.appRepo.DeleteAllAppOwners(txCtx, req.AppIDs); err != nil {
			return err
		}
		if err = appSvc.appRepo.InsertAppOwner(txCtx, owners); err != nil {
			return err
		}
		return nil
	}); err != nil {
		errInfo := fmt.Sprintf("[batchUpdateAppOwners] update error: %v", err)
		log.ErrorWithCtx(ctx, errInfo)
		return fmt.Errorf(errInfo)
	}

	return nil
}

func (appSvc *AppSvc) batchUpdateAppBizModule(ctx context.Context, req model.BatchUpdateParam, apps []dao.App) error {
	// 批量更新业务模块
	cmdbAppInfo := make(map[string]dao.App)
	for _, app := range apps {
		cmdbAppInfo[app.CmdbId] = app
	}

	daoApps := make([]dao.App, 0, len(apps))

	// generate cmdb patch_apps request content
	updateApps := tools.MapTo(apps, func(app dao.App) cmdb.App {
		return cmdb.App{
			ID: app.CmdbId,
		}
	})
	updateCmdbApps := &cmdb.BatchUpdateCmdbApp{
		Apps:               updateApps,
		BusinessLabelObjID: req.ThirdBizModuleId,
	}
	log.InfoWithCtx(ctx, "[batchUpdateAppBizModule] request content %s", updateCmdbApps)
	resp, err := appSvc.cmdbClient.BatchUpdateApp(ctx, updateCmdbApps)
	if err != nil {
		log.ErrorWithCtx(ctx, "[batchUpdateAppBizModule] request cmdb update error %v", err)
		return err
	}
	cmdbApps := resp.Result.Apps
	now := time.Now()
	for _, app := range cmdbApps {
		daoApp := cmdbAppInfo[app.ID]
		daoApp.Code = app.Alias
		daoApp.UpdatedAt = now
		daoApps = append(daoApps, daoApp)
	}

	// 更新应用code
	if err := appSvc.appRepo.BatchUpdateAppCols(ctx, daoApps, "code"); err != nil {
		log.ErrorWithCtx(ctx, "[batchUpdateAppRepoAddr] update app code error: %v", err)
		return err
	}

	return nil
}

func (appSvc *AppSvc) batchUpdateAppRepoAddr(ctx context.Context, req model.BatchUpdateParam, apps []dao.App) error {
	// 批量更新源码地址
	if !strings.HasSuffix(req.RepoAddr, ".git") || !strings.Contains(req.RepoAddr, "gitlab.ttyuyin.com") {
		return appErr.ErrRepoAddrInvalid
	}

	daoApps := make([]dao.App, 0, len(apps))
	appIds := make([]int64, 0, len(apps))
	now := time.Now()
	for _, app := range apps {
		app.RepoAddr = req.RepoAddr
		app.UpdatedAt = now
		daoApps = append(daoApps, app)
		appIds = append(appIds, app.ID)
	}

	if err := db.Transaction(func(tx *gorm.DB) error {
		txCtx := db.CtxWithTX(ctx, tx)
		if err := appSvc.appRepo.BatchUpdateAppCols(txCtx, daoApps, "repo_addr"); err != nil {
			log.ErrorWithCtx(ctx, "[batchUpdateAppRepoAddr] update app repo_addr error: %v", err)
			return err
		}
		if _, err := appSvc.pbPipeline.BatchUpdatePipelineAppMsg(ctx, &pbpipeline.PipelineAppsReq{
			AppIds:   appIds,
			RepoAddr: req.RepoAddr,
		}); err != nil {
			log.ErrorWithCtx(ctx, "[batchUpdateAppRepoAddr] update pipeline repo_addr error: %v", err)
			return err
		}
		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "批量更新更新应用%v源码地址[%v]，发生异常: %v", appIds, req.RepoAddr, err)
		return err
	}

	return nil
}

// SearchAppsByUser 提供给用户全局搜索应用
func (appSvc *AppSvc) SearchAppsByUser(ctx context.Context, userID int64, search model.AppSearchReq) ([]model.AppSearchResult, error) {
	var (
		appSearchResult  []model.AppSearchResult
		err              error
		uniqueProjectIds []int64
		projectName      map[int64]string // {projectID: projectName}
		appIdList        []int64
	)

	if !search.HasKeyword() {
		return appSearchResult, nil
	}

	appList, err := appSvc.appRepo.SearchApps(ctx, search.Keyword)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchAppsByUser dao.SearchApps failed: %v", err)
		return nil, err
	}
	if len(appList) == 0 {
		log.InfoWithCtx(ctx, "SearchAppsByUser dao.SearchApps no app found by search %+v: %v", search, err)
		return appSearchResult, nil
	}
	// 关键词搜索出来的服务名置顶 + 同名服务排序
	sort.SliceStable(appList, func(i, j int) bool {
		isExactMatchI := strings.EqualFold(appList[i].Name, search.Keyword)
		isExactMatchJ := strings.EqualFold(appList[j].Name, search.Keyword)
		if isExactMatchI != isExactMatchJ {
			return isExactMatchI
		}

		return appList[i].Name < appList[j].Name
	})
	// get unique project ids
	_projects := make(map[int64]struct{})
	for _, app := range appList {
		_projects[app.ProjectID] = struct{}{}
	}
	for projectID := range _projects {
		uniqueProjectIds = append(uniqueProjectIds, projectID)
	}

	// 获取用户所有所属项目组
	userProjectResp, err := appSvc.projectUserClient.GetUserProjectsBy(ctx, &pbiam.UserProParam{UserId: userID})
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchAppsByUser GetProjectUsersBy failed: %v", err)
		return nil, err
	}
	userProjectList := make(map[int64]struct{}) // {projectID: struct{}}
	for _, project := range userProjectResp.ProjectUsers {
		userProjectList[project.ProjectId] = struct{}{}
	}

	// 获取项目名称
	projectName = make(map[int64]string)
	projects, err := appSvc.projectRepo.GetUserProjects(ctx, false, uniqueProjectIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchAppsByUser get project name failed: %v", err)
		return nil, err
	}
	for _, projects := range projects {
		projectName[projects.ID] = projects.Name
	}

	// 获取应用生成环境部署信息
	for _, app := range appList {
		appIdList = append(appIdList, app.ID)
	}
	changeLogResp, err := appSvc.deployClient.ListAppsChangeLog(ctx, &pbdep.ListAppsChangeLogReq{AppIds: appIdList, Env: constants.PRODUCTION.String(), IsCurrent: true})
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchAppsByUser ListAppsChangeLog failed: %v", err)
		return nil, err
	}
	appActiveChangeLog := make(map[int64]struct{})
	for _, changeLog := range changeLogResp.ChangeLogs {
		appActiveChangeLog[changeLog.AppId] = struct{}{}
	}

	// 构造返回结果
	roles := cctx.Roles(ctx)
	isSuperAdmin := tools.Contains(roles, string(constants.ADMIN))

	for _, app := range appList {
		_, userInProject := userProjectList[app.ProjectID]
		_, appHasProductionChangeLog := appActiveChangeLog[app.ID]
		appSearchResult = append(appSearchResult, model.AppSearchResult{
			AppID:         app.ID,
			AppName:       app.Name,
			ProjectID:     app.ProjectID,
			ProjectName:   projectName[app.ProjectID],
			HasPermission: userInProject || isSuperAdmin, // 用户在项目组内或者是超级管理员都有权限
			IsProduction:  appHasProductionChangeLog,
		})
	}

	// Sort appSearchResult by HasPermission. True first.
	sort.Slice(appSearchResult, func(i, j int) bool {
		return appSearchResult[i].HasPermission && !appSearchResult[j].HasPermission
	})

	return appSearchResult, nil
}

func (appSvc *AppSvc) UpdateAppSenvStatus(ctx context.Context, app model.AppSenvStatus) error {
	obj, err := appSvc.appRepo.FindAppBasicInfoById(ctx, app.Id)
	if err != nil {
		return err
	}
	if obj == nil {
		// 查询不到直接返回不更新
		return nil
	}
	obj.SenvStatus = app.SenvStatus
	err = appSvc.appRepo.UpdateApp(ctx, obj)
	if err != nil {
		log.ErrorWithCtx(ctx, "[UpdateAppSenvStatus] failed, req: %=v", app)
		return err
	}
	return nil
}

func (appSvc *AppSvc) GetDeployPlanApps(ctx context.Context, req model.ProjectsAppsReq) ([]model.AppBasic, error) {
	if req.UserGroup != "" {
		// 获取同一用户群体下的项目列表
		projects, err := appSvc.projectRepo.ListProjects(ctx, &model.ListProjectsReq{UserGroup: req.UserGroup})
		if err != nil {
			log.ErrorWithCtx(ctx, "[GetDeployPlanApps] list projects failed: %v, userGroup: %v", err, req.UserGroup)
			return nil, err
		}
		req.ProjectIDs = tools.MapTo(projects, func(project dao.Project) int64 {
			return project.ID
		})
	} else {
		req.ProjectIDs = []int64{req.ProjectID}
	}
	apps, err := appSvc.appRepo.GetProjectsApps(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetDeployPlanApps] get projects apps failed: %v, req: %+v", err, req)
		return nil, err
	}
	planApps := tools.MapTo(apps, func(app dao.App) model.AppBasic {
		return model.AppBasic{
			ID:          app.ID,
			Name:        app.Name,
			BuildPath:   app.BuildPath,
			RepoAddr:    app.RepoAddr,
			ProjectID:   app.ProjectID,
			ProjectName: app.Project.Name,
			UserGroup:   app.Project.UserGroup,
		}
	})

	return planApps, nil
}

func (appSvc *AppSvc) GetCmdbData(ctx context.Context, originalReq *http.Request, url string) (any, error) {
	return appSvc.cmdbClient.Common(ctx, originalReq, url)
}

func (appSvc *AppSvc) GetAppEventlinkConfig(ctx context.Context, req *model.AppEventlinkReq) (*model.AppEventlinkResp, error) {
	eventlinks, err := appSvc.appRepo.ListAppEventlink(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	var resp model.AppEventlinkResp
	for _, l := range eventlinks {
		if l.Env == constants.TESTING {
			resp.Testing = model.EventlinkData{
				ConsumerType: l.ConsumerType,
				ProducerType: l.ProducerType,
			}
		}
		if l.Env == constants.PRODUCTION {
			resp.Production = model.EventlinkData{
				ConsumerType: l.ConsumerType,
				ProducerType: l.ProducerType,
			}
		}
	}

	return &resp, nil
}

func (appSvc *AppSvc) FindAppWithPrjs(ctx context.Context, req model.FindAppsReq) (objs []model.AppWithProject, err error) {
	apps, err := appSvc.appRepo.FindAppsByName(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "[FindAppWithPrjs] appRepo FindAppsByName ERR: %v", err)
		return
	}
	for _, app := range apps {
		item := model.AppWithProject{
			Name:        app.Name,
			Code:        app.Code,
			ProjectID:   app.ProjectID,
			BuildPath:   app.BuildPath,
			LangName:    app.LangName,
			LangVersion: app.LangVersion,
			RepoAddr:    app.RepoAddr,
			Status:      app.Status,
			CmdbId:      app.CmdbId,
			Description: app.Description,
		}
		if app.Project != nil {
			item.ProjectName = app.Project.Name
			item.ProjectDescription = app.Project.Description
			item.ProjectUserGroup = app.Project.UserGroup
			item.ProjectExtra = app.Project.Extra
		}
		objs = append(objs, item)
	}

	return
}

func (appSvc *AppSvc) DeleteApp(ctx *gin.Context, appId int64) (err error) {
	// 检查应用是否存在
	app, err := appSvc.appRepo.FindApp(ctx, appId)
	if err != nil {
		log.ErrorWithCtx(ctx, "查询应用失败: %v", err)
		return err
	}
	if app == nil {
		log.ErrorWithCtx(ctx, "应用不存在: %d", appId)
		return appErr.ErrAppNotFound
	}

	// 删除应用前先检查CMDB 应用是否可删除
	err = appSvc.cmdbClient.DelApp(ctx, app.CmdbId)
	if err != nil {
		log.ErrorWithCtx(ctx, "删除CMDB应用失败: %v", err)
		return err
	}

	// 删除应用流水线数据
	_, err = appSvc.pbPipeline.DelPipeline(ctx, &pbpipeline.DelPipelineReq{AppId: &app.ID, PrjId: &app.ProjectID})
	if err != nil {
		log.ErrorWithCtx(ctx, "删除应用流水线数据失败: %v", err)
		return
	}

	// 删除应用动态配置数据
	err = appSvc.cfgMngRepo.DeleteByApp(ctx, appId)
	if err != nil {
		log.ErrorWithCtx(ctx, "删除应用动态配置数据失败: %v", err)
		return
	}

	// 删除应用基本信息
	err = appSvc.appRepo.Delete(&dao.App{BaseModel: db.BaseModel{ID: appId}})
	if err != nil {
		log.ErrorWithCtx(ctx, "删除应用基本信息失败: %v", err)
		return
	}
	return
}

func (appSvc *AppSvc) AppCanDel(ctx *gin.Context, appIds []int64) (err error) {
	for _, appId := range appIds {
		// 检查应用是否存在
		app, err := appSvc.appRepo.FindApp(ctx, appId)
		if err != nil {
			log.ErrorWithCtx(ctx, "查询应用失败: %v", err)
			return err
		}
		if app == nil {
			log.ErrorWithCtx(ctx, "应用不存在: %d", appId)
			err = fmt.Errorf("应用不存在: %d", appId)
			return err
		}

		// 删除应用前先检查CMDB 应用是否可删除
		bindCrds, err := appSvc.cmdbClient.CheckAppCanDel(ctx, app.CmdbId)
		if err != nil {
			log.ErrorWithCtx(ctx, "检查CMDB应用是否可删除失败: %v", err)
			return err
		}
		if bindCrds > 0 {
			log.ErrorWithCtx(ctx, "CMDB应用不可删除: %d", app.CmdbId)
			err = fmt.Errorf("服务%s，在CMDB记录中仍有资源绑定，请联系业务运维接触资源绑定后再尝试删除", app.Name)
			return err
		}
	}

	return
}

func (appSvc *AppSvc) SyncCMDBInfo(ctx context.Context, appId int64) error {
	apps, err := appSvc.appRepo.FindAllAppBasicInfo(ctx, &model.AppQuery{})
	if err != nil {
		return err
	}
	for _, app := range apps {
		if app.CmdbId == "" {
			continue
		}
		cmdbApps, err := appSvc.cmdbClient.GetApps(ctx, []string{app.CmdbId})
		if err != nil {
			log.ErrorWithCtx(ctx, "查询CMDB应用(%s)失败: %v", app.CmdbId, err)
			continue
		}
		if len(cmdbApps) == 0 {
			continue
		}
		app.CmdbInfo, _ = json.Marshal(cmdbApps[0])
		err = appSvc.appRepo.UpdateApp(ctx, &app, "CmdbInfo")
		if err != nil {
			log.ErrorWithCtx(ctx, "更新应用(%s)CMDB信息失败: %v", app.Name, err)
			continue
		}
	}
	return nil
}
