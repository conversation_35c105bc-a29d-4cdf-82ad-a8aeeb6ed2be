//go:generate mockgen -destination=http_workload_mock.go -package=core -source=http_workload.go
package core

import (
	"context"
)

type WorkloadClient interface {
	GetReadyReplicas(ctx context.Context, in *GetReadyReplicasRequest) (*GetReadyReplicasResponse, error)
}

type GetReadyReplicasRequest struct {
	Cluster      string `json:"cluster"`
	Namespace    string `json:"namespace"`
	Kind         string `json:"kind"`
	ResourceName string `json:"resourceName"`
}

type GetReadyReplicasResponse struct {
	BaseResponse

	Data ReadyReplicasData `json:"data"`
}

type ReadyReplicasData struct {
	Replicas      int64 `json:"replicas"`
	ReadyReplicas int64 `json:"readyReplicas"`
}
