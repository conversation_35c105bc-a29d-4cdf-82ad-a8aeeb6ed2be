CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `username` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
  `chinese_name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '中文名',
  `email` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '邮箱',
  `employee_no` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '工号',
  `lark_union_id` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '飞书id',
  `gitlab_id` int DEFAULT '0' COMMENT 'gitlab用户id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态(0不可用,1正常)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户信息表';
