package token

import (
	"time"
)

// Generator is the interface that wraps the GenerateToken method.
//
// GenerateToken generate a token.
type Generator interface {
	GenerateToken(claims *PrivateClaims, expire time.Duration) (string, error)
}

// Verifier is the interface that wraps the Verify method.
//
// Verify verifies a token and returns uid
type Verifier interface {
	Verify(token string) (*PrivateClaims, error)
}

type PrivateClaims struct {
	Uid      int64
	Username string
}
