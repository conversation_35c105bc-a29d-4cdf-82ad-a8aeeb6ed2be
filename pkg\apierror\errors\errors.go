package errors

import (
	"fmt"

	"github.com/pkg/errors"

	"52tt.com/cicd/pkg/apierror"
)

type APIError struct {
	code    int
	message string
}

func (e *APIError) Error() string {
	return fmt.Sprintf("err code %d with message %s", e.code, e.message)
}

func (e *APIError) Code() int {
	return e.code
}

func (e *APIError) Message() string {
	return e.message
}

func New(msg string, code int) *APIError {
	return &APIError{
		code:    code,
		message: msg,
	}
}

type BizErrWithData struct {
	apierror.BizError
	data any
}

func (e *BizErrWithData) Data() any {
	return e.data
}

func NewWithData(err apierror.BizError, data any) *BizErrWithData {
	return &BizErrWithData{
		BizError: err,
		data:     data,
	}
}

func NewErrWithData(msg string, code int, data any) *BizErrWithData {
	return &BizErrWithData{
		BizError: New(msg, code),
		data:     data,
	}
}

// WithMessage 相比于 errors.WithMessage，如果 err 为 nil，会创建一个新的 apierror.BizError 来处理
func WithMessage(err error, message string) error {
	basicErr := err
	if basicErr == nil {
		basicErr = ErrInternalServer
	}

	return errors.WithMessage(basicErr, message)
}

// Wrap 相比于 errors.Wrap，如果 err 为 nil，会创建一个新的 apierror.BizError 来处理
func Wrap(err error, message string) error {
	basicErr := err
	if basicErr == nil {
		basicErr = ErrInternalServer
	}

	return errors.Wrap(basicErr, message)
}
