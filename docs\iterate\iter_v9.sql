-- 多云部署sql变更记录

alter table deploy.ticket
    add column `deploy_configs` json NULL COMMENT '部署配置信息',
    add column `deploy_flag` tinyint NOT NULL default 0 COMMENT '部署标识';

CREATE TABLE `pipeline_run_subtask` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `pipeline_run_task_id` bigint NOT NULL COMMENT 'pipeline_task_run 任务id',
    `name` varchar(256) NOT NULL COMMENT '子任务名称',
    `type` varchar(64) DEFAULT NULL COMMENT '子任务类型',
    `status` varchar(64) NOT NULL COMMENT '子任务运行状态',
    `started_time` timestamp NULL DEFAULT NULL COMMENT '子任务开始时间',
    `completed_time` timestamp NULL DEFAULT NULL COMMENT '子任务完成时间',
    `config` json NOT NULL COMMENT '子任务配置信息',
    `result` json DEFAULT NULL COMMENT '子任务结果',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tekton_name` varchar(128) DEFAULT NULL COMMENT 'tekton名称',
    `tekton_namespace` varchar(64) DEFAULT NULL COMMENT 'tekton命名空间',
    `pod_name` varchar(256) DEFAULT NULL COMMENT 'pod名称',
    `pipeline_run_id` bigint DEFAULT NULL COMMENT 'pipeline run id',
    `prefix_id` int DEFAULT NULL COMMENT '基于上个多云任务的id',
    `enabled` tinyint DEFAULT 1 COMMENT '是否生效',
    PRIMARY KEY (`id`),
    KEY `idx_pipeline_run_id` (`pipeline_run_id`) USING BTREE,
    KEY `idx_pipeline_task_run_id` (`pipeline_run_task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多云部署子任务运行信息表'

alter table deploy.deploy_change_log
    add column `subtask_run_id` bigint NOT NULL default 0 COMMENT 'pipeline_run_subtask 子任务id';