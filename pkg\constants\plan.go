package constants

type PlanStatus string

const (
	Disable PlanStatus = "disable" //禁用
	Enable  PlanStatus = "enable"  //启用
)

func (t PlanStatus) String() string {
	return string(t)
}

// DeployPlanRecordStatus 发布计划记录表状态
type DeployPlanRecordStatus string

const (
	// PlanRecordStatusUnrelated 未关联
	PlanRecordStatusUnrelated DeployPlanRecordStatus = "UNRELATED"
	// PlanRecordStatusRelated 已关联
	PlanRecordStatusRelated DeployPlanRecordStatus = "RELATED"
	// PlanRecordStatusPublished 已发布
	PlanRecordStatusPublished DeployPlanRecordStatus = "PUBLISHED"
)

func (p DeployPlanRecordStatus) String() string {
	return string(p)
}

// PlanTimeType 放量计划时间类型
type PlanTimeType string

const (
	// PlanTimeTypeAbsolute 绝对时间
	PlanTimeTypeAbsolute PlanTimeType = "ABSOLUTE"
	// PlanTimeTypeRelative 相对时间
	PlanTimeTypeRelative PlanTimeType = "RELATIVE"
)

func (p PlanTimeType) String() string {
	return string(p)
}

// PlanAction 放量计划动作
type PlanAction string

const (
	// PlanActionControlTraffic 调整流量比例
	PlanActionControlTraffic PlanAction = "CONTROL_TRAFFIC"
	// PlanActionPassCanary 通过金丝雀验证
	PlanActionPassCanary PlanAction = "PASS_CANARY"
	// PlanActionDeployOrigin 全量部署基准环境
	PlanActionDeployOrigin PlanAction = "DEPLOY_ORIGIN"
)

func (p PlanAction) String() string {
	return string(p)
}

// TimeUnit 时间单位
type TimeUnit string

const (
	// TimeUnitMinute 分钟
	TimeUnitMinute TimeUnit = "M"
	// TimeUnitHour 小时
	TimeUnitHour TimeUnit = "H"
)

type DyeingType string

const (
	UID           DyeingType = "uid"
	IP            DyeingType = "ip"
	ClientVersion DyeingType = "客户端版本"
	SecWebsocket  DyeingType = "Sec-WebSocket-Protocol"
	CliType       DyeingType = "cli"
	Market        DyeingType = "market"
)

const DefaultTrafficMark = "quwan-default-canary-client-version-traffic-mark"
const TrafficMarkPrefix = "canary-client-version"
const TTVsTrafficMarkKey = "x-qw-traffic-mark"

// EnumReqCliType 客户端类型
type EnumReqCliType int64

const (
	EnumEnumReqCliTypeAndroid EnumReqCliType = 0
	EnumEnumReqCliTypeIos     EnumReqCliType = 1
	EnumEnumReqCliTypePc      EnumReqCliType = 5
)

var enumReqCliTypeStringMapping = map[EnumReqCliType]string{
	EnumEnumReqCliTypeAndroid: "Android",
	EnumEnumReqCliTypeIos:     "iOS",
	EnumEnumReqCliTypePc:      "PC",
}

func (e EnumReqCliType) Value() int64 {
	return int64(e)
}

func (e EnumReqCliType) String() string {
	return e.ToReqCliTypeName()
}

func (e EnumReqCliType) ToReqCliTypeName() string {
	v, ok := enumReqCliTypeStringMapping[e]
	if ok {
		return v
	}
	return "unknown"
}

//ReqMarketName

// EnumReqMarket 马甲包类型
type EnumReqMarket int64

const (
	EnumReqMarketTT      EnumReqMarket = 0
	EnumReqMarketHuanYou EnumReqMarket = 2
	EnumReqMarketMaike   EnumReqMarket = 5
	EnumReqMarketMiJing  EnumReqMarket = 6
)

var enumEnumReqMarketStringMapping = map[EnumReqMarket]string{
	EnumReqMarketTT:      "TT",
	EnumReqMarketHuanYou: "欢游",
	EnumReqMarketMaike:   "麦可",
	EnumReqMarketMiJing:  "谜境",
}

func (e EnumReqMarket) String() string {
	return e.ToReqMarketName()
}

func (e EnumReqMarket) ToReqMarketName() string {
	v, ok := enumEnumReqMarketStringMapping[e]
	if ok {
		return v
	}
	return "unknown"
}
