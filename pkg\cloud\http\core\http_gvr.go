//go:generate mockgen -destination=http_gvr_mock.go -package=core -source=http_gvr.go
package core

import (
	"context"
)

// GVRHTTPClient 定义容器云文档提供的 GVR 操作接口
// 接口文档: https://q9jvw0u5f5.feishu.cn/wiki/wikcnqspxr7TjDieIKrDNnqcIsg 4.1 章节
// 后续容器云提供 rpc 接口后，直接进行替换
type GVRHTTPClient interface {
	CreateResource(ctx context.Context, in *CreateResourceRequest) (*CreateResourceResponse, error)
	GetResource(ctx context.Context, in *GetResourceRequest) (*GetResourceResponse, error)
	ListResource(ctx context.Context, in *ListResourceRequest) (*ListResourceResponse, error)
	UpdateResource(ctx context.Context, in *UpdateResourceRequest) (*UpdateResourceResponse, error)
	DeleteResource(ctx context.Context, in *DeleteResourceRequest) (*DeleteResourceResponse, error)
	FindAnyList(ctx context.Context, in AnyListReq, out any) error
	SetV2Host(hostV2 string)
}

// GVRClusterOption 定义 GVRClient 集群使用的参数
type GVRClusterOption struct {
	// 容器云 GVR 接口，目标集群名和集群 id 二选一，新系统只记录名称，因此接口忽略集群 id，后续替换 rpc 接口时，需要注意和调整
	Cluster   string `json:"cluster_name"` // http 文档名称叫 cluster_name, rpc 接口叫 cluster， 定义为 Cluster 容易在代码层面保持一致
	Namespace string `json:"namespace"`
}

type BaseResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type CreateResourceRequest struct {
	GVRClusterOption

	Data any    `json:"data,omitempty"` // 完整资源，json 格式
	Yaml string `json:"yaml,omitempty"` // 完整资源，yaml 格式
}

type CreateResourceResponse struct {
	BaseResponse

	Data any `json:"data,omitempty"`
}

type GetResourceRequest struct {
	GVRClusterOption

	Group         string   `json:"group"`
	Version       string   `json:"version"`
	Resource      string   `json:"resource"`
	ResourceNames []string `json:"resource_names"` // 查询的资源名列表
}

type GetResourceResponse struct {
	BaseResponse

	Data struct {
		Data []any `json:"data"`
	}
}

type ListResourceRequest struct{}

type ListResourceResponse struct{}

type UpdateResourceRequest struct{}

type UpdateResourceResponse struct{}

type DeleteResourceRequest struct {
	GVRClusterOption

	Group         string   `json:"group"`
	Version       string   `json:"version"`
	Resource      string   `json:"resource"`
	ResourceNames []string `json:"resource_names"` // 查询的资源名列表
}

type DeleteResourceResponse struct {
	BaseResponse
}

type AnyListReq struct {
	ClusterName   string     `json:"clusterName"`
	Namespace     string     `json:"namespace"`
	Group         string     `json:"group"`
	Version       string     `json:"version"`
	Resource      string     `json:"resource"`
	LabelSelector []Selector `json:"labelSelector"`
	FieldSelector []Selector `json:"fieldSelector"`
}

type Selector struct {
	Key   string `json:"key"`
	Op    string `json:"op"`
	Value string `json:"value"`
}

type AnyListResp struct {
	BaseResponse
	Data CloudData `json:"data"`
}

type CloudData struct {
	Data any `json:"data"`
}
