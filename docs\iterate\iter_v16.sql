-- 流水线删除标志字段
alter table `pipeline`.`pipeline` add column `is_deleted` tinyint DEFAULT 0 COMMENT  "是否已删除(逻辑删除),1已删除,0未删除";
-- 流水线模板删除标志字段
alter table `pipeline`.`template` add column `is_deleted` tinyint DEFAULT 0 COMMENT  "是否已删除(逻辑删除),1已删除,0未删除";
-- 流水线组删除标志字段
alter table `pipeline`.`pipeline_group` add column `is_deleted` tinyint DEFAULT 0 COMMENT  "是否已删除(逻辑删除),1已删除,0未删除";

CREATE TABLE `app`.`app_preference` (
  `id` int NOT NULL AUTO_INCREMENT,
  `app_id` bigint NOT NULL COMMENT '应用id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `project_id` bigint NOT NULL COMMENT '项目id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='应用收藏表';

CREATE INDEX idx_is_deleted ON `pipeline`.`pipeline` (is_deleted);
CREATE INDEX idx_is_deleted ON `pipeline`.`template` (is_deleted);
CREATE INDEX idx_is_deleted ON `pipeline`.`pipeline_group` (is_deleted);

-- 删除template unique_key
ALTER TABLE `pipeline`.`template` DROP INDEX uniq_tmlp