package gitlab

import (
	"52tt.com/cicd/pkg/constants"
	"time"
)

type Change struct {
	OldPath     string `json:"old_path"`
	NewPath     string `json:"new_path"`
	AMode       string `json:"a_mode"`
	BMode       string `json:"b_mode"`
	Diff        string `json:"diff"`
	NewFile     bool   `json:"new_file"`
	RenamedFile bool   `json:"renamed_file"`
	DeletedFile bool   `json:"deleted_file"`
}

type GitMergeChange struct {
	ID                        int64    `json:"id"`
	Iid                       int64    `json:"iid"`
	ProjectID                 int64    `json:"project_id"`
	Title                     string   `json:"title"`
	State                     string   `json:"state"`
	CreatedAt                 string   `json:"created_at"`
	UpdatedAt                 string   `json:"updated_at"`
	TargetBranch              string   `json:"target_branch"`
	SourceBranch              string   `json:"source_branch"`
	SourceProjectID           int64    `json:"source_project_id"`
	TargetProjectID           int64    `json:"target_project_id"`
	Description               string   `json:"description"`
	Draft                     bool     `json:"draft"`
	WorkInProgress            bool     `json:"work_in_progress"`
	MergeWhenPipelineSucceeds bool     `json:"merge_when_pipeline_succeeds"`
	MergeStatus               string   `json:"merge_status"`
	DetailedMergeStatus       string   `json:"detailed_merge_status"`
	Subscribed                bool     `json:"subscribed"`
	SHA                       string   `json:"sha"`
	UserNotesCount            int64    `json:"user_notes_count"`
	ChangesCount              string   `json:"changes_count"`
	ShouldRemoveSourceBranch  bool     `json:"should_remove_source_branch"`
	ForceRemoveSourceBranch   bool     `json:"force_remove_source_branch"`
	Squash                    bool     `json:"squash"`
	WebURL                    string   `json:"web_url"`
	DiscussionLocked          bool     `json:"discussion_locked"`
	Changes                   []Change `json:"changes"`
	Overflow                  bool     `json:"overflow"`
}

type Discussion struct {
	Body string `json:"body"`
}

type DiscussionParameter struct {
	ID        string
	MrID      int64
	ProjectID int
}

type DiscussionResp struct {
	ID              string      `json:"id"`
	IndividualNote  bool        `json:"individual_note"`
	Notes           interface{} `json:"notes"`
	Message         string      `json:"message"`
	ResolvedAt      time.Time   `json:"resolved_at"`
	Confidential    bool        `json:"confidential"`
	NoteableIid     int         `json:"noteable_iid"`
	CommandsChanges interface{} `json:"commands_changes"`
}

type MergeRequest struct {
	Id                       int64  `json:"merge_request_iid"`
	ProjectId                int    `json:"id"`
	ShouldRemoveSourceBranch bool   `json:"should_remove_source_branch"`
	MergeCommitMessage       string `json:"merge_commit_message"`
}

type SetPipelineOfCommitParams struct {
	ProjectID   int                          `json:"id"`
	ShaID       string                       `json:"sha"`
	Name        string                       `json:"name"`
	State       constants.GitlabPipelineType `json:"state"`
	Ref         string                       `json:"ref"`
	TargetUrl   string                       `json:"target_url"`
	Description string                       `json:"description"`
}

type MergeResponse struct {
	ID              int           `json:"id"`
	Iid             int           `json:"iid"`
	ProjectID       int           `json:"project_id"`
	Title           string        `json:"title"`
	Description     string        `json:"description"`
	State           string        `json:"state"`
	CreatedAt       time.Time     `json:"created_at"`
	UpdatedAt       time.Time     `json:"updated_at"`
	MergedBy        interface{}   `json:"merged_by"`
	MergeUser       interface{}   `json:"merge_user"`
	MergedAt        interface{}   `json:"merged_at"`
	ClosedBy        interface{}   `json:"closed_by"`
	ClosedAt        interface{}   `json:"closed_at"`
	TargetBranch    string        `json:"target_branch"`
	SourceBranch    string        `json:"source_branch"`
	SourceProjectID int           `json:"source_project_id"`
	TargetProjectID int           `json:"target_project_id"`
	Labels          []interface{} `json:"labels"`
	Draft           bool          `json:"draft"`
	MergeStatus     string        `json:"merge_status"`
	Sha             string        `json:"sha"`
	MergeCommitSha  interface{}   `json:"merge_commit_sha"`
	SquashCommitSha interface{}   `json:"squash_commit_sha"`
}
