package constants

type GitlabPipelineType string

// pending, running, success, failed, canceled
const (
	GitlabStatusPending  GitlabPipelineType = "pending"
	GitlabStatusRunning  GitlabPipelineType = "running"
	GitlabStatusSuccess  GitlabPipelineType = "success"
	GitlabStatusFailed   GitlabPipelineType = "failed"
	GitlabStatusCanceled GitlabPipelineType = "canceled"
)

const (
	GitlabMrPipelineName = "Sentinel—MR流水线"
)

func (g GitlabPipelineType) String() string {
	return string(g)
}
