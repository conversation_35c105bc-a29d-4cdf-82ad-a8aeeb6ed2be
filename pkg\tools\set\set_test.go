package set

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNew(t *testing.T) {
}

func TestOf(t *testing.T) {
}

func TestSet_Add(t *testing.T) {
}

func TestSet_Exists(t *testing.T) {
	a1 := Of([]int{3, 4, 5})
	assert.Equal(t, true, a1.Exists(4))
}

func TestSet_Difference(t *testing.T) {
	a1 := Of([]int{3, 4, 5})
	a2 := Of([]int{1, 2, 4})

	diffA1 := a1.Difference(a2)
	diffA2 := a2.Difference(a1)
	assert.Equal(t, []int{3, 5}, diffA1.Slice())
	assert.Equal(t, []int{1, 2}, diffA2.Slice())
}

func TestSet_ToSlice(t *testing.T) {
	arr := []int{1, 2, 3, 1, 2}
	intSets := Of(arr)
	assert.Equal(t, arr[0], intSets.Slice()[0])
}

func TestSet_Remove(t *testing.T) {
}

func TestSet_Size(t *testing.T) {
	arr := []int{1, 3, 2, 1, 2}
	intSets := Of(arr)
	assert.Equal(t, 3, intSets.Size())
}
