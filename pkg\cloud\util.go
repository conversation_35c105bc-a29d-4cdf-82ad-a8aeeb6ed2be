package cloud

import (
	"encoding/hex"
	"github.com/gogf/gf/v2/crypto/gaes"
)

// kubeconfig decode
const DefaultCipherKey = "dHQtY2xvdWQtdmlzdWFsCg=="

func DecryptCipher(hexCipherTxt string, cipherKey ...string) (plainTxt string, err error) {
	cKey := GenerateCipherKey(cipherKey...)
	dHBytes, err := hex.DecodeString(hexCipherTxt)
	if err != nil {
		return "", err
	}
	plainB, err := gaes.Decrypt(dHBytes, cKey)
	if err != nil {
		return "", err
	}
	return string(plainB), nil
}

func GenerateCipherKey(cipherKey ...string) []byte {
	var key string
	if len(cipherKey) == 0 {
		key = DefaultCipherKey

	} else {
		key = cipherKey[0]
	}
	return []byte(key)
}
