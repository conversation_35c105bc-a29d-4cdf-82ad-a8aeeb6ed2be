package main

import (
	"context"
	"log"
	"testing"
	"time"

	"52tt.com/cicd/pkg/event"
	"52tt.com/cicd/pkg/kafka"
)

func TestListConsumerGroups(t *testing.T) {
	// t.<PERSON><PERSON>("ingnore tests")
	myReceiver, err := event.NewReceiver(kafka.NewConfig(kafka.Name("test-receiver"), kafka.Brokers(brokers), kafka.ConsumerTopics(topics)))
	if err != nil {
		log.Fatalf("failed %v", err)
	}

	defer myReceiver.Close(context.Background())

	log.Printf("will listen consuming topic test-topic\n")
	myReceiver.StartReceiver(context.Background())
	if err != nil {
		log.Fatalf("failed to start receiver: %s", err)
	} else {
		log.Printf("receiver stopped\n")
	}

	time.Sleep(time.Minute * 30)
}
