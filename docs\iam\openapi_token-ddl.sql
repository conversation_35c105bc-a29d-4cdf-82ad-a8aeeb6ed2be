CREATE TABLE `iam`.`openapi_token` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(128) DEFAULT NULL COMMENT '唯一标识',
    `token` varchar(256) DEFAULT NULL COMMENT 'token',
    `description` varchar(128) DEFAULT NULL COMMENT '描述',
    `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_uniq_name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='openapi token表';