package main

import (
	"context"
	"os/signal"
	"syscall"

	"github.com/Shopify/sarama"

	"52tt.com/cicd/pkg/kafka"
	"52tt.com/cicd/pkg/log"
)

var (
	brokers = []string{"************:9092"}
	topics  = []string{"pipeline-test"}
)

type Service struct {
	consumer *kafka.Consumer
}

func (s *Service) handleEvent(msg *sarama.ConsumerMessage) (error, bool) {
	log.Infof("handle msg topic=%q key=%q value=%s", msg.Topic, msg.Key, msg.Value)
	return nil, false
}

func (s *Service) Start() {
	s.consumer.Start()
}

func (s *Service) Stop() {
	s.consumer.Stop()
}

func NewService(cfg *kafka.Config) *Service {
	s := &Service{}
	c, err := kafka.NewConsumer(cfg, s.handleEvent)
	if err != nil {
		log.Errorf("NewConsumer fail: %v", err)
	}
	s.consumer = c
	return s
}

func main() {
	cfg := kafka.NewConfig(kafka.Name("test-consumer"), kafka.Brokers(brokers), kafka.ConsumerTopics(topics))

	s := NewService(cfg)

	log.Infof("start consumer")
	s.Start()

	ctx, stop := signal.NotifyContext(context.Background(),
		syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	defer stop()

	<-ctx.Done()

	log.Infof("stop consumer")
	s.Stop()
}
