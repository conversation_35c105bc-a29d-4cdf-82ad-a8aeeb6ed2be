package constants

type ConsumerType string

const (
	ConsumerTypeKafka     ConsumerType = "kafka"
	ConsumerTypeMix       ConsumerType = "mix"
	ConsumerTypeEventlink ConsumerType = "eventlink"
)

func (c ConsumerType) String() string {
	return string(c)
}

type ProducerType string

const (
	ProducerTypeFalse ProducerType = "false"
	ProducerTypeTrue  ProducerType = "true"
)

func (p ProducerType) String() string {
	return string(p)
}

const ConsumerEnvKey = "TT_EVENT_SUBSCRIBE_MODE"
const ProducerEnvKey = "EVENT_LINK_PUBLISHER_ENABLED"

type EvtLinkCRDType string

const (
	ConsumerBindingType EvtLinkCRDType = "ConsumerBinding"
)
