package para

import (
	"fmt"
	"net/http"
	"strconv"

	"52tt.com/cicd/pkg/apierror"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/render"
	"52tt.com/cicd/pkg/validator"

	"github.com/gin-gonic/gin"
)

type BindFunc func(interface{}) error

type ExecFuncError struct {
	Err string
}

func (execError ExecFuncError) Error() string {
	return execError.Err
}

type ContextWrapper struct {
	//gin framework的Context
	ctx *gin.Context
	//绑定函数
	bindFns []BindFunc
	//错误信息prefix
	prefix string
	check  bool
}

// WrapperContext Gin context wrapper
// ctx the context of gin
// whether check parameter or not
func WrapperContext(ctx *gin.Context, check bool, prefix string) *ContextWrapper {
	wrapper := &ContextWrapper{
		ctx:     ctx,
		bindFns: make([]BindFunc, 0),
		check:   check,
		prefix:  prefix,
	}
	return wrapper
}

func (wrapper *ContextWrapper) RegisterBind(options ...BindFunc) {
	wrapper.bindFns = append(wrapper.bindFns, options...)
}

func (wrapper *ContextWrapper) GetUserID() int64 {
	userid := wrapper.ctx.GetString("userid")
	if v, err := strconv.ParseInt(userid, 10, 64); err == nil {
		return v
	}
	return 0
}

func (wrapper *ContextWrapper) GetUserName() string {
	return wrapper.ctx.GetString("username")
}

func (wrapper *ContextWrapper) GetEmployeeNo() string {
	return wrapper.ctx.GetString("employee_no")
}

func (wrapper *ContextWrapper) Bind(value interface{}) error {
	if len(wrapper.bindFns) == 0 {
		return fmt.Errorf("%s错误,至少指定一个参数绑定函数", wrapper.prefix)
	}
	for _, fn := range wrapper.bindFns {
		if err := fn(value); err != nil {
			wrapper.ctx.AbortWithStatusJSON(http.StatusBadRequest, render.Failf(http.StatusBadRequest, err.Error()))
			errInfo := fmt.Sprintf("%s绑定参数错误:%v", wrapper.prefix, err)
			log.Debug(errInfo)
			return fmt.Errorf("%s", errInfo)
		}
	}
	if wrapper.check {
		if err := wrapper.Check(value); err != nil {
			return err
		}
	}
	return nil
}

func (wrapper *ContextWrapper) Ok(values ...interface{}) {
	var v interface{}
	if len(values) > 0 {
		v = values[0]
	}
	wrapper.ctx.JSON(http.StatusOK, render.Ok(v))
}

func (wrapper *ContextWrapper) FailError(code int, err error) {
	wrapper.ctx.AbortWithStatusJSON(code, render.Failf(code, err.Error()))
}

func (wrapper *ContextWrapper) Failf(code int, msg string, args ...interface{}) {
	wrapper.ctx.AbortWithStatusJSON(code, render.Failf(code, msg, args...))
}

func (wrapper *ContextWrapper) FailJSON(code int, value interface{}) {
	wrapper.ctx.AbortWithStatusJSON(code, render.Fatal(code, "", value))
}

func (wrapper *ContextWrapper) FailWithErrorCode(httpStatusCode int, err error, errorCode int) {
	wrapper.ctx.AbortWithStatusJSON(httpStatusCode, render.Failf(errorCode, err.Error()))
}

func (wrapper *ContextWrapper) BadRequestWithErrorCode(err error, errorCode int) {
	wrapper.ctx.AbortWithStatusJSON(http.StatusBadRequest, render.Failf(errorCode, err.Error()))
}

func (wrapper *ContextWrapper) FailWithError(err error) {
	switch e := err.(type) {
	case apierror.ErrorWithData:
		// todo 先这样处理，后面再优化
		if e.Code() == http.StatusInternalServerError {
			wrapper.ctx.AbortWithStatusJSON(http.StatusInternalServerError, render.Failf(e.Code(), e.Message(), e.Data()))
			return
		}
		wrapper.ctx.AbortWithStatusJSON(http.StatusBadRequest, render.Fatal(e.Code(), e.Message(), e.Data()))
	case apierror.BizError:
		if e.Code() == http.StatusInternalServerError {
			wrapper.ctx.AbortWithStatusJSON(http.StatusInternalServerError, render.Failf(e.Code(), e.Message()))
			return
		}
		wrapper.ctx.AbortWithStatusJSON(http.StatusBadRequest, render.Failf(e.Code(), e.Message()))
	default:
		wrapper.ctx.AbortWithStatusJSON(http.StatusBadRequest, render.Failf(http.StatusBadRequest, err.Error()))
	}
}

func (wrapper *ContextWrapper) Check(value interface{}) error {
	if err := validator.Check(value); err != nil {
		wrapper.ctx.AbortWithStatusJSON(http.StatusBadRequest, render.Failf(http.StatusBadRequest, err.Error()))
		errInfo := fmt.Sprintf("%s校验参数错误:%v", wrapper.prefix, err)
		log.Debug(errInfo)
		return fmt.Errorf("%s", errInfo)
	}
	return nil
}
