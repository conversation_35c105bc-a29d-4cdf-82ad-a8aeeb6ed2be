CREATE TABLE `deploy_record`
(
    `id`                   bitint    NOT NULL AUTO_INCREMENT COMMENT 'id',
    `tekton_name`          varchar(128)       DEFAULT NULL,
    `created_at`           timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `deploy_change_log_id` bigint    NOT NULL COMMENT '变更记录ID',
    `number`               bigint    NOT NULL COMMENT '部署次数',
    `log_key`              json               DEFAULT NULL COMMENT '日志',
    `result`               json               DEFAULT NULL COMMENT '任务执行结果',
    PRIMARY KEY (`id`),
    UNIQUE KEY `number` (`number`,`deploy_change_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--- 部署步骤和状态
alter table deploy_record add column step varchar(32) not null default '' comment '处理的步骤';
alter table deploy_record add column status varchar(32) not null default '' comment '处理的步骤状态';

--- 唯一索引需要加上步骤
alter table deploy_record drop index `number`;
alter table deploy_record add unique index uq_number(`number`,`deploy_change_log_id`, `step`);
