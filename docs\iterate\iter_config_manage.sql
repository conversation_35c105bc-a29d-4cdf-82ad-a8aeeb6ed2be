CREATE TABLE `app`.`config` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
     `project_id` bigint NOT NULL COMMENT '项目id',
     `user_group` varchar(128) DEFAULT "" COMMENT '用户群体',
     `name` varchar(64) NOT NULL COMMENT '配置名称',
     `env` varchar(32)  NOT NULL COMMENT '名称',
     `created_by` bigint NOT NULL COMMENT '创建人',
     `created_by_chinese_name` varchar(64) NOT NULL COMMENT '创建人中文名',
     `created_by_employee_no` varchar(64) NOT NULL COMMENT '创建人工号',
     `apollo_namespace` varchar(64) COMMENT 'Apollo命名空间',
     `is_global` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否全局配置',
     `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
     `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='配置管理表';

CREATE TABLE `app`.`config_version` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `config_id` bigint NOT NULL COMMENT '配置id',
    `version` int COMMENT '用户群体',
    `content` longtext NOT NULL COMMENT '配置内容',
    `status` varchar(32) NOT NULL COMMENT '状态',
    `created_by` bigint NOT NULL COMMENT '创建人',
    `created_by_chinese_name` varchar(64) NOT NULL COMMENT '创建人中文名',
    `created_by_employee_no` varchar(64) NOT NULL COMMENT '创建人工号',
    `operator_by` bigint NOT NULL COMMENT '操作人',
    `operator_chinese_name` varchar(64) NOT NULL COMMENT '操作人中文名',
    `operator_employee_no` varchar(64) NOT NULL COMMENT '操作人工号',
    `description` varchar(255) DEFAULT "" COMMENT '描述',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_config` (`config_id`) USING BTREE
) ENGINE=InnoDB COMMENT='配置版本表';

CREATE TABLE `app`.`config_app` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `project_id` bigint NOT NULL COMMENT '项目id',
    `app_id` bigint NOT NULL COMMENT '应用id',
    `app_name` varchar(64) NOT NULL COMMENT '应用名称',
    `config_id` bigint NOT NULL COMMENT '配置id',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_app` (`app_id`) USING BTREE,
    KEY `idx_config` (`config_id`) USING BTREE
) ENGINE=InnoDB COMMENT='配置服务表';