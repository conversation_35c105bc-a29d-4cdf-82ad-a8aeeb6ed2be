package zap

import (
	"io"
	"os"
	"strings"

	"go.uber.org/zap"
	"go.uber.org/zap/buffer"
	"go.uber.org/zap/zapcore"
	"google.golang.org/grpc/grpclog"
)

type (
	Option      = zap.Option
	AtomicLevel = zap.AtomicLevel
	Logger      = zap.Logger
	Level       = zapcore.Level
)

var (
	defaultLogger         *Logger
	defaultAtomLevel      *AtomicLevel
	defaultEnableFuncName = false
	defaultCallerSkip     = 5

	defaultLogWriter io.Writer = os.Stdout
)

func init() {
	atomLevel := zap.NewAtomicLevelAt(zapcore.DebugLevel)
	defaultAtomLevel = &atomLevel
	resetLogger(newLogger())
}

func CurrentLogger() *Logger {
	return defaultLogger
}

type funcEncoder struct {
	zapcore.Encoder
}

func (f *funcEncoder) Clone() zapcore.Encoder {
	return &funcEncoder{
		Encoder: f.Encoder.Clone(),
	}
}

func (f *funcEncoder) EncodeEntry(ent zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	if index := strings.LastIndex(ent.Caller.Function, "."); index != -1 && index+1 < len(ent.Caller.Function) {
		ent.Caller.Function = ent.Caller.Function[index+1:]
	}
	return f.Encoder.EncodeEntry(ent, fields)
}

func CurrentLevel() Level {
	return defaultAtomLevel.Level()
}

func SetLevel(lv Level) {
	defaultAtomLevel.SetLevel(lv)
}

func Setup(opts ...Option) {
	if len(opts) != 0 {
		resetLogger(defaultLogger.WithOptions(opts...))
	} else {
		resetLogger(defaultLogger)
	}
}

func GetAtomicLevel() *AtomicLevel {
	return defaultAtomLevel
}

func SetLogWriter(writer io.Writer) {
	defaultLogWriter = writer
	resetLogger(newLogger())
}

func EnableFuncName() {
	defaultEnableFuncName = true
	resetLogger(newLogger())
}

func OpenGRPCLog() {
	grpclog.SetLoggerV2(newGRPCLogger(defaultLogger.Sugar()))
}

func resetLogger(l *Logger) {
	defaultLogger = l
}

func newLogger() *zap.Logger {
	enc := newEncoder(defaultEnableFuncName)
	core := zapcore.NewCore(enc, zapcore.AddSync(defaultLogWriter), zap.LevelEnablerFunc(func(l zapcore.Level) bool {
		return defaultAtomLevel.Enabled(l)
	}))
	return zap.New(core, zap.AddCaller(), zap.AddCallerSkip(defaultCallerSkip), zap.AddStacktrace(zap.NewAtomicLevelAt(zap.PanicLevel)))
}

func newEncoder(enableFuncName bool) zapcore.Encoder {
	encodeCfg := zap.NewProductionEncoderConfig()
	encodeCfg.EncodeTime = zapcore.ISO8601TimeEncoder

	if enableFuncName {
		encodeCfg.FunctionKey = "func"
	}

	return &funcEncoder{
		Encoder: zapcore.NewJSONEncoder(encodeCfg),
	}
}
