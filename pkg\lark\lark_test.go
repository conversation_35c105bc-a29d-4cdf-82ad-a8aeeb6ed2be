package lark

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestGetDepsList(t *testing.T) {
	t.Skip("忽略")
	//获取飞书client
	client := NewClient("cli_a21e8d6582fbd013", "M42W3G4c0eVsOppDczfmteDrnSimNuzb")

	/*
		level = 0:
		技术保障部：od-42056dea31b1531d819cff932516184a
		研发效能部：od-772f5c54b9031a64897a36a0b865d983

		level = 1：
		质量平台组：od-67a58368ec41beee99af19620d61c799
		效能平台组：od-ab50f13465f426c17583573ad930faf8

		level = 2
		IOS组：od-32da4cebd3a139bd545a22f4c4624912
	*/

	users, _ := client.GetUsrsList("od-ab50f13465f426c17583573ad930faf8")
	fmt.Println(users)

	//使用的root_dep_id是超管应用id
	departmentList, _ := client.GetDepsList("od-0a6ee50aa99abaf1f1b711ab75a8d9cf")
	fmt.Println(departmentList)
}

func TestSendMsgBot_Dev(t *testing.T) {
	t.Skip("忽略")
	users := map[string]string{
		"wangxiao":     "on_631a84a40a77414d713eefa385116635",
		"zengye":       "on_75677c6c5cdd73c5fb2adc24db1da9dd",
		"huangzexiong": "on_d14c20b4a0dc166ffc9d5c8896399852",
	}
	//获取飞书client
	client := NewClient("cli_a4f5adb0e2b9d00d", "m4rAc6pGodqPplQMZOTM30PVl0FBO0NJ")
	_ = ""
	content2 := "𝐇𝐞𝐥𝐥𝐨😍\\n你好！\\t瞧瞧咱新的机器人\\t\\n============>>>\\n【CICD播音员(Dev)】\\n流水线运行成功\\n服务：\\ttest001\\n"
	for _, v := range users {
		_, err := client.SendMsgToUsr(fmt.Sprintf("{\"text\":\"%s\"}", content2), v, "text")
		if err != nil {
			break
		}
	}
}

func TestSendMsgBot_Test(t *testing.T) {
	t.Skip("忽略")
	users := map[string]string{
		"wangxiao":     "on_631a84a40a77414d713eefa385116635",
		"zengye":       "on_75677c6c5cdd73c5fb2adc24db1da9dd",
		"huangzexiong": "on_d14c20b4a0dc166ffc9d5c8896399852",
	}
	//获取飞书client
	client := NewClient("cli_a418d7d7b5f9d00b", "W8w40s5ytc7ImbmzwHgFadD08rOeTiNF")
	_ = ""
	content2 := "𝐇𝐞𝐥𝐥𝐨😍\\n你好！\\t瞧瞧咱新的机器人\\t\\n============>>>\\n【CICD大喇叭(Test)】\\n流水线运行成功\\n服务：\\ttest001\\n"
	for _, v := range users {
		_, err := client.SendMsgToUsr(fmt.Sprintf("{\"text\":\"%s\"}", content2), v, "text")
		if err != nil {
			break
		}
	}
}

func TestSendMsgBot_Prod(t *testing.T) {
	t.Skip("忽略")
	users := map[string]string{
		//"wangxiao":     "on_631a84a40a77414d713eefa385116635",
		//"zengye":       "on_75677c6c5cdd73c5fb2adc24db1da9dd",
		//"huangzexiong": "on_d14c20b4a0dc166ffc9d5c8896399852",
		"limiaosheng": "on_e478bf0dddde2225451a99ef69a59dd9",
	}
	//获取飞书client
	client := NewClient("cli_a418d17680bc900b", "ESFd5ljfWBOQkmnW1MBDDhNTZXcdaVuy")
	_ = ""
	content2 := "𝐇𝐞𝐥𝐥𝐨😍\\n你好！\\t瞧瞧咱新的机器人\\t\\n============>>>\\n【CICD播音员(Prod)】\\n流水线运行成功\\n服务：\\ttest001\\n"
	for _, v := range users {
		_, err := client.SendMsgToUsr(fmt.Sprintf("{\"text\":\"%s\"}", content2), v, "text")
		if err != nil {
			break
		}
	}
}

func TestMessage_Send(t *testing.T) {
	t.Skip("忽略")
	title := NewTitle("测试标题", "indigo")
	paragraph := NewParagraph("测试内容: 今天是个好日子")
	paragraph1 := NewParagraph(fmt.Sprintf("测试内容222：%s", time.Now().Format("2006-01-02 15:04:05")))
	rowHeader := NewRowHeader("姓名", "性别", "年龄", "身高")
	row1 := NewRow("张三", "男", "32", "167cm")
	row2 := NewRow("李四", "女", "39", "181cm")
	row3 := NewRow("王五", "男", "41", "172cm")
	paragraph2 := NewParagraph("这是一个段落本文子")
	message := NewCardMessage(
		title,
		paragraph,
		paragraph1,
		rowHeader,
		row1,
		row2,
		row3,
		paragraph2,
	)
	client := NewClient("cli_a21e8d6582fbd013", "M42W3G4c0eVsOppDczfmteDrnSimNuzb")
	_, err := client.SendMsgToUsr(message.Fill(), "on_e478bf0dddde2225451a99ef69a59dd9", message.MessageType)
	assert.Equal(t, nil, err)
}

type MsgCard struct {
	TemplateId string        `json:"templateId"`
	Content    Content       `json:"content"`
	MockData   string        `json:"mock_data"`
	Variables  []interface{} `json:"variables"`
}

type Content struct {
	Config    ConfigT       `json:"config"`
	Elements  []Elements    `json:"elements"`
	Header    Header        `json:"header"`
	MockData  string        `json:"mock_data"`
	Variables []interface{} `json:"variables"`
}

type Header struct {
	Template string `json:"template"`
	Title    Title  `json:"title"`
}

type ConfigT struct {
	WideScreenMode bool `json:"wide_screen_mode"`
}

type Elements struct {
	Tag     string    `json:"tag"`
	Content string    `json:"content"`
	Alt     Alt       `json:"alt"`
	ImgKey  string    `json:"img_key"`
	Actions []Actions `json:"actions"`
}

type Alt struct {
	Content string `json:"content"`
	Tag     string `json:"tag"`
}

type Actions struct {
	Tag  string `json:"tag"`
	Text Text   `json:"text"`
	Type string `json:"type"`
	Url  string `json:"url"`
}

func TestSendInteractive(t *testing.T) {
	t.Skip("忽略")
	msgCard := MsgCard{
		TemplateId: "ctp_AA6MlvD2kwT4",
		Content: Content{
			Config: ConfigT{
				WideScreenMode: true,
			},
			Elements: []Elements{
				{Tag: "markdown", Content: "这里是卡片文本，支持使用markdown标签设置文本格式。例如：\\n*斜体* 、**粗体**、~~删除线~~、[文字链接](https://www.feishu.cn)、<at id=all></at>、<font color='red'> 彩色文本 </font>"},
				{Alt: Alt{Content: "", Tag: "plain_text"}, ImgKey: "img_v2_041b28e3-5680-48c2-9af2-497ace79333g", Tag: "img"},
				{Tag: "action", Actions: []Actions{
					{Tag: "button", Text: Text{
						Tag:     "plain_text",
						Content: "这是跳转按钮",
					},
						Type: "primary",
						Url:  "https://feishu.cn"},
				}},
			},
			Header: Header{
				Template: "blue",
				Title: Title{
					Template: "blue",
					Text: Text{
						Tag:     "plain_text",
						Content: "这里是卡片标题",
					},
				},
			},
			MockData:  "{}",
			Variables: nil,
		},
	}
	//获取飞书client
	client := NewClient("cli_a21e8d6582fbd013", "M42W3G4c0eVsOppDczfmteDrnSimNuzb")
	msg, _ := json.Marshal(msgCard)
	_, err := client.SendMsgToUsr(string(msg), "on_631a84a40a77414d713eefa385116635", "interactive")
	if err != nil {
		return
	}
}

func TestSendInteractive2(t *testing.T) {
	//t.Skip("忽略")
	// testApproval := CardContent{
	// 	BackGroundColor: "green",
	// 	TicketName:      "工单名称",
	// 	CardType:        "UpgradeTicket",
	// 	TicketType:      "提测工单",
	// 	TicketNo:        "No_68723",
	// 	Applicant:       "名字",
	// 	ApplicationTime: "2023-5-30 14:20:35 星期二 CST",
	// 	ApprovalNode:    "114433",
	// 	Approve:         "李秒胜、曾叶",
	// 	ServiceName:     "pipeline-service",
	// 	Branch:          "master",
	// 	Env:             "sub",
	// 	Clusters:        "k8s-dev-oggyhufrty34fw2/devops",
	// 	Detail:          "https://baidu.com",
	// }

	// //获取飞书client
	// client := NewClient("cli_a21e8d6582fbd013", "M42W3G4c0eVsOppDczfmteDrnSimNuzb")
	// m := make(map[string]string)

	// mapstructure.Decode(testApproval, &m)
	// updateTicketContent := pkg.GetRealContent(m, m["card_type"])
	// _, err := client.SendMsgToUsr(updateTicketContent, "on_d14c20b4a0dc166ffc9d5c8896399852", "interactive")
	// if err != nil {
	// 	return
	// }
}

func TestSendInteractive3(t *testing.T) {
	t.Skip("忽略")
	testApproval := CardContent{
		TicketType: "提测工单",
	}

	//获取飞书client
	client := NewClient("cli_a21e8d6582fbd013", "M42W3G4c0eVsOppDczfmteDrnSimNuzb")
	msg, _ := json.Marshal(testApproval)
	_, err := client.SendMsgToUsr(string(msg), "on_631a84a40a77414d713eefa385116635", "interactive")
	if err != nil {
		return
	}
}

func TestUpdateMsgCard(t *testing.T) {
	t.Skip("忽略")
	client := NewClient("cli_a4f5adb0e2b9d00d", "m4rAc6pGodqPplQMZOTM30PVl0FBO0NJ")
	content := "{\n  \"config\": {\n    \"wide_screen_mode\": true,\n    \"update_multi\": true\n  },\n  \"elements\": [\n    {\n      \"tag\": \"div\",\n      \"text\": {\n        \"content\": \"➤𝙎𝙚𝙣𝙩𝙞𝙣𝙖𝙡︰\\n*❗您有待处理的审批任务，请及时处理*\",\n        \"tag\": \"lark_md\"\n      }\n    },\n    {\n      \"tag\": \"hr\"\n    },\n    {\n      \"tag\": \"div\",\n      \"text\": {\n        \"content\": \"🟢**基本信息**\",\n        \"tag\": \"lark_md\"\n      }\n    },\n    {\n      \"tag\": \"markdown\",\n      \"content\": \"工单类型：提测工单\\n工单ID：　20230650011\\n变更原因：test\\n申请人：　<font color='red'>张伟</font>\\n申请时间：*2023-06-09 13:47:37 星期五 CST*\\n审批节点：114433\\n审批人：　<font color='red'>王骁</font>\"\n    },\n    {\n      \"tag\": \"markdown\",\n      \"content\": \"🟢**部署信息**\"\n    },\n    {\n      \"tag\": \"markdown\",\n      \"content\": \"服务名：　工单111\\n运行分支：master\\n部署环境：子环境\\n集群：　　k8s-dev-o功6389458564w2\\n命名空间：212\"\n    },\n    {\n      \"tag\": \"markdown\",\n      \"content\": \"[>>工单入口<<](http://cicd-testing.ttyuyin.com/#/tickets/applicant/detail/0)\",\n      \"text_align\": \"left\"\n    },\n    {\n      \"tag\": \"note\",\n      \"elements\": [\n        {\n          \"tag\": \"img\",\n          \"img_key\": \"img_b79d3e29-5ffe-4897-822a-9743fe9f7b1g\",\n          \"alt\": {\n            \"tag\": \"plain_text\",\n            \"content\": \"图片\"\n          }\n        },\n        {\n          \"tag\": \"plain_text\",\n          \"content\": \"状态已发生变更\"\n        }\n      ]\n    }\n  ],\n  \"header\": {\n    \"template\": \"green\",\n    \"title\": {\n      \"content\": \"CICD提测工单\",\n      \"tag\": \"plain_text\"\n    }\n  }\n}\n"
	_, _ = client.UpdateMsgCard("om_dd23e1f9fa0678cc1d85023c8555d819", content)
}

func TestUserInfo(t *testing.T) {
	t.Skip("忽略")

	//获取飞书client
	client := NewClient("cli_a21e8d6582fbd013", "M42W3G4c0eVsOppDczfmteDrnSimNuzb")
	users, err := client.GetUsrsByEmails([]string{"<EMAIL>", "<EMAIL>", "<EMAIL>"})
	if err != nil {
		return
	}
	for _, user := range users {

		println(fmt.Sprintf("%s %s %#v", *user.Email, *user.UserId, user.Mobile))
	}
}
