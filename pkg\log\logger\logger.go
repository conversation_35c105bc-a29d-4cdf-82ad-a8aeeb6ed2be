package logger

import (
	"context"

	"52tt.com/cicd/pkg/log/core"
	"52tt.com/cicd/pkg/log/logger/zap"
)

func New() *Adapter {
	return &Adapter{
		lm: zap.CurrentLevelManager(),
		hm: zap.CurrentHookManager(),
		lf: zap.CurrentLogger().Sugar(),
	}
}

var _ core.LoggerManager = (*Adapter)(nil)

type Adapter struct {
	lf core.LoggerFormat
	lm core.LevelManager
	hm core.HookManager
}

func (a *Adapter) CurrentHookManager() core.HookManager {
	return a
}

func (a *Adapter) CurrentLevelManager() core.LevelManager {
	return a.lm
}

func (a *Adapter) CurrentLoggerFormat() core.LoggerFormat {
	return a.lf
}

func (a *Adapter) CurrentLogger() core.Logger {
	return a
}

func (a *Adapter) RegisterHook(hooks ...core.Hook) {
	a.hm.RegisterHook(hooks...)
	a.lf = zap.CurrentLogger().Sugar()
}

func (a *Adapter) Hooks() []core.Hook {
	return a.hm.Hooks()
}

func (a *Adapter) handle(lv core.Level, ctx context.Context, format string, args ...any) {
	newHandleFuncWithHooks(func(logger core.LoggerFormat, lv core.Level, ctx context.Context, format string, args ...any) {
		switch lv {
		case core.DebugLevel:
			logger.Debugf(format, args...)
		case core.InfoLevel:
			logger.Infof(format, args...)
		case core.WarnLevel:
			logger.Warnf(format, args...)
		case core.ErrorLevel:
			logger.Errorf(format, args...)
		case core.PanicLevel:
			logger.Panicf(format, args...)
		case core.FatalLevel:
			logger.Fatalf(format, args...)
		default:
		}
	}, a.hm.Hooks()...)(a.lf, lv, ctx, format, args...)
}

func (a *Adapter) DebugWithCtx(ctx context.Context, format string, args ...any) {
	a.handle(core.DebugLevel, ctx, format, args...)
}

func (a *Adapter) InfoWithCtx(ctx context.Context, format string, args ...any) {
	a.handle(core.InfoLevel, ctx, format, args...)
}

func (a *Adapter) WarnWithCtx(ctx context.Context, format string, args ...any) {
	a.handle(core.WarnLevel, ctx, format, args...)
}

func (a *Adapter) ErrorWithCtx(ctx context.Context, format string, args ...any) {
	a.handle(core.ErrorLevel, ctx, format, args...)
}

func (a *Adapter) PanicWithCtx(ctx context.Context, format string, args ...any) {
	a.handle(core.PanicLevel, ctx, format, args...)
}

func (a *Adapter) FatalWithCtx(ctx context.Context, format string, args ...any) {
	a.handle(core.FatalLevel, ctx, format, args...)
}

func newHandleFuncWithHooks(handler core.Handle, hooks ...core.Hook) core.Handle {
	n := len(hooks)

	chainer := func(currentHook core.Hook, currentHandler core.Handle) core.Handle {
		return func(logger core.LoggerFormat, lv core.Level, ctx context.Context, format string, args ...any) {
			currentHook(logger, lv, ctx, format, args, currentHandler)
		}
	}

	chainedHandler := handler
	for i := n - 1; i >= 0; i-- {
		chainedHandler = chainer(hooks[i], chainedHandler)
	}

	return func(logger core.LoggerFormat, lv core.Level, ctx context.Context, format string, args ...any) {
		chainedHandler(logger, lv, ctx, format, args...)
	}
}
