-- 迭代7.5 的SQL变更记录  预计发生产之前，统一提单。如果已自行提单，请备注下

alter table app.app
    add column  `service_labels` json NULL COMMENT '服务发布时k8s Service的 selector',
    add column  `match_labels` json NULL COMMENT '服务发布时k8s Deployment的 selector MatchLabels';

-- 数据库变更  中台-推荐labels更新
-- UPDATE app SET match_labels = JSON_OBJECT('app', name, 'app.kubernetes.io/name', name, 'app.kubernetes.io/instance', name), service_labels = JSON_OBJECT('app.kubernetes.io/name',name, 'app.kubernetes.io/instance', name) project_id =3;
