package email

type Sender interface {
	Send() error
}

type Email struct {
	Config      *Config
	To          []string
	Subject     string
	Body        string
	Attachments []Attachment
}

type Builder interface {
	AddTo(to ...string) Builder
	AddSubject(subject string) Builder
	AddBody(body string) Builder
	AddAttachment(attachments ...Attachment) Builder
}

func NewEmail(config *Config) *Email {
	return &Email{Config: config}
}

func (email *Email) AddTo(to ...string) Builder {
	email.To = append(email.To, to...)
	return email
}

func (email *Email) AddSubject(subject string) Builder {
	email.Subject = subject
	return email
}

func (email *Email) AddBody(body string) Builder {
	email.Body = body
	return email
}

func (email *Email) AddAttachment(attachments ...Attachment) Builder {
	email.Attachments = append(email.Attachments, attachments...)
	return email
}

func (email *Email) Send() error {
	return nil
}
