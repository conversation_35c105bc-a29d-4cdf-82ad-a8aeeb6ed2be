CREATE TABLE `change_set`
(
    `id`                      int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `name`                    VARCHAR(512) NOT NULL COMMENT '名称',
    `status`                  VARCHAR(10)  NOT NULL COMMENT '状态',
    `template_id`             BIGINT       DEFAULT '0' COMMENT '引用模板id',
    `created_at`              timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`              timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `deploy_time`             timestamp NULL DEFAULT NULL COMMENT '最近部署生产时间',
    `project_id`              BIGINT       NOT NULL COMMENT '关联项目ID',
    `trigger_by`              VARCHAR(255) DEFAULT NULL COMMENT '触发人id',
    `trigger_by_chinese_name` VARCHAR(255) DEFAULT NULL COMMENT '触发人名称',
    `trigger_by_employee_no`  VARCHAR(255) DEFAULT NULL COMMENT '触发人工号',
    `duration`                BIGINT       DEFAULT '0' COMMENT '持续时间',
    `stage_sequence`          VARCHAR(255) NOT NULL COMMENT '关联的stage顺序,按照逗号分隔',
    `started_time`            timestamp NULL DEFAULT NULL COMMENT '开始时间',
    `completed_time`          timestamp NULL DEFAULT NULL COMMENT '结束时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='变更集信息表';



CREATE TABLE `change_set_pipeline`
(
    `id`               int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `change_set_id`    BIGINT       NOT NULL COMMENT '变更集id',
    `app_id`           BIGINT       NOT NULL COMMENT '服务id',
    `app_name`         VARCHAR(128) NOT NULL COMMENT '服务名称',
    `pipeline_id`      BIGINT       NOT NULL COMMENT '流水线id',
    `pipeline_name`    VARCHAR(128) NOT NULL COMMENT '流水线名称',
    `pipeline_run_id`  BIGINT       DEFAULT '0' COMMENT '最新的流水线运行id',
    `tip`              VARCHAR(64)  DEFAULT NULL COMMENT '提示信息(例如已更新)',
    `task_run_status`  VARCHAR(64)  DEFAULT NULL COMMENT '流水线任务节点运行状态',
    `artifact_version` VARCHAR(128) DEFAULT NULL COMMENT '构件(制品库)版本',
    `created_at`       DATETIME     NOT NULL COMMENT '创建时间',
    `updated_at`       DATETIME     NOT NULL COMMENT '更新时间',
    `task_run_id`      BIGINT       DEFAULT '0' COMMENT '任务运行id',
    `task_run_name`    VARCHAR(64)  DEFAULT NULL COMMENT '任务运行名称',
    `available`        BIGINT       NOT NULL COMMENT '是否有效(1被托管,2不被托管，被托管的时候可用，成功后失效)',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='变更集流水线托管信息表';



CREATE TABLE `change_set_run_task`
(
    `id`                    BIGINT       NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `change_set_id`         BIGINT       NOT NULL COMMENT '变更集id',
    `change_set_stage_id`   BIGINT       NOT NULL COMMENT '变更集阶段id',
    `change_set_task_id`    BIGINT       NOT NULL COMMENT '变更集任务id',
    `pipeline_id`           BIGINT       NOT NULL COMMENT '流水线id',
    `pipeline_run_id`       BIGINT       NOT NULL COMMENT '流水线运行id',
    `pipeline_run_stage_id` BIGINT       NOT NULL COMMENT '流水线运行阶段id',
    `pipeline_run_task_id`  BIGINT       NOT NULL COMMENT '流水线运行任务id',
    `type`                  VARCHAR(255) NOT NULL COMMENT '类型',
    `tekton_namespace`      VARCHAR(255) DEFAULT NULL COMMENT 'tekton命名空间',
    `tekton_label`          VARCHAR(255) DEFAULT NULL COMMENT 'tekton标签 unique',
    `created_at`            DATETIME     NOT NULL COMMENT '创建时间',
    `updated_at`            DATETIME     NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='变更集运行任务表';



CREATE TABLE `change_set_stage`
(
    `id`            int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `name`          VARCHAR(512) NOT NULL COMMENT '名称',
    `status`        VARCHAR(10)  NOT NULL COMMENT '状态',
    `type`          VARCHAR(64)  NOT NULL COMMENT '类型',
    `change_set_id` BIGINT       NOT NULL COMMENT '变更集id',
    `stage_id`      BIGINT       NOT NULL COMMENT '模板阶段ID',
    `created_at`    DATETIME     NOT NULL COMMENT '创建时间',
    `updated_at`    DATETIME     NOT NULL COMMENT '更新时间',
    `duration`      BIGINT DEFAULT '0' COMMENT '持续时间',
    `task_sequence` VARCHAR(255) NOT NULL COMMENT '关联的task顺序,按照逗号分隔',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='变更集阶段(快照)';


CREATE TABLE `change_set_task`
(
    `id`                  int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `name`                VARCHAR(512) NOT NULL COMMENT '名称',
    `status`              VARCHAR(10)  NOT NULL COMMENT '状态',
    `type`                VARCHAR(64)  NOT NULL COMMENT '类型',
    `change_set_stage_id` BIGINT       NOT NULL COMMENT '阶段id',
    `change_set_id`       BIGINT       NOT NULL COMMENT '变更集id',
    `task_id`             BIGINT       NOT NULL COMMENT '模板任务ID',
    `config`              json   DEFAULT NULL COMMENT '任务配置',
    `duration`            BIGINT DEFAULT '0' COMMENT '持续时间',
    `created_at`          DATETIME     NOT NULL COMMENT '创建时间',
    `updated_at`          DATETIME     NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='变更集阶段任务(快照)';