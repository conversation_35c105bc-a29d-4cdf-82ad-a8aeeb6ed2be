// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.4
// source: protocol/app/app.proto

package app

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AppService_GetApp_FullMethodName                      = "/app.AppService/GetApp"
	AppService_GetAppInfo_FullMethodName                  = "/app.AppService/GetAppInfo"
	AppService_GetAppBranchList_FullMethodName            = "/app.AppService/GetAppBranchList"
	AppService_GetAppListByIds_FullMethodName             = "/app.AppService/GetAppListByIds"
	AppService_GetAppDeployMsg_FullMethodName             = "/app.AppService/GetAppDeployMsg"
	AppService_GetUserApps_FullMethodName                 = "/app.AppService/GetUserApps"
	AppService_GetUserPreferenceApps_FullMethodName       = "/app.AppService/GetUserPreferenceApps"
	AppService_SearchAppByProjectIdAndName_FullMethodName = "/app.AppService/SearchAppByProjectIdAndName"
	AppService_CreateOrUpdateAppEventlink_FullMethodName  = "/app.AppService/CreateOrUpdateAppEventlink"
	AppService_GetAppEventlink_FullMethodName             = "/app.AppService/GetAppEventlink"
	AppService_GetAppByName_FullMethodName                = "/app.AppService/GetAppByName"
	AppService_SyncCMDBInfo_FullMethodName                = "/app.AppService/SyncCMDBInfo"
)

// AppServiceClient is the client API for AppService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppServiceClient interface {
	GetApp(ctx context.Context, in *AppParam, opts ...grpc.CallOption) (*APP, error)
	GetAppInfo(ctx context.Context, in *GetAppInfoReq, opts ...grpc.CallOption) (*GetAppInfoResp, error)
	GetAppBranchList(ctx context.Context, in *AppParam, opts ...grpc.CallOption) (*AppBranchList, error)
	GetAppListByIds(ctx context.Context, in *AppsReq, opts ...grpc.CallOption) (*AppList, error)
	GetAppDeployMsg(ctx context.Context, in *GetDeployMsgReq, opts ...grpc.CallOption) (*GetDeployMsgResp, error)
	GetUserApps(ctx context.Context, in *GetUserAppsReq, opts ...grpc.CallOption) (*AppList, error)
	GetUserPreferenceApps(ctx context.Context, in *GetUserAppsReq, opts ...grpc.CallOption) (*AppList, error)
	SearchAppByProjectIdAndName(ctx context.Context, in *SearchAppParam, opts ...grpc.CallOption) (*APP, error)
	CreateOrUpdateAppEventlink(ctx context.Context, in *CreateOrUpdateAppEventlinkReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetAppEventlink(ctx context.Context, in *GetAppEventlinkReq, opts ...grpc.CallOption) (*GetAppEventlinkResp, error)
	GetAppByName(ctx context.Context, in *AppByNameReq, opts ...grpc.CallOption) (*APP, error)
	SyncCMDBInfo(ctx context.Context, in *AppByNameReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type appServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppServiceClient(cc grpc.ClientConnInterface) AppServiceClient {
	return &appServiceClient{cc}
}

func (c *appServiceClient) GetApp(ctx context.Context, in *AppParam, opts ...grpc.CallOption) (*APP, error) {
	out := new(APP)
	err := c.cc.Invoke(ctx, AppService_GetApp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetAppInfo(ctx context.Context, in *GetAppInfoReq, opts ...grpc.CallOption) (*GetAppInfoResp, error) {
	out := new(GetAppInfoResp)
	err := c.cc.Invoke(ctx, AppService_GetAppInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetAppBranchList(ctx context.Context, in *AppParam, opts ...grpc.CallOption) (*AppBranchList, error) {
	out := new(AppBranchList)
	err := c.cc.Invoke(ctx, AppService_GetAppBranchList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetAppListByIds(ctx context.Context, in *AppsReq, opts ...grpc.CallOption) (*AppList, error) {
	out := new(AppList)
	err := c.cc.Invoke(ctx, AppService_GetAppListByIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetAppDeployMsg(ctx context.Context, in *GetDeployMsgReq, opts ...grpc.CallOption) (*GetDeployMsgResp, error) {
	out := new(GetDeployMsgResp)
	err := c.cc.Invoke(ctx, AppService_GetAppDeployMsg_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetUserApps(ctx context.Context, in *GetUserAppsReq, opts ...grpc.CallOption) (*AppList, error) {
	out := new(AppList)
	err := c.cc.Invoke(ctx, AppService_GetUserApps_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetUserPreferenceApps(ctx context.Context, in *GetUserAppsReq, opts ...grpc.CallOption) (*AppList, error) {
	out := new(AppList)
	err := c.cc.Invoke(ctx, AppService_GetUserPreferenceApps_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) SearchAppByProjectIdAndName(ctx context.Context, in *SearchAppParam, opts ...grpc.CallOption) (*APP, error) {
	out := new(APP)
	err := c.cc.Invoke(ctx, AppService_SearchAppByProjectIdAndName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) CreateOrUpdateAppEventlink(ctx context.Context, in *CreateOrUpdateAppEventlinkReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AppService_CreateOrUpdateAppEventlink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetAppEventlink(ctx context.Context, in *GetAppEventlinkReq, opts ...grpc.CallOption) (*GetAppEventlinkResp, error) {
	out := new(GetAppEventlinkResp)
	err := c.cc.Invoke(ctx, AppService_GetAppEventlink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) GetAppByName(ctx context.Context, in *AppByNameReq, opts ...grpc.CallOption) (*APP, error) {
	out := new(APP)
	err := c.cc.Invoke(ctx, AppService_GetAppByName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appServiceClient) SyncCMDBInfo(ctx context.Context, in *AppByNameReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AppService_SyncCMDBInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppServiceServer is the server API for AppService service.
// All implementations must embed UnimplementedAppServiceServer
// for forward compatibility
type AppServiceServer interface {
	GetApp(context.Context, *AppParam) (*APP, error)
	GetAppInfo(context.Context, *GetAppInfoReq) (*GetAppInfoResp, error)
	GetAppBranchList(context.Context, *AppParam) (*AppBranchList, error)
	GetAppListByIds(context.Context, *AppsReq) (*AppList, error)
	GetAppDeployMsg(context.Context, *GetDeployMsgReq) (*GetDeployMsgResp, error)
	GetUserApps(context.Context, *GetUserAppsReq) (*AppList, error)
	GetUserPreferenceApps(context.Context, *GetUserAppsReq) (*AppList, error)
	SearchAppByProjectIdAndName(context.Context, *SearchAppParam) (*APP, error)
	CreateOrUpdateAppEventlink(context.Context, *CreateOrUpdateAppEventlinkReq) (*emptypb.Empty, error)
	GetAppEventlink(context.Context, *GetAppEventlinkReq) (*GetAppEventlinkResp, error)
	GetAppByName(context.Context, *AppByNameReq) (*APP, error)
	SyncCMDBInfo(context.Context, *AppByNameReq) (*emptypb.Empty, error)
	mustEmbedUnimplementedAppServiceServer()
}

// UnimplementedAppServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppServiceServer struct {
}

func (UnimplementedAppServiceServer) GetApp(context.Context, *AppParam) (*APP, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApp not implemented")
}
func (UnimplementedAppServiceServer) GetAppInfo(context.Context, *GetAppInfoReq) (*GetAppInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppInfo not implemented")
}
func (UnimplementedAppServiceServer) GetAppBranchList(context.Context, *AppParam) (*AppBranchList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppBranchList not implemented")
}
func (UnimplementedAppServiceServer) GetAppListByIds(context.Context, *AppsReq) (*AppList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppListByIds not implemented")
}
func (UnimplementedAppServiceServer) GetAppDeployMsg(context.Context, *GetDeployMsgReq) (*GetDeployMsgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppDeployMsg not implemented")
}
func (UnimplementedAppServiceServer) GetUserApps(context.Context, *GetUserAppsReq) (*AppList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserApps not implemented")
}
func (UnimplementedAppServiceServer) GetUserPreferenceApps(context.Context, *GetUserAppsReq) (*AppList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserPreferenceApps not implemented")
}
func (UnimplementedAppServiceServer) SearchAppByProjectIdAndName(context.Context, *SearchAppParam) (*APP, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAppByProjectIdAndName not implemented")
}
func (UnimplementedAppServiceServer) CreateOrUpdateAppEventlink(context.Context, *CreateOrUpdateAppEventlinkReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateAppEventlink not implemented")
}
func (UnimplementedAppServiceServer) GetAppEventlink(context.Context, *GetAppEventlinkReq) (*GetAppEventlinkResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppEventlink not implemented")
}
func (UnimplementedAppServiceServer) GetAppByName(context.Context, *AppByNameReq) (*APP, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppByName not implemented")
}
func (UnimplementedAppServiceServer) SyncCMDBInfo(context.Context, *AppByNameReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncCMDBInfo not implemented")
}
func (UnimplementedAppServiceServer) mustEmbedUnimplementedAppServiceServer() {}

// UnsafeAppServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppServiceServer will
// result in compilation errors.
type UnsafeAppServiceServer interface {
	mustEmbedUnimplementedAppServiceServer()
}

func RegisterAppServiceServer(s grpc.ServiceRegistrar, srv AppServiceServer) {
	s.RegisterService(&AppService_ServiceDesc, srv)
}

func _AppService_GetApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetApp(ctx, req.(*AppParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetAppInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetAppInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetAppInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetAppInfo(ctx, req.(*GetAppInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetAppBranchList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetAppBranchList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetAppBranchList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetAppBranchList(ctx, req.(*AppParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetAppListByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetAppListByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetAppListByIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetAppListByIds(ctx, req.(*AppsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetAppDeployMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeployMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetAppDeployMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetAppDeployMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetAppDeployMsg(ctx, req.(*GetDeployMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetUserApps_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAppsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetUserApps(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetUserApps_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetUserApps(ctx, req.(*GetUserAppsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetUserPreferenceApps_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAppsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetUserPreferenceApps(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetUserPreferenceApps_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetUserPreferenceApps(ctx, req.(*GetUserAppsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_SearchAppByProjectIdAndName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAppParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).SearchAppByProjectIdAndName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_SearchAppByProjectIdAndName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).SearchAppByProjectIdAndName(ctx, req.(*SearchAppParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_CreateOrUpdateAppEventlink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateAppEventlinkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).CreateOrUpdateAppEventlink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_CreateOrUpdateAppEventlink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).CreateOrUpdateAppEventlink(ctx, req.(*CreateOrUpdateAppEventlinkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetAppEventlink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppEventlinkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetAppEventlink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetAppEventlink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetAppEventlink(ctx, req.(*GetAppEventlinkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_GetAppByName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppByNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).GetAppByName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_GetAppByName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).GetAppByName(ctx, req.(*AppByNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppService_SyncCMDBInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppByNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServiceServer).SyncCMDBInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppService_SyncCMDBInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServiceServer).SyncCMDBInfo(ctx, req.(*AppByNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AppService_ServiceDesc is the grpc.ServiceDesc for AppService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "app.AppService",
	HandlerType: (*AppServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetApp",
			Handler:    _AppService_GetApp_Handler,
		},
		{
			MethodName: "GetAppInfo",
			Handler:    _AppService_GetAppInfo_Handler,
		},
		{
			MethodName: "GetAppBranchList",
			Handler:    _AppService_GetAppBranchList_Handler,
		},
		{
			MethodName: "GetAppListByIds",
			Handler:    _AppService_GetAppListByIds_Handler,
		},
		{
			MethodName: "GetAppDeployMsg",
			Handler:    _AppService_GetAppDeployMsg_Handler,
		},
		{
			MethodName: "GetUserApps",
			Handler:    _AppService_GetUserApps_Handler,
		},
		{
			MethodName: "GetUserPreferenceApps",
			Handler:    _AppService_GetUserPreferenceApps_Handler,
		},
		{
			MethodName: "SearchAppByProjectIdAndName",
			Handler:    _AppService_SearchAppByProjectIdAndName_Handler,
		},
		{
			MethodName: "CreateOrUpdateAppEventlink",
			Handler:    _AppService_CreateOrUpdateAppEventlink_Handler,
		},
		{
			MethodName: "GetAppEventlink",
			Handler:    _AppService_GetAppEventlink_Handler,
		},
		{
			MethodName: "GetAppByName",
			Handler:    _AppService_GetAppByName_Handler,
		},
		{
			MethodName: "SyncCMDBInfo",
			Handler:    _AppService_SyncCMDBInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protocol/app/app.proto",
}
