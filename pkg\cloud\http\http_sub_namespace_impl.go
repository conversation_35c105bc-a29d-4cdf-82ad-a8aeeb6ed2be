package http

import (
	"context"
	"errors"

	"52tt.com/cicd/pkg/cloud/http/core"
	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
)

// https://q9jvw0u5f5.feishu.cn/wiki/wikcn4wuf4LoKd1gpDFO8D5tOFh
const (
	subNamespaceCreate = "/cicd/cluster/sub-namespace/create"
	subNamespaceGet    = "/cicd/cluster/sub-namespace/get"
)

func NewSubNamespaceHTTPClient(host, token string) core.SubNamespaceClient {
	session := buildSession(token)
	client := NewSubNamespaceHTTPClientWithSession(host, token, session)
	return client
}

func NewSubNamespaceHTTPClientWithSession(host, token string, session httpclient.Session) core.SubNamespaceClient {
	return &subNSHTTPClient{
		Host:    host,
		Token:   token,
		session: session,
	}
}

type subNSHTTPClient struct {
	Host    string
	Token   string
	session httpclient.Session
}

func (c *subNSHTTPClient) CreateSubNamespace(ctx context.Context, in *core.CreateSubNamespaceRequest) (*core.CreateSubNamespaceResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (c *subNSHTTPClient) GetSubNamespace(ctx context.Context, in *core.GetSubNamespaceRequest) (*core.GetSubNamespaceResponse, error) {
	url := buildURL(c.Host, subNamespaceGet)

	log.DebugWithCtx(ctx, "[GetSubNamespace] req=%+v", in)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: in})
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetSubNamespace] init cloud ceq, error: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Get(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetSubNamespace] send req get sub namespace, error: %v, cluster=%v", err, in.Cluster)
		return nil, err
	}

	var out core.GetSubNamespaceResponse
	if err = httpResp.JsonToStruct(&out); err != nil {
		log.ErrorWithCtx(ctx, "[GetSubNamespace] parse response, error: %v", err)
		return nil, err
	}
	if out.Code != 0 {
		log.ErrorWithCtx(ctx, "[GetSubNamespace] create resource failed, return code is not zero, req=%+v, resp=%+v", in, out)
		return nil, errors.New(out.Message)
	}

	log.DebugWithCtx(ctx, "[GetSubNamespace] resp=%+v", out)
	return &out, nil
}
