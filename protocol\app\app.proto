syntax = "proto3";
package app;
option go_package = "protocol/app";
import "google/protobuf/empty.proto";

message APP {
  int64 id = 1;
  string name = 2;
  repeated int64 owners = 3;
  string buildPath = 4;
  string repoAddr = 5;
  string langName = 6;
  string langVersion = 7;
  int64  projectID = 8;
  string description = 9;
  string cmdbId = 10;
  string ProjectName = 11;
  string senvStatus=12;
}

message AppParam{
  int64 id = 1;
  string branchSearch = 2;
  string regex = 3;
}

message SearchAppParam{
  int64 project_id = 1;
  string name = 2;
}

message AppBranchList{
  repeated string branchList = 1;
}

message AppsReq {
  repeated int64 id = 1;
  int64 project_id = 2;
}

message AppList {
  repeated APP apps = 1;
}

message GetDeployMsgReq {
  int64 id = 1;
  string env_type=2;
}

message GetDeployMsgResp {
  string level = 1;
  map<string, string> matchLabels = 2;
  map<string, string> serviceLabels = 3;
  //@gotags: json:"projectId"
  int64 project_id = 4;
  // cmdb id
  string cmdb_id = 5;
  // 开发语言
  string lang_name = 6;
  // 应用名称
  string app_name = 7;
  // 标准化Labels 环境配置
  string stand_label_envs=8;
  // 关联的动态配置
  repeated DynamicConfig dynamic_configs = 9;
}


message DynamicConfig {
  int64 id = 1;
  string file_name = 2;
  string apollo_ns = 3;
  string env_type = 4;
  bool is_global = 5;
}

message GetAppInfoReq {
  int64 app_id = 1 [json_name='appID'];
}

message GetAppInfoResp {
  message ProjectInfo {
    int64 id = 1;
    string name = 2;
    string type = 3;
    string description = 4;
    string identity = 5;
    string canaryDeployNotifyStatus=6;
  }

  APP app = 1;
  ProjectInfo Project = 2;
}

message GetUserAppsReq {
  int64 user_id = 1 [json_name="userId"];
  repeated int64 project_ids = 2 [json_name="projectIds"];
  string app_name = 3 [json_name="appName"];
}

message CreateOrUpdateAppEventlinkReq {
  int64 app_id = 1;
  string app_name = 2;
  int64 project_id = 3;
  string env = 4;
  string consumer_type = 5;
  string producer_type = 6;
}

message GetAppEventlinkReq {
  int64 app_id = 1;
  string env = 2;
}

message GetAppEventlinkResp {
  int64 app_id = 1;
  string app_name = 2;
  int64 project_id = 3;
  string env = 4;
  string consumer_type = 5;
  string producer_type = 6;
}

message AppByNameReq {
  string name = 1;
  string projectName = 2;
}

service AppService {
  rpc GetApp(AppParam) returns(APP);
  rpc GetAppInfo(GetAppInfoReq) returns(GetAppInfoResp);
  rpc GetAppBranchList(AppParam) returns(AppBranchList);
  rpc GetAppListByIds(AppsReq) returns(AppList);
  rpc GetAppDeployMsg(GetDeployMsgReq) returns(GetDeployMsgResp);
  rpc GetUserApps(GetUserAppsReq) returns(AppList);
  rpc GetUserPreferenceApps(GetUserAppsReq) returns(AppList);
  rpc SearchAppByProjectIdAndName(SearchAppParam) returns(APP);
  rpc CreateOrUpdateAppEventlink(CreateOrUpdateAppEventlinkReq) returns(google.protobuf.Empty);
  rpc GetAppEventlink(GetAppEventlinkReq) returns(GetAppEventlinkResp);
  rpc GetAppByName(AppByNameReq) returns(APP);
  rpc SyncCMDBInfo(AppByNameReq) returns(google.protobuf.Empty);
}
