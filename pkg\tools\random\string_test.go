package random

import (
	"testing"
)

func TestGenerateRandomAsciiString(t *testing.T) {
	length := 10
	str := GenerateRandomAlphaNumericString(length)
	if len(str) != length {
		t.Errorf("expect string length equal %d, but got %d", length, len(str))
	}
	for _, c := range str {
		if !((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9')) {
			t.Errorf("String contains non-alphanumeric character: %v", str)
			break
		}
	}
}
