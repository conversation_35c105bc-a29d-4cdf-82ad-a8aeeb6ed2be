//go:generate mockgen -destination=cloud_mock.go -package=cloud -source=cloud.go
package cloud

import (
	"context"
	"fmt"

	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/tools"
	"github.com/mitchellh/mapstructure"
)

const (
	ClusterList             = "/cicd/cluster/list"
	NamespaceList           = "/cicd/cluster/namespace/list"
	TrafficMarking          = "/cicd/cluster/sub-namespace/get"
	NameSpaceIsExist        = "/cicd/cluster/namespace/exist"
	SubEnvCreate            = "/cicd/cluster/sub-namespace/create"
	ClusterResource         = "/cicd/cluster/namespace/resource-name/list"
	RouteAdd                = "/cicd/cluster/namespace/route/add"
	CreateSubEnvTrafficMark = "/cicd/cluster/namespace/traffic-mark/add"
	UpdateSubEnvTrafficMark = "/cicd/cluster/namespace/traffic-mark/update"
	DeleteSubEnvTrafficMark = "/cicd/cluster/namespace/traffic-mark/delete"
	CopyResource            = "/cicd/cluster/namespace/resource/copy"
)

var _ Service = (*httpClient)(nil)

type httpClient struct {
	Host    string
	Token   string
	session httpclient.Session
}

type Option interface{ Apply(*httpClient) }

func NewServiceClient(host string, token string) Service {
	session := httpclient.NewSession(&httpclient.SessionOption{})
	headers := map[string]string{
		"X-TOKEN": token,
	}
	session.SetHeaders(headers)
	client := &httpClient{
		Host:    host,
		Token:   token,
		session: session,
	}
	return client
}

// Service 是容器云平台的接口对应的文档：https://wa653o7n1yo.feishu.cn/docx/T6GBdDPZtoz36WxFk8lcklxznhf
type Service interface {
	// GetClusterList 获取集群列表
	GetClusterList(usersID []string, env string) ([]string, error)
	// GetNamespaceList 获取命名空间列表
	GetNamespaceList(cluster string, usersID []string, nsType string) ([]NamespaceDetail, error)
	// GetSubTrafficMarking 查询子环境及其关联的流量标签
	GetSubTrafficMarking(string, string) (string, error)
	// IsNameSpaceExist 查询某个命名空间是否存在
	IsNameSpaceExist(cluster string, namespace string) (bool, error)
	// CreateSubEnv 新建子环境
	CreateSubEnv(cluster string, originNamespace string, targetNamespace string, userId []string, labels map[string]interface{}) (bool, error)
	// GetClusterResources 获取集群下的资源
	GetClusterResources(cluster string, namespace string, kind string, search string, page int64, size int64) (*ClusterResourceDetail, error)
	// CreateRoute [新增路由] 该接口用于在子环境部署服务后 在基准环境的vs(delegate)配置一条路由
	CreateRoute(cluster string, subNamespace string, serviceName string) (bool, error)
	CreateSubEnvTrafficMark(cluster string, subNamespace string, trafficMark string) (bool, error)
	UpdateSubEnvTrafficMark(cluster string, subNamespace string, trafficMark string) (bool, error)
	DeleteSubEnvTrafficMark(cluster string, subNamespace string) (bool, error)
	CopyClusterResource(cluster string, originNamespace string, targetNamespace string, resources []Resource) (bool, error)
}

func (c *httpClient) buildUrl(path string) string {
	newUrl := c.Host + fmt.Sprintf(path)
	return newUrl
}

func (c *httpClient) GetClusterList(userId []string, env string) ([]string, error) {
	url := c.buildUrl(ClusterList)
	req := map[string]interface{}{
		"userId": userId,
		"env":    env,
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口获取集群列表错误: %v", err)
		return nil, err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("结构化容器云Response 失败: %v", err)
		return nil, err
	}
	if resp.Code != 0 {
		errMsg := fmt.Errorf("查询容器云集群列表失败: %v", resp.Message)
		log.Errorf("%v", errMsg)
		return nil, errMsg
	}
	var result ResDataCloud
	decodeErr := mapstructure.Decode(resp.Data, &result)
	if decodeErr != nil {
		errMsg := fmt.Errorf("请求容器云集群列表错误: %v", decodeErr)
		log.Errorf("%v", errMsg)
		return nil, errMsg
	}
	return result.ClusterList, nil
}

func (c *httpClient) GetNamespaceList(cluster string, userId []string, nsType string) ([]NamespaceDetail, error) {
	url := c.buildUrl(NamespaceList)
	req := map[string]interface{}{
		"cluster": cluster,
		"userId":  userId,
		"type":    nsType,
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口获取集群[%v]命名空间列表错误: %v", cluster, err)
		return nil, err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("结构化容器云Response 失败: %v", err)
		return nil, err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("查询容器云命名空间列表失败: %v", resp.Message)
		log.Errorf("%v", codeErr)
		return nil, codeErr
	}
	var result ResDataCloud
	decodeErr := mapstructure.Decode(resp.Data, &result)
	if decodeErr != nil {
		errMsg := fmt.Errorf("请求容器云命名空间列表错误: %v", decodeErr)
		log.Errorf("%v", errMsg)
		return nil, errMsg
	}
	return result.NamespaceList, nil
}

func (c *httpClient) GetClusterResources(cluster string, namespace string, kind string, search string, page int64, size int64) (*ClusterResourceDetail, error) {
	url := c.buildUrl(ClusterResource)
	req := map[string]interface{}{
		"cluster":   cluster,
		"namespace": namespace,
		"kind":      tools.FirstUpper(kind),
		"search":    search,
	}
	if page != 0 {
		req["page"] = page
	}
	if size != 0 {
		req["size"] = size
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Get(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口获取集群[%s]命名空间[%s]资源名[%s]资源错误: %v", cluster, namespace, kind, err)
		return nil, err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("结构化容器云Response 失败: %v", err)
		return nil, err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("查询容器云接口获取集群[%s]命名空间[%s]资源名[%s]资源错误: %v", cluster, namespace, kind, resp.Message)
		log.Errorf("%v", codeErr)
		return nil, codeErr
	}
	var result ClusterResourceDetail
	decodeErr := mapstructure.Decode(resp.Data, &result)
	if decodeErr != nil {
		errMsg := fmt.Errorf("序列化容器云接口获取集群[%s]命名空间[%s]资源名[%s]列表错误: %v", cluster, namespace, kind, decodeErr)
		log.Errorf("%v", errMsg)
		return nil, errMsg
	}
	return &result, nil
}

func (c *httpClient) GetSubTrafficMarking(cluster string, namespace string) (string, error) {
	url := c.buildUrl(TrafficMarking)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithMapParams{
		"cluster":   cluster,
		"namespace": namespace,
	})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return "", err
	}
	httpResp, err := c.session.Get(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口获取子环境关联的流量标签错误: %v", err)
		return "", err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("结构化容器云Response 失败: %v", err)
		return "", err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("查询容器云子环境关联的流量标签失败: %v", resp.Message)
		log.Errorf("%v", codeErr)
		return "", codeErr
	}
	result, _ := resp.Data.(map[string]interface{})
	//获取返回的集群列表
	trafficMarking, exists := result["trafficMarking"]
	if exists {
		return trafficMarking.(string), nil
	} else {
		return "", fmt.Errorf("请求容器云接口获取子环境关联的流量标签错误")
	}
}

func (c *httpClient) IsNameSpaceExist(cluster string, namespace string) (bool, error) {
	url := c.buildUrl(NameSpaceIsExist)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithMapParams{
		"cluster":   cluster,
		"namespace": namespace,
	})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return false, err
	}
	httpResp, err := c.session.Get(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口：查询集群[%v]下命名空间[%v]是否存在，发生错误: %v", cluster, namespace, err)
		return false, err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("结构化容器云Response 失败: %v", err)
		return false, err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("获取容器云接口数据：查询集群[%v]下命名空间[%v]是否存在，发生错误: %v", cluster, namespace, resp.Message)
		log.Errorf("%v", codeErr)
		return false, codeErr
	}
	result, _ := resp.Data.(map[string]interface{})
	exist, exists := result["exist"]
	if exists {
		return exist.(bool), nil
	} else {
		return false, fmt.Errorf("请求容器云接口查询集群[%v]下命名空间[%v]是否存在，获取结果resp.data[exist]发生错误", cluster, namespace)
	}
}

func (c *httpClient) CreateSubEnv(cluster string, originNamespace string, targetNamespace string, userId []string, labels map[string]interface{}) (bool, error) {
	labels["mesh.quwan.io/is-sub-namespace"] = true
	labels["mesh.quwan.io/parent-namespace"] = originNamespace
	url := c.buildUrl(SubEnvCreate)
	req := map[string]interface{}{
		"cluster":         cluster,
		"originNamespace": originNamespace,
		"targetNamespace": targetNamespace,
		"userId":          userId,
		"labels":          labels,
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return false, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口创建子环境：集群名[%v]，基准命名空间[%v]，子环境命名空间[%v]，发生错误: %v", cluster, originNamespace, targetNamespace, err)
		return false, err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("结构化容器云Response 失败: %v", err)
		return false, err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("获取容器云接口创建子环境：集群名[%v]，基准命名空间[%v]，子环境命名空间[%v]，结果发生错误: %v", cluster, originNamespace, targetNamespace, resp.Message)
		log.Errorf("%v", codeErr)
		return false, codeErr
	}
	return true, nil
}

func (c *httpClient) CreateRoute(cluster string, subNamespace string, serviceName string) (bool, error) {
	url := c.buildUrl(RouteAdd)
	req := map[string]interface{}{
		"cluster":      cluster,
		"subNamespace": subNamespace,
		"serviceName":  serviceName,
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return false, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口创建子环境路由：集群名[%v]，子环境命名空间[%v]，服务名[%v]，发生错误: %v", cluster, subNamespace, serviceName, err)
		return false, err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("创建子环境路由结构化容器云Response 失败: %v", err)
		return false, err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("获取容器云接口创建子环境路由：集群名[%v]，子环境命名空间[%v]，服务名[%v]，结果发生错误: %v", cluster, subNamespace, serviceName, resp.Message)
		log.Errorf("%v", codeErr)
		return false, codeErr
	}
	return true, nil
}

func (c *httpClient) CreateSubEnvTrafficMark(cluster string, subNamespace string, trafficMark string) (bool, error) {
	url := c.buildUrl(CreateSubEnvTrafficMark)
	req := map[string]interface{}{
		"cluster":      cluster,
		"subNamespace": subNamespace,
		"trafficMark":  trafficMark,
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return false, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口新建子环境流量标记：集群名[%v]，子环境命名空间[%v]，流量标记[%v]，发生错误: %v", cluster, subNamespace, trafficMark, err)
		return false, err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("新建子环境流量标记结构化容器云Response 失败: %v", err)
		return false, err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("获取容器云接口新建子环境流量标记：集群名[%v]，子环境命名空间[%v]，流量标记[%v]，结果发生错误: %v", cluster, subNamespace, trafficMark, resp.Message)
		log.Errorf("%v", codeErr)
		return false, codeErr
	}
	return true, nil
}

func (c *httpClient) UpdateSubEnvTrafficMark(cluster string, subNamespace string, trafficMark string) (bool, error) {
	url := c.buildUrl(UpdateSubEnvTrafficMark)
	req := map[string]interface{}{
		"cluster":      cluster,
		"subNamespace": subNamespace,
		"trafficMark":  trafficMark,
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return false, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口更新子环境流量标记：集群名[%v]，子环境命名空间[%v]，流量标记[%v]，发生错误: %v", cluster, subNamespace, trafficMark, err)
		return false, err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("更新子环境流量标记结构化容器云Response 失败: %v", err)
		return false, err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("获取容器云接口更新子环境流量标记：集群名[%v]，子环境命名空间[%v]，流量标记[%v]，结果发生错误: %v", cluster, subNamespace, trafficMark, resp.Message)
		log.Errorf("%v", codeErr)
		return false, codeErr
	}
	return true, nil
}

func (c *httpClient) DeleteSubEnvTrafficMark(cluster string, subNamespace string) (bool, error) {
	url := c.buildUrl(DeleteSubEnvTrafficMark)
	req := map[string]interface{}{
		"cluster":      cluster,
		"subNamespace": subNamespace,
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return false, err
	}
	httpResp, err := c.session.Delete(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口删除子环境流量标记：集群名[%v]，子环境命名空间[%v]，发生错误: %v", cluster, subNamespace, err)
		return false, err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("删除子环境流量标记结构化容器云Response 失败: %v", err)
		return false, err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("获取容器云接口删除子环境流量标记：集群名[%v]，子环境命名空间[%v]，结果发生错误: %v", cluster, subNamespace, resp.Message)
		log.Errorf("%v", codeErr)
		return false, codeErr
	}
	return true, nil
}

func (c *httpClient) CopyClusterResource(cluster string, originNamespace string, targetNamespace string, resources []Resource) (bool, error) {
	url := c.buildUrl(CopyResource)
	req := map[string]interface{}{
		"cluster":         cluster,
		"originNamespace": originNamespace,
		"targetNamespace": targetNamespace,
		"resources":       resources,
	}
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.Errorf("初始化容器云请求参数错误: %v", err)
		return false, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.Errorf("请求容器云接口复制资源：集群名[%v]，基准命名空间[%v]，子环境命名空间[%v]，发生错误: %v", cluster, originNamespace, targetNamespace, err)
		return false, err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.Errorf("容器云接口复制资源结构化容器云Response 失败: %v", err)
		return false, err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("获取容器云接口复制资源：集群名[%v]，基准命名空间[%v]，子环境命名空间[%v]，发生错误: %v", cluster, originNamespace, targetNamespace, resp.Message)
		log.Errorf("%v", codeErr)
		return false, codeErr
	} else {
		log.Infof("获取容器云接口复制资源：集群名[%v]，基准命名空间[%v]，子环境命名空间[%v]，资源[%v]，成功", cluster, originNamespace, targetNamespace, resources)
	}
	return true, nil
}

type Resource struct {
	Kind          string   `json:"kind"`
	ResourceNames []string `json:"resourceNames"`
}

type Response struct {
	Code    int         `json:"code,omitempty"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

type ResDataCloud struct {
	ClusterList   []string          `json:"clusterList"`
	NamespaceList []NamespaceDetail `json:"namespaceList"`
}

type NamespaceDetail struct {
	Namespace      string `json:"namespace"`
	TrafficMarking string `json:"trafficMarking"`
	Describe       string `json:"describe"`
}

type ClusterResourceDetail struct {
	List  []string `json:"list"`
	Page  int64    `json:"page"`
	Size  int64    `json:"size"`
	Total int64    `json:"total"`
}
