# -*- coding:utf-8 -*-
import json
import logging
import os

import pymysql
from dotenv import load_dotenv

logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s]:[%(levelname)s]%(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)


def get_db_connection():
    host = os.getenv("MYSQL_HOST")
    password = os.getenv("MYSQL_PASSWORD")
    username = os.getenv("MYSQL_USERNAME")
    dbname = os.getenv("MYSQL_DBNAME")
    conn = pymysql.connect(host=host, user=username, password=password, database=dbname, port=3306, charset='utf8')

    return conn


def get_all_has_labels_data(cursor):
    query_sql = "select id, app_advanced_config from deploy_config where json_length(app_advanced_config->'$.labels') != 0"
    cursor.execute(query_sql)
    results = cursor.fetchall()

    return results


def try_build_update_config_sql(result):
    id_, config_ = result
    if config_ == '':
        return

    config = json.loads(config_)
    labels = config.get('labels')
    if not labels:
        return

    new_labels = list()
    num = 0
    for label in labels:
        if label.get('key') in ['type', 'lang', 'env', 'uuid', 'cluster_id']:
            num += 1
            continue

        new_labels.append(label)
    if num == 0:
        return ""

    logging.info("id=%d, config src_labels len is %d, dst_labels len is %d, removed labels is %d",
                 id_, len(labels), len(new_labels), num)

    config['labels'] = new_labels
    update_sql = "update deploy_config set app_advanced_config = '{}' where id = {}".format(json.dumps(config), id_)
    logging.info("id=%d, src config is '%s'", id_, config_)
    logging.info("id=%d, dst config is '%s'", id_, json.dumps(config))
    return update_sql


def update_config(cursor, updates_sql):
    for update_sql in updates_sql:
        cursor.execute(update_sql)


def main():
    load_dotenv(verbose=True)

    conn = get_db_connection()
    cursor = conn.cursor()

    updates_sql = list()
    results = get_all_has_labels_data(cursor)
    for result in results:
        update_sql = try_build_update_config_sql(result)
        if not update_sql:
            continue

        updates_sql.append(update_sql)

    logging.info("updates_sql len is %d", len(updates_sql))
    update_config(cursor, updates_sql[:2])

    conn.commit()
    cursor.close()
    conn.close()


if __name__ == '__main__':
    main()
