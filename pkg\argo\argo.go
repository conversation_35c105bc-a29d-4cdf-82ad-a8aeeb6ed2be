//go:generate mockgen -destination=argo_mock.go -package=argo -source=argo.go
package argo

import (
	"context"
)

const (
	defaultAppNamespace = `argocd`
	defaultProject      = `default`
)

// ArgoClient argo client interface
type ArgoClient interface {
	// DeleteApplication 删除 argo application, alias of argocd app delete
	//
	// 详细可见: https://argo-cd.readthedocs.io/en/stable/user-guide/app_deletion/
	DeleteApplication(context.Context, *ApplicationDeleteRequest) (*ApplicationResponse, error)
}
