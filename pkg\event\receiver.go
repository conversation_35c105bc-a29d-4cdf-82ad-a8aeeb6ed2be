//go:generate mockgen -destination=receiver_mock.go -package=event -source=receiver.go
package event

import (
	"context"
	"time"

	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/kafka"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/safego"
	"52tt.com/cicd/pkg/tools"
)

type Closer interface {
	Close(ctx context.Context) error
}

type Receiver interface {
	Closer
	StartReceiver(ctx context.Context)
	RegisterListener(listeners ...Listener)
}

type receiver struct {
	kafka.Consumer
	listeners []Listener
	serName   string
	topics    string
	groupID   string
}

func newReceiver(cfg *kafka.Config, topics string) (rcv *receiver, err error) {
	rcv = &receiver{
		serName: cfg.Name,
		topics:  topics,
		groupID: cfg.GetGroupID(),
	}
	isOffsetNewest := cfg.Consumer.Offsets == "latest"
	rcv.Consumer = *kafka.NewConsumer(cfg.<PERSON>roke<PERSON>, rcv.topics, rcv.groupID, rcv.serName, isOffsetNewest, rcv.kfkMsgCB)
	return
}

func NewReceiver(cfg *kafka.Config) (Receiver, error) {
	return newReceiver(cfg, cfg.GetConsumerTopics()[0])
}

func NewReceiverWithTopicIndex(cfg *kafka.Config, idx int64) (Receiver, error) {
	return newReceiver(cfg, cfg.GetConsumerTopics()[idx])
}

func NewSubReceiver(cfg *kafka.Config) (Receiver, error) {
	return newReceiver(cfg, cfg.GetSubTopics()[0])
}

func (c *receiver) kfkMsgCB(msg kafka.Event) (err error, b bool) {
	ctx := cctx.WithRequestInfo(context.Background(), &msg.ReqInfo)
	for _, listener := range c.listeners {
		isListenerSubscribedEvent := tools.Any(listener.GetSubscribeEvents(), func(subscribeEvent string) bool {
			return msg.Type() == subscribeEvent
		})
		if !isListenerSubscribedEvent {
			continue
		}

		log.Infof("Kafka msg callback  [%s] topic=%s groupId=%s  type=%s  msgUid=%s SubscribeEvents %v ;",
			c.serName, c.topics, c.groupID, msg.Types, msg.MsgId, listener.GetSubscribeEvents())

		c.HandleWithOut(ctx, msg, listener)
	}
	return
}

func (c *receiver) HandleWithOut(ctx context.Context, msg kafka.Event, listener Listener) {
	done := make(chan struct{}, 1)
	safego.Go(func() {
		err := listener.HandleEvent(ctx, msg)
		if err != nil {
			log.ErrorWithCtx(ctx, "Kafka msg callback listener %v topic=%s groupId=%s  type=%s  msgUid=%s, HandleEvent Err %v",
				listener.GetSubscribeEvents(), c.topics, c.groupID, msg.Types, msg.MsgId, err)
		}
		done <- struct{}{}
	})

	select {
	case <-done:
		break
	case <-time.After(time.Second * 10):
		log.ErrorWithCtx(ctx, "Kafka msg callback listener %v  topic=%s groupId=%s  type=%s  msgUid=%s,HandleEvent time out. data %v",
			listener.GetSubscribeEvents(), c.topics, c.groupID, msg.Types, msg.MsgId, msg)
		break
	}
}

func (c *receiver) Close(ctx context.Context) (err error) {
	c.Consumer.Stop()
	return
}

func (c *receiver) StartReceiver(ctx context.Context) {
	c.Consumer.Start()
}

func (c *receiver) RegisterListener(listeners ...Listener) {
	c.listeners = append(c.listeners, listeners...)
}
