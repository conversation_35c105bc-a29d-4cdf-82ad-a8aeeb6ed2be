CREATE TABLE `message_sender` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `title` varchar(512) COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `type` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息类型',
  `content` varchar(2048) COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息内容',
  `receiver` varchar(512) COLLATE utf8mb4_general_ci NOT NULL COMMENT '接收者',
  `cc_receiver` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '抄送人',
  `send_type` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息发送类型(飞书/邮箱/钉钉)',
  `status` tinyint NOT NULL COMMENT '消息状态(0失败,1成功)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='消息发送记录信息表';
