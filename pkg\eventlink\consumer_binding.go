package eventlink

import (
	"context"
	"encoding/json"

	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type ConsumerBinding struct {
	v1.TypeMeta `json:",inline"`
	// +optional
	v1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	Spec          CBSpec   `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`
	Status        CBStatus `json:"status"`
}

func (cb *ConsumerBinding) SetType() {
	cb.TypeMeta = cbType
	return
}

type CBStatus struct {
	AccessMode      string `json:"accessMode"`
	SubscriberReady bool   `json:"subscriberReady"`
	Unbinding       bool   `json:"unbinding"`
}

type CBSpec struct {
	OverwriteGroupName string `json:"overwriteGroupName"`
	TopicBinding       string `json:"topicBinding"`
	CBProcessor        `json:"processor"`
	*CBOptions         `json:"options,omitempty" `
	*CBStrategy        `json:"strategy,omitempty"`
}

type CBProcessor struct {
	Target string `json:"target"`
}

type CBOptions struct {
	OptionsKafka `json:"kafka,omitempty"`
}

type OptionsKafka struct {
	Version string `json:"version,omitempty"`
}

type CBStrategy struct {
	ParallelConsume `json:"parallelConsume,omitempty"`
}

type ParallelConsume struct {
	BatchNum      int  `json:"batchNum,omitempty"`
	ConcurrentNum int  `json:"concurrentNum,omitempty"`
	Enable        bool `json:"enable,omitempty"`
}

var cbType = v1.TypeMeta{
	Kind:       "ConsumerBinding",
	APIVersion: "event.tt.io/v1alpha2",
}

func (c *eventLinkImpl) CreatCB(ctx context.Context, cluster, ns string, req ConsumerBinding) (err error) {
	req.SetType()
	data, err := json.Marshal(req)
	if err != nil {
		return
	}
	_, err = c.cloudClient.Apply(ctx, &constack.ApplyRequest{
		Cluster:   cluster,
		Namespace: ns,
		Data:      string(data),
	})
	if err != nil {
		return
	}
	return
}

func (c *eventLinkImpl) GetCB(ctx context.Context, cluster, ns, name string) (data string, resp ConsumerBinding, has bool, err error) {
	crd, err := c.cloudClient.Get(ctx, &constack.GetRequest{
		Cluster:   cluster,
		Namespace: ns,
		Name:      name,
		GroupVersionResource: &constack.GroupVersionResource{
			Group:    "event.tt.io",
			Version:  "v1alpha2",
			Resource: "consumerbindings",
		},
	})
	if err != nil {
		if status.Code(err) == codes.NotFound {
			err = nil
			return
		}
		return
	}

	err = json.Unmarshal([]byte(crd.Data), &resp)
	if err != nil {
		return
	}
	has = true
	data = crd.Data
	return
}

func (c *eventLinkImpl) DelCB(ctx context.Context, cluster, ns, name string) (err error) {
	_, err = c.cloudClient.Delete(ctx, &constack.DeleteRequest{
		Cluster:   cluster,
		Namespace: ns,
		Name:      name,
		GroupVersionResource: &constack.GroupVersionResource{
			Group:    "event.tt.io",
			Version:  "v1alpha2",
			Resource: "consumerbindings",
		},
	})
	if err != nil {
		return
	}
	return
}
