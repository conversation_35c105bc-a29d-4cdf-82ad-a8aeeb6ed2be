package db

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type EMPTY struct{}
type JSON json.RawMessage

func EmptyJson() []byte {
	bytes, _ := json.Marshal(EMPTY{})
	return bytes
}

func (j *JSON) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("Failed to unmarshal JSONB value:", value))
	}

	result := json.RawMessage{}
	err := json.Unmarshal(bytes, &result)
	*j = JSON(result)
	return err
}

func (j JSON) Value() (driver.Value, error) {
	if len(j) == 0 {
		return nil, nil
	}
	return json.RawMessage(j).MarshalJSON()
}
func (JSON) GormDataType() string {
	return "json"
}

func (j JSON) MarshalJSON() ([]byte, error) {
	if len(j) == 0 {
		return nil, nil
	}
	return json.RawMessage(j).MarshalJSON()
}

func (j JSON) GormValue(ctx context.Context, db *gorm.DB) clause.Expr {
	return clause.Expr{
		SQL:  "?",
		Vars: []interface{}{string(j)},
	}
}

type BaseModel struct {
	ID        int64     `json:"id" sql:"AUTO_INCREMENT" gorm:"primary_key,column:id"`
	CreatedAt time.Time `json:"createdAt" gorm:"column:created_at" sql:"DEFAULT:current_timestamp"`
	UpdatedAt time.Time `json:"updatedAt" gorm:"column:updated_at" sql:"DEFAULT:current_timestamp"`
}

func (model BaseModel) BeforeCreate(_ *gorm.DB) error {
	now := time.Now().Local()
	model.CreatedAt = now
	model.UpdatedAt = now
	return nil
}

func (model BaseModel) BeforeUpdate(_ *gorm.DB) error {
	now := time.Now().Local()
	model.UpdatedAt = now
	return nil
}

func (model BaseModel) IDString() string {
	return strconv.FormatInt(model.ID, 10)
}
