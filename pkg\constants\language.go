package constants

import "strings"

type Language string

const (
	GO     Language = "GO"
	PYTHON Language = "PYTHON"
	NODE   Language = "NODE"
	CPP    Language = "CPP"
	JAVA   Language = "JAVA"
)

func (i Language) String() string {
	return strings.ToLower(string(i))
}

var languageMap = map[string]Language{
	"GO":     GO,
	"PYTHON": PYTHON,
	"NODE":   NODE,
	"CPP":    CPP,
	"JAVA":   JAVA,
}

func TransLan(lan string) Language {
	return languageMap[strings.ToUpper(lan)]
}
