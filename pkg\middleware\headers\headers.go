package headers

import (
	"52tt.com/cicd/pkg/context"
	"github.com/gin-gonic/gin"
	"google.golang.org/grpc/metadata"
	"net/http"
)

func ConvertHeaderToMD() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == http.MethodOptions {
			return
		}

		md := metadata.New(nil)
		for k, v := range c.Request.Header {
			md.Set(k, v...)
		}

		context.WithMDFromGin(c, md)
		c.Next()
	}
}

func AuthContext() gin.HandlerFunc {
	return func(c *gin.Context) {
		context.WithUseridFromGin(c)
		context.WithUsernameFromGin(c)
		context.WithChineseNameFromGin(c)
		context.WithEmployeeNoFromGin(c)
		context.WithRolesFromGin(c)
		context.WithProjectIdFromGin(c)
		c.Next()
	}
}
