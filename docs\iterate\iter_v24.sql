-- 发布计划表
CREATE TABLE `deploy`.`deploy_plan` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
   `project_id` int NOT NULL COMMENT '所属项目id',
   `user_group` varchar(128) NOT NULL default '' COMMENT '所属用户群体',
   `name` varchar(512)  NOT NULL default '' COMMENT '名称',
   `status` varchar(64)  NOT NULL default '' COMMENT '状态 启用：enable 禁用： disable',
   `versions` varchar(1024)  NOT NULL default '' COMMENT '版本号,多个版本号以逗号分隔',
   `operator_by` bigint NOT NULL COMMENT '操作人id',
   `operator_by_chinese_name` varchar(128) NOT NULL default '' COMMENT '操作人中文名',
   `operator_by_employee_no` varchar(128) NOT NULL default '' COMMENT '操作人工号',
   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
   `is_deleted` tinyint DEFAULT '0' COMMENT '是否已删除(逻辑删除),0未删除,1已删除',
   PRIMARY KEY (`id`),
   KEY `idx_project_id` (`project_id`) USING BTREE,
   KEY `idx_user_group` (`user_group`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='发布计划表';

-- 发布计划记录表
CREATE TABLE `deploy`.`deploy_plan_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `deploy_plan_id` bigint NOT NULL COMMENT '所属发布计划id',
    `project_id` int NOT NULL COMMENT '所属项目id',
    `status` varchar(512) NOT NULL default '' COMMENT '金丝雀状态',
    `app_id` bigint NOT NULL COMMENT '服务id',
    `app_name` varchar(512)  NOT NULL default '' COMMENT '服务名称',
    `pipeline_run_id` bigint NOT NULL COMMENT '流水线pipeline_run_id',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_app_id` (`app_id`) USING BTREE,
    KEY `idx_deploy_plan_id` (`deploy_plan_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='发布计划记录表';

-- 项目添加网关标签
alter table `app`.`project`
    add column `user_group` varchar(64) NULL comment '用户群体';


-- 部署配置添加 部署
alter table `deploy`.`deploy_config`
   add column `deploy_type` varchar(32)  NOT NULL default 'TRAFFIC' COMMENT '细分不同的部署方式 [TRAFFIC,CLIENT_VERSION,soso]';

CREATE INDEX idx_meta_id ON `deploy`.`deploy_config` (`metadata_id`);