package kafka

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"github.com/IBM/sarama"

	"52tt.com/cicd/pkg/log"
)

const (
	maxTryTimes = 5
)

// Kafka 消息到达回调
type MessageProcessor func(msg Event) (error, bool)

type Consumer struct {
	name   string // 服务名
	topics []string
	wg     sync.WaitGroup

	handler sarama.ConsumerGroupHandler
	group   sarama.ConsumerGroup
	ctx     context.Context
	cancel  context.CancelFunc
}

func NewConsumer(brokers, topics, groupID, serName string, isOffsetNewest bool, processor MessageProcessor) *Consumer {
	config := sarama.NewConfig()
	config.Version = sarama.V2_8_1_0
	config.Consumer.Offsets.Initial = sarama.OffsetOldest
	if isOffsetNewest {
		config.Consumer.Offsets.Initial = sarama.OffsetNewest
	}
	config.Consumer.Return.Errors = true

	group, err := sarama.NewConsumerGroup(strings.Split(brokers, ","), groupID, config)
	if err != nil {
		errMsg := fmt.Sprintf("kafka Init groupID :%s  Err %v", groupID, err)
		log.Errorf(errMsg)
		panic(errMsg)
	}

	ctx, cancel := context.WithCancel(context.Background())

	handler := &eventHandler{
		name:      serName,
		topics:    topics,
		groupID:   groupID,
		ctx:       ctx,
		processor: processor,
	}

	return &Consumer{
		name:    serName,
		topics:  strings.Split(topics, ","),
		handler: handler,
		group:   group,
		ctx:     ctx,
		cancel:  cancel,
	}
}

func (e *Consumer) Consume() {
	e.wg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("kafka [%s]  Consumer Panic %v", e.name, r)
			}
		}()

		defer func() {
			_ = e.group.Close()
			e.wg.Done()
		}()

		for {
			select {
			case <-e.ctx.Done():
				log.Infof("kafka [%s] consumer group ctx done", e.name)
				return
			default:
				if err := e.group.Consume(e.ctx, e.topics, e.handler); err != nil {
					log.Errorf("kafka [%s] consumer consume failed err: %s\n", e.name, err.Error())
					if errors.Is(err, sarama.ErrClosedConsumerGroup) {
						return
					}
				}
			}
		}
	}()
}

func (e *Consumer) Start() {
	e.Consume()
}

func (e *Consumer) Stop() {
	e.cancel()
	e.wg.Wait()
}

type eventHandler struct {
	name      string
	topics    string
	groupID   string
	ctx       context.Context
	processor MessageProcessor
}

func (e *eventHandler) Setup(session sarama.ConsumerGroupSession) error {
	log.Infof("kafka [%s] setup claims=%v member-id=%s", e.name, session.Claims(), session.MemberID())
	return nil
}

func (e *eventHandler) Cleanup(session sarama.ConsumerGroupSession) error {
	log.Infof("kafka [%s] cleanup claims=%v member-id=%s", e.name, session.Claims(), session.MemberID())
	return nil
}

func (e *eventHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	// NOTE:
	// Do not move the code below to a goroutine.
	// The `ConsumeClaim` itself is called within a goroutine, see:
	// https://github.com/IBM/sarama/blob/main/consumer_group.go#L27-L29

	defer func() {
		r := recover()
		if r != nil {
			log.Errorf("kafka [%s]  claims=%v member-id=%s topic=%s groupId=%s partition=%d  ConsumeClaim Panic %v",
				e.name, session.Claims(), session.MemberID(), claim.Topic(), e.groupID, claim.Partition(), r)
		}
	}()

	log.Infof("kafka [%s] consume claims=%v member-id=%s topic=%s groupId=%s partition=%d",
		e.name, session.Claims(), session.MemberID(), claim.Topic(), e.groupID, claim.Partition())
	for msg := range claim.Messages() {
		log.Infof("kafka [%s][%d:%d] processMessage topic=%s groupId=%s key=%s ",
			e.name, msg.Partition, msg.Offset, msg.Topic, e.groupID, msg.Key)

		println(string(msg.Value))

		evntdata := Event{}
		err := json.Unmarshal(msg.Value, &evntdata)
		if err != nil {
			log.Errorf("kafka [%s][%d:%d] processMessage topic=%s groupId=%s type=%s key=%s Json Unmarshal Err %v",
				e.name, msg.Partition, msg.Offset, msg.Topic, e.groupID, evntdata.Type, msg.Key, err)
			continue
		}
		evntdata.EventTime = msg.Timestamp
		if e.processMessage(msg, &evntdata) {
			session.MarkMessage(msg, "OK")
		}
	}
	return nil
}

func (e *eventHandler) processMessage(msg *sarama.ConsumerMessage, evntdata *Event) bool {
	defer func() {
		err := recover()
		if err == nil {
			return
		}

		var stack string
		var buf bytes.Buffer
		buf.Write(debug.Stack())
		stack = buf.String()

		log.Errorf("kafka processMessage Panic %v %s %s   msgUid:%s", err, "\n", stack, evntdata.MsgId)
	}()

	var (
		triedTimes         int
		shouldCommitOffset = true
	)

	for {
		err, retryable := e.processor(*evntdata)
		if err == nil {
			return shouldCommitOffset
		}

		if !retryable {
			e.logFailedMessage(msg, err)
			return shouldCommitOffset
		}

		triedTimes++
		if triedTimes >= maxTryTimes {
			e.logFailedMessage(msg, err)
			return shouldCommitOffset
		}

		b := backoff(triedTimes)
		select {
		case <-time.After(b):
			continue
		case <-e.ctx.Done():
			shouldCommitOffset = false
			return shouldCommitOffset
		}
	}
}

func (e *eventHandler) logFailedMessage(msg *sarama.ConsumerMessage, err error) {
	log.Errorf("kafka [%s] failed to process msg at %d:%d, topic=%s key=%s  err: %v . value=%s",
		e.name, msg.Partition, msg.Offset, msg.Topic, msg.Key, err, msg.Value)
}

func backoff(retries int) time.Duration {
	const (
		MaxDelay  = 15 * time.Second
		baseDelay = 500 * time.Millisecond
		factor    = 1.1
		jitter    = 0.01
	)

	if retries == 0 {
		return baseDelay
	}
	backoff, max := float64(baseDelay), float64(MaxDelay)
	for backoff < max && retries > 0 {
		backoff *= factor
		retries--
	}
	if backoff > max {
		backoff = max
	}

	backoff *= 1 + jitter*(rand.Float64()*2-1)
	if backoff < 0 {
		return 0
	}
	return time.Duration(backoff)
}
