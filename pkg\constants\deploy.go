package constants

import (
	"fmt"

	constackv1alpha "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
)

type DeployStatus int8

func (s DeployStatus) Value() int8 {
	return int8(s)
}

func (s DeployStatus) String() string {
	switch s {
	case DeployStatusDeploying:
		return "DEPLOY_STATUS_DEPLOYING"
	case DeployStatusSuccessful:
		return "DEPLOY_STATUS_SUCCESSFUL"
	case DeployStatusFailed:
		return "DEPLOY_STATUS_FAILED"
	case DeployStatusRollbacking:
		return "DEPLOY_STATUS_ROLLBACKING"
	case DeployStatusOfflining:
		return "DEPLOY_STATUS_OFFLINING"
	default:
		return "DEPLOY_STATUS_UNKNOWN"
	}
}

const (
	DeployStatusUnknown     DeployStatus = 0 // 未知
	DeployStatusDeploying   DeployStatus = 1 // 部署中
	DeployStatusSuccessful  DeployStatus = 2 // 成功
	DeployStatusFailed      DeployStatus = 3 // 失败
	DeployStatusRollbacking DeployStatus = 4 // 回滚中
	DeployStatusOfflining   DeployStatus = 5 // 下线中
)

func (s DeployStatus) IsFinished() bool {
	return s == DeployStatusFailed || s == DeployStatusSuccessful || s == DeployStatusUnknown
}

const (
	HistoryDeployConfigTemplateTag = -1
)

type DeployConfigType int8

func (t DeployConfigType) ToWorkloadKind() constackv1alpha.WorkloadKind {
	switch t {
	case ConfigTypeGeneral:
		return constackv1alpha.WorkloadKind_DEPLOYMENT
	case ConfigTypeCronJob:
		return constackv1alpha.WorkloadKind_WORKLOAD_KIND_UNKNOWN
	case ConfigTypeStatefulSet:
		return constackv1alpha.WorkloadKind_STATEFUL_SET
	default:
		return constackv1alpha.WorkloadKind_WORKLOAD_KIND_UNKNOWN
	}
}

func (t DeployConfigType) ToKind() string {
	switch t {
	case ConfigTypeGeneral:
		return "Deployment"
	case ConfigTypeCronJob:
		return "CronJob"
	case ConfigTypeStatefulSet:
		return "StatefulSet"
	default:
		return "Unknown"
	}
}

func (t DeployConfigType) TypeName() string {
	switch t {
	case ConfigTypeGeneral:
		return "general"
	case ConfigTypeCronJob:
		return "cron-task"
	case ConfigTypeStatefulSet:
		return "stateful-set"
	default:
		return "unknown"
	}
}

const (
	ConfigTypeGeneral     DeployConfigType = 1
	ConfigTypeCronJob     DeployConfigType = 2
	ConfigTypeStatefulSet DeployConfigType = 3
)

type DeployType string

const (
	DeployTypeDeploy   DeployType = "deploy"
	DeployTypeRollback DeployType = "rollback"
	DeployTypeSimple   DeployType = "simple" // approval 部署类型 短流程
)

type DeployAction int8 // 变更类型：1部署、2回滚、3下线

const (
	DeployActionDeploy   DeployAction = 1
	DeployActionRollback DeployAction = 2
	DeployActionOffline  DeployAction = 3
)

// GenerateGeneralComponentName 生成组件的名字, 默认值是 AppName.
//
// 值得注意的是, 子环境 2.0 生效的时候该值将会变成 {{ .AppName }}-{{ .senv }},
// 主要的原因是如果只是增加了额外的参数传入 Senv 将会导致其他的运维组件无法指向正确的资源,
// 例如 HPA 中的 scaleTargetRef.name 使用的是 general component 的 name,
// 如果只改了 deployment 的 metadata name 将会导致 HPA 无法指向正确的 deployment.
func GenerateGeneralComponentName(appName string, envTarget EnvTargetType, senv string) string {
	if envTarget != SUB_V2 {
		return appName
	}
	return fmt.Sprintf("%s-%s", appName, senv)
}

// GenerateSubEnvV2ArgoAppName 生成子环境 2.0 服务的 Argo Application name
func GenerateSubEnvV2ArgoAppName(appName string, metadataID int64) string {
	return fmt.Sprintf("%s-%d", appName, metadataID)
}

// GenerateBaseEnvArgoAppName 生成基础环境服务的 Argo Application name
func GenerateBaseEnvArgoAppName(appID int64, appName, cluster, namespace string) string {
	res := fmt.Sprintf("%s-%s-%s", appName, cluster, namespace)
	if len(res) > 51 {
		res = fmt.Sprintf("tt%d-%s-%s", appID, cluster, namespace)
	}
	return res
}

// DefaultDeployTimeout 部署默认超时时长是 10 * 60 s
const DefaultDeployTimeout int64 = 10 * 60

// 金丝雀策略，设计遗留和老数据兼容 需要分 发布计划里[1] 和 工单里[2] 两个场景，含义略有不同。
type EnumCanaryPolicy string

const (
	// PolicyTraffic 流量比例[发布计划]  <===>  流量比例（手动放量，无法使用放量计划）[工单]
	PolicyTraffic EnumCanaryPolicy = "TRAFFIC"
	// PolicyClientVersion 客户端版本号[发布计划]  <===>  使用发布计划策略[工单]
	PolicyClientVersion EnumCanaryPolicy = "CLIENT_VERSION"
)

func (p EnumCanaryPolicy) String() string {
	return string(p)
}

type LabelLogo string

const (
	// resource logo 由平台创建/变更后对资源留下的标识
	ResourceLogoLableKey   LabelLogo = "star.constack.io/managed-by"
	ResourceLogoLableValue LabelLogo = "infra"
)
