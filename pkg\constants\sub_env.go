package constants

var SystemSenvArray = []string{"base", "staging", "canary"}

var STAGING = "staging"
var CANARY = "canary"

// EnumSubEnvGroup 子环境分组枚举类型
type EnumSubEnvGroup int8

func (e EnumSubEnvGroup) Value() int8 {
	return int8(e)
}

func (e EnumSubEnvGroup) String() string {
	return ""
}

const (
	// EnumSubEnvGroupAll 所有
	EnumSubEnvGroupAll EnumSubEnvGroup = -1
	// EnumSubEnvGroupNone 无分组
	EnumSubEnvGroupNone EnumSubEnvGroup = 0
	// EnumSubEnvGroupIntegrationSelfTest 联调自测
	EnumSubEnvGroupIntegrationSelfTest EnumSubEnvGroup = 1
	// EnumSubEnvGroupTestAcceptance 测试验收
	EnumSubEnvGroupTestAcceptance EnumSubEnvGroup = 2
)

// EnumTrafficControl 金丝雀流量控制方式
type EnumTrafficControl int8

const (
	// EnumTrafficControlNull 无流量控制
	EnumTrafficControlNull EnumTrafficControl = -1
	// EnumTrafficControlDyeing 流量染色&切片，默认方式
	EnumTrafficControlDyeing EnumTrafficControl = 0
	// EnumTrafficControlWeight istio权重路由
	EnumTrafficControlWeight EnumTrafficControl = 1
)

func (e EnumTrafficControl) Value() int8 {
	return int8(e)
}
