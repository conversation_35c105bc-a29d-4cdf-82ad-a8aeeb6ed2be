package dao

import (
	"context"
	"errors"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/log"
	models "52tt.com/cicd/services/schedule/internal/model"
	"gorm.io/gorm"
)

func NewAppPrjRepo() *appPrjRepo {
	return &appPrjRepo{}
}

type appPrjRepo struct {
}

type ParmsApp struct {
	ProjectId int64
	Names     []string
	Ids       []int64
	NotPrjIds []int64
}

func (r *appPrjRepo) GetApp(ctx context.Context, id int64) (app models.App, has bool, err error) {
	err = AppDb.Model(&models.App{}).Where("id = ?", id).First(&app).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return app, false, nil
		}
		return app, false, err
	}
	has = true
	return
}

func (r *appPrjRepo) FindApps(ctx context.Context, query ParmsApp) (objs []models.App, err error) {
	objs = make([]models.App, 0)
	tx := AppDb.Table("app").Preload("Project").Preload("AppUsers").Preload("AppUsers.User")
	if query.ProjectId > 0 {
		tx = tx.Where("project_id = ?", query.ProjectId)
	}
	if len(query.Names) > 0 {
		tx = tx.Where("name in ?", query.Names)
	}
	if len(query.Ids) > 0 {
		tx = tx.Where("id in ?", query.Ids)
	}
	if len(query.NotPrjIds) > 0 {
		tx = tx.Where("project_id not in ?", query.NotPrjIds)
	}

	err = tx.Find(&objs).Error
	if err != nil {
		log.Errorf("根据id查询应用信息错误: %v", err)
		return
	}
	return
}

func (r *appPrjRepo) FindAppsNoPreload(ctx context.Context, query ParmsApp) (objs []models.App, err error) {
	objs = make([]models.App, 0)
	tx := AppDb.Table("app")
	if query.ProjectId > 0 {
		tx = tx.Where("project_id = ?", query.ProjectId)
	}
	if len(query.Names) > 0 {
		tx = tx.Where("name in ?", query.Names)
	}
	if len(query.Ids) > 0 {
		tx = tx.Where("id in ?", query.Ids)
	}
	if len(query.NotPrjIds) > 0 {
		tx = tx.Where("project_id not in ?", query.NotPrjIds)
	}

	err = tx.Find(&objs).Error
	if err != nil {
		log.Errorf("根据id查询应用信息错误: %v", err)
		return
	}
	return
}

func (r *appPrjRepo) ListAppsProjects(ctx context.Context, appIds []int64) ([]int64, error) {
	var projectIds []int64
	if err := AppDb.Table("app").Where("id in ?", appIds).Distinct("project_id").Pluck("project_id", &projectIds).Error; err != nil {
		log.ErrorWithCtx(ctx, "[ListAppsProjects] list apps projects error: %v", err)
		return nil, err
	}
	return projectIds, nil
}

func (r *appPrjRepo) UpdateApp(ctx context.Context, obj *models.App) error {
	if err := AppDb.Model(&models.App{}).Where("id = ?", obj.ID).Select("*").Omit("id", "created_at").Updates(obj).Error; err != nil {
		log.ErrorWithCtx(ctx, "[UpdateApp] update app error: %v", err)
		return err
	}
	return nil
}

func (r *appPrjRepo) GetAppEventlink(ctx context.Context, appId int64, env constants.EnvType) (*models.AppEventlink, error) {
	var obj models.AppEventlink
	if err := AppDb.Model(&models.AppEventlink{}).Where("app_id = ? and env = ?", appId, env).First(&obj).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "[GetAppEventlink] get app eventlink config error: %v", err)
		return nil, err
	}
	return &obj, nil
}

func (r *appPrjRepo) CreateAppEventlink(ctx context.Context, obj *models.AppEventlink) error {
	if err := AppDb.Model(&models.AppEventlink{}).Create(obj).Error; err != nil {
		log.ErrorWithCtx(ctx, "[CreateAppEventlink] create app eventlink config error: %v", err)
		return err
	}
	return nil
}

func (r *appPrjRepo) UpdateAppEventlink(ctx context.Context, obj *models.AppEventlink) error {
	if err := AppDb.Model(&models.AppEventlink{}).Where("id = ?", obj.ID).Updates(map[string]interface{}{
		"consumer_type": obj.ConsumerType,
		"producer_type": obj.ProducerType,
	}).Error; err != nil {
		log.ErrorWithCtx(ctx, "[UpdateAppEventlink] update app eventlink config error: %v", err)
		return err
	}
	return nil
}

func (pr *appPrjRepo) ListProjects(ctx context.Context, req *models.ListProjectsReq) ([]models.Project, error) {
	var projects []models.Project
	tx := AppDb.Model(&models.Project{})
	if len(req.ProjectIDs) > 0 {
		tx = tx.Where("id in (?)", req.ProjectIDs)
	}
	if req.UserGroup != "" {
		tx = tx.Where("user_group = ?", req.UserGroup)
	}
	if err := tx.Find(&projects).Error; err != nil {
		log.ErrorWithCtx(ctx, "[ListProjects] get all projects failed: %v", err)
		return nil, err
	}
	return projects, nil
}
