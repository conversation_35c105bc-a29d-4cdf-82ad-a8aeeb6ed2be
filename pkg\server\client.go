package server

import (
	"context"
	"time"

	"52tt.com/cicd/pkg/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport"
	transgrpc "github.com/go-kratos/kratos/v2/transport/grpc"
	"google.golang.org/grpc"
)

type ClientOption = transgrpc.ClientOption

func WithAuthority(a string) ClientOption {
	return transgrpc.WithOptions(grpc.WithAuthority(a))
}

func WithTimeout(timeout time.Duration) ClientOption {
	return transgrpc.WithTimeout(timeout)
}

func clientLoggingMiddler(next middleware.Handler) middleware.Handler {
	return func(ctx context.Context, req interface{}) (interface{}, error) {
		if tr, ok := transport.FromClientContext(ctx); ok {
			log.InfoWithCtx(ctx, "gRPC send Request: %+v - Header: %+v - To: %+v", tr.Operation(), tr.<PERSON>eader(), tr.Endpoint())
		}
		reply, err := next(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "gRPC error: %v", err)
		}
		return reply, err
	}
}

func NewRpcClient(url string, opts ...ClientOption) (*grpc.ClientConn, error) {
	newOpts := []ClientOption{
		transgrpc.WithEndpoint(url),
		transgrpc.WithMiddleware(
			recovery.Recovery(),
			clientLoggingMiddler,
		),
		transgrpc.WithTimeout(time.Second * 222),
		transgrpc.WithUnaryInterceptor(UnaryClientInterceptor),
	}
	if len(opts) > 0 {
		newOpts = append(newOpts, opts...)
	}

	conn, err := transgrpc.DialInsecure(
		context.Background(),
		newOpts...,
	)

	if err != nil {
		return nil, err
	}

	return conn, nil
}
