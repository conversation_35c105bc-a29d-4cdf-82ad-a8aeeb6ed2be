package godis

import (
	"context"
	"time"

	"52tt.com/cicd/pkg/log"
	"github.com/redis/go-redis/v9"
)

var Rdb *redis.Client

func InitRedis(config *redis.Options) error {
	if config != nil {
		log.Info("init redis...")
		Rdb = redis.NewClient(config)

		err := Rdb.Ping(context.Background()).Err()
		if err != nil {
			log.Errorf("connect to target redisConfig error: %v", err)
			return err
		}
	}
	return nil
}

func LockWithExpirationCtx(ctx context.Context, key, value string) (bool, error) {
	deadline, ok := ctx.Deadline()
	if !ok {
		return false, context.DeadlineExceeded
	}
	return Rdb.SetNX(ctx, key, value, time.Until(deadline)).Result()
}

func Unlock(ctx context.Context, key string) (int64, error) {
	return Rdb.Del(ctx, key).Result()
}

// LockWithSpin 带有自旋锁特性的锁抢占。
func LockWithSpin(ctx context.Context, key, value string, retry int, interval time.Duration) (bool, error) {
	ctx, cancel := context.WithTimeout(ctx, interval*time.Duration(retry))
	defer cancel()

	for i := 0; i < retry; i++ {

		locked, err := LockWithExpirationCtx(ctx, key, value)
		if err != nil {
			return false, err
		}
		if locked {
			return true, nil
		}
		time.Sleep(interval)

	}
	return false, nil
}
