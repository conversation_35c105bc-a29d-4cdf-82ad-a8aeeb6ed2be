- name: cicd-iam-sub01
  serviceType: deployment
  containers:
    - name: cicd-iam
      dev:
        gitUrl: ""
        image: cr.ttyuyin.com/tt_oss/tt-golang-dev:latest
        devContainerName: "cicd-iam"
        shell: ""
        workDir: ""
        storageClass: ""
        resources: null
        persistentVolumeDirs: []
        command:
          run: # run参数
            - go run ./services/iam/cmd/main.go
            - --s=./services/iam/etc
          debug:
            - dlv debug --headless --accept-multiclient --listen :9099
            - --accept-multiclient --api-version=2
            - ./services/iam/cmd/main.go
            - --
            - --s=./services/iam/etc
        debug:
          remoteDebugPort: 9099
          language: go
        env:
          - name: GONOSUMDB
            value: gitlab.ttyuyin.com
          - name: GOPROXY
            value: http://yw-nexus.ttyuyin.com:8081/repository/group-go/
          - name: GOSUMDB
            value: off
          - name: CGO_ENABLED
            value: 0
          - name: GOOS
            value: linux
          - name: GOARCH
            value: amd64
        hotReload: false
        sync: null
        portForward: []
