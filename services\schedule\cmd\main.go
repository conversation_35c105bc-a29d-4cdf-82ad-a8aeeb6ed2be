package main

import (
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"52tt.com/cicd/pkg/admin"
	"52tt.com/cicd/pkg/engine"
	"52tt.com/cicd/pkg/engine/middleware"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/safego"
	localServer "52tt.com/cicd/pkg/server"
	pbapp "52tt.com/cicd/protocol/app"
	pbdeploy "52tt.com/cicd/protocol/deploy"
	pbNotify "52tt.com/cicd/protocol/notify"
	pbPipeline "52tt.com/cicd/protocol/pipeline"
	"52tt.com/cicd/services/schedule/internal/conf"
	"52tt.com/cicd/services/schedule/internal/dao"
	"52tt.com/cicd/services/schedule/internal/job"
	"52tt.com/cicd/services/schedule/internal/job/clean_job"
	"52tt.com/cicd/services/schedule/internal/job/plan_run"
	"52tt.com/cicd/services/schedule/internal/job/plan_run/rpc_clients"
	"52tt.com/cicd/services/schedule/internal/job/sync_job"
	"github.com/gin-gonic/gin"
	"github.com/spf13/pflag"
)

func Run(path string) error {
	c, err := conf.LoadConfig(path)
	if err != nil {
		return err
	}
	local := engine.NewEngine(c, &engine.EngineOptions{
		BasePath: "/api/v1",
	})
	local.Use(gin.Logger())
	local.Use(middleware.CORS())
	local.Use(middleware.AuthContext())

	if err := local.Init(); err != nil {
		return err
	}

	if err := dao.Init(*c); err != nil {
		return err
	}

	svr := localServer.NewServer(local, c)

	pipelineGrpcClient, err := localServer.NewRpcClient(c.GetRegistry().PipelineRpcUrl)
	notifyGrpcClient, err := localServer.NewRpcClient(c.GetRegistry().NotifyRpcUrl)
	deployGrpcClient, err := localServer.NewRpcClient(c.GetRegistry().DeployRpcUrl)
	appGrpcClient, err := localServer.NewRpcClient(c.GetRegistry().AppRpcUrl)

	if err != nil {
		return err
	}
	pipelineClient := pbPipeline.NewPipelineServiceClient(pipelineGrpcClient)
	notifyClient := pbNotify.NewNotifyClient(notifyGrpcClient)
	storyClient := pbdeploy.NewStoryServiceClient(deployGrpcClient)
	pplRunRpc := pbPipeline.NewPipelineRunServiceClient(pipelineGrpcClient)
	ticketRpc := pbdeploy.NewTicketServiceClient(deployGrpcClient)
	deployRpc := pbdeploy.NewDeployServiceClient(deployGrpcClient)
	appClient := pbapp.NewAppServiceClient(appGrpcClient)

	pipelineRunClient := pbPipeline.NewPipelineRunServiceClient(pipelineGrpcClient)
	// init jobs
	tektonJob := clean_job.NewCleanJobTekton("CleanJobTekton", c)
	preparingPipelineRunJob := job.NewPreparingPipelineRunCronJob("PreparingPipelineRunCronJob", pipelineClient)
	cjKafka := clean_job.NewCleanJobKafka("CleanJobKafka", c)
	tapdStoryNotifyJob := job.NewTapdStoryNotifyJob("TapdStoryNotifyJob", storyClient)
	expiredJob := clean_job.NewCleanJobExpired("这个Name误导人的,配置文件里的必须和类名一样", pplRunRpc, ticketRpc)
	thanos := clean_job.NewThanos(c)
	cleanCDLogs := clean_job.NewCleanCDLogs(deployRpc)
	harborJob := clean_job.NewCleanJobHarbor(c)
	syncCMDBAppJob := sync_job.NewSyncCMDBAppJob(appClient)

	sch := job.NewSchedule(c)
	sch.RegisterJob(
		cjKafka,
		tektonJob,
		preparingPipelineRunJob,
		tapdStoryNotifyJob,
		expiredJob,
		thanos,
		cleanCDLogs,
		harborJob,
		syncCMDBAppJob,
	)
	if err := sch.Start(); err != nil {
		return err
	}

	go func() {
		if err := admin.ListenAndServe(admin.DefaultAdminAddr); err != nil && err != http.ErrServerClosed {
			log.Error(err.Error())
		}
	}()

	// 初始化调度器
	rpc_clients.SetInternalRpcClient(pipelineRunClient, notifyClient)
	plan_run.SingleRootAgg.Init()

	if err := svr.Run(); err != nil {
		return fmt.Errorf("start service error: %v", err)
	}

	return nil
}

func main() {
	var configPath string

	pflag.StringVar(&configPath, "s", "services/schedule/etc/", "path of application setting file")
	pflag.Parse()

	// Allocate SIG channel
	exitChan := make(chan os.Signal, 1)
	// Register the signal channel
	signal.Notify(exitChan, os.Interrupt, syscall.SIGTERM)
	safego.Go(func() {
		if err := Run(configPath); err != nil {
			panic(err)
		}
	})

	// Wait for exit signal
	c := <-exitChan
	log.Infof("exit signal received. Exiting...", c.String())

	plan_run.SingleRootAgg.CleanUp()

	// 主协程续命，让工作协程有机会把剩下的事情干完！
	time.Sleep(time.Second * 15)

	log.Info("service done")
}
