package httpclient

import (
	"context"
	"encoding/json"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

type User struct {
	Name string `json:"name" query:"name"`
	Age  int64  `json:"age" query:"age"`
}

func TestRequestWithJsonStruct(t *testing.T) {
	// given
	req := newDefaultRequest()
	user := User{
		Name: "test",
		Age:  18,
	}
	userJson, _ := json.Marshal(user)
	// when
	err := RequestWithJson{Data: user}.applyToRequest(req)
	// then
	assert.<PERSON>l(t, err)
	assert.Equal(t, "application/json", req.Head<PERSON>["Content-Type"])
	assert.Equal(t, userJson, req.Json)
	assert.Equal(t, userJson, req.BodyData.Bytes())
}

func TestRequestWithJsonMap(t *testing.T) {
	req := newDefaultRequest()
	requestData := map[string]string{
		"name": "test",
		"age":  "18",
	}
	requestDataJson, _ := json.Marshal(requestData)
	err := RequestWithJson{Data: requestData}.applyToRequest(req)
	assert.Nil(t, err)
	assert.Equal(t, "application/json", req.Headers["Content-Type"])
	assert.Equal(t, requestDataJson, req.Json)
	assert.Equal(t, requestDataJson, req.BodyData.Bytes())
}

func TestRequestWithStructParams(t *testing.T) {
	req := newDefaultRequest()
	user := User{
		Name: "test",
		Age:  18,
	}
	err := RequestWithStrcutParams{Data: user}.applyToRequest(req)
	assert.Nil(t, err)
	assert.Equal(t, map[string]string{
		"name": "test",
		"age":  "18",
	}, req.Params)
}

func TestRequestWithInsecure(t *testing.T) {
	req := newDefaultRequest()
	err := RequestWithInsecure(true).applyToRequest(req)
	assert.Nil(t, err)
	assert.Equal(t, true, *req.InsecureSkipVerify)
}

func TestNewRequestDefault(t *testing.T) {
	r, err := NewRequest("http://test.com")
	assert.Nil(t, err)
	assert.Equal(t, "GET", r.Method)
	assert.Equal(t, "http://test.com", r.URL.String())
}

func TestNewRequestIsSkipVerify(t *testing.T) {
	// false by default
	r, err := NewRequest("http://test.com")
	assert.Nil(t, err)
	assert.Equal(t, false, r.IsSkipVerify())

	r, err = NewRequest("http://test.com", RequestWithInsecure(true))
	assert.Nil(t, err)
	assert.Equal(t, true, r.IsSkipVerify())
}

func TestNewRequestWithJsonStruct(t *testing.T) {
	user := User{
		Name: "test",
		Age:  18,
	}
	jsonUser, _ := json.Marshal(user)
	r, err := NewRequest("http://test.com", RequestWithJson{Data: user})
	assert.Nil(t, err)
	assert.Equal(t, "application/json", r.Headers["Content-Type"])
	assert.Equal(t, jsonUser, r.Json)
}

func TestNewRequestWithJsonMap(t *testing.T) {
	userMap := map[string]string{
		"name": "test",
		"age":  "18",
	}
	userJson, _ := json.Marshal(userMap)
	r, err := NewRequest("http://test.com", RequestWithJson{Data: userMap})
	assert.Nil(t, err)
	assert.Equal(t, "application/json", r.Headers["Content-Type"])
	assert.Equal(t, userJson, r.Json)
}

func TestNewRequestWithStructParams(t *testing.T) {
	r, err := NewRequest("http://test.com", RequestWithStrcutParams{Data: User{
		Name: "test",
		Age:  18,
	}})
	assert.Nil(t, err)
	assert.Equal(t, map[string]string{
		"name": "test",
		"age":  "18",
	}, r.Params)
}

// Session Uset Test

type httpbinResponse struct {
	Args struct {
		Age  string `json:"age"`
		Name string `json:"name"`
	} `json:"args"`
	Headers struct {
		Accept       string `json:"Accept"`
		Host         string `json:"Host"`
		UserAgent    string `json:"User-Agent"`
		XAmznTraceId string `json:"X-Amzn-Trace-Id"`
	} `json:"headers"`
	Origin string `json:"origin"`
	URL    string `json:"url"`
}

func TestSessionGet(t *testing.T) {
	session := NewSession(&SessionOption{})
	req, err := NewRequest("https://httpbin.org/get")
	assert.Nil(t, err)
	resp, err := session.Get(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, 200, resp.StatusCode)
	assert.Equal(t, "https://httpbin.org/get", resp.Request.URL.String())
}

func TestSessionPost(t *testing.T) {
	session := NewSession(&SessionOption{})
	req, err := NewRequest("https://httpbin.org/post")
	assert.Nil(t, err)
	resp, err := session.Post(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func TestSessionPut(t *testing.T) {
	session := NewSession(&SessionOption{})
	req, err := NewRequest("https://httpbin.org/put")
	assert.Nil(t, err)
	resp, err := session.Put(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func TestSessionDelete(t *testing.T) {
	session := NewSession(&SessionOption{})
	req, err := NewRequest("https://httpbin.org/delete")
	assert.Nil(t, err)
	resp, err := session.Delete(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, 200, resp.StatusCode)
}

func TestResponseJsonToStruct(t *testing.T) {
	session := NewSession(&SessionOption{})
	user := User{
		Name: "test",
		Age:  18,
	}
	req, err := NewRequest("https://httpbin.org/get", RequestWithStrcutParams{user})
	assert.Nil(t, err)
	resp, err := session.Get(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, 200, resp.StatusCode)

	var result httpbinResponse
	err = resp.JsonToStruct(&result)
	assert.Nil(t, err)
	assert.Equal(t, result.URL, resp.Request.URL.String())
	assert.Equal(t, result.Args.Name, user.Name)
	assert.Equal(t, result.Args.Age, strconv.FormatInt(user.Age, 10))
}

func TestResponseJsonToMap(t *testing.T) {
	session := NewSession(&SessionOption{})
	user := User{
		Name: "test",
		Age:  18,
	}
	req, err := NewRequest("https://httpbin.org/get", RequestWithStrcutParams{user})
	assert.Nil(t, err)
	resp, err := session.Get(context.Background(), req)
	assert.Nil(t, err)
	assert.Equal(t, 200, resp.StatusCode)

	// http Response body like this:
	// {
	//     "args": {
	//       "age": "18",
	//       "name": "test"
	//     },
	//     "headers": {
	//       "Accept": "*/*",
	//       "Host": "httpbin.org",
	//       "User-Agent": "curl/7.77.0",
	//       "X-Amzn-Trace-Id": "Root=1-64589ae2-0f242f5160e85e7a40031ac6"
	//     },
	//     "origin": "*************",
	//     "url": "https://httpbin.org/get?age=18&name=test"
	//   }

	var result map[string]any
	err = resp.JsonToMap(&result)
	assert.Nil(t, err)
	url := result["url"].(string)
	assert.Equal(t, url, resp.Request.URL.String())
	args := result["args"].(map[string]any)
	name := args["name"].(string)
	age := args["age"].(string)
	assert.Equal(t, name, user.Name)
	assert.Equal(t, age, strconv.FormatInt(user.Age, 10))
}
