package constants

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type ValidatorTestSuite struct {
	suite.Suite
}

func TestValidatorInit(t *testing.T) {
	suite.Run(t, new(ValidatorTestSuite))
}

func (v *ValidatorTestSuite) SetupTest() {}

func (v *ValidatorTestSuite) TestStageValidation_Sub__v2() {
	assert.Equal(v.T(), fmt.Sprintf("%s", SUB_V2), "sub_v2")
	assert.Equal(v.T(), SUB_V2.Value(), int8(3))
	assert.Equal(v.T(), TransferToEnvTargetType(int8(3)), SUB_V2)
	assert.Equal(v.T(), SUB_V2.Value(), int8(3))
	assert.Equal(v.T(), SUB_V2.IsValid(), true)
	assert.Equal(v.T(), EnvTargetType("unknown").IsValid(), false)
}

func TestEnumEnvTarget_ToEnvTargetType(t *testing.T) {
	testcases := []struct {
		name string
		src  int8
		dst  EnvTargetType
	}{
		{
			name: "test int8_1_to_ORIGIN",
			src:  1,
			dst:  ORIGIN,
		},
		{
			name: "test int8_2_to_SUB",
			src:  2,
			dst:  SUB,
		},
		{
			name: "test int8_3_to_SUB_V2",
			src:  3,
			dst:  SUB_V2,
		},
		{
			name: "test int8_0_to_UNKNOWN",
			src:  0,
			dst:  EnvTargetType("unknown"),
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			de := EnumEnvTarget(tc.src)
			actual := de.ToEnvTargetType()
			assert.Equal(t, tc.dst, actual)
		})
	}
}

func TestEnumEnvTarget_String(t *testing.T) {
	testcases := []struct {
		name string
		src  EnumEnvTarget
		dst  string
	}{
		{
			name: "test EnumEnvTargetOrigin.String() equal ORIGIN.String()",
			src:  EnumEnvTargetOrigin,
			dst:  ORIGIN.String(),
		},
		{
			name: "test EnumEnvTargetOrigin.String() equal SUB.String()",
			src:  EnumEnvTargetSub,
			dst:  SUB.String(),
		},
		{
			name: "test EnumEnvTargetOrigin.String() equal SUB_V2.String()",
			src:  EnumEnvTargetSubV2,
			dst:  SUB_V2.String(),
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			actual := tc.src.String()
			assert.Equal(t, tc.dst, actual)
		})
	}
}
