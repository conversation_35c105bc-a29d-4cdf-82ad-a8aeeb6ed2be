package main

import (
	"52tt.com/cicd/pkg/gitlab"
	"52tt.com/cicd/pkg/lark"
	toolsTime "52tt.com/cicd/pkg/tools/time"
	"fmt"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	"os"
	"runtime"
	"strings"
	"testing"
	"time"
)

func TestWriteUsers(t *testing.T) {
	// 获取当前函数所在文件的绝对路径
	//t.Skip("ingnore tests")
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		return
	}
	temp := strings.Split(filename, "users-migration-sql_test.go")[0]
	filePath := fmt.Sprintf("%susers-migration-sql.txt", temp)
	//file, err := os.Open(filePath)
	file, err := os.OpenFile(filePath, os.O_RDWR, 0644)
	if err != nil {
		t.<PERSON>("无法打开文件%s", err.Error())
		return
	}
	defer file.Close()
	_, err = file.WriteString(">>>>>>>>>>>>>>>>>>>>>>>>>>>\n从此处开始运行了一次测试！写入了内容\n")
	if err != nil {
		return
	}

	type User struct {
		ID          int       `json:"id" sql:"AUTO_INCREMENT" gorm:"primary_key,column:id"`
		CreatedAt   time.Time `json:"createdAt" gorm:"column:created_at" sql:"DEFAULT:current_timestamp"`
		UpdatedAt   time.Time `json:"updatedAt" gorm:"column:updated_at" sql:"DEFAULT:current_timestamp"`
		Username    string    `gorm:"column:username;NOT NULL"`         // 用户名
		ChineseName string    `gorm:"column:chinese_name;NOT NULL"`     // 中文名
		Email       string    `gorm:"column:email;NOT NULL"`            // 邮箱
		EmployeeNo  string    `gorm:"column:employee_no;NOT NULL"`      // 工号
		LarkUnionID string    `gorm:"column:lark_union_id;NOT NULL"`    // 飞书id
		GitlabID    int       `gorm:"column:gitlab_id;default:0"`       // gitlab用户id
		Status      int       `gorm:"column:status;default:1;NOT NULL"` // 状态(0不可用,1正常)
	}

	feishuClient := lark.NewClient("********************", "m4rAc6pGodqPplQMZOTM30PVl0FBO0NJ")
	deps, err := feishuClient.GetDepsList("od-0a6ee50aa99abaf1f1b711ab75a8d9cf")
	if err != nil {
		fmt.Printf("从飞书获取研发中心部门信息出错：%v", err)
	}
	var fsUsers = make([]*larkcontact.User, 0)
	for _, dep := range deps {
		u, err := feishuClient.GetUsrsList(*dep.OpenDepartmentId)
		if err != nil {
			return
		}
		fsUsers = append(fsUsers, u...)
	}
	gitlabClient := gitlab.NewClient("https://gitlab.ttyuyin.com", "********************")
	users := &[]User{}
	for _, u := range fsUsers {
		user := User{
			Username:    strings.Split(*u.Email, "@")[0],
			ChineseName: *u.Name,
			Email:       *u.Email,
			EmployeeNo:  *u.EmployeeNo,
			LarkUnionID: *u.UnionId,
			GitlabID:    gitlabClient.GetUserIdByEmail(*u.Email),
		}
		*users = append(*users, user)
	}
	str := fmt.Sprintf("\n\n获取用户信息成功！共 %d 条用户信息\n\n开始写入文件，更新时间：%s\n>>>>>>>>>>>>>>>>>>>>>>>>>>>\n\n",
		len(*users), toolsTime.Format(time.Now()))
	fmt.Printf("")
	_, err = file.WriteString(str)

	countErr := 0
	for _, u := range *users {
		//insert into pipeline.project_agents(project_id, lang, agent_id) values (35, 'NodeJS1.14', '1');
		sql := fmt.Sprintf("INSERT INTO iam.user (username,chinese_name,email,employee_no,lark_union_id,gitlab_id) VALUES ('%s','%s','%s','%s','%s',%d);\n", u.Username, u.ChineseName, u.Email, u.EmployeeNo, u.LarkUnionID, u.GitlabID)
		_, err := file.WriteString(sql)
		if err != nil {
			countErr++
			continue
		}
	}
	msg := fmt.Sprintf("\n\n>>>>>>>>>>>>>>>>>>>>>>>>>>>\n本次共写入 %d 条记录，其中 %d 条写入失败\n>>>>>>>>>>>>>>>>>>>>>>>>>>>", len(*users), countErr)
	fmt.Println(msg)
	_, _ = file.WriteString(msg)
}
