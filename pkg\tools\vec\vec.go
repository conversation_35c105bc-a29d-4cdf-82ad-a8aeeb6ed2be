package vec

// Flat flatten a nested slice with T parameter
func Flat[T any](slices [][]T) []T {
	var result []T
	for _, slice := range slices {
		result = append(result, slice...)
	}
	return result
}

// FindDuplicate find duplicate element in a slice
func FindDuplicate[T comparable](slices []T) []T {
	sets := make(map[T]bool)
	result := make([]T, 0, len(slices))
	for _, val := range slices {
		if sets[val] {
			result = append(result, val)
		} else {
			sets[val] = true
		}
	}
	return result
}

func MapTo[T, U any](arr []T, fn func(entity T) U) []U {
	size := len(arr)
	newArr := make([]U, 0, size)
	for _, v := range arr {
		newArr = append(newArr, fn(v))
	}
	return newArr
}

// ToMap slice to map, key is slice element, value is index
func ToMap[T comparable](slices []T) map[T]int {
	sets := make(map[T]int)
	for i, val := range slices {
		sets[val] = i
	}
	return sets
}

// HasSameElement check if two slices has same element
func HasSameElement[T comparable](slices1, slices2 []T) bool {
	if len(slices1) == 0 || len(slices2) == 0 {
		return false
	}

	first := ToMap(slices1)
	for _, val := range slices2 {
		if _, ok := first[val]; ok {
			return true
		}
	}
	return false
}

func Filter[T any](arr []T, fn func(entity T) bool) []T {
	newArr := make([]T, 0, len(arr))
	for _, entry := range arr {
		if fn(entry) {
			newArr = append(newArr, entry)
		}
	}
	return newArr
}

func HasElements[T any](slices []T) bool {
	return !NoElements(slices)
}

func NoElements[T any](slices []T) bool {
	return slices == nil || len(slices) == 0
}

func Difference[T comparable](first, second []T) []T {
	sets := make(map[T]struct{}, 0)
	for _, val := range first {
		sets[val] = struct{}{}
	}
	for _, val := range second {
		delete(sets, val)
	}
	result := make([]T, 0, len(sets))
	for val := range sets {
		result = append(result, val)
	}
	return result
}

func Intersection[T comparable](first, second []T) []T {
	sets := make(map[T]struct{}, 0)
	for _, val := range first {
		sets[val] = struct{}{}
	}
	result := make([]T, 0, len(sets))
	for _, val := range second {
		if _, ok := sets[val]; ok {
			result = append(result, val)
		}
	}
	return result
}

func RemoveDuplicate[T comparable](slices []T) []T {
	sets := make(map[T]struct{})
	result := make([]T, 0, len(slices))
	for _, val := range slices {
		if _, exists := sets[val]; !exists {
			sets[val] = struct{}{}
			result = append(result, val)
		}
	}
	return result
}

func RemoveDuplicatesByKey[T any](slices []T, uniqueFunc func(entry T) string) []T {
	seen := make(map[string]bool)
	uniques := make([]T, 0)
	for _, apple := range slices {
		key := uniqueFunc(apple)
		if !seen[key] {
			seen[key] = true
			uniques = append(uniques, apple)
		}
	}
	return uniques
}
