package service

import (
	"context"
	"net/http"
)

type Workflows struct {
	cmd Cmdable
}

func NewWorkflows(cmd Cmdable) *Workflows {
	return &Workflows{cmd: cmd}
}

type GetStatusMapRequest struct {
	BaseRequest
	System         string `json:"system" url:"system"`                     // 系统名, 可选值: bug(缺陷), story(需求)
	WorkitemTypeID int64  `json:"workitem_type_id" url:"workitem_type_id"` // 需求类别id
}

type GetStatusMapResponse struct {
	BaseResponse
	Data map[string]string `json:"data"`
}

// GetStatusMap
// Doc: https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/api_reference/workflow/get_workflow_status_map.html
func (r *Workflows) GetStatusMap(ctx context.Context, req *GetStatusMapRequest) (*GetStatusMapResponse, error) {
	resp := &GetStatusMapResponse{}
	err := r.cmd(ctx, http.MethodGet, "workflows/status_map", req, resp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

type GetAllTransitionsRequest struct {
	BaseRequest
	System         string `json:"system" url:"system"` // 系统名, 可选值: bug(缺陷), story(需求)
	WorkitemTypeID int64  `json:"workitem_type_id" url:"workitem_type_id"`
}

type GetAllTransitionsData struct {
	Name         string                      `json:"Name"`         // 状态（步骤）流转名称
	StepPrevious string                      `json:"StepPrevious"` // 当前状态（步骤）
	StepNext     string                      `json:"StepNext"`     // 目标状态（步骤）
	AppendField  []AllTransitionsAppendField `json:"Appendfield"`  // 状态（步骤）流转时需要补充的附加字段
}

type AllTransitionsAppendField struct {
	DBModel      string                    `json:"DBModel"`      // 数据库字段名
	FieldName    string                    `json:"FieldName"`    // 字段名
	FieldLabel   string                    `json:"FieldLabel"`   // 字段标签
	NotNull      string                    `json:"Notnull"`      // 是否必填
	Sort         int                       `json:"Sort"`         // 排序
	Type         string                    `json:"Type"`         // 字段类型
	DefaultValue []AppendFiledDefaultValue `json:"DefaultValue"` // 默认值
}

type AppendFiledDefaultValue struct {
	Type  string `json:"Type"`  // 默认值类型
	Value string `json:"Value"` // 默认值
}

type GetAllTransitionsResponse struct {
	BaseResponse
	Data []GetAllTransitionsData `json:"data"`
}

// GetAllTransitions
// Doc: https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/api_reference/workflow/get_workflow_all_transitions.html
func (r *Workflows) GetAllTransitions(ctx context.Context, req *GetAllTransitionsRequest) (*GetAllTransitionsResponse, error) {
	resp := &GetAllTransitionsResponse{}
	err := r.cmd(ctx, http.MethodGet, "workflows/all_transitions", req, resp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
