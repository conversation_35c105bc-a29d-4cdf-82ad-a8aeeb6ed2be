package service

import (
	"context"
	"net/http"
	"strings"
)

type Workspace struct {
	cmd Cmdable
}

func NewWorkspace(cmd Cmdable) *Workspace {
	return &Workspace{cmd: cmd}
}

// WorkspaceUsersRequest Fields:user, role_id, email, name
type WorkspaceUsersRequest struct {
	BaseRequest
	Fields string `json:"fields,omitempty" url:"fields,omitempty"` // 设置获取的字段，多个字段间以','逗号隔开
	User   string `json:"user,omitempty" url:"user,omitempty"`     // 用户名
}

func (q *WorkspaceUsersRequest) Select(field ...string) {
	q.Fields = strings.Join(field, ",")
}

type WorkspaceUsersData struct {
	User   string   `json:"user,omitempty"` // 昵称
	RoleID []string `json:"role_id,omitempty"`
	Email  string   `json:"email,omitempty"`
	Name   string   `json:"name,omitempty"` // 真实姓名
}

type WorkspaceUsersResponse struct {
	BaseResponse
	Data []struct {
		UserWorkspace WorkspaceUsersData `json:"UserWorkspace"`
	} `json:"data"`
}

// GetWorkspaceUsers
// Doc: https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/api_reference/workspace/get_workspace_users.html
func (r *Workspace) GetWorkspaceUsers(ctx context.Context, req *WorkspaceUsersRequest) (*WorkspaceUsersResponse, error) {
	resp := &WorkspaceUsersResponse{}
	err := r.cmd(ctx, http.MethodGet, "workspaces/users", req, resp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
