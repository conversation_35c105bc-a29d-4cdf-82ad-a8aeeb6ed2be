package clean_job

import (
	"context"
	"fmt"

	"52tt.com/cicd/pkg/harbor"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/schedule/internal/conf"
)

// 使用反射获取的名称，故类名需要和配置中的保持一致
type CleanJobHarbor struct {
	cfg *conf.Config
}

// 定期清理无效 Harbor镜像缓存
func NewCleanJobHarbor(cfg *conf.Config) *CleanJobHarbor {
	return &CleanJobHarbor{cfg: cfg}
}

func (job *CleanJobHarbor) Run() {
	log.Infof("CleanJobHarbor %s  激活,开始 清理 Harbor 镜像缓存")

	err := job.clean()
	if err != nil {
		log.Errorf("CleanJobHarbor  clean Err %v", err)
	}

	log.Infof("CleanJobHarbor %s  任务结束。")
}

func (job *CleanJobHarbor) clean() (err error) {
	ctx := context.Background()

	harbor := harbor.NewRepository(job.cfg.HarborCfg.Host, job.cfg.HarborCfg.AuthName, job.cfg.HarborCfg.AuthPsw)
	for _, app := range apps {

		errDel := harbor.Delete("devops", fmt.Sprintf("%s/cache", app.Name))
		if errDel != nil {
			log.ErrorWithCtx(ctx, "删除 Harbor 缓存失败: %v", errDel)
		} else {
			log.InfoWithCtx(ctx, "删除 Harbor 缓存成功: %s/cache", app.Name)
		}

		helmCache := fmt.Sprintf("helm/%s", app.Name)
		errDel = harbor.Delete("devops", helmCache)
		if errDel != nil {
			log.ErrorWithCtx(ctx, "删除 Harbor Helm 缓存失败: %v", errDel)
		} else {
			log.InfoWithCtx(ctx, "删除 Harbor Helm 缓存成功: %s", helmCache)
		}
	}

	return
}
