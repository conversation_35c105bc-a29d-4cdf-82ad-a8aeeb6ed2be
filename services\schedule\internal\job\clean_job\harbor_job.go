package clean_job

import (
	"fmt"
	"strings"

	"52tt.com/cicd/pkg/harbor"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/services/schedule/internal/conf"
)

// 使用反射获取的名称，故类名需要和配置中的保持一致
type CleanJobHarbor struct {
	cfg        *conf.Config
	isCleaning bool
	reposCh    chan harbor.RepositoryInfo
}

// 定期清理无效 Harbor镜像缓存
func NewCleanJobHarbor(cfg *conf.Config) *CleanJobHarbor {
	return &CleanJobHarbor{cfg: cfg, reposCh: make(chan harbor.RepositoryInfo, 10)}
}

func (job *CleanJobHarbor) Run() {
	if job.isCleaning {
		return
	}
	job.isCleaning = true
	defer func() {
		job.isCleaning = false
	}()

	log.Infof("CleanJobHarbor %s  激活,开始 清理 Harbor 镜像缓存")
	err := job.clean()
	if err != nil {
		log.Errorf("CleanJobHarbor  clean Err %v", err)
	}

	log.Infof("CleanJobHarbor %s  任务结束。")
}

func (job *CleanJobHarbor) clean() (err error) {
	for range 2 {
		go job.cleanArtifact()
	}

	repository := harbor.NewRepository(job.cfg.HarborCfg.Host, job.cfg.HarborCfg.AuthName, job.cfg.HarborCfg.AuthPsw)
	page := 1
	for {
		objs, errIn := repository.List("devops", page, 100)
		if errIn != nil {
			err = fmt.Errorf("Harbor List Err %w", errIn)
			return
		}
		if len(objs) == 0 {
			break
		}

		for _, obj := range objs {
			job.reposCh <- obj
		}
		page++
	}
	return
}

func (job *CleanJobHarbor) cleanArtifact() (err error) {
	art := harbor.NewArtifact(job.cfg.HarborCfg.Host, job.cfg.HarborCfg.AuthName, job.cfg.HarborCfg.AuthPsw)
	for obj := range job.reposCh {
		if strings.HasSuffix(obj.Name, "/cache") {
			repoName := strings.Replace(obj.Name, "devops/", "", 1)
			log.Infof("Harbor Start Clean %s", repoName)
			// 只保留最近 30 条缓存
			getArtifactsFn := func() []harbor.ArtifactInfo {
				p := 2
				artifacts := make([]harbor.ArtifactInfo, 0)
				for {
					items, errIn := art.List("devops", repoName, p, 30)
					if errIn != nil {
						log.Errorf("Harbor List %s Artifact Err %v", repoName, errIn)
						break
					}
					if len(items) == 0 {
						break
					}
					p++
					artifacts = append(artifacts, items...)
				}
				return artifacts
			}

			artifacts := getArtifactsFn()
			for _, artifact := range artifacts {
				errIn := art.Delete("devops", repoName, artifact.Digest)
				if errIn != nil {
					log.Errorf("Harbor Delete %s Artifact Err %v", repoName, errIn)
					continue
				}
			}
		}
	}
	return
}
