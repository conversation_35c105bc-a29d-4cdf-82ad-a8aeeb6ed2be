package scope

import (
	"fmt"

	"gorm.io/gorm"
)

func FuzzySearch(key, value string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(fmt.Sprintf("%s like ?", key), fmt.Sprintf("%%%s%%", value))
	}
}

func StrictMatchFuzzySearch(key, value string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(fmt.Sprintf("BINARY %s like ?", key), fmt.Sprintf("%%%s%%", value))
	}
}

func StrictMatch(key, value string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(fmt.Sprintf("BINARY %s = ?", key), value)
	}
}
