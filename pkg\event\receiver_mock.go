// Code generated by MockGen. DO NOT EDIT.
// Source: receiver.go

// Package event is a generated GoMock package.
package event

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockCloser is a mock of Closer interface.
type MockCloser struct {
	ctrl     *gomock.Controller
	recorder *MockCloserMockRecorder
}

// MockCloserMockRecorder is the mock recorder for MockCloser.
type MockCloserMockRecorder struct {
	mock *MockCloser
}

// NewMockCloser creates a new mock instance.
func NewMockCloser(ctrl *gomock.Controller) *MockCloser {
	mock := &MockCloser{ctrl: ctrl}
	mock.recorder = &MockCloserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCloser) EXPECT() *MockCloserMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockCloser) Close(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockCloserMockRecorder) Close(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockCloser)(nil).Close), ctx)
}

// MockReceiver is a mock of Receiver interface.
type MockReceiver struct {
	ctrl     *gomock.Controller
	recorder *MockReceiverMockRecorder
}

// MockReceiverMockRecorder is the mock recorder for MockReceiver.
type MockReceiverMockRecorder struct {
	mock *MockReceiver
}

// NewMockReceiver creates a new mock instance.
func NewMockReceiver(ctrl *gomock.Controller) *MockReceiver {
	mock := &MockReceiver{ctrl: ctrl}
	mock.recorder = &MockReceiverMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReceiver) EXPECT() *MockReceiverMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockReceiver) Close(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockReceiverMockRecorder) Close(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockReceiver)(nil).Close), ctx)
}

// RegisterListener mocks base method.
func (m *MockReceiver) RegisterListener(listeners ...Listener) {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range listeners {
		varargs = append(varargs, a)
	}
	m.ctrl.Call(m, "RegisterListener", varargs...)
}

// RegisterListener indicates an expected call of RegisterListener.
func (mr *MockReceiverMockRecorder) RegisterListener(listeners ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterListener", reflect.TypeOf((*MockReceiver)(nil).RegisterListener), listeners...)
}

// StartReceiver mocks base method.
func (m *MockReceiver) StartReceiver(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "StartReceiver", ctx)
}

// StartReceiver indicates an expected call of StartReceiver.
func (mr *MockReceiverMockRecorder) StartReceiver(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartReceiver", reflect.TypeOf((*MockReceiver)(nil).StartReceiver), ctx)
}
