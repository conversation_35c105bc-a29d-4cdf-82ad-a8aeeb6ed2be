package event

import (
	"fmt"

	"52tt.com/cicd/pkg/kafka"
	"github.com/google/uuid"
)

const (
	ApplicationJSON = "application/json"
)

// 定义事件

type options struct {
	id        string
	eventType string
}

type Option func(o *options)

func WithID(id string) Option {
	return func(o *options) {
		o.id = id
	}
}

func WithEventType(eventType string) Option {
	return func(o *options) {
		o.eventType = eventType
	}
}

func NewEvent() Event {
	return kafka.Event{}
}

func IsUndelivered(res error) bool {
	return res != nil
}
func IsACK(res error) bool {
	return res == nil
}

func NewProjectEvent(data any, opts ...Option) Event {
	e := NewEvent()

	defaultOptions := &options{
		id:        uuid.New().String(),
		eventType: "unknown",
	}
	for _, o := range opts {
		o(defaultOptions)
	}

	e.SetID(defaultOptions.id)
	e.SetType(fmt.Sprintf("cicd.app.%s", defaultOptions.eventType))
	e.SetSource("https://gitlab.ttyuyin.com/harmony/cicd")

	e.SetDatas(data)
	return e
}

func NewPipelineRelatedEvent(data any, opts ...Option) Event {
	e := NewEvent()

	defaultOptions := &options{
		id:        uuid.New().String(),
		eventType: "unknown",
	}
	for _, o := range opts {
		o(defaultOptions)
	}

	e.SetID(defaultOptions.id)
	e.SetType(fmt.Sprintf("cicd.pipeline.%s", defaultOptions.eventType))
	e.SetSource("https://gitlab.ttyuyin.com/harmony/cicd")

	_ = e.SetData(ApplicationJSON, data)
	return e
}

func NewTicketRelatedEvent(data any, opts ...Option) Event {
	e := NewEvent()

	defaultOptions := &options{
		id:        uuid.New().String(),
		eventType: "unknown",
	}
	for _, o := range opts {
		o(defaultOptions)
	}

	e.SetID(defaultOptions.id)
	e.SetType(fmt.Sprintf("cicd.deploy.%s", defaultOptions.eventType))
	e.SetSource("https://gitlab.ttyuyin.com/harmony/cicd")

	_ = e.SetData(ApplicationJSON, data)
	return e
}

func NewDeployRelatedEvent(data any, opts ...Option) Event {
	e := NewEvent()

	defaultOptions := &options{
		id:        uuid.New().String(),
		eventType: "unknown",
	}
	for _, o := range opts {
		o(defaultOptions)
	}

	e.SetID(defaultOptions.id)
	e.SetType(fmt.Sprintf("cicd.deploy.%s", defaultOptions.eventType))
	e.SetSource("https://gitlab.ttyuyin.com/harmony/cicd")

	_ = e.SetData(ApplicationJSON, data)
	return e
}

func New(data any, opts ...Option) Event {
	e := NewEvent()

	defaultOptions := &options{
		id:        uuid.New().String(),
		eventType: "unknown",
	}
	for _, o := range opts {
		o(defaultOptions)
	}

	e.SetID(defaultOptions.id)
	e.SetType(defaultOptions.eventType)
	e.SetSource("https://gitlab.ttyuyin.com/harmony/cicd")

	_ = e.SetData(ApplicationJSON, data)
	return e
}
