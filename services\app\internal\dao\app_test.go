package dao

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"testing"
	"time"

	"52tt.com/cicd/pkg/testify/testdb"

	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/page"
	"52tt.com/cicd/services/app/internal/model"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
)

type AppSuite struct {
	db.Suite
	appRepository AppRepository
}

func TestAppRepositoryInit(t *testing.T) {
	suite.Run(t, new(AppSuite))
}

func (s *AppSuite) SetupTest() {
	s.Suite.SetupTest()

	s.appRepository = NewAppRepository(db.DB)
}

func (s *AppSuite) TestApp_QueryAll() {
	projectId := int64(1)
	appId := int64(10)
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT count(*) FROM `app`")).
		WillReturnRows(s.Mock.NewRows([]string{"count"}).AddRow(1))

	columns := []string{"id", "name", "code", "owner", "build_path", "lang", "repo_addr", "status", "cmdb_id", "project_id"}

	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE project_id = ? ORDER BY `app`.id DESC LIMIT 10")).
		WithArgs(projectId).
		WillReturnRows(s.Mock.NewRows(columns).
			AddRow(appId, "test", "x-project", "admin", "./", "Java1.8", "https://www.baodu.com", 1, "5f6c790cc820857cf861b240", int64(1)))

	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app_user` WHERE `app_user`.`app_id` = ?")).
		WithArgs(appId).
		WillReturnRows(s.Mock.NewRows([]string{"id", "app_id", "user_id"}).
			AddRow(int64(3), appId, int64(14)))

	var count int64

	query := model.AppQuery{
		PageModel: page.PageModel{
			Num:  1,
			Size: 10,
		},
		ProjectId: projectId,
	}

	app := AppRepo{}
	apps, err := app.FindAll(context.Background(), &query, &count)
	require.NoError(s.T(), err)
	require.Equal(s.T(), "test", apps[0].Name)
	require.Equal(s.T(), appId, apps[0].Users[0].AppId)
}

func (s *AppSuite) TestApp_Delete() {

}

func (s *AppSuite) TestApp_Insert() {
	appData := App{
		Name:              "hello",
		Code:              "hello-service",
		BuildPath:         "./",
		LangVersion:       "1.2",
		LangName:          "java",
		RepoAddr:          "https://gitlab.ttyuyin.com/TT-Yunwei/cicd.git",
		Status:            1,
		CmdbId:            "sfxhj2367adkjk24",
		ProjectID:         1,
		Description:       "hello",
		MatchLabels:       make(db.DbStrMap),
		ServiceLabels:     make(db.DbStrMap),
		StandLabelEnvs:    "prod",
		SenvStatus:        "enabled",
		NeedExternalRoute: true,
		EventlinkType:     "consumer,producer",
		CmdbInfo:          []byte("{}"),
	}
	sql := "INSERT IGNORE INTO `app` (`created_at`,`updated_at`,`name`,`code`,`build_path`,`lang_name`,`lang_version`,`repo_addr`,`status`,`cmdb_id`,`project_id`,`description`,`match_labels`,`service_labels`,`stand_label_envs`,`senv_status`,`need_external_route`,`eventlink_type`,`cmdb_info`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	s.ExpectExec(sql, func(expectedExec *sqlmock.ExpectedExec) {
		expectedExec.WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), appData.Name, appData.Code, appData.BuildPath, appData.LangName, appData.LangVersion, appData.RepoAddr, appData.Status, appData.CmdbId, appData.ProjectID,
			appData.Description, appData.MatchLabels, appData.ServiceLabels, appData.StandLabelEnvs, appData.SenvStatus, appData.NeedExternalRoute, appData.EventlinkType, appData.CmdbInfo).
			WillReturnResult(sqlmock.NewResult(1, 1))
	})
	err := s.appRepository.Insert(context.Background(), &appData)
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), int64(1), appData.ID)
}

func (s *AppSuite) TestAppRepo_FindAppsByUserAndProject() {
	// given
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT `app`.`id`,`app`.`created_at`,`app`.`updated_at`,`app`.`name`,`app`.`code`,`app`.`build_path`,`app`.`lang_name`,`app`.`lang_version`,`app`.`repo_addr`,`app`.`status`,`app`.`cmdb_id`,`app`.`project_id`,`app`.`description`,`app`.`match_labels`,`app`.`service_labels`,`app`.`stand_label_envs`,`app`.`senv_status`,`app`.`need_external_route`,`app`.`eventlink_type`,`app`.`cmdb_info` FROM `app` left join app_user on app_id = app.id WHERE project_id = ? AND user_id = ?")).
		WillReturnRows(s.Mock.NewRows([]string{"id", "name", "project_id", "description"}).
			AddRow(int64(1), "test-app1", int64(1), "测试应用1").
			AddRow(int64(2), "test-app2", int64(1), "测试应用2"))

	// when
	var userId, projectId int64 = 1, 1
	apps, err := s.appRepository.FindAppsByUserAndProject(context.Background(), userId, projectId)

	// then
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), 2, len(apps))
	assert.Equal(s.T(), int64(1), apps[0].ID)
	assert.Equal(s.T(), "test-app1", apps[0].Name)
	assert.Equal(s.T(), "测试应用1", apps[0].Description)
}

func (s *AppSuite) TestAppRepo_FindAppsByUserAndProject_Error() {
	// given
	s.Mock.ExpectQuery(
		regexp.QuoteMeta("SELECT `app`.`id`,`app`.`created_at`,`app`.`updated_at`,`app`.`name`,`app`.`code`,`app`.`build_path`,`app`.`lang_name`,`app`.`lang_version`,`app`.`repo_addr`,`app`.`status`,`app`.`cmdb_id`,`app`.`project_id`,`app`.`description`,`app`.`match_labels`,`app`.`service_labels`,`app`.`stand_label_envs`,`app`.`senv_status`,`app`.`need_external_route`,`app`.`eventlink_type`,`app`.`cmdb_info` FROM `app` left join app_user on app_id = app.id WHERE project_id = ? AND user_id = ?")).
		WillReturnError(fmt.Errorf("can not find the apps"))

	// when
	var userId, projectId int64 = 1, 1
	_, err := s.appRepository.FindAppsByUserAndProject(context.Background(), userId, projectId)

	// then
	assert.Equal(s.T(), "按条件查应用信息出错：can not find the apps", err.Error())
}

func (s *AppSuite) TestAppRepo_FindAppBy_Should_Return_Empty_App_When_Not_Found_By_ProjectId_And_Name() {
	// given
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT `id`,`name` FROM `app` WHERE project_id = ? and name = ?")).
		WithArgs(int64(1), "test").
		WillReturnRows(s.Mock.NewRows([]string{"id", "name"}))

	// when
	app, err := s.appRepository.FindAppBy(context.Background(), int64(1), "test")

	// then
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), &App{}, app)
}

func (s *AppSuite) TestAppRepo_FindAppBy_Should_Return_App_Successfully_When_Found_By_ProjectId_And_Name() {
	// given
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT `id`,`name` FROM `app` WHERE project_id = ? and name = ?")).
		WithArgs(int64(2), "test").
		WillReturnRows(s.Mock.NewRows([]string{"id", "name"}).AddRow(int64(1), "test"))

	// when
	app, err := s.appRepository.FindAppBy(context.Background(), int64(2), "test")

	// then
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), app.ID, int64(1))
}

func (s *AppSuite) TestAppRepo_FindAppBy_Should_Return_Error() {
	// given
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT `id`,`name` FROM `app` WHERE project_id = ? and name = ?")).
		WillReturnError(fmt.Errorf("can not find the app"))

	// when
	var projectId int64 = 1
	_, err := s.appRepository.FindAppBy(context.Background(), projectId, "test")

	// then
	assert.Equal(s.T(), "按条件查询应用信息出错：can not find the app", err.Error())
}

func (s *AppSuite) TestAppRepo_FindApp() {
	// given
	expectedApp := &App{
		BaseModel:   db.BaseModel{ID: 1},
		Name:        "test_app",
		Code:        "test_code",
		BuildPath:   "/test",
		RepoAddr:    "https://github.com/test/test_app",
		Status:      1,
		CmdbId:      "test_cmdb_id",
		ProjectID:   1,
		Description: "test_app_description",
		Users: []AppUser{
			{AppId: 1, UserId: 1},
			{AppId: 1, UserId: 2},
		},
	}
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE `app`.`id` = ? ORDER BY `app`.`id` LIMIT 1")).
		WillReturnRows(s.Mock.NewRows([]string{"id", "name", "code", "build_path", "repo_addr", "status", "cmdb_id", "project_id", "description"}).
			AddRow(expectedApp.ID, expectedApp.Name, expectedApp.Code, expectedApp.BuildPath, expectedApp.RepoAddr, expectedApp.Status, expectedApp.CmdbId, expectedApp.ProjectID,
				expectedApp.Description))
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app_user` WHERE `app_user`.`app_id` = ?")).
		WillReturnRows(s.Mock.NewRows([]string{"id", "app_id", "user_id"}).
			AddRow(expectedApp.Users[0].ID, expectedApp.Users[0].AppId, expectedApp.Users[0].UserId).
			AddRow(expectedApp.Users[1].ID, expectedApp.Users[1].AppId, expectedApp.Users[1].UserId))

	// when
	app, err := s.appRepository.FindApp(context.Background(), 1)

	// then
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), expectedApp, app)
}

func (s *AppSuite) TestAppRepo_FindApp_Miss() {
	// given
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE `app`.`id` = ? ORDER BY `app`.`id` LIMIT 1")).
		WillReturnError(gorm.ErrRecordNotFound)

	// when
	app, err := s.appRepository.FindApp(context.Background(), 1)

	// then
	assert.Nil(s.T(), err)
	assert.Nil(s.T(), app)
}

func (s *AppSuite) TestAppRepo_DeleteAllAppOwners() {
	// given
	s.Mock.ExpectBegin()
	s.Mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `app_user` WHERE app_id in (?)")).
		WithArgs(1).
		WillReturnResult(sqlmock.NewResult(0, 1))
	s.Mock.ExpectCommit()

	// when
	err := s.appRepository.DeleteAllAppOwners(context.Background(), []int64{1})

	// then
	assert.NoError(s.T(), err)
}

func (s *AppSuite) TestAppRepo_UpdateApp_Success() {
	// given
	s.Mock.ExpectBegin()
	now := time.Now()
	s.Mock.ExpectExec(regexp.QuoteMeta("UPDATE `app` SET `updated_at`=?,`name`=?,`code`=?,`build_path`=?,`lang_name`=?,`lang_version`=?,`repo_addr`=?,`status`=?,`cmdb_id`=?,`project_id`=?,`description`=?,`match_labels`=?,`service_labels`=?,`stand_label_envs`=?,`senv_status`=?,`need_external_route`=?,`eventlink_type`=?,`cmdb_info`=? WHERE id = ?")).
		WithArgs(sqlmock.AnyArg(), "test_app", "test_code", "/test", "Java", "8", "http://gitlab.com/test.git", 1, "test_cmdb_id", 1, "test_app_description", "{}", "{}", "prod", "enabled", true, "consumer,producer", 1, "{}").
		WillReturnResult(sqlmock.NewResult(1, 1))
	s.Mock.ExpectCommit()

	// when
	err := s.appRepository.UpdateApp(context.Background(), &App{
		BaseModel:         db.BaseModel{ID: 1, UpdatedAt: now},
		Name:              "test_app",
		Code:              "test_code",
		BuildPath:         "/test",
		LangName:          "Java",
		LangVersion:       "8",
		RepoAddr:          "http://gitlab.com/test.git",
		Status:            1,
		CmdbId:            "test_cmdb_id",
		ProjectID:         1,
		Description:       "test_app_description",
		MatchLabels:       db.DbStrMap{},
		ServiceLabels:     db.DbStrMap{},
		StandLabelEnvs:    "prod",
		SenvStatus:        "enabled",
		NeedExternalRoute: true,
		EventlinkType:     "consumer,producer",
		CmdbInfo:          []byte("{}"),
	})

	// then
	assert.NoError(s.T(), err)
}

func (s *AppSuite) TestAppRepo_FindAppBasicInfoById_Success() {
	// given
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE id = ?")).
		WillReturnRows(s.Mock.NewRows([]string{"id", "name", "lang_name"}).
			AddRow(int64(1), "app1", "java"))

	result, err := s.appRepository.FindAppBasicInfoById(context.Background(), int64(1))

	// then
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), int64(1), result.ID)
	assert.Equal(s.T(), "app1", result.Name)
	assert.Equal(s.T(), "java", result.LangName)
}

func (s *AppSuite) TestAppRepo_FindAppBasicInfoById_Error() {
	// given
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE id = ?")).
		WillReturnError(fmt.Errorf("查找失败"))

	_, err := s.appRepository.FindAppBasicInfoById(context.Background(), int64(1))

	// then
	assert.Equal(s.T(), "按条件查询应用信息出错：查找失败", err.Error())
}

func (s *AppSuite) TestAppRepo_UpdateApp_Clear_Description() {
	// given
	s.Mock.ExpectBegin()
	now := time.Now()
	s.Mock.ExpectExec(regexp.QuoteMeta("UPDATE `app` SET `updated_at`=?,`name`=?,`code`=?,`build_path`=?,`lang_name`=?,`lang_version`=?,`repo_addr`=?,`status`=?,`cmdb_id`=?,`project_id`=?,`description`=?,`match_labels`=?,`service_labels`=?,`stand_label_envs`=?,`senv_status`=?,`need_external_route`=?,`eventlink_type`=? WHERE id = ?")).
		WithArgs(sqlmock.AnyArg(), "test_app", "test_code", "/test", "Java", "8", "http://gitlab.com/test.git", 1, "test_cmdb_id", 1, "", "{}", "{}", "prod", "enabled", true, "producer,consumer", 1).
		WillReturnResult(sqlmock.NewResult(1, 1))
	s.Mock.ExpectCommit()

	// when
	err := s.appRepository.UpdateApp(context.Background(), &App{
		BaseModel:         db.BaseModel{ID: 1, UpdatedAt: now},
		Name:              "test_app",
		Code:              "test_code",
		BuildPath:         "/test",
		LangName:          "Java",
		LangVersion:       "8",
		RepoAddr:          "http://gitlab.com/test.git",
		Status:            1,
		CmdbId:            "test_cmdb_id",
		ProjectID:         1,
		Description:       "",
		MatchLabels:       make(db.DbStrMap),
		ServiceLabels:     make(db.DbStrMap),
		StandLabelEnvs:    "prod",
		SenvStatus:        "enabled",
		NeedExternalRoute: true,
		EventlinkType:     "producer,consumer",
	})

	// then
	assert.NoError(s.T(), err)
}

func (s *AppSuite) TestAppRepo_FindAppsByIds_Success() {
	// given
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE id in (?) AND project_id = ?")).
		WillReturnRows(s.Mock.NewRows([]string{"id", "name", "lang_name"}).
			AddRow(int64(1), "app1", "java"))

	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app_user` WHERE `app_user`.`app_id` = ?")).
		WillReturnRows(s.Mock.NewRows([]string{"app_id", "user_id"}).
			AddRow(int64(1), int64(2)))

	result, err := s.appRepository.FindAppsByIds(context.Background(), []int64{1}, 1)

	// then
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), int64(1), result[0].ID)
	assert.Equal(s.T(), "app1", result[0].Name)
	assert.Equal(s.T(), "java", result[0].LangName)
}

func (s *AppSuite) TestAppRepo_FindAppsByIds_Error() {
	// given
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE id in (?)")).
		WillReturnError(fmt.Errorf("get apps failed"))

	_, err := s.appRepository.FindAppsByIds(context.Background(), []int64{1}, 0)

	// then
	assert.Equal(s.T(), "get apps failed", err.Error())
}

const (
	testAppTableSQL = `
create table app
(
    id             int auto_increment comment '主键Id' primary key,
    name           varchar(128) not null comment '应用名称',
    code           varchar(128) not null comment '应用代号(唯一标识)',
    build_path     varchar(100) null comment '构建路径',
    repo_addr      varchar(200) null comment '源码库地址',
    status         int          not null comment '状态',
    cmdb_id        varchar(32)  not null comment '对应的cmdb的 唯一Id',
    project_id     int          not null comment '所属项目id',
    created_at     datetime     not null comment '创建时间',
    updated_at     datetime     not null comment '更新时间',
    description    varchar(400) null comment '描述',
    lang_name      varchar(255) null comment '语言名称',
    lang_version   varchar(255) not null comment '语言版本',
    match_labels   json         null comment '服务发布时特殊的MatchLabels',
    service_labels json         null comment 'k8s Service selector Labels',
	stand_label_envs  varchar(64) null comment '使用标准化Labels部署的环境配置',
	senv_status varchar(64) null comment '',
	need_external_route tinyint(1) null comment '是否需要外部路由',
	eventlink_type varchar(64) null comment 'eventlink类型',
    constraint uniq_cmdb_id unique (cmdb_id),
    constraint uniq_name unique (project_id, name),
    constraint uniq_project_app unique (project_id, name)
) comment '应用信息表' collate = utf8mb4_general_ci;
`
	testAppUserTableSQL = `
create table app_user
(
    id         bigint auto_increment comment 'id'  primary key,
    app_id     bigint                              not null comment '应用id',
    user_id    bigint                              not null comment '用户id',
    created_at timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp default CURRENT_TIMESTAMP not null comment '更新时间',
    constraint uniq_app_user unique (app_id, user_id)
)comment '应用负责人信息表' collate = utf8mb4_general_ci;
`

	testAppIdx1SQL = "create index idx_project_id on app (project_id);"
)

func TestAppRepoSuite(t *testing.T) {
	suite.Run(t, new(AppRepoSuite))
}

type AppRepoSuite struct {
	testdb.Suite
	repo AppRepository
}

func (s *AppRepoSuite) SetupTest() {
	s.Suite.SetupTest()
	s.AddTable(testAppTableSQL)
	s.AddTable(testAppUserTableSQL)
	s.AddIndex(testAppIdx1SQL)
	s.AddTable(testProjectTableSQL)
	s.repo = NewAppRepository(db.DB)
}

func (s *AppRepoSuite) makeApp() *App {
	return &App{
		Name:              "hello",
		Code:              "hello-service",
		BuildPath:         "./",
		LangVersion:       "1.2",
		LangName:          "java",
		RepoAddr:          "https://gitlab.ttyuyin.com/TT-Yunwei/cicd.git",
		Status:            1,
		CmdbId:            "sfxhj2367adkjk24",
		ProjectID:         1,
		Description:       "hello",
		MatchLabels:       make(db.DbStrMap),
		ServiceLabels:     make(db.DbStrMap),
		StandLabelEnvs:    "prod",
		SenvStatus:        "enabled",
		NeedExternalRoute: true,
		EventlinkType:     "consumer,producer",
		Project:           &Project{BaseModel: db.BaseModel{ID: 1}},
	}
}

func (s *AppRepoSuite) TestCreate_ShouldSuccess() {
	// given
	a := s.makeApp()

	// when
	err := s.repo.Insert(context.Background(), a)

	// then
	s.Require().NoError(err)
	s.Require().Equal(int64(1), a.ID)
}

func (s *AppRepoSuite) TestDelete_ShouldSuccess() {
	// given
	a := s.makeApp()
	_ = s.repo.Insert(context.Background(), a)

	// when
	err := s.repo.Delete(a)

	// then
	s.Require().NoError(err)
}

func (s *AppRepoSuite) TestFindAll_ShouldSuccess() {
	// given
	a1 := s.makeApp()
	a2 := s.makeApp()
	a2.Name = "hello2"
	a2.CmdbId = "cmdbid2"
	_ = s.repo.Insert(context.Background(), a1)
	_ = s.repo.Insert(context.Background(), a2)

	// when
	var (
		total int64
	)
	query := model.AppQuery{
		ProjectId: 1,
	}
	apps, err := s.repo.FindAll(context.Background(), &query, &total)

	// then
	s.Require().NoError(err)
	s.Require().Equal(2, len(apps))
	s.Require().Equal(int64(len(apps)), total)
}

func (s *AppRepoSuite) TestFindAppsByUserAndProject_ShouldSuccess() {
	// given
	a := s.makeApp()
	a.Users = []AppUser{
		{AppId: 1, UserId: 1},
	}
	_ = s.repo.Insert(context.Background(), a)

	// when
	apps, err := s.repo.FindAppsByUserAndProject(context.Background(), 1, 1)

	// then
	s.Require().NoError(err)
	s.Require().Equal(1, len(apps))
}

func (s *AppRepoSuite) TestInsertAppOwner_shouldSuccess() {
	// given
	au := []AppUser{
		{AppId: 1, UserId: 1},
	}

	// when
	err := s.repo.InsertAppOwner(context.Background(), au)

	// then
	s.Require().NoError(err)
}

func (s *AppRepoSuite) TestFindAppBy_shouldSuccess() {
	// given
	a := s.makeApp()
	_ = s.repo.Insert(context.Background(), a)

	// when
	findA, err := s.repo.FindAppBy(context.Background(), a.ProjectID, a.Name)

	// then
	s.Require().NoError(err)
	s.Require().Equal(a.ID, findA.ID)
	s.Require().Equal(a.Name, findA.Name)
}

func (s *AppRepoSuite) TestFindApp_shouldSuccess() {
	// given
	a := s.makeApp()
	a.Users = []AppUser{
		{AppId: 1, UserId: 1},
	}
	_ = s.repo.Insert(context.Background(), a)

	// when
	findA, err := s.repo.FindApp(context.Background(), a.ID)

	// then
	s.Require().NoError(err)
	s.Require().Equal(a.ID, findA.ID)
	s.Require().Equal(a.Name, findA.Name)
	s.Require().Equal(len(a.Users), len(findA.Users))
}

func (s *AppRepoSuite) TestFindRepoByUrl_shouldSuccess() {
	s.T().Skip("skip")
}

func (s *AppRepoSuite) TestDeleteAllAppOwners_shouldSuccess() {
	// given
	a := s.makeApp()
	a.Users = []AppUser{
		{AppId: 1, UserId: 1},
	}
	_ = s.repo.Insert(context.Background(), a)

	// when
	err := s.repo.DeleteAllAppOwners(context.Background(), []int64{a.ID})

	// then
	s.Require().NoError(err)
}

func (s *AppRepoSuite) TestFindAppBasicInfoById_shouldSuccess() {
	// given
	a := s.makeApp()
	_ = s.repo.Insert(context.Background(), a)

	// when
	findA, err := s.repo.FindAppBasicInfoById(context.Background(), a.ID)

	// then
	s.Require().NoError(err)
	s.Require().Equal(a.ID, findA.ID)
	s.Require().Equal(a.Name, findA.Name)
}

func (s *AppRepoSuite) TestUpdateApp_shouldSuccess() {
	// given
	a := s.makeApp()
	a.Users = []AppUser{
		{AppId: 1, UserId: 1},
	}
	_ = s.repo.Insert(context.Background(), a)

	// when
	a.Name = "newName"
	err := s.repo.UpdateApp(context.Background(), a)

	// then
	s.Require().NoError(err)
	findA, _ := s.repo.FindAppBasicInfoById(context.Background(), a.ID)
	s.Require().Equal(a.Name, findA.Name)
}

func (s *AppRepoSuite) TestFindAppsByIds_shouldSuccess() {
	// given
	a1 := s.makeApp()
	a2 := s.makeApp()
	a2.Name = "hello2"
	a2.CmdbId = "cmdbid2"
	_ = s.repo.Insert(context.Background(), a1)
	_ = s.repo.Insert(context.Background(), a2)

	// when
	apps, err := s.repo.FindAppsByIds(context.Background(), []int64{1, 2}, a1.ProjectID)

	// then
	s.Require().NoError(err)
	s.Require().Equal(2, len(apps))
}

func (s *AppRepoSuite) TestFindProjectAppsByLanguage_shouldSuccess() {
	// given
	a := s.makeApp()
	_ = s.repo.Insert(context.Background(), a)

	// when
	query := model.ProjectAppsParam{
		ID:       a.ID,
		Language: a.LangName,
	}
	apps, err := s.repo.FindProjectAppsByLanguage(context.Background(), &query)

	// then
	s.Require().NoError(err)
	s.Require().NotNil(apps)
}

func (s *AppSuite) TestListAppSuccess() {
	query := &model.AppQuery{
		ProjectId: 1,
		Name:      "haha",
	}

	var apps []App

	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE project_id = ? AND name like ?")).
		WithArgs(query.ProjectId, "%"+query.Name+"%").
		WillReturnRows(s.Mock.NewRows([]string{"id", "name", "code", "build_path", "repo_addr", "status", "cmdb_id", "project_id", "created_at", "updated_at", "description", "lang_name", "lang_version"}).
			AddRow(1, "haha-001", "haha001", "./", "https://gitlab.ttyuyin.com/TT-Yunwei/cicd.git", 1, "sfxhj2367adkjk24", 1, time.Now(), time.Now(), "hello", "java", "1.2").
			AddRow(2, "haha-002", "haha002", "./", "https://gitlab.ttyuyin.com/TT-Yunwei/cicd.git", 1, "sfxhj2367adkjk24", 1, time.Now(), time.Now(), "hello", "java", "1.2"))
	err := s.appRepository.List(context.Background(), query, &apps)
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), 2, len(apps))
	assert.Equal(s.T(), "haha-001", apps[0].Name)
	assert.Equal(s.T(), "haha-002", apps[1].Name)
}

func (s *AppRepoSuite) TestBatchUpdateAppCols_shouldSuccess() {
	// given
	a := s.makeApp()
	_ = s.repo.Insert(context.Background(), a)

	// when
	apps := []App{{BaseModel: db.BaseModel{ID: 1}, BuildPath: "./"}}

	err := s.repo.BatchUpdateAppCols(context.Background(), apps, "build_path")

	// then
	s.Require().NoError(err)
}

func (s *AppRepoSuite) TestBatchUpdateAppCols_AppsIsNone_shouldSuccess() {
	// given
	a := s.makeApp()
	_ = s.repo.Insert(context.Background(), a)

	// when
	apps := make([]App, 0)

	err := s.repo.BatchUpdateAppCols(context.Background(), apps, "build_path")

	// then
	s.Require().NoError(err)
}

func (s *AppRepoSuite) TestSearchApps_Success() {
	// given
	a := s.makeApp()
	_ = s.repo.Insert(context.Background(), a)

	// when
	apps, err := s.repo.SearchApps(context.Background(), a.Name)

	// then
	s.Require().NoError(err)
	s.Require().Equal(a.ID, apps[0].ID)
}

func (s *AppRepoSuite) TestSearchApps_NotFound() {
	// given
	a := s.makeApp()
	_ = s.repo.Insert(context.Background(), a)

	// when
	apps, err := s.repo.SearchApps(context.Background(), "notFound")

	// then
	s.Require().NoError(err)
	s.Require().Len(apps, 0)
}

func (s *AppSuite) Test_GetUserApps_Success() {
	query := model.UserAppsReq{
		UserID:     1,
		ProjectIDs: []int64{1},
		AppName:    "hello",
	}

	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE BINARY name like ? AND project_id in (?) ORDER BY project_id, name")).
		WillReturnRows(s.Mock.NewRows([]string{"id", "name", "code", "build_path", "repo_addr", "status", "cmdb_id", "project_id", "created_at", "updated_at", "description", "lang_name", "lang_version"}).
			AddRow(1, "haha-001", "haha001", "./", "https://gitlab.ttyuyin.com/TT-Yunwei/cicd.git", 1, "sfxhj2367adkjk24", 1, time.Now(), time.Now(), "hello", "java", "1.2").
			AddRow(2, "haha-002", "haha002", "./", "https://gitlab.ttyuyin.com/TT-Yunwei/cicd.git", 1, "sfxhj2367adkjk24", 1, time.Now(), time.Now(), "hello", "java", "1.2"))
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `project` WHERE `project`.`id` = ?")).
		WillReturnRows(s.Mock.NewRows([]string{"id", "name"}).
			AddRow(1, "测试项目"))

	apps, err := s.appRepository.GetUserApps(context.Background(), query)
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), 2, len(apps))
	assert.Equal(s.T(), "haha-001", apps[0].Name)
	assert.Equal(s.T(), "haha-002", apps[1].Name)
}

func (s *AppSuite) Test_GetUserApps_Error() {
	query := model.UserAppsReq{
		UserID:     1,
		ProjectIDs: []int64{1},
		AppName:    "hello",
	}

	wantErr := errors.New("get user apps failed")
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE BINARY name like ? AND project_id in (?) ORDER BY project_id, name")).
		WillReturnError(wantErr)

	_, err := s.appRepository.GetUserApps(context.Background(), query)
	assert.Equal(s.T(), wantErr, err)
}

func (s *AppSuite) Test_GetUserPreferenceApps_Success() {
	query := model.UserAppsReq{
		UserID:     1,
		ProjectIDs: []int64{1},
		AppName:    "hello",
	}

	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE BINARY name like ? AND project_id in (?) AND id in (select app_id from app_preference WHERE user_id = ?) ORDER BY project_id, name")).
		WillReturnRows(s.Mock.NewRows([]string{"id", "name", "code", "build_path", "repo_addr", "status", "cmdb_id", "project_id", "created_at", "updated_at", "description", "lang_name", "lang_version"}).
			AddRow(1, "haha-001", "haha001", "./", "https://gitlab.ttyuyin.com/TT-Yunwei/cicd.git", 1, "sfxhj2367adkjk24", 1, time.Now(), time.Now(), "hello", "java", "1.2").
			AddRow(2, "haha-002", "haha002", "./", "https://gitlab.ttyuyin.com/TT-Yunwei/cicd.git", 1, "sfxhj2367adkjk24", 1, time.Now(), time.Now(), "hello", "java", "1.2"))
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `project` WHERE `project`.`id` = ?")).
		WillReturnRows(s.Mock.NewRows([]string{"id", "name"}).
			AddRow(1, "测试项目"))

	apps, err := s.appRepository.GetUserPreferenceApps(context.Background(), query)
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), 2, len(apps))
	assert.Equal(s.T(), "haha-001", apps[0].Name)
	assert.Equal(s.T(), "haha-002", apps[1].Name)
}

func (s *AppSuite) Test_GetUserPreferenceApps_Error() {
	query := model.UserAppsReq{
		UserID:     1,
		ProjectIDs: []int64{1},
		AppName:    "hello",
	}

	wantErr := errors.New("get user apps failed")
	s.Mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `app` WHERE BINARY name like ? AND project_id in (?) AND id in (select app_id from app_preference WHERE user_id = ?) ORDER BY project_id, name")).
		WillReturnError(wantErr)

	_, err := s.appRepository.GetUserPreferenceApps(context.Background(), query)
	assert.Equal(s.T(), wantErr, err)
}
