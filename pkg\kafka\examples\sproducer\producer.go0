package main

import (
	"context"
	"encoding/json"

	"52tt.com/cicd/pkg/kafka"
	"52tt.com/cicd/pkg/log"
)

var (
	brokers = []string{"************:9092"}
	topic   = "pipeline-test"
)

type EventMsg struct {
	Detail string
	ID     uint64
}

type Service struct {
	syncProducer *kafka.SyncProducer
}

func (s *Service) PushEvent() error {
	err := s.syncProducer.Publish("test1", []byte("hello world sync"))
	if err != nil {
		return err
	}

	payload := &EventMsg{
		Detail: "payload-detail",
		ID:     1,
	}
	err = s.syncProducer.PublishAny("test2", payload, func(value any) ([]byte, error) {
		return json.Marshal(payload)
	})
	return err

}

func (s *Service) Stop() {
	s.syncProducer.Close(context.TODO())
}

func NewService(cfg *kafka.Config) *Service {
	s := &Service{}

	sp, err := kafka.NewSyncProducer(cfg)
	if err != nil {
		log.Errorf("NewEventProducer fail: %v", err)
	}
	s.syncProducer = sp

	return s
}

func main() {
	cfg := kafka.NewConfig(kafka.Name("test-producer"), kafka.Brokers(brokers), kafka.ProducerTopic(topic))

	s := NewService(cfg)

	if err := s.PushEvent(); err != nil {
		log.Errorf("err %v\n", err)
	}

	// stop
	s.Stop()
}
