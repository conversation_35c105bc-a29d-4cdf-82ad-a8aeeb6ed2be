CREATE TABLE `task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称中文',
  `stage_id` bigint NOT NULL COMMENT '关联阶段ID',
  `config` json NOT NULL COMMENT '任务配置信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='流水线模板任务';
