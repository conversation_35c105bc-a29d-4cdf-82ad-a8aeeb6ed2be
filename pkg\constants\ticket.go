package constants

type TicketType string

const (
	RaiseTestTicket       TicketType = "RAISE_TEST"       // 提测工单
	GrayUpgradeTicket     TicketType = "GRAY_UPGRADE"     // 灰度升级工单
	UpgradeTicket         TicketType = "UPGRADE"          // 升级工单
	TestAcceptanceTicket  TicketType = "TEST_ACCEPTANCE"  // 测试验收工单
	ReDeployTicket        TicketType = "RE_DEPLOY"        // 重新部署工单
	RouteDeployTicket     TicketType = "ROUTE_DEPLOY"     // 路由部署工单
	EventlinkDeployTicket TicketType = "EVENTLINK_DEPLOY" // eventlink工单
)

type TicketStatus string

const (
	TicketPending   TicketStatus = "PENDING"   // 等待中，节点过渡状态
	TicketRunning   TicketStatus = "RUNNING"   // 审批中
	TicketApproved  TicketStatus = "APPROVED"  // 已通过
	TicketRejected  TicketStatus = "REJECTED"  // 已驳回
	TicketRevoked   TicketStatus = "REVOKED"   // 已撤销
	TicketAbandoned TicketStatus = "ABANDONED" // 已废弃
	TicketChanged   TicketStatus = "CHANGED"   // 验收人已变更
	TicketResent    TicketStatus = "RESENT"    // 已重发消息

	LarkApproved string = "飞书审批通过"
	LarkRejected string = "飞书审批驳回"
)

func (ty TicketType) String() string {
	return string(ty)
}

// IsOneOf check TicketType is one of the following
func (ty TicketType) IsOneOf(types ...TicketType) bool {
	for _, t := range types {
		if ty == t {
			return true
		}
	}
	return false
}

func (ts TicketStatus) String() string {
	return string(ts)
}

func Chinese(t TicketType) string {
	switch t {
	case RaiseTestTicket:
		return "提测工单"
	case GrayUpgradeTicket:
		return "灰度升级工单"
	case UpgradeTicket:
		return "升级工单"
	case TestAcceptanceTicket:
		return "测试验收工单"
	case ReDeployTicket:
		return "升级工单"
	case RouteDeployTicket:
		return "路由部署工单"
	case EventlinkDeployTicket:
		return "eventlink部署工单"
	default:
		return ""
	}
}

func (ts TicketStatus) Chinese() string {
	switch ts {
	case TicketPending:
		return "等待中"
	case TicketRunning:
		return "审批中"
	case TicketApproved:
		return "已通过"
	case TicketRejected:
		return "已驳回"
	case TicketRevoked:
		return "已撤销"
	default:
		return ""
	}
}
