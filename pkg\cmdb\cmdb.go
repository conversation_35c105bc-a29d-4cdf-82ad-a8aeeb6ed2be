//go:generate mockgen -destination=cmdb_mock.go -package=cmdb -source=cmdb.go
package cmdb

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
	apperr "52tt.com/cicd/services/app/pkg/error"
)

var _ Service = (*Client)(nil)

const (
	CMDB_APPID_EXISTED = -101
	ModelList          = "/cmdb/inner-general-model/v2/%v/"
)

type Client struct {
	Host    string
	Token   string
	session httpclient.Session
}

func NewClient(host, token string) *Client {
	session := httpclient.NewSession(&httpclient.SessionOption{})
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": token,
	}
	session.SetHeaders(headers)
	client := &Client{
		Host:    host,
		Token:   token,
		session: session,
	}
	return client
}

// Service 是cmdb的接口，对应接口文档：https://q9jvw0u5f5.feishu.cn/docs/doccn8vPYWxIuL2QyV4IRukeeKc#hW4bN8
type Service interface {
	// CreateApp 创建应用信息
	CreateApp(context.Context, *App) (string, error)

	// UpdateApp 修复应用信息
	UpdateApp(context.Context, *App) (*Response, error)

	// GetApps 获取应用详情
	GetApps(context.Context, []string) ([]CmdbApp, error)

	// GetAlias 获取应用alias
	GetAlias(context.Context, *App) (string, error)

	// GetModulesTree 获取业务模块 树
	GetModulesTree(context.Context) (*CmdbModelTreeResponse, error)

	// GetModulesSubTree 业务模块子树
	GetModulesSubTree(context.Context, string) (*CmdbModelTreeResponse, error)

	// GetModelList 获取模型实例数据列表
	GetModelList(ctx context.Context, modelCode string, params map[string]string) ([]map[string]interface{}, error)

	// BatchUpdateApp 批量更新应用信息
	BatchUpdateApp(ctx context.Context, apps *BatchUpdateCmdbApp) (*BatchUpdateCmdbResponse, error)
	CheckAppCanDel(ctx context.Context, uid string) (bindCrs int, err error)
	DelApp(ctx context.Context, uid string) (err error)
	// Common 通用请求
	Common(ctx context.Context, httpRequest *http.Request, url string) (any, error)
}

func (c *Client) buildUrl(path string) string {
	newUrl := strings.Replace(c.Host, "/api/jsonrpc/", "", 1) + fmt.Sprintf(path)
	return newUrl
}

func (c *Client) CreateApp(ctx context.Context, app *App) (string, error) {
	req := NewRequest("create_app", app)
	httpReq, err := httpclient.NewRequest(c.Host, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "初始化CMDB请求参数错误: %v", err)
		return "", err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口创建应用错误: %v, %+v", err, httpResp.String())
		return "", err
	}
	var resp Response
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.ErrorWithCtx(ctx, "结构化CMDB Response 失败: %v", err)
		return "", err
	}
	result, _ := resp.Result.(map[string]interface{})
	//获取返回的cmdb id
	id, exists := result["id"]
	if exists {
		return id.(string), nil
	} else {
		return "", fmt.Errorf("请求cmdb创建应用信息错误")
	}
}

func (c *Client) GetAlias(ctx context.Context, app *App) (string, error) {
	req := NewRequest("gen_app_alias", app)
	httpReq, err := httpclient.NewRequest(c.Host, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "初始化CMDB请求参数错误: %v", err)
		return "", err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "获取CMDB应用可读标识错误: %v", err)
		return "", err
	}
	var response Response
	if err = httpResp.JsonToStruct(&response); err != nil {
		log.ErrorWithCtx(ctx, "结构化CMDB Response 失败: %v", err)
		return "", err
	}
	if response.Err != nil {
		if response.ErrorCode() == CMDB_APPID_EXISTED {
			return "", apperr.ErrAppExisted
		}
		return "", fmt.Errorf("获取CMDB应用可读标识错误: %v", response.Err)
	}
	if response.Result != nil {
		result, ok := response.Result.(map[string]interface{})
		if ok {
			return result["app_alias"].(string), nil
		}
	}
	return "", fmt.Errorf("获取CMDB应用可读标识错误: %v", response)
}

func (c *Client) UpdateApp(ctx context.Context, app *App) (*Response, error) {
	req := NewRequest("patch_app", app)
	httpReq, err := httpclient.NewRequest(c.Host, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "修改CMDB应用序列化参数错误: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口修改应用错误: %v", err)
		return nil, err
	}
	var response Response
	if err = httpResp.JsonToStruct(&response); err != nil {
		log.ErrorWithCtx(ctx, "结构化CMDB Response 失败: %v", err)
		return nil, err
	}
	if response.Err != nil {
		if response.ErrorCode() == CMDB_APPID_EXISTED {
			return nil, apperr.ErrAppExisted
		}
		return nil, fmt.Errorf("请求CMDB接口修改应用错误: %v", response.Err)
	}
	return &response, nil
}

func (c *Client) GetApps(ctx context.Context, ids []string) ([]CmdbApp, error) {
	params := map[string][]string{
		"id_list": ids,
	}
	req := NewRequest("batch_app_list", params)
	httpReq, err := httpclient.NewRequest(c.Host, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "获取CMDB应用详情序列化参数错误: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口获取应用错误: %v", err)
		return nil, err
	}
	var response AppListResponse
	if err = httpResp.JsonToStruct(&response); err != nil {
		log.ErrorWithCtx(ctx, "结构化CMDB Response 失败: %v", err)
		return nil, err
	}
	if response.Err != nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口获取应用错误: %v", response.Err)
		return nil, fmt.Errorf("请求CMDB接口获取应用错误: %v", response.Err)
	}
	if response.Result == nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口获取应用错误: %v", response)
		return nil, fmt.Errorf("请求CMDB接口获取应用错误: %s", response.String())
	}

	return response.Result, nil
}

func (c *Client) GetModulesTree(ctx context.Context) (*CmdbModelTreeResponse, error) {
	params := map[string]string{
		"version": "latest",
	}
	req := NewRequest("business_label_tree", params)
	httpReq, err := httpclient.NewRequest(c.Host, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "获取CMDB业务模块序列化参数错误: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口获取业务模块错误: %v", err)
		return nil, err
	}
	var response CmdbModelTreeResponse
	if err = httpResp.JsonToStruct(&response); err != nil {
		log.ErrorWithCtx(ctx, "结构化CMDB Response 失败: %v", err)
		return nil, err
	}
	return &response, nil
}

func (c *Client) GetModulesSubTree(ctx context.Context, nodeId string) (*CmdbModelTreeResponse, error) {
	params := map[string]string{
		"parent":  nodeId,
		"version": "latest",
	}
	req := NewRequest("business_label_children", params)
	httpReq, err := httpclient.NewRequest(c.Host, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "获取CMDB业务子模块序列化参数错误: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口获取业务子模块错误: %v", err)
		return nil, err
	}
	var response CmdbModelTreeResponse
	if err = httpResp.JsonToStruct(&response); err != nil {
		log.ErrorWithCtx(ctx, "结构化CMDB Response 失败: %v", err)
		return nil, err
	}
	return &response, nil
}

func (c *Client) GetModelList(ctx context.Context, modelCode string, params map[string]string) ([]map[string]interface{}, error) {
	reqParams := make(httpclient.RequestWithMapParams, 0)
	for k, v := range params {
		reqParams[k] = v
	}
	url := c.buildUrl(fmt.Sprintf(ModelList, modelCode))
	httpReq, err := httpclient.NewRequest(url, reqParams)
	if err != nil {
		log.ErrorWithCtx(ctx, "初始化CMDB请求参数错误: %v", err)
		return nil, err
	}

	httpResp, err := c.session.Get(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口获取模型实例数据列表错误: %v", err)
		return nil, err
	}
	var resp CmdbModelListResponse
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.ErrorWithCtx(ctx, "结构化获取模型实例数据列表Response 失败: %v", err)
		return nil, err
	}
	if resp.Code != 0 {
		codeErr := fmt.Errorf("获取模型实例数据列表失败: %v", resp.Error)
		log.Errorf("%v", codeErr)
		return nil, codeErr
	}

	return resp.Items, nil
}

func (c *Client) BatchUpdateApp(ctx context.Context, apps *BatchUpdateCmdbApp) (*BatchUpdateCmdbResponse, error) {
	req := NewRequest("patch_apps", apps)
	httpReq, err := httpclient.NewRequest(c.Host, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "[BatchUpdateApp] parse request content error: %v", err)
		return nil, err
	}
	log.InfoWithCtx(ctx, "BatchUpdateApp req: %v", req)
	startedTime := time.Now()
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[BatchUpdateApp] request error: %v", err)
		return nil, err
	}
	endTime := time.Now()
	log.DebugWithCtx(ctx, "[BatchUpdateApp] request cmdb time %v, %v, %v", endTime.Sub(startedTime).Seconds(), startedTime, endTime)
	var response BatchUpdateCmdbResponse
	if err = httpResp.JsonToStruct(&response); err != nil {
		log.ErrorWithCtx(ctx, "[BatchUpdateApp] parse Response error: %v", err)
		return nil, err
	}
	if response.Err != nil {
		log.ErrorWithCtx(ctx, "[BatchUpdateApp] patch apps error: %v", response.Err)
		if response.ErrorCode() == CMDB_APPID_EXISTED {
			return nil, apperr.ErrAppExisted
		}
		return nil, fmt.Errorf("请求CMDB接口批量修改应用错误: %v", response.Error())
	}
	return &response, nil
}

func (c *Client) CheckAppCanDel(ctx context.Context, uid string) (bindCrs int, err error) {
	params := map[string]string{
		"uid": uid,
	}
	req := NewRequest("cicd_application_check", params)
	httpReq, err := httpclient.NewRequest(c.Host, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "初始化CMDB请求参数错误: %v", err)
		return 0, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口检查应用是否可删除错误: %v", err)
		return 0, err
	}
	var response Response
	if err = httpResp.JsonToStruct(&response); err != nil {
		log.ErrorWithCtx(ctx, "结构化CMDB Response 失败: %v", err)
		return 0, err
	}
	if response.Result != nil {
		result, ok := response.Result.(map[string]any)
		if ok {
			code, _ := result["code"].(float64)
			data, _ := result["data"].(float64)
			if code == 0 {
				bindCrs = int(data)
			} else {
				err = fmt.Errorf("检查应用是否可删除错误: CMDB API Code %f", code)
				return
			}
		}
	}
	return
}

func (c *Client) DelApp(ctx context.Context, uid string) (err error) {
	params := map[string]string{
		"uid": uid,
	}
	req := NewRequest("cicd_application_delete", params)
	httpReq, err := httpclient.NewRequest(c.Host, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "初始化CMDB请求参数错误: %v", err)
		return err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口删除应用错误: %v", err)
		return err
	}
	var response Response
	if err = httpResp.JsonToStruct(&response); err != nil {
		log.ErrorWithCtx(ctx, "结构化CMDB Response 失败: %v", err)
		return err
	}
	if response.Result != nil {
		result, ok := response.Result.(map[string]any)
		if ok {
			code, _ := result["code"].(float64)
			msg, _ := result["msg"].(string)
			if code != 0 {
				err = fmt.Errorf("删除应用错误: CMDB API Code %f,MSG:%s", code, msg)
				return
			}
		}
	}
	return
}

func NewRequest(method string, args any) Request {
	req := Request{
		ID:      0,
		Method:  method,
		Params:  args,
		Jsonrpc: "2.0",
	}
	return req
}

type Response struct {
	Id      int         `json:"id,omitempty"`
	Jsonrpc string      `json:"jsonrpc,omitempty"`
	Result  interface{} `json:"result,omitempty"`
	Err     interface{} `json:"error,omitempty"`
}

type Request struct {
	ID      int         `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params"`
	Jsonrpc string      `json:"jsonrpc"`
}

func (resp Response) Error() string {
	if data, ok := resp.Err.(map[string]interface{}); ok {
		return (data["message"]).(string)
	}
	return fmt.Sprintf("cmdb error: %v", resp.Err)
}

func (resp Response) ErrorCode() int {
	if data, ok := resp.Err.(map[string]interface{}); ok {
		return int(data["code"].(float64))
	}
	return 0
}

func (resp Response) String() string {
	data, err := json.Marshal(resp)
	if err != nil {
		return ""
	}
	return string(data)
}

type AppListResponse struct {
	Response
	Result []CmdbApp `json:"result,omitempty"`
}

type User struct {
	Email    string `json:"email,omitempty"`
	Name     string `json:"name,omitempty"`
	Phone    string `json:"phone,omitempty"`
	RealName string `json:"real_name,omitempty"`
}

type App struct {
	ID                   string   `json:"id,omitempty"`
	Name                 string   `json:"name,omitempty"`
	Level                string   `json:"level,omitempty"` //应用等级
	AppClass             string   `json:"app_class,omitempty"`
	BusinessLabelL1ObjId string   `json:"_business_label_l1_obj_id,omitempty"`
	BusinessLabelL2ObjId string   `json:"_business_label_l2_obj_id,omitempty"`
	BusinessLabelL3ObjId string   `json:"_business_label_l3_obj_id,omitempty"`
	BusinessLabelL4ObjId string   `json:"_business_label_l4_obj_id,omitempty"`
	BusinessLabelL1Name  string   `json:"business_label_l1_name,omitempty"`
	BusinessLabelL2Name  string   `json:"business_label_l2_name,omitempty"`
	BusinessLabelL3Name  string   `json:"business_label_l3_name,omitempty"`
	BusinessLabelL4Name  string   `json:"business_label_l4_name,omitempty"`
	IsBundled            bool     `json:"is_bundled,omitempty"`        //是否捆绑应用
	Language             string   `json:"language,omitempty"`          //应用语言, java/python/golang
	Category             string   `json:"category,omitempty"`          //应用类别
	AppCreateTime        int      `json:"app_create_time,omitempty"`   //应用创建时间
	DevelopUserList      []string `json:"develop_user_list,omitempty"` //应用负责人列表
	Description          string   `json:"description,omitempty"`
}

func (a App) String() string {
	data, err := json.Marshal(a)
	if err != nil {
		return ""
	}
	return string(data)
}

type CmdbApp struct {
	App
	DevelopUserList []User `json:"develop_user_list,omitempty"`
	OpsUserList     []User `json:"ops_user_list,omitempty"`
}

type CmdbModelTreeResponse struct {
	Response
	Result map[string]interface{} `json:"result,omitempty"`
}

type CmdbModelListResponse struct {
	Code  int8                     `json:"code,omitempty"`
	Count int32                    `json:"count,omitempty"`
	Items []map[string]interface{} `json:"items,omitempty"`
	Error string                   `json:"error,omitempty"`
}

type BatchUpdateCmdbApp struct {
	Apps               []App    `json:"apps"`
	BusinessLabelObjID string   `json:"_business_label_obj_id"` // 应用模块列表
	DevelopUserList    []string `json:"develop_user_list"`      //应用负责人列表
}

type BatchUpdateCmdbAppItem struct {
	ID    string `json:"id,omitempty"`
	Alias string `json:"alias,omitempty"`
}

type BatchUpdateCmdbResult struct {
	Status bool                     `json:"status,omitempty"`
	Apps   []BatchUpdateCmdbAppItem `json:"apps,omitempty"`
}

type BatchUpdateCmdbResponse struct {
	Response
	Result BatchUpdateCmdbResult `json:"result,omitempty"`
}

func (c *Client) Common(ctx context.Context, httpRequest *http.Request, url string) (any, error) {
	// 创建新的请求
	cmdbDomain := c.buildUrl("")
	// 复制body
	reqBody, err := ioutil.ReadAll(httpRequest.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "读取请求body错误: %v", err)
	}
	var bodyJson map[string]interface{}
	if err = json.Unmarshal(reqBody, &bodyJson); err != nil {
		log.ErrorWithCtx(ctx, "解析请求body错误: %v", err)
	}
	// 复制params
	reqParams := make(httpclient.RequestWithMapParams, 0)
	for key, values := range httpRequest.URL.Query() {
		for _, value := range values {
			reqParams[key] = value
		}
	}
	httpReq, err := httpclient.NewRequest(cmdbDomain+"/"+url, httpclient.RequestWithJson{Data: bodyJson}, reqParams)
	if err != nil {
		log.ErrorWithCtx(ctx, "初始化CMDB请求参数错误: %v", err)
		return nil, err
	}
	// 发送请求
	httpResp, err := c.session.Get(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求CMDB接口获取数据错误: %v", err)
		return nil, err
	}

	var response json.RawMessage
	if err = httpResp.JsonToStruct(&response); err != nil {
		log.ErrorWithCtx(ctx, "结构化cmdb数据Response 失败: %v", err)
		return nil, err
	}
	return response, nil
}
