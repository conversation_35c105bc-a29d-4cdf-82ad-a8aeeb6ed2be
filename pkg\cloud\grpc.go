package cloud

import (
	"context"
	"crypto/tls"

	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	mesh "golang.ttyuyin.com/genproto/quwanapis/cloud/mesh/v1alpha"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"

	"52tt.com/cicd/pkg/log"
)

type gClient struct {
	mesh.ServiceRouteServiceClient
	mesh.RouteManagementServiceClient
	constack.WorkloadServiceClient
	constack.ScaledObjectServiceClient
	constack.KubeConfigServiceClient
	constack.AnyResourceServiceClient
	constack.ResourceSyncServiceClient
	rc credentials.PerRPCCredentials
}

var _ GRPCClient = (*gClient)(nil)

func NewGRPCClient(url, token string) (GRPCClient, error) {
	g := &gClient{
		rc: newPerRPCCredential(token),
	}
	conn, err := grpc.DialContext(context.Background(), url,
		grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{MinVersion: tls.VersionTLS12})),
		grpc.WithChainUnaryInterceptor(g.withToken))
	if err != nil {
		log.Errorf("failed to dial grpc, err: %v, target: %s", err, url)
		return nil, err
	}
	log.Infof("dial grpc success, target: %s", url)
	g.ServiceRouteServiceClient = mesh.NewServiceRouteServiceClient(conn)
	g.RouteManagementServiceClient = mesh.NewRouteManagementServiceClient(conn)
	g.WorkloadServiceClient = constack.NewWorkloadServiceClient(conn)
	g.ScaledObjectServiceClient = constack.NewScaledObjectServiceClient(conn)
	g.KubeConfigServiceClient = constack.NewKubeConfigServiceClient(conn)
	g.AnyResourceServiceClient = constack.NewAnyResourceServiceClient(conn)
	g.ResourceSyncServiceClient = constack.NewResourceSyncServiceClient(conn)
	return g, nil
}

var _ grpc.UnaryClientInterceptor = (*gClient)(nil).withToken

func (g *gClient) withToken(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	return invoker(ctx, method, req, reply, cc, append(opts, grpc.PerRPCCredentials(g.rc))...)
}

// perRPCCredential implements "grpccredentials.PerRPCCredentials" interface.
type perRPCCredential struct {
	authToken string
}

var _ credentials.PerRPCCredentials = (*perRPCCredential)(nil)

func newPerRPCCredential(token string) *perRPCCredential { return &perRPCCredential{authToken: token} }

func (rc *perRPCCredential) RequireTransportSecurity() bool { return false }

func (rc *perRPCCredential) GetRequestMetadata(_ context.Context, _ ...string) (map[string]string, error) {
	return map[string]string{"X-TOKEN": rc.authToken}, nil
}
