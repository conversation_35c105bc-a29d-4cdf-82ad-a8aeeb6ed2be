FROM cr.ttyuyin.com/devops/golang:1.22 AS BuildStage

ARG serviceName

COPY . /go/src/52tt.com/cicd

WORKDIR /go/src/52tt.com/cicd

RUN export GOSUMDB=off && export GOPROXY=http://yw-nexus.ttyuyin.com:8081/repository/group-go/ && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o app services/${serviceName}/cmd/main.go

FROM cr.ttyuyin.com/library/ubuntu:16.04

ARG serviceName

WORKDIR /app/

COPY --from=BuildStage /go/src/52tt.com/cicd/app .
COPY --from=BuildStage /go/src/52tt.com/cicd/services/${serviceName}/etc/config.toml .
COPY --from=BuildStage /go/src/52tt.com/cicd/services services
COPY --from=BuildStage /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai

ENV TZ=Asia/Shanghai

ENTRYPOINT [ "/app/app" ]

CMD [ "--s", "." ]