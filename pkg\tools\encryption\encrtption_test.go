package encryption

import (
	"fmt"
	"testing"
)

const (
	content = `apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: 11=
    server: https://kubernetes.docker.internal:6443
  name: docker-desktop
contexts:
- context:
    cluster: docker-desktop
    user: docker-desktop
  name: docker-desktop
current-context: docker-desktop
kind: Config
preferences: {}
users:
- name: docker-desktop
  user:
    client-certificate-data: 22==
    client-key-data: 33=
`
)

func TestEncrypt(t *testing.T) {
	b := []byte(content)
	c, _ := Encrypt(b)
	fmt.Print(c)
}

func TestDecrypt(t *testing.T) {
	c := "WySupLGxvtUEE9nIl5/K5v0zqaA0PVjcNScsTWOjOEuqrInJ2IBdWJmfH67usiyCdbCcDPZ4F2Asqmlh10nMw1GWWbWH+07hb7441eX+gpFMDOsv6MNNbYhfRg0twpYXVHvxsH5ffLWJ75RYO/oyXBzCP/usAhob6QpCVvEovyA6LMKsZklOlEfZUhPlBB906CMHm4Vkz2jTBHRAben5Gu4rCV0oxrlZ5o5tkwooFqhBlObWgSDHj0mTi9+yXGI+2WUEKT1YSOBpDGFVa9OLULZ3fL+jlNQ8snbUhs9zEAYxgTYDcTvLx84SXzR/CE41hhFcUpctY3jPiBjtwffq2MNScS3X9XhNWaTE9eYcvWAl3hAHa4F7sIFdv7DBEG3oCjOg36+RfGRJt9fJgg+WSkmCjADeVmJ6fDfdQNNm3hqFhgdK94xw8G12J4MDE9jNbRDz3YFoO0qTJHXB26IobuB+ZVUj4nwqtcXWnufOWv7azE06uA43RIrc9j5VrBD7bHFeQa/58h75SC+ep0W+1l7IK22TIkKT3Ibo9acjiNhlww=="
	b, _ := Decrypt(c)
	fmt.Printf(b)
}
