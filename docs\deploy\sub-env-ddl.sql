CREATE TABLE `sub_env`
(
    `id`              bigint                                  NOT NULL AUTO_INCREMENT COMMENT 'id',
    `env`             tinyint                                 NOT NULL COMMENT '环境类型: 1开发、2测试、3灰度、4生产',
    `cluster`         varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '容器云集群',
    `namespace`       varchar(63) COLLATE utf8mb4_general_ci  NOT NULL COMMENT '容器云命名空间',
    `project_id`      bigint                                  NOT NULL COMMENT '项目id',
    `traffic_mark_id` bigint                                  NOT NULL COMMENT '流量标记id',
    `created_at`      timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='子环境';

-- 子环境版本标识
alter table sub_env add column env_target varchar(32) not null default 'sub' comment '版本号, constants.EnvTargetType';

-- 子环境 2.0 名称, 需要符合 kubernetes label value 命名规范
alter table sub_env add column senv varchar(63) not null default '' comment '子环境 2.0 名称, 需要符合 kubernetes label value 命名规范';

-- 分组归属
alter table sub_env add column belong tinyint not null default 0 comment '分组归属';