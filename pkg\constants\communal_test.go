package constants

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestEnumEnv_ToEnvType(t *testing.T) {
	testcases := []struct {
		name string
		src  int8
		dst  EnvType
	}{
		{
			name: "test int8_1_to_DEV",
			src:  1,
			dst:  DEV,
		},
		{
			name: "test int8_2_to_TESTING",
			src:  2,
			dst:  TESTING,
		},
		{
			name: "test int8_3_to_PREVIEW",
			src:  3,
			dst:  PREVIEW,
		},
		{
			name: "test int8_4_to_PRODUCTION",
			src:  4,
			dst:  PRODUCTION,
		},
		{
			name: "test int8_0_to_UNKNOWN",
			src:  0,
			dst:  EnvType("unknown"),
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			de := EnumEnv(tc.src)
			actual := de.ToEnvType()
			assert.Equal(t, tc.dst, actual)
		})
	}
}

func TestEnumEnv_String(t *testing.T) {
	testcases := []struct {
		name string
		src  EnumEnv
		dst  string
	}{
		{
			name: "test EnumEnvDev.String() equal DEV.String()",
			src:  EnumEnvDev,
			dst:  DEV.String(),
		},
		{
			name: "test EnumEnvTesting.String() equal TESTING.String()",
			src:  EnumEnvTesting,
			dst:  TESTING.String(),
		},
		{
			name: "test EnumEnvPreview.String() equal PREVIEW.String()",
			src:  EnumEnvPreview,
			dst:  PREVIEW.String(),
		},
		{
			name: "test EnumEnvProduction.String() equal PRODUCTION.String()",
			src:  EnumEnvProduction,
			dst:  PRODUCTION.String(),
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			actual := tc.src.String()
			assert.Equal(t, tc.dst, actual)
		})
	}
}
