CREATE TABLE `approval_flow_node_instance` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点名称',
  `flow_inst_id` bigint NOT NULL COMMENT '审批流实例id',
  `approval_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `opinions` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核意见',
  `approver_id` bigint DEFAULT NULL COMMENT '当前审核人id',
  `approver` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '当前审核人展示信息(name/employee_no)',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态',
  `report` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '验收报告地址',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_flow_inst_id` (`flow_inst_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批流节点实例信息表';
