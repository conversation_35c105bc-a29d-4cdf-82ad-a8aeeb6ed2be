package http

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/require"

	"52tt.com/cicd/pkg/cloud/http/core"
)

func TestGvrHTTPClient_GetResource(t *testing.T) {
	t.<PERSON>p()
	host := os.Getenv("CLOUD_HOST")
	token := os.Getenv("CLOUD_TOKEN")
	client := NewGVRHTTPClient(host, token)

	req := &core.GetResourceRequest{
		GVRClusterOption: core.GVRClusterOption{
			Cluster:   "k8s-tc-bj-cicd-test",
			Namespace: "cicd-dev",
		},
		Group:         "apps",
		Version:       "v1",
		Resource:      "deployments",
		ResourceNames: []string{"cicd-helloworld-2"},
	}

	resp, err := client.GetResource(context.Background(), req)
	require.NoError(t, err)
	t.Logf("%+v", resp)
}

func TestGvrHTTPClient_CreateResource(t *testing.T) {
	t.<PERSON><PERSON>()
	host := os.Getenv("CLOUD_HOST")
	token := os.Getenv("CLOUD_TOKEN")
	client := NewGVRHTTPClient(host, token)

	yamlContent := `
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  labels:
    will.test/name: "cicd-helloworld-2"
  name: cicd-helloworld-2
  namespace: cicd-dev
spec:
  ports:
  - name: http
    port: 80
    protocol: TCP
    targetPort: 8091
  - name: rpc
    port: 9000
    protocol: TCP
    targetPort: 9000
  type: ClusterIP
`

	req := &core.CreateResourceRequest{
		GVRClusterOption: core.GVRClusterOption{
			Cluster:   "k8s-tc-bj-cicd-test",
			Namespace: "cicd-dev",
		},
		Yaml: yamlContent,
	}

	resp, err := client.CreateResource(context.Background(), req)
	require.NoError(t, err)
	t.Logf("%+v", resp)
}

func TestGvrHTTPClient_DeleteResource(t *testing.T) {
	t.Skip()
	host := os.Getenv("CLOUD_HOST")
	token := os.Getenv("CLOUD_TOKEN")
	client := NewGVRHTTPClient(host, token)

	req := &core.DeleteResourceRequest{
		GVRClusterOption: core.GVRClusterOption{
			Cluster:   "k8s-tc-bj-cicd-test",
			Namespace: "cicd-dev",
		},
		Group:         "",
		Version:       "v1",
		Resource:      "services",
		ResourceNames: []string{"cicd-helloworld-2"},
	}

	resp, err := client.DeleteResource(context.Background(), req)
	require.NoError(t, err)
	t.Logf("%+v", resp)
}
