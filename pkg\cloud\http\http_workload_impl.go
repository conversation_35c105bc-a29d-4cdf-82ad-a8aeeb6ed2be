package http

import (
	"context"
	"errors"

	"52tt.com/cicd/pkg/log"

	"52tt.com/cicd/pkg/cloud/http/core"
	"52tt.com/cicd/pkg/httpclient"
)

// https://q9jvw0u5f5.feishu.cn/wiki/wikcn4wuf4LoKd1gpDFO8D5tOFh
const (
	readyReplicasGet = "/cicd/cluster/namespace/workload/ready-replicas/get"
)

func NewWorkloadHTTPClient(host, token string) core.WorkloadClient {
	session := buildSession(token)
	client := NewWorkloadHTTPClientWithSession(host, token, session)
	return client
}

func NewWorkloadHTTPClientWithSession(host, token string, session httpclient.Session) core.WorkloadClient {
	return &workloadHTTPClient{
		Host:    host,
		Token:   token,
		session: session,
	}
}

type workloadHTTPClient struct {
	Host    string
	Token   string
	session httpclient.Session
}

func (c *workloadHTTPClient) GetReadyReplicas(ctx context.Context, in *core.GetReadyReplicasRequest) (*core.GetReadyReplicasResponse, error) {
	url := buildURL(c.Host, readyReplicasGet)

	log.DebugWithCtx(ctx, "[GetReadyReplicas] req=%+v", in)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: in})
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetReadyReplicas] init cloud ceq, error: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Get(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetReadyReplicas] send req get ready replicas, error: %v, req=%+v", err, in)
		return nil, err
	}

	var out core.GetReadyReplicasResponse
	log.Debugf("httpResp: %v", httpResp)
	if err = httpResp.JsonToStruct(&out); err != nil {
		log.ErrorWithCtx(ctx, "[GetReadyReplicas] parse response, error: %v", err)
		return nil, err
	}
	if out.Code != 0 {
		log.ErrorWithCtx(ctx, "[GetReadyReplicas] get ready replicas failed, return code is not zero, req=%+v, resp=%+v", in, out)
		return nil, errors.New(out.Message)
	}

	log.DebugWithCtx(ctx, "[GetReadyReplicas] resp=%+v", out)
	return &out, nil
}
