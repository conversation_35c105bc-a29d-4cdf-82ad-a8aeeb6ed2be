// Code generated by MockGen. DO NOT EDIT.
// Source: client.go

// Package cloud is a generated GoMock package.
package cloud

import (
	context "context"
	reflect "reflect"

	core "52tt.com/cicd/pkg/cloud/http/core"
	gomock "github.com/golang/mock/gomock"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	continuousdeployment "golang.ttyuyin.com/genproto/quwanapis/cloud/continuousdeployment/v1alpha"
	mesh "golang.ttyuyin.com/genproto/quwanapis/cloud/mesh/v1alpha"
	grpc "google.golang.org/grpc"
)

// MockGRPCClient is a mock of GRPCClient interface.
type MockGRPCClient struct {
	ctrl     *gomock.Controller
	recorder *MockGRPCClientMockRecorder
}

// MockGRPCClientMockRecorder is the mock recorder for MockGRPCClient.
type MockGRPCClientMockRecorder struct {
	mock *MockGRPCClient
}

// NewMockGRPCClient creates a new mock instance.
func NewMockGRPCClient(ctrl *gomock.Controller) *MockGRPCClient {
	mock := &MockGRPCClient{ctrl: ctrl}
	mock.recorder = &MockGRPCClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGRPCClient) EXPECT() *MockGRPCClientMockRecorder {
	return m.recorder
}

// Apply mocks base method.
func (m *MockGRPCClient) Apply(ctx context.Context, in *constack.ApplyRequest, opts ...grpc.CallOption) (*constack.ApplyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Apply", varargs...)
	ret0, _ := ret[0].(*constack.ApplyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Apply indicates an expected call of Apply.
func (mr *MockGRPCClientMockRecorder) Apply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Apply", reflect.TypeOf((*MockGRPCClient)(nil).Apply), varargs...)
}

// BuildPrimaryRoute mocks base method.
func (m *MockGRPCClient) BuildPrimaryRoute(ctx context.Context, in *mesh.BuildPrimaryRouteRequest, opts ...grpc.CallOption) (*mesh.BuildPrimaryRouteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuildPrimaryRoute", varargs...)
	ret0, _ := ret[0].(*mesh.BuildPrimaryRouteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildPrimaryRoute indicates an expected call of BuildPrimaryRoute.
func (mr *MockGRPCClientMockRecorder) BuildPrimaryRoute(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildPrimaryRoute", reflect.TypeOf((*MockGRPCClient)(nil).BuildPrimaryRoute), varargs...)
}

// BuildSubenvRoute mocks base method.
func (m *MockGRPCClient) BuildSubenvRoute(ctx context.Context, in *mesh.BuildSubenvRouteRequest, opts ...grpc.CallOption) (*mesh.BuildSubenvRouteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuildSubenvRoute", varargs...)
	ret0, _ := ret[0].(*mesh.BuildSubenvRouteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildSubenvRoute indicates an expected call of BuildSubenvRoute.
func (mr *MockGRPCClientMockRecorder) BuildSubenvRoute(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildSubenvRoute", reflect.TypeOf((*MockGRPCClient)(nil).BuildSubenvRoute), varargs...)
}

// BuildSubenvRouteWithMultiTrafficMark mocks base method.
func (m *MockGRPCClient) BuildSubenvRouteWithMultiTrafficMark(ctx context.Context, in *mesh.BuildSubenvRouteWithMultiTrafficMarkRequest, opts ...grpc.CallOption) (*mesh.BuildSubenvRouteWithMultiTrafficMarkResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuildSubenvRouteWithMultiTrafficMark", varargs...)
	ret0, _ := ret[0].(*mesh.BuildSubenvRouteWithMultiTrafficMarkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildSubenvRouteWithMultiTrafficMark indicates an expected call of BuildSubenvRouteWithMultiTrafficMark.
func (mr *MockGRPCClientMockRecorder) BuildSubenvRouteWithMultiTrafficMark(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildSubenvRouteWithMultiTrafficMark", reflect.TypeOf((*MockGRPCClient)(nil).BuildSubenvRouteWithMultiTrafficMark), varargs...)
}

// BuildSubenvRouteWithWeight mocks base method.
func (m *MockGRPCClient) BuildSubenvRouteWithWeight(ctx context.Context, in *mesh.BuildSubenvRouteWithWeightRequest, opts ...grpc.CallOption) (*mesh.BuildSubenvRouteWithWeightResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuildSubenvRouteWithWeight", varargs...)
	ret0, _ := ret[0].(*mesh.BuildSubenvRouteWithWeightResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildSubenvRouteWithWeight indicates an expected call of BuildSubenvRouteWithWeight.
func (mr *MockGRPCClientMockRecorder) BuildSubenvRouteWithWeight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildSubenvRouteWithWeight", reflect.TypeOf((*MockGRPCClient)(nil).BuildSubenvRouteWithWeight), varargs...)
}

// ClearPrimaryRoute mocks base method.
func (m *MockGRPCClient) ClearPrimaryRoute(ctx context.Context, in *mesh.ClearPrimaryRouteRequest, opts ...grpc.CallOption) (*mesh.ClearPrimaryRouteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearPrimaryRoute", varargs...)
	ret0, _ := ret[0].(*mesh.ClearPrimaryRouteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearPrimaryRoute indicates an expected call of ClearPrimaryRoute.
func (mr *MockGRPCClientMockRecorder) ClearPrimaryRoute(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearPrimaryRoute", reflect.TypeOf((*MockGRPCClient)(nil).ClearPrimaryRoute), varargs...)
}

// ClearSubenvRoute mocks base method.
func (m *MockGRPCClient) ClearSubenvRoute(ctx context.Context, in *mesh.ClearSubenvRouteRequest, opts ...grpc.CallOption) (*mesh.ClearSubenvRouteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearSubenvRoute", varargs...)
	ret0, _ := ret[0].(*mesh.ClearSubenvRouteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearSubenvRoute indicates an expected call of ClearSubenvRoute.
func (mr *MockGRPCClientMockRecorder) ClearSubenvRoute(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearSubenvRoute", reflect.TypeOf((*MockGRPCClient)(nil).ClearSubenvRoute), varargs...)
}

// ClearSubenvRouteWithWeight mocks base method.
func (m *MockGRPCClient) ClearSubenvRouteWithWeight(ctx context.Context, in *mesh.ClearSubenvRouteWithWeightRequest, opts ...grpc.CallOption) (*mesh.ClearSubenvRouteWithWeightResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearSubenvRouteWithWeight", varargs...)
	ret0, _ := ret[0].(*mesh.ClearSubenvRouteWithWeightResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearSubenvRouteWithWeight indicates an expected call of ClearSubenvRouteWithWeight.
func (mr *MockGRPCClientMockRecorder) ClearSubenvRouteWithWeight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearSubenvRouteWithWeight", reflect.TypeOf((*MockGRPCClient)(nil).ClearSubenvRouteWithWeight), varargs...)
}

// Create mocks base method.
func (m *MockGRPCClient) Create(ctx context.Context, in *constack.CreateRequest, opts ...grpc.CallOption) (*constack.CreateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Create", varargs...)
	ret0, _ := ret[0].(*constack.CreateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockGRPCClientMockRecorder) Create(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockGRPCClient)(nil).Create), varargs...)
}

// Delete mocks base method.
func (m *MockGRPCClient) Delete(ctx context.Context, in *constack.DeleteRequest, opts ...grpc.CallOption) (*constack.DeleteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Delete", varargs...)
	ret0, _ := ret[0].(*constack.DeleteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockGRPCClientMockRecorder) Delete(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockGRPCClient)(nil).Delete), varargs...)
}

// DeleteCollection mocks base method.
func (m *MockGRPCClient) DeleteCollection(ctx context.Context, in *constack.DeleteCollectionRequest, opts ...grpc.CallOption) (*constack.DeleteCollectionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteCollection", varargs...)
	ret0, _ := ret[0].(*constack.DeleteCollectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCollection indicates an expected call of DeleteCollection.
func (mr *MockGRPCClientMockRecorder) DeleteCollection(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCollection", reflect.TypeOf((*MockGRPCClient)(nil).DeleteCollection), varargs...)
}

// DeleteWorkloadRelatedHpa mocks base method.
func (m *MockGRPCClient) DeleteWorkloadRelatedHpa(ctx context.Context, in *constack.DeleteWorkloadRelatedHpaRequest, opts ...grpc.CallOption) (*constack.DeleteWorkloadRelatedHpaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteWorkloadRelatedHpa", varargs...)
	ret0, _ := ret[0].(*constack.DeleteWorkloadRelatedHpaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteWorkloadRelatedHpa indicates an expected call of DeleteWorkloadRelatedHpa.
func (mr *MockGRPCClientMockRecorder) DeleteWorkloadRelatedHpa(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkloadRelatedHpa", reflect.TypeOf((*MockGRPCClient)(nil).DeleteWorkloadRelatedHpa), varargs...)
}

// Detail mocks base method.
func (m *MockGRPCClient) Detail(ctx context.Context, in *constack.DetailRequest, opts ...grpc.CallOption) (*constack.DetailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Detail", varargs...)
	ret0, _ := ret[0].(*constack.DetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Detail indicates an expected call of Detail.
func (mr *MockGRPCClientMockRecorder) Detail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Detail", reflect.TypeOf((*MockGRPCClient)(nil).Detail), varargs...)
}

// FindCluster mocks base method.
func (m *MockGRPCClient) FindCluster(ctx context.Context, in *constack.ClusterReq, opts ...grpc.CallOption) (*constack.ClusterResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindCluster", varargs...)
	ret0, _ := ret[0].(*constack.ClusterResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindCluster indicates an expected call of FindCluster.
func (mr *MockGRPCClientMockRecorder) FindCluster(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindCluster", reflect.TypeOf((*MockGRPCClient)(nil).FindCluster), varargs...)
}

// FindNS mocks base method.
func (m *MockGRPCClient) FindNS(ctx context.Context, in *constack.NamespaceReq, opts ...grpc.CallOption) (*constack.NamespaceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindNS", varargs...)
	ret0, _ := ret[0].(*constack.NamespaceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindNS indicates an expected call of FindNS.
func (mr *MockGRPCClientMockRecorder) FindNS(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindNS", reflect.TypeOf((*MockGRPCClient)(nil).FindNS), varargs...)
}

// Get mocks base method.
func (m *MockGRPCClient) Get(ctx context.Context, in *constack.GetRequest, opts ...grpc.CallOption) (*constack.GetResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(*constack.GetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockGRPCClientMockRecorder) Get(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockGRPCClient)(nil).Get), varargs...)
}

// GetConfig mocks base method.
func (m *MockGRPCClient) GetConfig(ctx context.Context, in *constack.GetConfigRequest, opts ...grpc.CallOption) (*constack.GetConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConfig", varargs...)
	ret0, _ := ret[0].(*constack.GetConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfig indicates an expected call of GetConfig.
func (mr *MockGRPCClientMockRecorder) GetConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfig", reflect.TypeOf((*MockGRPCClient)(nil).GetConfig), varargs...)
}

// GetVirtualServiceRelatedService mocks base method.
func (m *MockGRPCClient) GetVirtualServiceRelatedService(ctx context.Context, in *mesh.GetVirtualServiceRelatedServiceRequest, opts ...grpc.CallOption) (*mesh.GetVirtualServiceRelatedServiceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVirtualServiceRelatedService", varargs...)
	ret0, _ := ret[0].(*mesh.GetVirtualServiceRelatedServiceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualServiceRelatedService indicates an expected call of GetVirtualServiceRelatedService.
func (mr *MockGRPCClientMockRecorder) GetVirtualServiceRelatedService(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualServiceRelatedService", reflect.TypeOf((*MockGRPCClient)(nil).GetVirtualServiceRelatedService), varargs...)
}

// GetWorkloadRelatedHpa mocks base method.
func (m *MockGRPCClient) GetWorkloadRelatedHpa(ctx context.Context, in *constack.GetWorkloadRelatedHpaRequest, opts ...grpc.CallOption) (*constack.GetWorkloadRelatedHpaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWorkloadRelatedHpa", varargs...)
	ret0, _ := ret[0].(*constack.GetWorkloadRelatedHpaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkloadRelatedHpa indicates an expected call of GetWorkloadRelatedHpa.
func (mr *MockGRPCClientMockRecorder) GetWorkloadRelatedHpa(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkloadRelatedHpa", reflect.TypeOf((*MockGRPCClient)(nil).GetWorkloadRelatedHpa), varargs...)
}

// List mocks base method.
func (m *MockGRPCClient) List(ctx context.Context, in *constack.ListRequest, opts ...grpc.CallOption) (*constack.ListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "List", varargs...)
	ret0, _ := ret[0].(*constack.ListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockGRPCClientMockRecorder) List(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockGRPCClient)(nil).List), varargs...)
}

// ListGatewayHosts mocks base method.
func (m *MockGRPCClient) ListGatewayHosts(ctx context.Context, in *mesh.ListGatewayHostsRequest, opts ...grpc.CallOption) (*mesh.ListGatewayHostsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListGatewayHosts", varargs...)
	ret0, _ := ret[0].(*mesh.ListGatewayHostsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListGatewayHosts indicates an expected call of ListGatewayHosts.
func (mr *MockGRPCClientMockRecorder) ListGatewayHosts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGatewayHosts", reflect.TypeOf((*MockGRPCClient)(nil).ListGatewayHosts), varargs...)
}

// ListServiceRelatedVirtualService mocks base method.
func (m *MockGRPCClient) ListServiceRelatedVirtualService(ctx context.Context, in *mesh.ListServiceRelatedVirtualServiceRequest, opts ...grpc.CallOption) (*mesh.ListServiceRelatedVirtualServiceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListServiceRelatedVirtualService", varargs...)
	ret0, _ := ret[0].(*mesh.ListServiceRelatedVirtualServiceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListServiceRelatedVirtualService indicates an expected call of ListServiceRelatedVirtualService.
func (mr *MockGRPCClientMockRecorder) ListServiceRelatedVirtualService(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListServiceRelatedVirtualService", reflect.TypeOf((*MockGRPCClient)(nil).ListServiceRelatedVirtualService), varargs...)
}

// MultiResource mocks base method.
func (m *MockGRPCClient) MultiResource(ctx context.Context, in *constack.MultiResourceReq, opts ...grpc.CallOption) (*constack.MultiResourceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MultiResource", varargs...)
	ret0, _ := ret[0].(*constack.MultiResourceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiResource indicates an expected call of MultiResource.
func (mr *MockGRPCClientMockRecorder) MultiResource(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiResource", reflect.TypeOf((*MockGRPCClient)(nil).MultiResource), varargs...)
}

// Restart mocks base method.
func (m *MockGRPCClient) Restart(ctx context.Context, in *constack.RestartRequest, opts ...grpc.CallOption) (*constack.RestartResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Restart", varargs...)
	ret0, _ := ret[0].(*constack.RestartResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Restart indicates an expected call of Restart.
func (mr *MockGRPCClientMockRecorder) Restart(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Restart", reflect.TypeOf((*MockGRPCClient)(nil).Restart), varargs...)
}

// Save mocks base method.
func (m *MockGRPCClient) Save(ctx context.Context, in *constack.SaveRequest, opts ...grpc.CallOption) (*constack.SaveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Save", varargs...)
	ret0, _ := ret[0].(*constack.SaveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockGRPCClientMockRecorder) Save(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockGRPCClient)(nil).Save), varargs...)
}

// ScaleWorkloadReplicas mocks base method.
func (m *MockGRPCClient) ScaleWorkloadReplicas(ctx context.Context, in *constack.ScaleWorkloadReq, opts ...grpc.CallOption) (*constack.ScaleWorkloadResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScaleWorkloadReplicas", varargs...)
	ret0, _ := ret[0].(*constack.ScaleWorkloadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScaleWorkloadReplicas indicates an expected call of ScaleWorkloadReplicas.
func (mr *MockGRPCClientMockRecorder) ScaleWorkloadReplicas(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScaleWorkloadReplicas", reflect.TypeOf((*MockGRPCClient)(nil).ScaleWorkloadReplicas), varargs...)
}

// SyncUnifiedClusterSidecar mocks base method.
func (m *MockGRPCClient) SyncUnifiedClusterSidecar(ctx context.Context, in *constack.SyncUnifiedClusterSidecarReq, opts ...grpc.CallOption) (*constack.SyncUnifiedClusterSidecarResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SyncUnifiedClusterSidecar", varargs...)
	ret0, _ := ret[0].(*constack.SyncUnifiedClusterSidecarResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncUnifiedClusterSidecar indicates an expected call of SyncUnifiedClusterSidecar.
func (mr *MockGRPCClientMockRecorder) SyncUnifiedClusterSidecar(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncUnifiedClusterSidecar", reflect.TypeOf((*MockGRPCClient)(nil).SyncUnifiedClusterSidecar), varargs...)
}

// Update mocks base method.
func (m *MockGRPCClient) Update(ctx context.Context, in *constack.UpdateRequest, opts ...grpc.CallOption) (*constack.UpdateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Update", varargs...)
	ret0, _ := ret[0].(*constack.UpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockGRPCClientMockRecorder) Update(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockGRPCClient)(nil).Update), varargs...)
}

// MockDeployGRPCClient is a mock of DeployGRPCClient interface.
type MockDeployGRPCClient struct {
	ctrl     *gomock.Controller
	recorder *MockDeployGRPCClientMockRecorder
}

// MockDeployGRPCClientMockRecorder is the mock recorder for MockDeployGRPCClient.
type MockDeployGRPCClientMockRecorder struct {
	mock *MockDeployGRPCClient
}

// NewMockDeployGRPCClient creates a new mock instance.
func NewMockDeployGRPCClient(ctrl *gomock.Controller) *MockDeployGRPCClient {
	mock := &MockDeployGRPCClient{ctrl: ctrl}
	mock.recorder = &MockDeployGRPCClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeployGRPCClient) EXPECT() *MockDeployGRPCClientMockRecorder {
	return m.recorder
}

// DeployOAM mocks base method.
func (m *MockDeployGRPCClient) DeployOAM(ctx context.Context, in *continuousdeployment.DeployOAMRequest, opts ...grpc.CallOption) (continuousdeployment.ContinuousDeploymentService_DeployOAMClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeployOAM", varargs...)
	ret0, _ := ret[0].(continuousdeployment.ContinuousDeploymentService_DeployOAMClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeployOAM indicates an expected call of DeployOAM.
func (mr *MockDeployGRPCClientMockRecorder) DeployOAM(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeployOAM", reflect.TypeOf((*MockDeployGRPCClient)(nil).DeployOAM), varargs...)
}

// GenerateYaml mocks base method.
func (m *MockDeployGRPCClient) GenerateYaml(ctx context.Context, in *continuousdeployment.GenerateYamlRequest, opts ...grpc.CallOption) (*continuousdeployment.GenerateYamlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateYaml", varargs...)
	ret0, _ := ret[0].(*continuousdeployment.GenerateYamlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateYaml indicates an expected call of GenerateYaml.
func (mr *MockDeployGRPCClientMockRecorder) GenerateYaml(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateYaml", reflect.TypeOf((*MockDeployGRPCClient)(nil).GenerateYaml), varargs...)
}

// UnDeployOAM mocks base method.
func (m *MockDeployGRPCClient) UnDeployOAM(ctx context.Context, in *continuousdeployment.UnDeployOAMRequest, opts ...grpc.CallOption) (continuousdeployment.ContinuousDeploymentService_UnDeployOAMClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnDeployOAM", varargs...)
	ret0, _ := ret[0].(continuousdeployment.ContinuousDeploymentService_UnDeployOAMClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnDeployOAM indicates an expected call of UnDeployOAM.
func (mr *MockDeployGRPCClientMockRecorder) UnDeployOAM(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnDeployOAM", reflect.TypeOf((*MockDeployGRPCClient)(nil).UnDeployOAM), varargs...)
}

// UndoDeploy mocks base method.
func (m *MockDeployGRPCClient) UndoDeploy(ctx context.Context, in *continuousdeployment.UndoDeployRequest, opts ...grpc.CallOption) (continuousdeployment.ContinuousDeploymentService_UndoDeployClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UndoDeploy", varargs...)
	ret0, _ := ret[0].(continuousdeployment.ContinuousDeploymentService_UndoDeployClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UndoDeploy indicates an expected call of UndoDeploy.
func (mr *MockDeployGRPCClientMockRecorder) UndoDeploy(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UndoDeploy", reflect.TypeOf((*MockDeployGRPCClient)(nil).UndoDeploy), varargs...)
}

// MockHTTPClient is a mock of HTTPClient interface.
type MockHTTPClient struct {
	ctrl     *gomock.Controller
	recorder *MockHTTPClientMockRecorder
}

// MockHTTPClientMockRecorder is the mock recorder for MockHTTPClient.
type MockHTTPClientMockRecorder struct {
	mock *MockHTTPClient
}

// NewMockHTTPClient creates a new mock instance.
func NewMockHTTPClient(ctrl *gomock.Controller) *MockHTTPClient {
	mock := &MockHTTPClient{ctrl: ctrl}
	mock.recorder = &MockHTTPClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHTTPClient) EXPECT() *MockHTTPClientMockRecorder {
	return m.recorder
}

// CreateResource mocks base method.
func (m *MockHTTPClient) CreateResource(ctx context.Context, in *core.CreateResourceRequest) (*core.CreateResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateResource", ctx, in)
	ret0, _ := ret[0].(*core.CreateResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateResource indicates an expected call of CreateResource.
func (mr *MockHTTPClientMockRecorder) CreateResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateResource", reflect.TypeOf((*MockHTTPClient)(nil).CreateResource), ctx, in)
}

// CreateSubNamespace mocks base method.
func (m *MockHTTPClient) CreateSubNamespace(ctx context.Context, in *core.CreateSubNamespaceRequest) (*core.CreateSubNamespaceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubNamespace", ctx, in)
	ret0, _ := ret[0].(*core.CreateSubNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubNamespace indicates an expected call of CreateSubNamespace.
func (mr *MockHTTPClientMockRecorder) CreateSubNamespace(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubNamespace", reflect.TypeOf((*MockHTTPClient)(nil).CreateSubNamespace), ctx, in)
}

// DeleteResource mocks base method.
func (m *MockHTTPClient) DeleteResource(ctx context.Context, in *core.DeleteResourceRequest) (*core.DeleteResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteResource", ctx, in)
	ret0, _ := ret[0].(*core.DeleteResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteResource indicates an expected call of DeleteResource.
func (mr *MockHTTPClientMockRecorder) DeleteResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteResource", reflect.TypeOf((*MockHTTPClient)(nil).DeleteResource), ctx, in)
}

// FindAnyList mocks base method.
func (m *MockHTTPClient) FindAnyList(ctx context.Context, in core.AnyListReq, out any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAnyList", ctx, in, out)
	ret0, _ := ret[0].(error)
	return ret0
}

// FindAnyList indicates an expected call of FindAnyList.
func (mr *MockHTTPClientMockRecorder) FindAnyList(ctx, in, out interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAnyList", reflect.TypeOf((*MockHTTPClient)(nil).FindAnyList), ctx, in, out)
}

// GetReadyReplicas mocks base method.
func (m *MockHTTPClient) GetReadyReplicas(ctx context.Context, in *core.GetReadyReplicasRequest) (*core.GetReadyReplicasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReadyReplicas", ctx, in)
	ret0, _ := ret[0].(*core.GetReadyReplicasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReadyReplicas indicates an expected call of GetReadyReplicas.
func (mr *MockHTTPClientMockRecorder) GetReadyReplicas(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadyReplicas", reflect.TypeOf((*MockHTTPClient)(nil).GetReadyReplicas), ctx, in)
}

// GetResource mocks base method.
func (m *MockHTTPClient) GetResource(ctx context.Context, in *core.GetResourceRequest) (*core.GetResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResource", ctx, in)
	ret0, _ := ret[0].(*core.GetResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResource indicates an expected call of GetResource.
func (mr *MockHTTPClientMockRecorder) GetResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResource", reflect.TypeOf((*MockHTTPClient)(nil).GetResource), ctx, in)
}

// GetSubNamespace mocks base method.
func (m *MockHTTPClient) GetSubNamespace(ctx context.Context, in *core.GetSubNamespaceRequest) (*core.GetSubNamespaceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubNamespace", ctx, in)
	ret0, _ := ret[0].(*core.GetSubNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubNamespace indicates an expected call of GetSubNamespace.
func (mr *MockHTTPClientMockRecorder) GetSubNamespace(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubNamespace", reflect.TypeOf((*MockHTTPClient)(nil).GetSubNamespace), ctx, in)
}

// ListCluster mocks base method.
func (m *MockHTTPClient) ListCluster(ctx context.Context, in *core.ListClusterRequest) (*core.ListClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCluster", ctx, in)
	ret0, _ := ret[0].(*core.ListClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCluster indicates an expected call of ListCluster.
func (mr *MockHTTPClientMockRecorder) ListCluster(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCluster", reflect.TypeOf((*MockHTTPClient)(nil).ListCluster), ctx, in)
}

// ListClusterNamespace mocks base method.
func (m *MockHTTPClient) ListClusterNamespace(ctx context.Context, in *core.ListClusterNamespaceRequest) (*core.ListClusterNamespaceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClusterNamespace", ctx, in)
	ret0, _ := ret[0].(*core.ListClusterNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClusterNamespace indicates an expected call of ListClusterNamespace.
func (mr *MockHTTPClientMockRecorder) ListClusterNamespace(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClusterNamespace", reflect.TypeOf((*MockHTTPClient)(nil).ListClusterNamespace), ctx, in)
}

// ListResource mocks base method.
func (m *MockHTTPClient) ListResource(ctx context.Context, in *core.ListResourceRequest) (*core.ListResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListResource", ctx, in)
	ret0, _ := ret[0].(*core.ListResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListResource indicates an expected call of ListResource.
func (mr *MockHTTPClientMockRecorder) ListResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResource", reflect.TypeOf((*MockHTTPClient)(nil).ListResource), ctx, in)
}

// SetV2Host mocks base method.
func (m *MockHTTPClient) SetV2Host(hostV2 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetV2Host", hostV2)
}

// SetV2Host indicates an expected call of SetV2Host.
func (mr *MockHTTPClientMockRecorder) SetV2Host(hostV2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetV2Host", reflect.TypeOf((*MockHTTPClient)(nil).SetV2Host), hostV2)
}

// UpdateResource mocks base method.
func (m *MockHTTPClient) UpdateResource(ctx context.Context, in *core.UpdateResourceRequest) (*core.UpdateResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResource", ctx, in)
	ret0, _ := ret[0].(*core.UpdateResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateResource indicates an expected call of UpdateResource.
func (mr *MockHTTPClientMockRecorder) UpdateResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResource", reflect.TypeOf((*MockHTTPClient)(nil).UpdateResource), ctx, in)
}
