package model

import (
	"time"

	"52tt.com/cicd/pkg/constants"
)

type DeployChangeLog struct {
	ID                    int64                      `gorm:"column:id;AUTO_INCREMENT;primary_key"`     // id
	Env                   constants.EnumEnv          `gorm:"column:env;NOT NULL"`                      // 部署环境: 1开发、2测试、3灰度、4生产
	EnvTarget             constants.EnumEnvTarget    `gorm:"column:env_target;NOT NULL"`               // 部署环境目标：1基准环境、2子环境、3子环境2.0
	Cluster               string                     `gorm:"column:cluster;NOT NULL"`                  // 部署集群
	Namespace             string                     `gorm:"column:namespace;NOT NULL"`                // 部署命名空间
	AppID                 int64                      `gorm:"column:app_id;NOT NULL"`                   // 应用id
	AppName               string                     `gorm:"column:app_name;NOT NULL"`                 // 应用名称
	ConfigID              int64                      `gorm:"column:config_id;NOT NULL"`                // 配置id
	ConfigVersion         int                        `gorm:"column:config_version;NOT NULL"`           // 配置版本号
	ConfigType            constants.DeployConfigType `gorm:"column:config_type;default:1;NOT NULL"`    // 应用类型：1通用应用、2定时任务、3statefulset
	MetadataID            int64                      `gorm:"column:metadata_id;NOT NULL"`              // 部署配置id
	IsCurrent             bool                       `gorm:"column:is_current;NOT NULL"`               // 是否当前运行
	Description           string                     `gorm:"column:description;NOT NULL"`              // 描述
	Status                constants.DeployStatus     `gorm:"column:status;NOT NULL"`                   // 部署状态
	ArtifactVersion       string                     `gorm:"column:artifact_version;NOT NULL"`         // 制品版本
	TaskRunID             int64                      `gorm:"column:task_run_id;NOT NULL"`              // 任务实例id
	ImageUrl              string                     `gorm:"column:image_url;NOT NULL"`                // 镜像地址
	SubtaskRunId          int64                      `gorm:"column:subtask_run_id;"`                   // 子任务Id
	OperatorBy            int64                      `gorm:"column:operator_by;NOT NULL"`              // 操作人id
	OperatorByChineseName string                     `gorm:"column:operator_by_chinese_name;NOT NULL"` // 操作人中文名
	OperatorByEmployeeNo  string                     `gorm:"column:operator_by_employee_no;NOT NULL"`  // 操作人工号
	OperatedAt            time.Time                  `gorm:"column:operated_at"`                       // 操作时间
	Senv                  string                     `gorm:"column:senv"`                              // 子环境 2.0 名称
	Branch                string                     `gorm:"column:branch"`                            // 构建分支
	Action                constants.DeployAction     `gorm:"column:action;NOT NULL"`                   // 变更类型: 1部署、2回滚、3下线
	ProjectID             int64                      `gorm:"column:project_id"`
}

type ListChangeLogReq struct {
	IsCurrent      *bool
	Env            constants.EnumEnv
	EnvTarget      constants.EnumEnvTarget
	AppId          int64
	ProjectId      int64
	Action         constants.DeployAction
	OperatedBefore time.Time
}
