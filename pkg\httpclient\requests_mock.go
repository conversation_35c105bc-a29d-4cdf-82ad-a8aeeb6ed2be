// Code generated by MockGen. DO NOT EDIT.
// Source: ./pkg/httpclient/requests.go

// Package httpclient is a generated GoMock package.
package httpclient

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockRequestOption is a mock of RequestOption interface.
type MockRequestOption struct {
	ctrl     *gomock.Controller
	recorder *MockRequestOptionMockRecorder
}

// MockRequestOptionMockRecorder is the mock recorder for MockRequestOption.
type MockRequestOptionMockRecorder struct {
	mock *MockRequestOption
}

// NewMockRequestOption creates a new mock instance.
func NewMockRequestOption(ctrl *gomock.Controller) *MockRequestOption {
	mock := &MockRequestOption{ctrl: ctrl}
	mock.recorder = &MockRequestOptionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRequestOption) EXPECT() *MockRequestOptionMockRecorder {
	return m.recorder
}

// applyToRequest mocks base method.
func (m *MockRequestOption) applyToRequest(arg0 *Request) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "applyToRequest", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// applyToRequest indicates an expected call of applyToRequest.
func (mr *MockRequestOptionMockRecorder) applyToRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "applyToRequest", reflect.TypeOf((*MockRequestOption)(nil).applyToRequest), arg0)
}

// MockSession is a mock of Session interface.
type MockSession struct {
	ctrl     *gomock.Controller
	recorder *MockSessionMockRecorder
}

// MockSessionMockRecorder is the mock recorder for MockSession.
type MockSessionMockRecorder struct {
	mock *MockSession
}

// NewMockSession creates a new mock instance.
func NewMockSession(ctrl *gomock.Controller) *MockSession {
	mock := &MockSession{ctrl: ctrl}
	mock.recorder = &MockSessionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSession) EXPECT() *MockSessionMockRecorder {
	return m.recorder
}

// Delete mocks base method.
func (m *MockSession) Delete(ctx context.Context, request *Request) (*Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, request)
	ret0, _ := ret[0].(*Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockSessionMockRecorder) Delete(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockSession)(nil).Delete), ctx, request)
}

// Get mocks base method.
func (m *MockSession) Get(ctx context.Context, request *Request) (*Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, request)
	ret0, _ := ret[0].(*Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockSessionMockRecorder) Get(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockSession)(nil).Get), ctx, request)
}

// Post mocks base method.
func (m *MockSession) Post(ctx context.Context, request *Request) (*Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Post", ctx, request)
	ret0, _ := ret[0].(*Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Post indicates an expected call of Post.
func (mr *MockSessionMockRecorder) Post(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Post", reflect.TypeOf((*MockSession)(nil).Post), ctx, request)
}

// Put mocks base method.
func (m *MockSession) Put(ctx context.Context, request *Request) (*Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Put", ctx, request)
	ret0, _ := ret[0].(*Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Put indicates an expected call of Put.
func (mr *MockSessionMockRecorder) Put(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Put", reflect.TypeOf((*MockSession)(nil).Put), ctx, request)
}

// SetHeaders mocks base method.
func (m *MockSession) SetHeaders(headers map[string]string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetHeaders", headers)
}

// SetHeaders indicates an expected call of SetHeaders.
func (mr *MockSessionMockRecorder) SetHeaders(headers interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeaders", reflect.TypeOf((*MockSession)(nil).SetHeaders), headers)
}
