package context

import (
	"context"
	"net/url"
	"strconv"

	"google.golang.org/grpc/metadata"
)

func InjectRequestInfo(ctx context.Context, ri *RequestInfo) context.Context {
	newCtx := metadata.AppendToOutgoingContext(ctx,
		userid<PERSON>ey, strconv.FormatInt(ri.UserID, 10),
		username<PERSON>ey, ri.Username,
		chineseNameKey, url.QueryEscape(ri.ChineseName),
		employee<PERSON>o<PERSON>ey, ri.EmployeeNo,
		projectIdKey, strconv.FormatInt(ri.ProjectID, 10),
	)
	for _, v := range ri.Roles {
		newCtx = metadata.AppendToOutgoingContext(newCtx, rolesKey, v)
	}

	return newCtx
}

func InjectToken(ctx context.Context, val string) context.Context {
	return metadata.AppendToOutgoingContext(ctx, AuthorizationKey, val)
}

func InjectRequestID(ctx context.Context, val string) context.Context {
	return metadata.AppendToOutgoingContext(ctx, requestIDKey, val)
}

func InjectUserinfo(ctx context.Context, ui *UserInfo) context.Context {
	return appendToOutgoingCtxWithUserinfo(ctx, ui)
}

func InjectTrafficMark(ctx context.Context, val string) context.Context {
	return metadata.AppendToOutgoingContext(ctx, trafficMarkKey, val)
}

func appendToOutgoingCtxWithUserinfo(ctx context.Context, ui *UserInfo) context.Context {
	newCtx := metadata.AppendToOutgoingContext(ctx,
		useridKey, strconv.FormatInt(ui.UserID, 10),
		usernameKey, ui.Username,
		chineseNameKey, url.QueryEscape(ui.ChineseName),
		employeeNoKey, ui.EmployeeNo,
		projectIdKey, strconv.FormatInt(ui.ProjectID, 10),
	)
	for _, v := range ui.Roles {
		newCtx = metadata.AppendToOutgoingContext(newCtx, rolesKey, v)
	}

	return newCtx
}

func AppendToOutgoingCtx(ctx context.Context, key, val string) context.Context {
	return metadata.AppendToOutgoingContext(ctx, key, val)
}

func GetUserinfoFromIncomingCtx(ctx context.Context) *UserInfo {
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		return mdToUserinfo(md)
	}

	return &emptyUserinfo
}

func GetRequestInfoFromIncomingCtx(ctx context.Context) *RequestInfo {
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		return mdToRequestInfo(md)
	}

	return &emptyRequestInfo
}

func GetTokenFromIncomingCtx(ctx context.Context) string {
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		return getFirstStringInMD(md, AuthorizationKey)
	}

	return ""
}

func GetTrafficMarkFromCtx(ctx context.Context) string {
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		return getFirstStringInMD(md, trafficMarkKey)
	}

	return ""
}

func mdToRequestInfo(md metadata.MD) *RequestInfo {
	ri := &RequestInfo{}
	ri.UserInfo = *mdToUserinfo(md)
	ri.RequestID = getFirstStringInMD(md, requestIDKey)
	return ri
}

func mdToUserinfo(md metadata.MD) *UserInfo {
	ui := &UserInfo{}
	ui.UserID = getFirstInt64InMD(md, useridKey)
	ui.Username = getFirstStringInMD(md, usernameKey)
	ui.ChineseName, _ = url.QueryUnescape(getFirstStringInMD(md, chineseNameKey))
	ui.EmployeeNo = getFirstStringInMD(md, employeeNoKey)
	ui.ProjectID = getFirstInt64InMD(md, projectIdKey)
	ui.Roles = md.Get(rolesKey)

	return ui
}

func getFirstStringInMD(md metadata.MD, key string) string {
	if v := md.Get(key); len(v) > 0 {
		return v[0]
	}
	return ""
}

func getFirstInt64InMD(md metadata.MD, key string) int64 {
	if v := md.Get(key); len(v) > 0 {
		i, _ := strconv.ParseInt(v[0], 10, 64)
		return i
	}
	return 0
}
