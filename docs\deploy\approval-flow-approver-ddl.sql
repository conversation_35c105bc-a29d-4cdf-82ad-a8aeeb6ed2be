CREATE TABLE `approval_flow_approver` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `node_inst_id` bigint NOT NULL COMMENT '节点实例id',
  `approver_id` bigint NOT NULL COMMENT '节点审核人id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批流节点审核人';
