/*
 Navicat Premium Data Transfer

 Source Server         : cicd-test
 Source Server Type    : MySQL
 Source Server Version : 80022
 Source Host           : ************:3306
 Source Schema         : idehub

 Target Server Type    : MySQL
 Target Server Version : 80022
 File Encoding         : 65001

 Date: 11/06/2024 15:47:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for idehub_user_appconfig
-- ----------------------------
DROP TABLE IF EXISTS `idehub_user_appconfig`;
CREATE TABLE `idehub_user_appconfig`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `user_id` bigint(0) NOT NULL COMMENT '用户id',
  `app_id` bigint(0) NOT NULL COMMENT '应用id',
  `config` json NOT NULL COMMENT '部署配置',
  `nhctl_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'nhctl配置',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_user_app`(`user_id`, `app_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户服务部署配置表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
