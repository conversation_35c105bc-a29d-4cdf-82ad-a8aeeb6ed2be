package constants

type StoryNotifyStatus int8

const (
	// StoryNotifyStatusInit 初始化
	StoryNotifyStatusInit StoryNotifyStatus = 0
	// StoryNotifyStatusNotifying 通知中
	StoryNotifyStatusNotifying StoryNotifyStatus = 1
	// StoryNotifyStatusNotifyEnd 通知结束
	StoryNotifyStatusNotifyEnd StoryNotifyStatus = 2
)

type StoryPipeRunStep int8

const (
	StoryPipeRunStepInit              StoryPipeRunStep = 0  // 初始化
	StoryPipeRunStepRaiseTestApproved StoryPipeRunStep = 30 // 提测工单已通过
	StoryPipeRunStepTestDeployed      StoryPipeRunStep = 50 // 部署测试环境已完成
	StoryPipeRunStepTestApproved      StoryPipeRunStep = 60 // 测试验证工单已通过
	StoryPipeRunStepUpgradeCreated    StoryPipeRunStep = 80 // 升级工单已创建
	StoryPipeRunStepUpgradeApproved   StoryPipeRunStep = 85 // 升级工单已通过
	StoryPipeRunStepStagingDeployed   StoryPipeRunStep = 90 // 预发布已部署
)

type StoryStatus string

const (
	StoryStatusDeveloping  StoryStatus = "开发中"
	StoryStatusToBeTest    StoryStatus = "待测试"
	StoryStatusTesting     StoryStatus = "测试中"
	StoryStatusStageDeploy StoryStatus = "预发布部署"
	StoryStatusToBeAccept  StoryStatus = "待验收"
	StoryStatusAccepting   StoryStatus = "验收中"
	StoryStatusToBePublish StoryStatus = "待发布"
)

func (s StoryStatus) String() string {
	return string(s)
}
