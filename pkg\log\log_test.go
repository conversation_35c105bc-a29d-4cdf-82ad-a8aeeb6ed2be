package log

import (
	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/log/hook"
	myzap "52tt.com/cicd/pkg/log/logger/zap"
	"context"
	"testing"
)

func TestLog(t *testing.T) {
	ri := &cctx.RequestInfo{
		UserInfo: cctx.UserInfo{
			UserID:      297,
			Username:    "zhangwei",
			ChineseName: "张伟",
			EmployeeNo:  "T1862",
			Roles:       nil,
			ProjectID:   1,
		},
		RequestID: "1234567890",
	}
	ctx := cctx.WithRequestInfo(context.Background(), ri)

	Debugf("test log")
	DebugWithCtx(ctx, "ctx >> test log")

	RegisterHook(hook.ExtraFieldFromContext)
	Debugf("hook >> test log")
	DebugWithCtx(ctx, "hook + ctx >> test log")
	DebugWithCtx(ctx, "hook + ctx >> test log2")

	myzap.EnableFuncName()
	Debugf("hook + ctx + func name >> test log")
	DebugWithCtx(ctx, "hook + ctx + func name >> test log")
	DebugWithCtx(ctx, "hook + ctx + func name >> test log2")
}
