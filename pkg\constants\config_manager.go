package constants

import "fmt"

type ConfigVersionStatus string

const (
	ConfigVersionStatusActive    ConfigVersionStatus = "active"    // 生效中
	ConfigVersionStatusInactive  ConfigVersionStatus = "inactive"  // 未生效
	ConfigVersionStatusApproving ConfigVersionStatus = "approving" // 审批中
)

type ApolloAppId string

func (a ApolloAppId) String() string {
	return string(a)
}

const (
	ApolloAppIdDev  ApolloAppId = "tt-cicd-dev"  // 开发环境
	ApolloAppIdQa   ApolloAppId = "tt-cicd-qa"   // 测试环境
	ApolloAppIdStag ApolloAppId = "tt-cicd-stag" // 预发布环境
	ApolloAppIdProd ApolloAppId = "tt-cicd-prod" // 生产环境
)

var envTypeApolloAppIdMapping = map[EnvType]ApolloAppId{
	DEV:        ApolloAppIdDev,
	TESTING:    ApolloAppIdQa,
	PREVIEW:    ApolloAppIdStag,
	PRODUCTION: ApolloAppIdProd,
}

func (e EnvType) ToApolloAppId() ApolloAppId {
	v, ok := envTypeApolloAppIdMapping[e]
	if ok {
		return v
	}
	return ""
}

const (
	ApolloDefaultAccount     = "apollo"
	ApolloDefaultClusterName = "default"
	ApolloDefaultKey         = "content"
)

const ApolloNamespacePrefix = "tt-cicd-config" // apollo namespace前缀

const (
	ApolloRedisProjectKey   = "tt-cicd-config-%v-prId-%d-%d" // apollo redis project global key
	ApolloRedisUserGroupKey = "tt-cicd-config-%v-ug-%v-%d"   // apollo redis user group global key
)

func GetApolloRedisProjectKey(env EnvType, projectId, appId int64) string {
	return fmt.Sprintf(ApolloRedisProjectKey, env, projectId, appId)
}

func GetApolloRedisUserGroupKey(env EnvType, userGroup string, appId int64) string {
	return fmt.Sprintf(ApolloRedisUserGroupKey, env, userGroup, appId)
}

const (
	DefaultThirdPartyChineseName = "第三方接口"
	DefaultThirdPartyEmployeeNo  = "System"
)
