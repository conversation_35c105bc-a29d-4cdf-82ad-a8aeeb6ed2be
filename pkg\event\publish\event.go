package publish

import (
	"context"
	"encoding/json"

	"52tt.com/cicd/pkg/kafka"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/tools"
)

const EventKey = "cicd-event"

type EventType string

const (
	AppDeploy               EventType = "appDeploy"
	PipelineRunStatusChange EventType = "pipelineRunStatusChange"
	TaskRunStatusChange     EventType = "taskRunStatusChange"
	SubTaskRunStatusChange  EventType = "subTaskRunStatusChange"
	StageRunStatusChange    EventType = "stageRunStatusChange"
)

type EventDispatcher interface {
	Dispatch(context.Context, *Event) error
}

type EventPublisher struct {
	producer *kafka.Producer
}

func NewEventPublisher(config *kafka.Config) (EventDispatcher, error) {
	producer, err := kafka.NewProducer(config)
	if err != nil {
		return nil, err
	}
	return &EventPublisher{producer: producer}, nil
}

func (ep *EventPublisher) Dispatch(ctx context.Context, event *Event) error {
	//event marshal
	data, err := json.Marshal(event)
	if err != nil {
		log.ErrorWithCtx(ctx, "[publishEvent] event marshal error: %v", err)
		return err
	}
	ep.producer.Publish(EventKey, data)

	log.InfoWithCtx(ctx, "[publishEvent] event publish success: %v", string(data))
	return nil
}

// Event 事件模型
type Event struct {
	// 事件 unique id
	UUID string `json:"uuid"`
	//事件类型
	Type EventType `json:"type"`
	// 事件关联的上下文元数据
	Metadata Metadatas `json:"metadata"`
	// 事件源payload
	Payload interface{} `json:"payload"`
}

type AppInfos struct {
	CmdbId      string `json:"cmdbId,omitempty"`
	AppId       int64  `json:"appId,omitempty"`
	AppName     string `json:"appName,omitempty"`
	LangName    string `json:"langName,omitempty"`
	LangVersion string `json:"langVersion,omitempty"`
	ProjectID   int64  `json:"projectID,omitempty"`
	ProjectName string `json:"projectName,omitempty"`
}

type Metadatas struct {
	AppInfo AppInfos `json:"appInfo,omitempty"`
}

func OfEvent(ctx context.Context, eventType EventType, meta Metadatas, payload interface{}) *Event {
	log.InfoWithCtx(ctx, "[OfEvent] eventType: %s, payload: %v", eventType, payload)
	e := &Event{
		UUID:     tools.UUID(),
		Type:     eventType,
		Metadata: meta,
		Payload:  payload,
	}
	return e
}
