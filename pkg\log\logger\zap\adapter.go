package zap

import (
	"52tt.com/cicd/pkg/log/core"
	"go.uber.org/zap"
)

var defaultLevelManager = &levelManager{}

func CurrentLevelManager() core.LevelManager {
	return defaultLevelManager
}

type levelManager struct{}

func (l *levelManager) CurrentLevel() core.Level {
	lv, _ := core.ParseLevel(defaultAtomLevel.Level().String())
	return lv
}

func (l *levelManager) SetLevel(lv core.Level) {
	defaultAtomLevel.SetLevel(Level(lv))
}

var defaultHookManager = &hookManager{hooks: []core.Hook{}}

func CurrentHookManager() core.HookManager {
	return defaultHookManager
}

type hookManager struct {
	hooks []core.Hook
}

func (h *hookManager) RegisterHook(hooks ...core.Hook) {
	h.hooks = append(h.hooks, hooks...)
	resetLogger(defaultLogger.WithOptions(zap.AddCallerSkip(len(hooks) * 2)))
}

func (h *hookManager) Hooks() []core.Hook {
	return h.hooks
}
