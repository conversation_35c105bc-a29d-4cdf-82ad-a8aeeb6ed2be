-- 路由规则表
CREATE TABLE `deploy`.`route` (
    `id`           bigint                                  NOT NULL AUTO_INCREMENT COMMENT 'id',
    `app_id`       bigint                                  NOT NULL COMMENT '服务id',
    `app_name`     varchar(128) NOT NULL COMMENT '服务名',
    `protocol`     varchar(128) NOT NULL COMMENT '协议',
    `revision`     bigint NOT NULL COMMENT '版本id',
    `config` json  comment '路由配置',
    `created_by_chinese_name` varchar(128) NOT NULL COMMENT '操作人中文名',
    `created_by_employee_no`  varchar(128) NOT NULL COMMENT '操作人工号',
    `created_at`   timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1:删除 0:未删除',
    PRIMARY KEY (`id`),
    KEY `idx_app_id` (`app_id`)
) ENGINE=InnoDB  COMMENT='路由规则表';


-- 应用路由规则记录表
CREATE TABLE `deploy`.`route_apply` (
    `id`            bigint          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `route_name`    varchar(63)     NOT NULL COMMENT '路由名称',
    `env`           varchar(64)     NOT NULL COMMENT '环境类型',
    `cluster`       varchar(256)    NOT NULL COMMENT '部署集群',
    `namespace`     varchar(63)     NOT NULL COMMENT '部署命名空间',
    `route_id`      bigint          NOT NULL COMMENT '路由规则id',
    `gateway_cfg`   json                     COMMENT '网关组合配置',
    `revision`      bigint          NOT NULL COMMENT '版本id',
    `generation`    bigint          NOT NULL DEFAULT 0 COMMENT '提单时的VirtualService资源的generation版本号',
    `app_id`        bigint          NOT NULL COMMENT '服务id',
    `app_name`      varchar(128)    NOT NULL COMMENT '服务名',
    `created_by_chinese_name` varchar(128) NOT NULL COMMENT '操作人中文名',
    `created_by_employee_no`  varchar(128) NOT NULL COMMENT '操作人工号',
    `created_at`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_applied`   tinyint(1)   NOT NULL DEFAULT 0 COMMENT '1:已应用 0:未应用,工单审批中',
    `ticket_id`    bigint       NOT NULL DEFAULT 0 COMMENT '工单id',
    PRIMARY KEY (`id`),
    KEY `idx_app` (`app_id`,`is_applied`),
    KEY `idx_name` (`route_name`)
) ENGINE=InnoDB  COMMENT='应用路由规则记录表';

-- 网关配置
CREATE TABLE `deploy`.`gateway` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) NOT NULL COMMENT '网关名称',
  `env` tinyint NOT NULL COMMENT '环境类型: 2测试、4生产',
  `gateways` varchar(2048) NOT NULL COMMENT '网关配置',
  `hosts` varchar(2048) NOT NULL COMMENT 'hosts',
  `protocol` varchar(32) NOT NULL COMMENT '协议',
  `project_id` bigint NOT NULL COMMENT '项目id',
  `user_group` varchar(64) NOT NULL default '' COMMENT '所属用户群体',
  `created_by` bigint NOT NULL COMMENT '创建人',
  `created_by_chinese_name` varchar(255) NOT NULL COMMENT '创建人中文名',
  `created_by_employee_no` varchar(64) NOT NULL COMMENT '创建人工号',
  `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB COMMENT='路由网关配置';

-- 路由管理配置
CREATE TABLE `deploy`.`route_manage_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `project_id` bigint NOT NULL COMMENT '项目id',
  `approval_flow_id` bigint NOT NULL COMMENT '审批流id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB COMMENT='路由管理配置';

-- 服务
alter table `app`.`app`
     add column `need_external_route` tinyint(1) NOT NULL default 0 comment '是否需要外部路由';