CREATE TABLE `ticket` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工单号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工单名称',
  `type` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工单类型',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变更原因',
  `origin_reason` VARCHAR(255) default '' COMMENT '基准提测原因',
  `is_change_set` tinyint NOT NULL COMMENT '是否变更集(0否,1是)',
  `applicant_id` bigint NOT NULL COMMENT '创建人',
  `project_id` bigint NOT NULL COMMENT '项目id',
  `app_id` bigint NOT NULL COMMENT '服务id',
  `task_run_id` bigint NOT NULL COMMENT '关联的流水线任务id',
  `artifact_version` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '制品版本',
  `branch` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运行分支',
  `deploy_config_id` bigint NOT NULL DEFAULT '0' COMMENT '部署配置',
  `deploy_env` json DEFAULT NULL COMMENT '部署环境',
  `check_report` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '验收报告地址',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `app_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务名称',
  `event_id` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'stage事件推送id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_event_id` (`event_id`) USING BTREE,
  KEY `idx_project_id` (`project_id`),
  KEY `idx_name` (`name`),
  KEY `idx_type` (`type`),
  KEY `idx_task_id` (`task_run_id`) USING BTREE,
  KEY `idx_app_id` (`app_id`) USING BTREE,
  KEY `idx_applicant_id` (`applicant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单信息表';
