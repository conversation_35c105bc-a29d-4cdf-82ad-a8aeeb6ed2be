CREATE TABLE `repo` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '仓库名称',
  `repo_type` varchar(16) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '仓库类型(git,harbor,nexus)',
  `auth_type` varchar(16) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '认证类型(token,密码)',
  `url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '目标服务器',
  `port` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '默认端口',
  `username` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
  `password` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码',
  `access_token` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '访问Token',
  `ca_cert` longtext COLLATE utf8mb4_general_ci COMMENT 'ca证书',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `remark` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `status` tinyint NOT NULL COMMENT '状态0连接不上,1正常连接',
  `is_deleted` tinyint DEFAULT '1' COMMENT '是否已删除(逻辑删除),0已删除,1未删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_url` (`url`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='仓库信息表';
