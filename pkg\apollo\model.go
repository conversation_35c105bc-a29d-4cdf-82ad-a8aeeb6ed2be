package apollo

type BaseReq struct {
	Token string `json:"token"`
}

type AddNamespaceContent struct {
	Name                string `json:"name"`                // Namespace的名字
	AppId               string `json:"appId"`               // Namespace所属的AppId
	Format              string `json:"format"`              // Namespace的格式，只能是以下类型： properties、xml、json、yml、yaml
	IsPublic            bool   `json:"isPublic"`            // 是否是公共文件
	Comment             string `json:"comment"`             // Namespace说明
	DataChangeCreatedBy string `json:"dataChangeCreatedBy"` // namespace的创建人，格式为域账号，也就是sso系统的User ID
}

type AddNamespaceReq struct {
	BaseReq
	AddNamespaceContent
}

type AddNamespaceResp struct {
	AddNamespaceContent
	DataChangeCommonResp
}

type DataChangeCommonResp struct {
	DataChangeLastModifiedBy   string `json:"dataChangeLastModifiedBy"`   // namespace的最后修改人，格式为域账号，也就是sso系统的User ID
	DataChangeCreatedTime      string `json:"dataChangeCreatedTime"`      // namespace的创建时间
	DataChangeLastModifiedTime string `json:"dataChangeLastModifiedTime"` // namespace的最后修改时间
}

type ConfigUri struct {
	Env           string `json:"env"`           // 环境
	AppId         string `json:"appId"`         // 应用ID
	ClusterName   string `json:"clusterName"`   // 集群名称
	NamespaceName string `json:"namespaceName"` // Namespace的名字
}

type AddConfigParams struct {
	Key                 string `json:"key"`                 // 配置的key，长度不能超过128个字符。非properties格式，key固定为content
	Value               string `json:"value"`               // 配置的value，长度不能超过20000个字符，非properties格式，value为文件全部内容
	Comment             string `json:"comment"`             // 配置的备注,长度不能超过256个字符
	DataChangeCreatedBy string `json:"dataChangeCreatedBy"` // 配置的创建人，格式为域账号，也就是sso系统的User ID
}

type AddConfigReq struct {
	BaseReq
	ConfigUri
	AddConfigParams
}

type AddConfigResp struct {
	AddConfigParams
	DataChangeCommonResp
}

type ModifyConfigReq struct {
	BaseReq
	ConfigUri
	AddConfigParams
	DataChangeLastModifiedBy string `json:"dataChangeLastModifiedBy"` // item的修改人，格式为域账号，也就是sso系统的User ID
	CreateIfNotExists        bool   `form:"createIfNotExists"`        // 当配置不存在时是否自动创建
}

type ReleaseConfigReq struct {
	BaseReq
	ConfigUri
	ReleaseTitle   string `json:"releaseTitle"`   // 此次发布的标题，长度不能超过64个字符
	ReleaseComment string `json:"releaseComment"` // 发布的备注，长度不能超过256个字符
	ReleasedBy     string `json:"releasedBy"`     // 发布人，格式为域账号，也就是sso系统的User ID
}

type ReleaseConfigResp struct {
	AppId               string         `json:"appId"`               // 应用ID
	ClusterName         string         `json:"clusterName"`         // 集群名称
	NamespaceName       string         `json:"namespaceName"`       // Namespace的名字
	Name                string         `json:"name"`                // Namespace的名字
	Configurations      Configurations `json:"configurations"`      // 配置的内容
	Comment             string         `json:"comment"`             // 配置的备注
	DataChangeCreatedBy string         `json:"dataChangeCreatedBy"` // namespace的创建人，格式为域账号，也就是sso系统的User ID
	DataChangeCommonResp
}

type Configurations struct {
	Timeout string `json:"timeout"` // 超时时间
}
