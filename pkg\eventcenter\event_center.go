package eventcenter

import (
	"52tt.com/cicd/pkg/cmdb"
	"52tt.com/cicd/pkg/kafka"
	"52tt.com/cicd/pkg/log"
	pbapp "52tt.com/cicd/protocol/app"
	"context"
	"encoding/json"
	"fmt"
	"time"
)

type EventCenter struct {
	appClient  pbapp.AppServiceClient
	cmdbClient cmdb.Service
	producer   *kafka.Producer
}

func NewClient(appClient pbapp.AppServiceClient, cmdbClient cmdb.Service, producer *kafka.Producer) *EventCenter {
	return &EventCenter{
		appClient:  appClient,
		cmdbClient: cmdbClient,
		producer:   producer,
	}
}

type Service interface {
	SendEvent(ctx context.Context, sourceData EventSource) error
}

func (s *EventCenter) generateEventReport(ctx context.Context, sourceData EventSource) (*sendEventData, error) {
	app, err := s.appClient.GetApp(ctx, &pbapp.AppParam{Id: sourceData.AppId})
	if err != nil {
		log.ErrorWithCtx(ctx, "根据应用ID[%d]查询应用信息，发生异常: %v", sourceData.AppId, err)
		return nil, err
	}
	if app.Id == 0 {
		log.ErrorWithCtx(ctx, "根据应用ID[%d]查询应用信息，应用不存在", sourceData.AppId)
		return nil, fmt.Errorf("根据应用ID[%d]查询应用信息，应用不存在", sourceData.AppId)
	}

	params := map[string]string{
		"name": sourceData.Cluster,
	}
	clusterList, err := s.cmdbClient.GetModelList(ctx, "kubernetes", params)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据集群名称[%s]查询集群信息，发生异常: %v", sourceData.Cluster, err)
		return nil, err
	}
	if len(clusterList) == 0 || clusterList[0]["private_id"].(string) == "" {
		log.ErrorWithCtx(ctx, "根据集群名称[%s]查询集群信息，集群不存在: +%v", sourceData.Cluster, clusterList)
		return nil, fmt.Errorf("根据集群名称[%s]查询集群信息，集群不存在", sourceData.Cluster)
	}

	log.DebugWithCtx(ctx, "sourceData: %v, cmdb data: %v", sourceData, clusterList)
	privateId := clusterList[0]["private_id"].(string)
	timeFormat := "2006-01-02 15:04:05"
	eventReport := &sendEventData{
		Source:    "CICD",
		Name:      sourceData.Action.CNString(),
		StartTime: sourceData.StartTime.Format(timeFormat),
		EndTime:   sourceData.EndTime.Format(timeFormat),
		EventData: eventMessage{
			Description: sourceData.Description,
			Name:        sourceData.Action.ENString(),
		},
		ResourceData: []resourceData{
			{
				Namespace: namespace{
					NamespaceName:       []string{sourceData.Namespace},
					KubernetesPrivateID: []string{privateId},
					ApplicationID:       []string{app.CmdbId},
				},
			},
		},
	}

	return eventReport, nil
}

func (s *EventCenter) SendEvent(ctx context.Context, sourceData EventSource) error {
	eventReport, err := s.generateEventReport(ctx, sourceData)
	if err != nil {
		return err
	}
	s.producer.PublishAny("cicd-event", eventReport, func(value any) ([]byte, error) {
		return json.Marshal(eventReport)
	})

	log.InfoWithCtx(ctx, "发送部署事件[%d]至事件中心成功，事件: %+v", sourceData.EventID, eventReport)

	return nil
}

type DeployAction int8

const (
	DeployActionDeploy   DeployAction = 1 // 部署
	DeployActionRollback DeployAction = 2 // 回滚
	DeployActionOffline  DeployAction = 3 // 下线
	DeployActionUpgrade  DeployAction = 4 // 更新
	DeployActionRestart  DeployAction = 5 // 重启
	DeployActionStop     DeployAction = 6 // 停止，不提供此类操作
)

func (da DeployAction) Value() int8 {
	return int8(da)
}

func (da DeployAction) ENString() string {
	switch da.Value() {
	case 1:
		return "CICD_INSTALL"
	case 2:
		return "CICD_ROLLBACK"
	case 3:
		return "CICD_OFFLINE"
	case 4:
		return "CICD_UPGRADE"
	case 5:
		return "CICD_RESTART"
	case 6:
		return "CICD_STOP"
	default:
		return "CICD_UNKNOWN"
	}
}

func (da DeployAction) CNString() string {
	switch da.Value() {
	case 1:
		return "服务部署"
	case 2:
		return "服务回滚"
	case 3:
		return "服务下线"
	case 4:
		return "服务更新"
	case 5:
		return "服务重启"
	case 6:
		return "服务停止"
	default:
		return "未知操作"
	}
}

type EventSource struct {
	EventID     int64
	AppId       int64
	Action      DeployAction
	StartTime   time.Time
	EndTime     time.Time
	Description string
	Cluster     string
	Namespace   string
}
