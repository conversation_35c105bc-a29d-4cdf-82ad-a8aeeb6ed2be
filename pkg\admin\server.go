package admin

import (
	"context"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"net/http"
	"net/http/pprof"
)

const DefaultAdminAddr = ":8666"

var (
	serverMux = http.NewServeMux()
	server    *http.Server
)

func init() {
	serverMux.HandleFunc("/debug/pprof/", pprof.Index)
	serverMux.HandleFunc("/debug/pprof/cmdline", pprof.Cmdline)
	serverMux.HandleFunc("/debug/pprof/profile", pprof.Profile)
	serverMux.HandleFunc("/debug/pprof/symbol", pprof.Symbol)
	serverMux.HandleFunc("/debug/pprof/trace", pprof.Trace)

	serverMux.Handle("/metrics", promhttp.Handler())
	serverMux.Handle("/metrics/prom", promhttp.Handler())
	serverMux.Handle("/metrics/prometheus", promhttp.Handler())

	serverMux.HandleFunc("/ping", func(writer http.ResponseWriter, request *http.Request) {
		writer.Header().Set("Content-Type", "application/json")
		_, _ = writer.Write([]byte(`{"msg": "ok"}`))
	})
}

func ListenAndServe(addr string) error {
	server = &http.Server{Addr: addr, Handler: serverMux}
	return server.ListenAndServe()
}

func Shutdown(ctx context.Context) error {
	if server != nil {
		return server.Shutdown(ctx)
	}
	return nil
}
