package service

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"52tt.com/cicd/services/pipeline/internal/conf"
	"github.com/stretchr/testify/require"

	cloudagg "52tt.com/cicd/pkg/cloud/aggregate"

	"52tt.com/cicd/pkg/approval"
	"52tt.com/cicd/pkg/godis"

	"github.com/golang/mock/gomock"
	pkgerrors "github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/datatypes"

	"52tt.com/cicd/pkg/cloud"
	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/event"
	"52tt.com/cicd/pkg/gitlab"
	pbdep "52tt.com/cicd/protocol/deploy"
	pbevent "52tt.com/cicd/protocol/event"
	"52tt.com/cicd/protocol/iam"
	"52tt.com/cicd/services/pipeline/internal/dao"
	"52tt.com/cicd/services/pipeline/internal/model"
	pipelineErr "52tt.com/cicd/services/pipeline/pkg/error"
	"52tt.com/cicd/services/pipeline/pkg/tekton"
)

type PRServiceTestSuite struct {
	db.Suite
	redisSuite           godis.Suite
	ctrl                 *gomock.Controller
	tektonClient         *tekton.MockService
	mockPRepo            *dao.MockPipelineRunRepository
	mockCloudClient      *cloud.MockService
	mockConfigClient     *pbdep.MockDeployConfigServiceClient
	mockTicketClient     *pbdep.MockTicketServiceClient
	mockDeployClient     *pbdep.MockDeployServiceClient
	mockSubEnvClient     *pbdep.MockSubEnvServiceClient
	mockUserClient       *iam.MockUserServiceClient
	mockPipelineRepo     *dao.MockPipelineRepository
	mockPipelineService  *MockPipelineService
	pRService            *PipelineRunSvc
	deployApprovalClient pbdep.ApprovalServiceClient
	gitClient            gitlab.Service
	mockSender           *event.MockSender
	mockChSetRepo        *dao.MockChangeSetRepository
	mockChangeSetService *MockChangeSetService
	mockApprovalClient   *approval.MockService
	mockCloudgRPCClient  *cloudagg.MockAggClient
	txCtx                context.Context
	mockGroupRepo        *dao.MockPipelineGroupRepository
	mockPlanClient       *pbdep.MockPlanServiceClient
	mockPROLogRepo       *dao.MockPipelineRunOperateLogRepository
}

func TestPipelineRunServiceSuite(t *testing.T) {
	suite.Run(t, new(PRServiceTestSuite))
}

func (s *PRServiceTestSuite) SetupTest() {
	s.Suite.SetupTest()

	_, err := conf.LoadConfig("../../etc/")
	require.NoError(s.T(), err)

	s.redisSuite = godis.Suite{Suite: s.Suite.Suite}
	s.redisSuite.SetupTest()
	s.ctrl = gomock.NewController(s.T())
	s.tektonClient = tekton.NewMockService(s.ctrl)
	s.mockPRepo = dao.NewMockPipelineRunRepository(s.ctrl)
	s.tektonClient = tekton.NewMockService(s.ctrl)
	s.mockConfigClient = pbdep.NewMockDeployConfigServiceClient(s.ctrl)
	s.mockDeployClient = pbdep.NewMockDeployServiceClient(s.ctrl)
	s.mockCloudClient = cloud.NewMockService(s.ctrl)
	s.mockTicketClient = pbdep.NewMockTicketServiceClient(s.ctrl)
	s.mockUserClient = iam.NewMockUserServiceClient(s.ctrl)
	s.mockPipelineRepo = dao.NewMockPipelineRepository(s.ctrl)
	s.mockPipelineService = NewMockPipelineService(s.ctrl)
	s.mockSubEnvClient = pbdep.NewMockSubEnvServiceClient(s.ctrl)
	s.mockSender = event.NewMockSender(s.ctrl)
	s.gitClient = gitlab.NewMockService(s.ctrl)
	s.mockChSetRepo = dao.NewMockChangeSetRepository(s.ctrl)
	s.mockChangeSetService = NewMockChangeSetService(s.ctrl)
	s.mockGroupRepo = dao.NewMockPipelineGroupRepository(s.ctrl)
	s.mockCloudgRPCClient = cloudagg.NewMockAggClient(s.ctrl)
	s.mockApprovalClient = approval.NewMockService(s.ctrl)
	s.mockPlanClient = pbdep.NewMockPlanServiceClient(s.ctrl)
	s.mockPROLogRepo = dao.NewMockPipelineRunOperateLogRepository(s.ctrl)
	s.txCtx = db.CtxWithTX(context.Background(), s.DB)
	s.pRService = NewPipelineRunService(s.tektonClient, s.mockPRepo,
		s.mockCloudClient, s.mockConfigClient, s.mockTicketClient,
		s.mockDeployClient, s.mockSubEnvClient, s.mockUserClient,
		s.mockPipelineRepo, s.mockPipelineService, s.deployApprovalClient, s.gitClient, s.mockSender,
		s.mockChSetRepo, s.mockChangeSetService, s.redisSuite.Cli, s.mockApprovalClient,
		s.mockCloudgRPCClient, s.mockGroupRepo, s.mockPlanClient, s.mockPROLogRepo)
}

func (s *PRServiceTestSuite) TearDownTest() {
	s.Suite.TearDownTest()
	s.ctrl.Finish()
}

func (s *PRServiceTestSuite) TestGetPipelineRunTaskById_Success() {
	// given
	runTask := dao.PipelineRunTask{
		BaseModel: db.BaseModel{
			ID: 1,
		},
		PipelineRunStageId: 1,
		Name:               "task-1",
		Type:               "UNIT_TEST",
		Status:             "RUNNING",
		TektonName:         "11",
		TektonNamespace:    "test",
	}
	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), gomock.Any()).Return(&runTask, nil)

	// when
	task, err := s.pRService.GetPipelineRunTaskById(context.Background(), int64(1))

	// then
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), "task-1", task.Name)
}

func (s *PRServiceTestSuite) TestGetPipelineRunTaskById_Error() {
	// given
	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), gomock.Any()).Return(nil, errors.New("get task failed"))

	// when
	_, err := s.pRService.GetPipelineRunTaskById(context.Background(), int64(1))

	// then
	assert.Equal(s.T(), "get task failed", err.Error())
}

func (s *PRServiceTestSuite) TestBuildSonarScanResult_Success() {
	// given
	config := `{"excludeRule": "./", "testFileIncludeRule": "", "scanParam":"hh", "terminatePipeline": true,
"scanThreshold": [{"name":"BUG", "value":0},{"name":"VULNERABILITY", "value":0},{"name":"BAD_SMELL", "value":10},{"name":"TEST_COVERAGE", "value":50}]}`
	result := `{"results": [{"name": "bug-num", "type": "string", "value": "1\n"}, {"name": "code-smell-num", "type": "string", "value": "1944\n"}, {"name": "coverage", "type": "string", "value": "\"19.8%\"\n"}, {"name": "sonar-report", "type": "string", "value": "http://test\n"}, {"name": "vulnerability-num", "type": "string", "value": "0\n"}, {"name": "fail-reason", "type": "string", "value": ""}]}`
	runTask := dao.PipelineRunTask{
		BaseModel: db.BaseModel{
			ID: 1,
		},
		PipelineRunStageId: 1,
		Name:               "task-1",
		Type:               "SONAR_SCAN",
		Status:             "SUCCESSFUL",
		TektonName:         "11",
		TektonNamespace:    "test",
		Config:             datatypes.JSON(config),
		Result:             datatypes.JSON(result),
		DurationModel: dao.DurationModel{
			StartedTime:   time.Now(),
			CompletedTime: time.Now(),
		},
	}

	// when
	sonarResult, err := s.pRService.BuildSonarScanResult(context.Background(), &runTask)

	// then
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), int64(1), sonarResult.MetricResult.Bug)
	assert.Equal(s.T(), "http://test", sonarResult.SonarUrl)
}

func (s *PRServiceTestSuite) TestBuildSonarScanResult_Config_Error() {
	// given
	config := `{"excludeRule": "./", "testFileIncludeRule": "", "scanParam":"hh", "terminatePipeline": true,
"scanThreshold": [{"name":"BUG", "value":0},{"name":"VULNERABILITY", "value":0},{"name":"BAD_SMELL", "value":10},{"name":"TEST_COVERAGE", "value":50}]`
	result := `{"results": [{"name": "bug-num", "type": "string", "value": "0\n"}, {"name": "code-smell-num", "type": "string", "value": "1944\n"}, {"name": "coverage", "type": "string", "value": "\"19.8%\"\n"}, {"name": "sonar-report", "type": "string", "value": "https://test\n"}, {"name": "vulnerability-num", "type": "string", "value": "0\n"}]}`
	runTask := dao.PipelineRunTask{
		BaseModel: db.BaseModel{
			ID: 1,
		},
		PipelineRunStageId: 1,
		Name:               "task-1",
		Type:               "SONAR_SCAN",
		Status:             "SUCCESSFUL",
		TektonName:         "11",
		TektonNamespace:    "test",
		Config:             datatypes.JSON(config),
		Result:             datatypes.JSON(result),
	}

	// when
	_, err := s.pRService.BuildSonarScanResult(context.Background(), &runTask)

	// then
	assert.Equal(s.T(), "unexpected end of JSON input", err.Error())
}

func (s *PRServiceTestSuite) TestBuildSonarScanResult_Result_Error() {
	// given
	config := `{"excludeRule": "./", "testFileIncludeRule": "", "scanParam":"hh", "terminatePipeline": true,
"scanThreshold": [{"name":"BUG", "value":0},{"name":"VULNERABILITY", "value":0},{"name":"BAD_SMELL", "value":10},{"name":"TEST_COVERAGE", "value":50}]}`
	result := `{"results": [{"name": "bug-num", "type": "string", "value": "0\n"}, {"name": "code-smell-num", "type": "string", "value": "1944\n"}, {"name": "coverage", "type": "string", "value": "\"19.8%\"\n"}, {"name": "sonar-report", "type": "string", "value": "https://test\n"}, {"name": "vulnerability-num", "type": "string", "value": "0\n"}]`
	runTask := dao.PipelineRunTask{
		BaseModel: db.BaseModel{
			ID: 1,
		},
		PipelineRunStageId: 1,
		Name:               "task-1",
		Type:               "SONAR_SCAN",
		Status:             "SUCCESSFUL",
		TektonName:         "11",
		TektonNamespace:    "test",
		Config:             datatypes.JSON(config),
		Result:             datatypes.JSON(result),
	}

	// when
	_, err := s.pRService.BuildSonarScanResult(context.Background(), &runTask)

	// then
	assert.Equal(s.T(), "unexpected end of JSON input", err.Error())
}

func generateDurationModel() dao.DurationModel {
	return dao.DurationModel{
		StartedTime:   time.Now(),
		CompletedTime: time.Now(),
	}
}

func (s *PRServiceTestSuite) generatePipelineRunDao() *dao.PipelineRun {
	s.T().Helper()
	return &dao.PipelineRun{
		BaseModel: db.BaseModel{
			ID: 2,
		},
		DurationModel:        generateDurationModel(),
		PipelineId:           1,
		AppId:                1,
		BuildNumber:          202305150001,
		RepoAddress:          "https://www.test.com",
		SourceBranch:         "",
		Branch:               "master",
		TriggerBy:            1,
		TriggerByChineseName: "zhang",
		TriggerByEmployeeNo:  "T0001",
		Status:               "FAILED",
		Workspaces:           nil,
		StageSequence:        "1,2,3",
		Stages: []dao.PipelineRunStage{{
			BaseModel:     db.BaseModel{ID: 1},
			DurationModel: generateDurationModel(),
			PipelineRunId: 2,
			Name:          "stage-1",
			Type:          "DEPLOY_TEST_ENV",
			Status:        "SUCCESSFUL",
			TaskSequence:  "1",
			Tasks: []dao.PipelineRunTask{{
				BaseModel: db.BaseModel{
					ID: 1,
				},
				DurationModel:      generateDurationModel(),
				PipelineRunId:      2,
				PipelineRunStageId: 1,
				Name:               "task-1",
				Type:               "TEST_APPROVAL",
				Status:             "SUCCESSFUL",
			}},
		}, {
			BaseModel:     db.BaseModel{ID: 2},
			DurationModel: generateDurationModel(),
			PipelineRunId: 2,
			Name:          "stage-2",
			Type:          "TEST_ACCEPTANCE",
			Status:        "FAILED",
			TaskSequence:  "2",
			Tasks: []dao.PipelineRunTask{{
				BaseModel: db.BaseModel{
					ID: 2,
				},
				DurationModel:      generateDurationModel(),
				PipelineRunId:      2,
				PipelineRunStageId: 2,
				Name:               "task-2",
				Type:               "TEST_ACCEPTANCE",
				Status:             "FAILED",
			}},
		}, {
			BaseModel:     db.BaseModel{ID: 3},
			DurationModel: generateDurationModel(),
			PipelineRunId: 2,
			Name:          "stage-3",
			Type:          "DEPLOY_GRAY_ENV",
			Status:        "FAILED",
			TaskSequence:  "3",
			Tasks: []dao.PipelineRunTask{{
				BaseModel: db.BaseModel{
					ID: 3,
				},
				DurationModel:      generateDurationModel(),
				PipelineRunId:      2,
				PipelineRunStageId: 3,
				Name:               "task-3",
				Type:               "GRAY_UPGRADE_APPROVAL",
				Status:             "FAILED",
			}},
		}},
		TektonNamespace: "test",
		TektonName:      "test",
	}
}

func (s *PRServiceTestSuite) generateApprovalTaskRunDao() *dao.PipelineRunTask {
	s.T().Helper()
	return &dao.PipelineRunTask{
		BaseModel: db.BaseModel{
			ID: 2,
		},
		PipelineRun: &dao.PipelineRun{
			BaseModel: db.BaseModel{
				ID: 2,
			},
			DurationModel: generateDurationModel(),
		},
		DurationModel:      generateDurationModel(),
		PipelineRunId:      2,
		PipelineRunStageId: 2,
		Name:               "task-run-1",
		Type:               "TEST_ACCEPTANCE",
		Status:             "FAILED",
		Config:             nil,
		Result:             nil,
		TektonName:         "test-task-run1",
		TektonNamespace:    "default",
		StageRun: &dao.PipelineRunStage{
			BaseModel: db.BaseModel{
				ID: 2,
			},
			DurationModel: generateDurationModel(),
			PipelineRunId: 2,
			Name:          "stage-run-1",
			Type:          "TEST_ACCEPTANCE",
			Status:        "FAILED",
			TaskSequence:  "1",
			PipelineRun: &dao.PipelineRun{
				BaseModel: db.BaseModel{
					ID: 2,
				},
				DurationModel:        generateDurationModel(),
				PipelineId:           1,
				AppId:                1,
				BuildNumber:          202305150001,
				RepoAddress:          "https://www.test.com",
				SourceBranch:         "",
				Branch:               "master",
				TriggerBy:            1,
				TriggerByChineseName: "zhang",
				TriggerByEmployeeNo:  "T0001",
				Status:               "FAILED",
				Workspaces:           nil,
				StageSequence:        "2",
				Stages:               nil,
				TektonNamespace:      "test",
				TektonName:           "test",
			},
		},
		TaskId:  2,
		PodName: "",
	}
}

func (s *PRServiceTestSuite) TestGetNextTaskRunByTaskRunId_Success() {
	now := time.Now()
	pipelineRun := s.generatePipelineRunDao()
	newTaskRun := &dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: int64(1)}, Status: constants.RUNNING, PipelineRunStageId: int64(1), PipelineRunId: int64(1), DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)},
		StageRun: &dao.PipelineRunStage{Status: constants.RUNNING, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}}, PipelineRun: &dao.PipelineRun{Status: constants.RUNNING, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}},
	}
	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(3)).Return(newTaskRun, nil)
	_, err := s.pRService.GetNextTaskRunByTaskRunId(context.Background(), 2, *pipelineRun)
	assert.NoError(s.T(), err)
}

func (s *PRServiceTestSuite) TestGetNextTaskRunByTaskRunId_Null() {
	pipelineRun := s.generatePipelineRunDao()
	expectErr := fmt.Errorf("nextTaskRunId is null")

	_, err := s.pRService.GetNextTaskRunByTaskRunId(context.Background(), 3, *pipelineRun)
	assert.Equal(s.T(), expectErr, err)
}

func (s *PRServiceTestSuite) TestRollbackDeploy_Error() {
	tektonName := "test-rollback-changelog-1"
	id := int64(1)
	expectReq := pbdep.RollbackReq{
		Id:         id,
		TektonName: tektonName,
	}
	expectErr := fmt.Errorf("rollback deploy failed")

	s.mockDeployClient.EXPECT().Rollback(gomock.Any(), &expectReq).Return(nil, expectErr)

	err := s.pRService.RollbackDeploy(context.Background(), tektonName, id)

	assert.Equal(s.T(), expectErr, err)
}

func (s *PRServiceTestSuite) TestRollbackDeploy_ErrorCode() {
	tektonName := "test-rollback-changelog-1"
	id := int64(1)
	expectReq := pbdep.RollbackReq{
		Id:         id,
		TektonName: tektonName,
	}
	expectErr := fmt.Errorf("rollback deploy failed")

	resp := pbdep.DeployResp{
		Code: 1,
		Msg:  "rollback deploy failed",
	}
	s.mockDeployClient.EXPECT().Rollback(gomock.Any(), &expectReq).Return(&resp, nil)

	err := s.pRService.RollbackDeploy(context.Background(), tektonName, id)

	assert.Equal(s.T(), expectErr, err)
}

func (s *PRServiceTestSuite) Test_UpdateRunStatus_Success() {
	// given
	now := time.Now()
	newTaskStatus := &model.RunStatus{TaskID: int64(1), Status: constants.RUNNING}
	taskRun := &dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: int64(1)}, Status: constants.UNHANDLED, PipelineRunStageId: int64(1), PipelineRunId: int64(1), DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)},
		StageRun: &dao.PipelineRunStage{Status: constants.UNHANDLED, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}}, PipelineRun: &dao.PipelineRun{Status: constants.UNHANDLED, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}},
	}
	newTaskRun := &dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: int64(1)}, Status: constants.RUNNING, PipelineRunStageId: int64(1), PipelineRunId: int64(1), DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)},
		StageRun: &dao.PipelineRunStage{Status: constants.RUNNING, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}}, PipelineRun: &dao.PipelineRun{Status: constants.RUNNING, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}},
	}
	//taskRunEvent := pbevent.TaskRunEvent{
	//	Id:                 taskRun.TaskId,
	//	StartedTime:        timestamppb.New(taskRun.StartedTime),
	//	Status:             constants.RUNNING.String(),
	//	PipelineRunId:      int64(1),
	//	Results:            &structpb.Struct{},
	//	PipelineRunStageId: int64(1),
	//}
	//stageRunEvent := pbevent.StageRunEvent{}
	//pipelineRunEvent := pbevent.PipelineRunEvent{}
	updatedRunField := map[string]interface{}{"status": constants.RUNNING}

	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(taskRun, nil)
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindPipelineRunLocked(gomock.Any(), int64(1)).Return(&dao.PipelineRun{
			BaseModel: db.BaseModel{ID: 1},
		}, nil)
		s.mockPRepo.EXPECT().UpdatePipelineRunStatusAndTime(gomock.Any(), int64(1), updatedRunField).Return(nil)
		s.mockPRepo.EXPECT().UpdateStageRunStatusAndTime(gomock.Any(), int64(1), updatedRunField).Return(nil)
		s.mockPRepo.EXPECT().UpdateTaskRunStatusAndTime(gomock.Any(), int64(1), updatedRunField).Return(nil)
	})
	s.mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil)
	s.mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil)
	s.mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil)
	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(newTaskRun, nil)

	// when
	err := s.pRService.UpdateRunStatus(context.Background(), newTaskStatus)

	// then
	assert.NoError(s.T(), err)
}

func (s *PRServiceTestSuite) Test_Should_Not_Update_Status_When_Status_Is_Not_Changed() {
	// given
	now := time.Now()
	newTaskStatus := &model.RunStatus{TaskID: int64(1), Status: constants.RUNNING}
	taskRun := &dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: int64(1)}, Status: constants.RUNNING, PipelineRunStageId: int64(1), PipelineRunId: int64(1), DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)},
		StageRun: &dao.PipelineRunStage{Status: constants.RUNNING, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}}, PipelineRun: &dao.PipelineRun{Status: constants.RUNNING, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}},
	}

	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(taskRun, nil)
	// when
	err := s.pRService.UpdateRunStatus(context.Background(), newTaskStatus)

	// then
	assert.NoError(s.T(), err)
}

func (s *PRServiceTestSuite) Test_Should_Should_Return_Error_When_Original_Status_Is_Successful() {
	// given
	now := time.Now()
	newTaskStatus := &model.RunStatus{TaskID: int64(1), Status: constants.RUNNING}
	taskRun := &dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: int64(1)}, Status: constants.SUCCESSFUL, PipelineRunStageId: int64(1), PipelineRunId: int64(1), DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)},
		StageRun: &dao.PipelineRunStage{Status: constants.RUNNING, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}}, PipelineRun: &dao.PipelineRun{Status: constants.RUNNING, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}},
	}

	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(taskRun, nil)
	// when
	err := s.pRService.UpdateRunStatus(context.Background(), newTaskStatus)

	// then
	assert.Equal(s.T(), fmt.Sprintf("任务已经结束，taskrun id: %d, taskrun status: SUCCESSFUL", newTaskStatus.TaskID), err.Error())
}

func (s *PRServiceTestSuite) Test_Should_Should_Return_Error_When_Task_Run_Is_Not_Found() {
	// given
	newTaskStatus := &model.RunStatus{TaskID: int64(1), Status: constants.RUNNING}

	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(nil, nil)
	// when
	err := s.pRService.UpdateRunStatus(context.Background(), newTaskStatus)

	// then
	assert.Equal(s.T(), fmt.Sprintf("找不到任务记录，taskrun id: %d", newTaskStatus.TaskID), err.Error())
}

func (s *PRServiceTestSuite) Test_Should_Should_Return_Error_When_Find_Task_Run_Error() {
	// given
	newTaskStatus := &model.RunStatus{TaskID: int64(1), Status: constants.RUNNING}

	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(nil, errors.New("find task run failed"))
	// when
	err := s.pRService.UpdateRunStatus(context.Background(), newTaskStatus)

	// then
	assert.Equal(s.T(), "find task run failed", err.Error())
}

func (s *PRServiceTestSuite) Test_Should_Should_Return_Error_When_Original_Status_Is_Failed_And_Is_Not_Approval_Task() {
	// given
	now := time.Now()
	newTaskStatus := &model.RunStatus{TaskID: int64(1), Status: constants.RUNNING}
	taskRun := &dao.PipelineRunTask{
		Type: constants.TASK_SONAR_SCAN.String(), BaseModel: db.BaseModel{ID: int64(1)}, Status: constants.FAILED, PipelineRunStageId: int64(1), PipelineRunId: int64(1), DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)},
		StageRun: &dao.PipelineRunStage{Status: constants.FAILED, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}}, PipelineRun: &dao.PipelineRun{Status: constants.FAILED, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}},
	}

	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(taskRun, nil)
	// when
	err := s.pRService.UpdateRunStatus(context.Background(), newTaskStatus)

	// then
	assert.Equal(s.T(), fmt.Sprintf("任务已经结束，taskrun id: %d, taskrun status: FAILED", newTaskStatus.TaskID), err.Error())
}

func (s *PRServiceTestSuite) Test_UpdateRunStatus_Success_When_TaskRun_Status_Is_Failed_And_Is_Approval_Task() {
	// given
	now := time.Now()
	newTaskStatus := &model.RunStatus{TaskID: int64(1), Status: constants.RUNNING}
	taskRun := &dao.PipelineRunTask{
		Type: constants.TASK_TEST_APPROVAL.String(), BaseModel: db.BaseModel{ID: int64(1)}, Status: constants.FAILED, PipelineRunStageId: int64(1), PipelineRunId: int64(1), DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)},
		StageRun: &dao.PipelineRunStage{Status: constants.FAILED, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}}, PipelineRun: &dao.PipelineRun{Status: constants.FAILED, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}},
	}
	newTaskRun := &dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: int64(1)}, Status: constants.RUNNING, PipelineRunStageId: int64(1), PipelineRunId: int64(1), DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)},
		StageRun: &dao.PipelineRunStage{Status: constants.RUNNING, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}}, PipelineRun: &dao.PipelineRun{Status: constants.RUNNING, DurationModel: dao.DurationModel{StartedTime: now.Add(-10 * time.Minute)}},
	}

	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(taskRun, nil)
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindPipelineRunLocked(gomock.Any(), int64(1)).Return(&dao.PipelineRun{
			BaseModel: db.BaseModel{ID: 1},
		}, nil)
		s.mockPRepo.EXPECT().UpdatePipelineRunStatusAndTime(gomock.Any(), int64(1), gomock.Any()).Return(nil)
		s.mockPRepo.EXPECT().UpdateStageRunStatusAndTime(gomock.Any(), int64(1), gomock.Any()).Return(nil)
		s.mockPRepo.EXPECT().UpdateTaskRunStatusAndTime(gomock.Any(), int64(1), gomock.Any()).Return(nil)
	})
	s.mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil)
	s.mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil)
	s.mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil)
	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(newTaskRun, nil)

	// when
	err := s.pRService.UpdateRunStatus(context.Background(), newTaskStatus)

	// then
	assert.NoError(s.T(), err)
}

func (s *PRServiceTestSuite) TestBuildApiAutoTestResult_JsonFailed() {
	task := &dao.PipelineRunTask{
		Result: []byte(`{"test: "test"}`),
	}

	resp, err := s.pRService.BuildApiAutoTestResult(context.Background(), task)

	assert.Nil(s.T(), resp)
	assert.Error(s.T(), err)
}

func (s *PRServiceTestSuite) TestBuildApiAutoTest_ResultFailed() {
	task := &dao.PipelineRunTask{
		Status: constants.FAILED,
		Result: []byte(`{"results": [{"name": "total_case", "value": "10"}, {"name": "success_case", "value": "3"}, {"name": "failure_case", "value": "1"}, {"name": "report_path", "value": "https://testing-dev-quality.ttyuyin.com/#/api-test/testplan/reporter?plan_id=plan_id:7pQYvZL-VIAVZqyB8NiNT&task_id=task_id:JN5gbY2z_D2qS5ak5fZDb&plan_execute_id=execute_id:6y0KMlHqNOnem0S3CaZ2C&project_id=project_id:wpmjOS5bAjeNDSitM51bN&execute_id=execute_id:6y0KMlHqNOnem0S3CaZ2C"}]}`),
	}

	resp, err := s.pRService.BuildApiAutoTestResult(context.Background(), task)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), constants.FAILED.String(), resp.Result)
	assert.Equal(s.T(), int64(10), resp.TotalCase)
	assert.Equal(s.T(), "30.0", resp.Rate)
}

func (s *PRServiceTestSuite) TestBuildApiAutoTest_ResultSuccess() {
	task := &dao.PipelineRunTask{
		Status: constants.SUCCESSFUL,
		Result: []byte(`{"results": [{"name": "total_case", "value": "10"}, {"name": "success_case", "value": "10"}, {"name": "failure_case", "value": "0"}, {"name": "report_path", "value": "https://testing-dev-quality.ttyuyin.com/#/api-test/testplan/reporter?plan_id=plan_id:7pQYvZL-VIAVZqyB8NiNT&task_id=task_id:JN5gbY2z_D2qS5ak5fZDb&plan_execute_id=execute_id:6y0KMlHqNOnem0S3CaZ2C&project_id=project_id:wpmjOS5bAjeNDSitM51bN&execute_id=execute_id:6y0KMlHqNOnem0S3CaZ2C"}]}`),
	}

	resp, err := s.pRService.BuildApiAutoTestResult(context.Background(), task)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), constants.SUCCESSFUL.String(), resp.Result)
	assert.Equal(s.T(), int64(10), resp.SuccessCase)
	assert.Equal(s.T(), "100.0", resp.Rate)
}

func (s *PRServiceTestSuite) TestBuildApiAutoTest_ResultPrecision() {
	task := &dao.PipelineRunTask{
		Status: constants.SUCCESSFUL,
		Result: []byte(`{"results": [{"name": "total_case", "value": "197"}, {"name": "success_case", "value": "138"}, {"name": "failure_case", "value": "0"}, {"name": "report_path", "value": "https://testing-dev-quality.ttyuyin.com/#/api-test/testplan/reporter?plan_id=plan_id:7pQYvZL-VIAVZqyB8NiNT&task_id=task_id:JN5gbY2z_D2qS5ak5fZDb&plan_execute_id=execute_id:6y0KMlHqNOnem0S3CaZ2C&project_id=project_id:wpmjOS5bAjeNDSitM51bN&execute_id=execute_id:6y0KMlHqNOnem0S3CaZ2C"}]}`),
	}

	resp, _ := s.pRService.BuildApiAutoTestResult(context.Background(), task)

	assert.Equal(s.T(), "70.1", resp.Rate)
}

func (s *PRServiceTestSuite) TestBuildApiAutoTest_Zero() {
	task := &dao.PipelineRunTask{
		Status: constants.SUCCESSFUL,
		Result: []byte(`{"results": [{"name": "total_case", "value": "0"}, {"name": "success_case", "value": "0"}, {"name": "failure_case", "value": "0"}, {"name": "report_path", "value": "https://testing-dev-quality.ttyuyin.com/#/api-test/testplan/reporter?plan_id=plan_id:7pQYvZL-VIAVZqyB8NiNT&task_id=task_id:JN5gbY2z_D2qS5ak5fZDb&plan_execute_id=execute_id:6y0KMlHqNOnem0S3CaZ2C&project_id=project_id:wpmjOS5bAjeNDSitM51bN&execute_id=execute_id:6y0KMlHqNOnem0S3CaZ2C"}]}`),
	}

	resp, _ := s.pRService.BuildApiAutoTestResult(context.Background(), task)

	assert.Equal(s.T(), "0.0", resp.Rate)
}

func (s *PRServiceTestSuite) Test_GetDeployTaskTrafficMark_DBError() {
	expectErr := fmt.Errorf("db error")
	queryTaskID := int64(1)
	s.mockPRepo.EXPECT().FindTaskWithSubTask(gomock.Any(), queryTaskID).Return(nil, expectErr)

	_, err := s.pRService.GetDeployTaskTrafficMark(context.Background(), queryTaskID)

	assert.Error(s.T(), err)
}

func (s *PRServiceTestSuite) Test_GetDeployTaskTrafficMark_TaskNotSupport() {
	queryTaskID := int64(1)
	task := dao.PipelineRunTask{
		Type: string(constants.TASK_AUTOMATION_DEPLOY),
		SubRunTasks: []dao.PipelineRunSubtask{
			{Status: constants.FAILED},
		},
	}
	s.mockPRepo.EXPECT().FindTaskWithSubTask(gomock.Any(), queryTaskID).Return(&task, nil)

	_, err := s.pRService.GetDeployTaskTrafficMark(context.Background(), queryTaskID)

	assert.ErrorContains(s.T(), err, "not support")
}

func (s *PRServiceTestSuite) Test_GetDeployTaskTrafficMark_SubtaskNil() {
	queryTaskID := int64(1)
	task := dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: 1},
		Type:      string(constants.TASK_DEPLOY_SUB),
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Status: constants.FAILED},
		},
	}
	s.mockPRepo.EXPECT().FindTaskWithSubTask(gomock.Any(), queryTaskID).Return(&task, nil)

	_, err := s.pRService.GetDeployTaskTrafficMark(context.Background(), queryTaskID)

	assert.ErrorContains(s.T(), err, "subtask nil")
}

func (s *PRServiceTestSuite) Test_GetDeployTaskTrafficMark_JsonError() {
	queryTaskID := int64(1)
	task := dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: 1},
		Type:      string(constants.TASK_DEPLOY_SUB),
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Status: constants.FAILED},
			{BaseModel: db.BaseModel{ID: 1}, Status: constants.SUCCESSFUL, Enabled: true, Config: []byte(`{"name: 11}`)},
		},
	}
	s.mockPRepo.EXPECT().FindTaskWithSubTask(gomock.Any(), queryTaskID).Return(&task, nil)

	_, err := s.pRService.GetDeployTaskTrafficMark(context.Background(), queryTaskID)

	assert.ErrorContains(s.T(), err, "config failed")
}

func (s *PRServiceTestSuite) Test_GetDeployTaskTrafficMark_GetPipelineError() {
	queryTaskID := int64(1)
	task := dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: 1},
		Type:      string(constants.TASK_DEPLOY_SUB),
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Status: constants.FAILED},
			{BaseModel: db.BaseModel{ID: 1}, Status: constants.SUCCESSFUL, Enabled: true, Config: []byte(`{"name": "test"}`)},
		},
	}
	s.mockPRepo.EXPECT().FindTaskWithSubTask(gomock.Any(), queryTaskID).Return(&task, nil)
	s.mockPipelineRepo.EXPECT().GetPipelineByPipelineRunId(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("get pipeline error"))

	_, err := s.pRService.GetDeployTaskTrafficMark(context.Background(), queryTaskID)

	assert.ErrorContains(s.T(), err, "get pipeline error")
}

func (s *PRServiceTestSuite) Test_GetDeployTaskTrafficMark_DeployError() {
	expectErr := fmt.Errorf("deploy error")
	queryTaskID := int64(1)
	task := dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: 1},
		Type:      string(constants.TASK_DEPLOY_SUB),
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Status: constants.FAILED},
			{
				BaseModel: db.BaseModel{ID: 1}, Status: constants.SUCCESSFUL, Enabled: true,
				Config: []byte(`{"cluster": "test-cluster", "subNamespace": "subenv", "deployEnv": "dev"}`),
			},
		},
	}
	pipeline := dao.Pipeline{ProjectID: 1}
	s.mockPRepo.EXPECT().FindTaskWithSubTask(gomock.Any(), queryTaskID).Return(&task, nil)
	s.mockPipelineRepo.EXPECT().GetPipelineByPipelineRunId(gomock.Any(), gomock.Any()).Return(&pipeline, nil)
	s.mockSubEnvClient.EXPECT().GetSubEnvByResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, expectErr)

	_, err := s.pRService.GetDeployTaskTrafficMark(context.Background(), queryTaskID)

	assert.ErrorIs(s.T(), pkgerrors.Cause(err), expectErr)
}

func (s *PRServiceTestSuite) Test_GetDeployTaskTrafficMark_Success() {
	expectResp := &pbdep.GetSubEnvResp{
		TrafficMarkName: "test-mark",
	}
	queryTaskID := int64(1)
	task := dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: 1},
		Type:      string(constants.TASK_DEPLOY_SUB),
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Status: constants.FAILED},
			{
				BaseModel: db.BaseModel{ID: 1}, Status: constants.SUCCESSFUL, Enabled: true,
				Config: []byte(`{"cluster": "test-cluster", "subNamespace": "subenv", "deployEnv": "dev"}`),
			},
		},
	}
	pipeline := dao.Pipeline{ProjectID: 1}
	s.mockPRepo.EXPECT().FindTaskWithSubTask(gomock.Any(), queryTaskID).Return(&task, nil)
	s.mockPipelineRepo.EXPECT().GetPipelineByPipelineRunId(gomock.Any(), gomock.Any()).Return(&pipeline, nil)
	s.mockSubEnvClient.EXPECT().GetSubEnvByResource(gomock.Any(), gomock.Any(), gomock.Any()).Return(expectResp, nil)

	mark, err := s.pRService.GetDeployTaskTrafficMark(context.Background(), queryTaskID)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), mark, expectResp.TrafficMarkName)
}

func (s *PRServiceTestSuite) TestBuildScaScanResult_ResultEmpty() {
	now := time.Now()
	task := &dao.PipelineRunTask{
		DurationModel: dao.DurationModel{CompletedTime: now},
		Status:        constants.FAILED,
	}

	resp, err := s.pRService.BuildScaScanResult(context.Background(), task)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.CompletedTime, now)
	assert.Equal(s.T(), resp.Result, constants.FAILED.String())
}

func (s *PRServiceTestSuite) TestBuildScaScanResult_WithUrl() {
	now := time.Now()
	task := &dao.PipelineRunTask{
		DurationModel: dao.DurationModel{CompletedTime: now},
		Result:        []byte(`{"results": [{"name": "result_url", "value": "https://test"}]}`),
		Status:        constants.FAILED,
	}

	resp, err := s.pRService.BuildScaScanResult(context.Background(), task)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.ReportPath, "https://test")
}

func (s *PRServiceTestSuite) Test_GetCanaryPolicy_TaskNotFound() {
	taskID := int64(1)
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(nil, nil)
	})

	_, err := s.pRService.GetCanaryPolicy(s.txCtx, taskID)

	assert.ErrorIs(s.T(), err, pipelineErr.ErrPipelineRunTaskNotExisted)
}

func (s *PRServiceTestSuite) Test_GetCanaryPolicy_TaskTypeInvalid() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		Type: "INVALID_TYPE",
	}

	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})

	_, err := s.pRService.GetCanaryPolicy(s.txCtx, taskID)

	assert.ErrorIs(s.T(), err, pipelineErr.ErrPipelineRunTaskNotExisted)
}

func (s *PRServiceTestSuite) Test_GetCanaryPolicy_DefaultTraffic_Success() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		Type: string(constants.TASK_DEPLOY_CANARY),
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		}, Config: []byte(`{}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})

	resp, err := s.pRService.GetCanaryPolicy(s.txCtx, taskID)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.Current, int64(10))
}

func (s *PRServiceTestSuite) Test_GetCanaryPolicy_OnlyFailedTask() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		Type: string(constants.TASK_DEPLOY_CANARY),
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Config: []byte(`{"percentage": 10}`)},
		}, Config: []byte(`{"canaryPolicy": "TRAFFIC"}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})

	resp, err := s.pRService.GetCanaryPolicy(s.txCtx, taskID)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.Current, int64(0))
}

func (s *PRServiceTestSuite) Test_GetCanaryPolicy_MultiPercentage_Success() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		Type: string(constants.TASK_DEPLOY_CANARY),
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 20}`)},
		},
		Config: []byte(`{"canaryPolicy": "TRAFFIC"}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})

	resp, err := s.pRService.GetCanaryPolicy(s.txCtx, taskID)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.Current, int64(10))
}

func (s *PRServiceTestSuite) Test_GetCanaryPolicy_CLIENT_VERSION_Success() {
	taskID := int64(1)
	deployPlanName := "plan"
	task := &dao.PipelineRunTask{
		Type: string(constants.TASK_DEPLOY_CANARY),
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 20}`)},
		},
		Config: []byte(`{"canaryPolicy": "CLIENT_VERSION","canaryDeployPlanId": 1239}`),
	}
	deployPlan := &pbdep.DeployPlan{
		Name:         deployPlanName,
		CanaryPolicy: "CLIENT_VERSION,TRAFFIC",
	}
	planRun := &pbdep.PlanRunResp{
		Id:                 1,
		DeployPlanId:       1,
		DeployPlanRecordId: 1,
		TimeType:           constants.PlanTimeTypeAbsolute.String(),
		Status:             "FAILED",
		Nodes: []*pbdep.PlanRunResp_Node{{
			Id:            1,
			Action:        constants.PlanActionControlTraffic.String(),
			StartedTime:   time.Now().Unix() + 1000,
			CompletedTime: 0,
			Status:        "SUCCESSFUL",
		},
			{
				Id:            1,
				Action:        constants.PlanActionPassCanary.String(),
				StartedTime:   time.Now().Unix() + 2000,
				CompletedTime: 0,
				Status:        "Failed",
			}},
		PlanRunConfig: datatypes.JSON(`{"canaryPolicy": ["CLIENT_VERSION", "TRAFFIC"]}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})
	s.mockPlanClient.EXPECT().GetDeployPlanByID(gomock.Any(), gomock.Any()).Return(deployPlan, nil)
	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(planRun, nil)
	resp, err := s.pRService.GetCanaryPolicy(s.txCtx, taskID)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.DeployPlanName, deployPlanName)
	assert.Equal(s.T(), 2, len(resp.CanaryPolicy))
	assert.Equal(s.T(), constants.PolicyClientVersion, resp.CanaryPolicy[0])
	assert.Equal(s.T(), true, resp.IsShowTrafficControl)
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_OutOfRange() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		Type: string(constants.TASK_DEPLOY_CANARY),
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		},
		Status: constants.SUCCESSFUL,
		Config: datatypes.JSON(`{}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})

	_, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 55})

	assert.ErrorIs(s.T(), err, pipelineErr.ErrCanaryParamInvalid)
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_FirstBuildSubEnvRoute_Failed() {
	expectErr := fmt.Errorf("failed to build subenv route")
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		},
		Config: datatypes.JSON(`{}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})
	s.mockCloudgRPCClient.EXPECT().BuildSubenvRouteWithMultiTrafficMark(gomock.Any(), gomock.Any()).Return(nil, expectErr)

	_, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 20})

	assert.ErrorIs(s.T(), err, pipelineErr.ErrCanaryShiftingFailed)
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_SecondBuildSubEnvRoute_Failed() {
	expectErr := fmt.Errorf("failed to build subenv route")
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		},
		Config: datatypes.JSON(`{}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})
	s.mockCloudgRPCClient.EXPECT().BuildSubenvRouteWithMultiTrafficMark(gomock.Any(), gomock.Any()).Return(nil, nil)
	s.mockCloudgRPCClient.EXPECT().BuildSubenvRouteWithMultiTrafficMark(gomock.Any(), gomock.Any()).Return(nil, expectErr)
	s.mockPRepo.EXPECT().UpdatePipelineRunSubtaskConfigPercentage(gomock.Any(), task.SubRunTasks[0].ID, int64(20)).Return(nil)

	_, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 20})

	assert.ErrorIs(s.T(), err, pipelineErr.ErrCanaryShiftingFailed)
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_BuildRoute_Success() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		},
		Config: datatypes.JSON(`{}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})
	s.mockCloudgRPCClient.EXPECT().BuildSubenvRouteWithMultiTrafficMark(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
	s.mockPRepo.EXPECT().UpdatePipelineRunSubtaskConfigPercentage(gomock.Any(), task.SubRunTasks[0].ID, int64(20)).Return(nil)
	s.mockPRepo.EXPECT().UpdatePipelineRunSubtaskConfigPercentage(gomock.Any(), task.SubRunTasks[1].ID, int64(20)).Return(nil)
	s.mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil)

	resp, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 20})

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.Current, int64(20))
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_BuildRouteWithWeight_Success() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		},
		Config: datatypes.JSON(`{"trafficControlMethod": 1}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})
	s.mockCloudgRPCClient.EXPECT().BuildSubenvRouteWithWeight(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
	s.mockCloudgRPCClient.EXPECT().BuildSubenvRouteWithMultiTrafficMark(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
	s.mockPRepo.EXPECT().UpdatePipelineRunSubtaskConfigPercentage(gomock.Any(), task.SubRunTasks[0].ID, int64(20)).Return(nil)
	s.mockPRepo.EXPECT().UpdatePipelineRunSubtaskConfigPercentage(gomock.Any(), task.SubRunTasks[1].ID, int64(20)).Return(nil)
	s.mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil)

	resp, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 20})

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.Current, int64(20))
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_ClearRouteWithWeight_Success() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		},
		Config: datatypes.JSON(`{"trafficControlMethod": 1}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})
	s.mockCloudgRPCClient.EXPECT().ClearSubenvRouteWithWeight(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
	s.mockPRepo.EXPECT().UpdatePipelineRunSubtaskConfigPercentage(gomock.Any(), task.SubRunTasks[0].ID, int64(0)).Return(nil)
	s.mockPRepo.EXPECT().UpdatePipelineRunSubtaskConfigPercentage(gomock.Any(), task.SubRunTasks[1].ID, int64(0)).Return(nil)
	s.mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil)

	resp, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 0})

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.Current, int64(0))
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_ClearRoute_Success() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		},
		Config: datatypes.JSON(`{}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})
	s.mockCloudgRPCClient.EXPECT().ClearSubenvRoute(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
	s.mockPRepo.EXPECT().UpdatePipelineRunSubtaskConfigPercentage(gomock.Any(), task.SubRunTasks[0].ID, int64(0)).Return(nil)
	s.mockPRepo.EXPECT().UpdatePipelineRunSubtaskConfigPercentage(gomock.Any(), task.SubRunTasks[1].ID, int64(0)).Return(nil)
	s.mockSender.EXPECT().Send(gomock.Any(), gomock.Any()).Return(nil)

	resp, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 0})

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.Current, int64(0))
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_GetRunTask_Error() {
	taskID := int64(1)
	s.ExecTransactionRollback(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(nil, errors.New("error"))
	})

	_, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 20})

	assert.Equal(s.T(), "get task 1 failed: error", err.Error())
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_GetRunTaskNotExist() {
	taskID := int64(1)
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(nil, nil)
	})

	_, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 20})

	assert.Equal(s.T(), err, pipelineErr.ErrPipelineRunTaskNotExisted)
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_GetRunTaskType_Error() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_STAGING),
		Status: constants.SUCCESSFUL,
		Config: datatypes.JSON(`{}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})

	_, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 20})

	assert.ErrorIs(s.T(), err, pipelineErr.ErrPipelineRunTaskNotExisted)
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_GetRunTask_NotSuccessful() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.RUNNING,
		Config: datatypes.JSON(`{}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})

	_, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 20})

	assert.ErrorIs(s.T(), err, pipelineErr.ErrCanaryParamInvalid)
}

func (s *PRServiceTestSuite) Test_ShiftCanaryPercentage_SameWithCurrent() {
	taskID := int64(1)
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 20}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 20}`)},
		},
		Config: datatypes.JSON(`{}`),
	}
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxFindRunTask(gomock.Any(), gomock.Any(), taskID).Return(task, nil)
	})

	resp, err := s.pRService.ShiftCanaryPercentage(s.txCtx, model.CanaryShiftReq{ID: taskID, Value: 20})

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), resp.Current, int64(20))
}

func (s *PRServiceTestSuite) Test_RolloutCanary_UnmarshalConfig_Error() {
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		},
		Config: datatypes.JSON(`{`),
	}

	err := s.pRService.rolloutCanary(s.txCtx, task, 20, []string{})

	assert.Equal(s.T(), "unmarshal config error: unexpected end of JSON input", err.Error())
}

func (s *PRServiceTestSuite) Test_RolloutCanary_UnmarshalSubTaskConfigWithDyeing_Error() {
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 1`)},
			{BaseModel: db.BaseModel{ID: 2}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		},
		Config: datatypes.JSON(`{}`),
	}

	err := s.pRService.rolloutCanary(s.txCtx, task, 20, []string{})

	assert.Equal(s.T(), "unmarshal subtask 1 canary config failed: unexpected end of JSON input", err.Error())
}

func (s *PRServiceTestSuite) Test_RolloutCanary_ClearSubEnvRouteWithDyeing_Error() {
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		SubRunTasks: []dao.PipelineRunSubtask{
			{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		},
		Config: datatypes.JSON(`{}`),
	}
	cleaErr := errors.New("clear sub env failed")
	s.mockCloudgRPCClient.EXPECT().ClearSubenvRoute(gomock.Any(), gomock.Any()).Return(nil, cleaErr)
	err := s.pRService.rolloutCanary(s.txCtx, task, 0, []string{})

	assert.ErrorIs(s.T(), err, pipelineErr.ErrCanaryShiftingFailed)
}

func (s *PRServiceTestSuite) Test_ShiftCanaryWithDyeing_SamePercentage() {
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		Config: datatypes.JSON(`{}`),
	}
	subTasks := []dao.PipelineRunSubtask{
		{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 0}`)},
	}

	_, num, err := s.pRService.shiftCanaryWithDyeing(s.txCtx, task, subTasks, 0)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), num, int64(1))
}

func (s *PRServiceTestSuite) Test_ShiftCanaryWithWeight_SamePercentage() {
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		Config: datatypes.JSON(`{"trafficControlMethod": 1}`),
	}
	subTasks := []dao.PipelineRunSubtask{
		{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 0}`)},
	}

	_, num, err := s.pRService.shiftCanaryWithWeight(s.txCtx, task, subTasks, 0)

	assert.NoError(s.T(), err)
	assert.Equal(s.T(), num, int64(1))
}

func (s *PRServiceTestSuite) Test_ShiftCanaryWithWeight_Build_Error() {
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		Config: datatypes.JSON(`{"trafficControlMethod": 1}`),
	}
	subTasks := []dao.PipelineRunSubtask{
		{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 0}`)},
		{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 0}`)},
	}
	s.mockCloudgRPCClient.EXPECT().BuildSubenvRouteWithWeight(gomock.Any(), gomock.Any()).Return(nil, nil)
	s.mockCloudgRPCClient.EXPECT().BuildSubenvRouteWithMultiTrafficMark(gomock.Any(), gomock.Any()).Return(nil, nil)
	wantErr := errors.New("build sub env failed")
	s.mockCloudgRPCClient.EXPECT().BuildSubenvRouteWithWeight(gomock.Any(), gomock.Any()).Return(nil, wantErr)

	_, num, _ := s.pRService.shiftCanaryWithWeight(s.txCtx, task, subTasks, 10)

	assert.Equal(s.T(), num, int64(1))
}

func (s *PRServiceTestSuite) Test_ShiftCanaryWithWeight_Clear_Error() {
	task := &dao.PipelineRunTask{
		PipelineRunId: int64(123),
		StageRun: &dao.PipelineRunStage{
			PipelineRun: &dao.PipelineRun{
				Pipeline: &dao.Pipeline{AppName: "test"},
			},
		},
		Type:   string(constants.TASK_DEPLOY_CANARY),
		Status: constants.SUCCESSFUL,
		Config: datatypes.JSON(`{"trafficControlMethod": 1}`),
	}
	subTasks := []dao.PipelineRunSubtask{
		{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
		{BaseModel: db.BaseModel{ID: 1}, Enabled: true, Config: []byte(`{"percentage": 10}`)},
	}
	s.mockCloudgRPCClient.EXPECT().ClearSubenvRouteWithWeight(gomock.Any(), gomock.Any()).Return(nil, nil)
	wantErr := errors.New("clear sub env failed")
	s.mockCloudgRPCClient.EXPECT().ClearSubenvRouteWithWeight(gomock.Any(), gomock.Any()).Return(nil, wantErr)

	_, num, _ := s.pRService.shiftCanaryWithWeight(s.txCtx, task, subTasks, 0)

	assert.Equal(s.T(), num, int64(1))
}

func (s *PRServiceTestSuite) Test_ClearAfterDeletePipelineRuns_Empty() {
	err := s.pRService.ClearAfterDeletePipelineRuns(context.Background(), nil)
	assert.Nil(s.T(), err)
}

func (s *PRServiceTestSuite) Test_ClearAfterDeletePipelineRuns_NoData() {
	s.mockPRepo.EXPECT().FindApprovalTasksByPipelineRunID(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	s.mockPRepo.EXPECT().FindPipelineRunPreloadSubRunTasks(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	err := s.pRService.ClearAfterDeletePipelineRuns(context.Background(), []int64{1, 2})
	assert.Nil(s.T(), err)
}

func (s *PRServiceTestSuite) Test_ClearAfterDeletePipelineRuns_TektonResourceNotFound() {
	expectSubtasks := []dao.PipelineRunSubtask{
		{BaseModel: db.BaseModel{ID: 1}, TektonName: "tkn-sub-pr-1", TektonNamespace: "default"},
		{BaseModel: db.BaseModel{ID: 2}, TektonName: "tkn-sub-pr-2", TektonNamespace: "default"},
	}
	expectPR := dao.PipelineRun{
		BaseModel: db.BaseModel{ID: 1}, TektonNamespace: "default", TektonName: "tkn-pr-1",
		SubRunTasks: expectSubtasks,
	}

	s.mockPRepo.EXPECT().FindApprovalTasksByPipelineRunID(gomock.Any(), gomock.Any()).Return(nil, nil)
	s.mockPRepo.EXPECT().FindPipelineRunPreloadSubRunTasks(gomock.Any(), gomock.Any()).Return(&expectPR, nil)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectPR.TektonNamespace, expectPR.TektonName).Return(pipelineErr.ErrTektonPipelineRunNotFound)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectSubtasks[0].TektonNamespace, expectSubtasks[0].TektonName).Return(pipelineErr.ErrTektonPipelineRunNotFound)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectSubtasks[1].TektonNamespace, expectSubtasks[1].TektonName).Return(pipelineErr.ErrTektonPipelineRunNotFound)

	err := s.pRService.ClearAfterDeletePipelineRuns(context.Background(), []int64{1})
	assert.Nil(s.T(), err)
}

func (s *PRServiceTestSuite) Test_ClearAfterDeletePipelineRuns_TektonClearSuccess() {
	expectSubtasks := []dao.PipelineRunSubtask{
		{BaseModel: db.BaseModel{ID: 1}, TektonName: "tkn-sub-pr-1", TektonNamespace: "default"},
		{BaseModel: db.BaseModel{ID: 2}, TektonName: "tkn-sub-pr-2", TektonNamespace: "default"},
	}
	expectPR := dao.PipelineRun{
		BaseModel: db.BaseModel{ID: 1}, TektonNamespace: "default", TektonName: "tkn-pr-1",
		SubRunTasks: expectSubtasks,
	}

	s.mockPRepo.EXPECT().FindApprovalTasksByPipelineRunID(gomock.Any(), gomock.Any()).Return(nil, nil)
	s.mockPRepo.EXPECT().FindPipelineRunPreloadSubRunTasks(gomock.Any(), gomock.Any()).Return(&expectPR, nil)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectPR.TektonNamespace, expectPR.TektonName).Return(nil)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectSubtasks[0].TektonNamespace, expectSubtasks[0].TektonName).Return(nil)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectSubtasks[1].TektonNamespace, expectSubtasks[1].TektonName).Return(nil)

	err := s.pRService.ClearAfterDeletePipelineRuns(context.Background(), []int64{1})
	assert.Nil(s.T(), err)
}

func (s *PRServiceTestSuite) Test_ClearAfterDeletePipelineRuns_TicketFailed() {
	expectSubtasks := []dao.PipelineRunSubtask{
		{BaseModel: db.BaseModel{ID: 1}, TektonName: "tkn-sub-pr-1", TektonNamespace: "default"},
		{BaseModel: db.BaseModel{ID: 2}, TektonName: "tkn-sub-pr-2", TektonNamespace: "default"},
	}
	expectTask := []dao.PipelineRunTask{
		{BaseModel: db.BaseModel{ID: 1}, Type: string(constants.TASK_TEST_APPROVAL)},
	}
	expectPR := dao.PipelineRun{
		BaseModel: db.BaseModel{ID: 1}, TektonNamespace: "default", TektonName: "tkn-pr-1",
		SubRunTasks: expectSubtasks,
	}

	s.mockPRepo.EXPECT().FindApprovalTasksByPipelineRunID(gomock.Any(), gomock.Any()).Return(expectTask, nil)
	s.mockPRepo.EXPECT().FindPipelineRunPreloadSubRunTasks(gomock.Any(), gomock.Any()).Return(&expectPR, nil)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectPR.TektonNamespace, expectPR.TektonName).Return(nil)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectSubtasks[0].TektonNamespace, expectSubtasks[0].TektonName).Return(nil)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectSubtasks[1].TektonNamespace, expectSubtasks[1].TektonName).Return(nil)
	s.mockTicketClient.EXPECT().AbandonTicketBy(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("abandon ticket failed"))

	err := s.pRService.ClearAfterDeletePipelineRuns(context.Background(), []int64{1})
	assert.NotNil(s.T(), err)
}

func (s *PRServiceTestSuite) Test_ClearAfterDeletePipelineRuns_Success() {
	expectSubtasks := []dao.PipelineRunSubtask{
		{BaseModel: db.BaseModel{ID: 1}, TektonName: "tkn-sub-pr-1", TektonNamespace: "default"},
		{BaseModel: db.BaseModel{ID: 2}, TektonName: "tkn-sub-pr-2", TektonNamespace: "default"},
	}
	expectTask := []dao.PipelineRunTask{
		{BaseModel: db.BaseModel{ID: 1}, Type: string(constants.TASK_TEST_APPROVAL)},
	}
	expectPR := dao.PipelineRun{
		BaseModel: db.BaseModel{ID: 1}, TektonNamespace: "default", TektonName: "tkn-pr-1",
		SubRunTasks: expectSubtasks,
	}

	s.mockPRepo.EXPECT().FindApprovalTasksByPipelineRunID(gomock.Any(), gomock.Any()).Return(expectTask, nil)
	s.mockPRepo.EXPECT().FindPipelineRunPreloadSubRunTasks(gomock.Any(), gomock.Any()).Return(&expectPR, nil)

	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectPR.TektonNamespace, expectPR.TektonName).Return(nil)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectSubtasks[0].TektonNamespace, expectSubtasks[0].TektonName).Return(nil)
	s.tektonClient.EXPECT().DeletePipelineRun(gomock.Any(), expectSubtasks[1].TektonNamespace, expectSubtasks[1].TektonName).Return(nil)
	s.mockTicketClient.EXPECT().AbandonTicketBy(gomock.Any(), gomock.Any()).Return(nil, nil)

	err := s.pRService.ClearAfterDeletePipelineRuns(context.Background(), []int64{1})
	assert.Nil(s.T(), err)
}

func (s *PRServiceTestSuite) Test_HandleDeleteEvent_UnknowEvent() {
	e := &pbevent.DeletePipelineResourceEvent{
		From: pbevent.DeletePipelineResourceEvent_DEFAULT,
	}
	err := s.pRService.HandleDeleteEvent(context.Background(), e)

	assert.ErrorContains(s.T(), err, "unknown delete event from")
}

func (s *PRServiceTestSuite) Test_HandleDeleteEvent_PipelineEvent_DaoFailed() {
	e := &pbevent.DeletePipelineResourceEvent{
		From:       pbevent.DeletePipelineResourceEvent_PIPELINE,
		PipelineId: int64(1),
	}
	expectErr := fmt.Errorf("dao error")
	s.mockPRepo.EXPECT().FindPipelineRunsByPipelines(gomock.Any(), []int64{e.PipelineId}).Return(nil, expectErr)

	err := s.pRService.HandleDeleteEvent(context.Background(), e)

	assert.ErrorIs(s.T(), err, expectErr)
}

func (s *PRServiceTestSuite) Test_HandleDeleteEvent_PipelineEvent_NotRelatedPipelineRun() {
	e := &pbevent.DeletePipelineResourceEvent{
		From:       pbevent.DeletePipelineResourceEvent_PIPELINE,
		PipelineId: int64(1),
	}
	s.mockPRepo.EXPECT().FindPipelineRunsByPipelines(gomock.Any(), []int64{e.PipelineId}).Return(nil, nil)

	err := s.pRService.HandleDeleteEvent(context.Background(), e)

	assert.NoError(s.T(), err)
}

func (s *PRServiceTestSuite) Test_HandleDeleteEvent_GroupeEvent_GetPipelinesFailed() {
	e := &pbevent.DeletePipelineResourceEvent{
		From:            pbevent.DeletePipelineResourceEvent_PIPELINE_GROUP,
		PipelineGroupId: int64(1),
	}
	expectErr := fmt.Errorf("dao error")
	s.mockGroupRepo.EXPECT().GetAllPipelines(gomock.Any(), e.PipelineGroupId).Return(nil, expectErr)

	err := s.pRService.HandleDeleteEvent(context.Background(), e)

	assert.ErrorIs(s.T(), err, expectErr)
}

func (s *PRServiceTestSuite) Test_HandleDeleteEvent_GroupEvent_FindPipelineRunsByPipelinesFailed() {
	e := &pbevent.DeletePipelineResourceEvent{
		From:            pbevent.DeletePipelineResourceEvent_PIPELINE_GROUP,
		PipelineGroupId: int64(1),
	}
	expectPipelines := []dao.Pipeline{
		{BaseModel: db.BaseModel{ID: 1}},
	}
	expectErr := fmt.Errorf("dao error")
	s.mockGroupRepo.EXPECT().GetAllPipelines(gomock.Any(), e.PipelineGroupId).Return(expectPipelines, nil)
	s.mockPRepo.EXPECT().FindPipelineRunsByPipelines(gomock.Any(), []int64{1}).Return(nil, expectErr)

	err := s.pRService.HandleDeleteEvent(context.Background(), e)

	assert.ErrorIs(s.T(), err, expectErr)
}

func (s *PRServiceTestSuite) Test_HandleDeleteEvent_TemplateEvent_GetPipelinesByTemplateID_Failed() {
	e := &pbevent.DeletePipelineResourceEvent{
		From:       pbevent.DeletePipelineResourceEvent_TEMPLATE,
		TemplateId: int64(1),
	}
	expectErr := fmt.Errorf("dao error")
	s.mockPipelineRepo.EXPECT().GetAllPipelinesByTemplateID(gomock.Any(), e.TemplateId).Return(nil, expectErr)

	err := s.pRService.HandleDeleteEvent(context.Background(), e)

	assert.ErrorIs(s.T(), err, expectErr)
}

func (s *PRServiceTestSuite) Test_HandleDeleteEvent_TemplateEvent_FindPipelineRunsByPipelines_Failed() {
	e := &pbevent.DeletePipelineResourceEvent{
		From:       pbevent.DeletePipelineResourceEvent_TEMPLATE,
		TemplateId: int64(1),
	}
	expectPipelines := []dao.Pipeline{
		{BaseModel: db.BaseModel{ID: 1}},
	}
	expectErr := fmt.Errorf("dao error")
	s.mockPipelineRepo.EXPECT().GetAllPipelinesByTemplateID(gomock.Any(), e.TemplateId).Return(expectPipelines, nil)
	s.mockPRepo.EXPECT().FindPipelineRunsByPipelines(gomock.Any(), []int64{1}).Return(nil, expectErr)

	err := s.pRService.HandleDeleteEvent(context.Background(), e)

	assert.ErrorIs(s.T(), err, expectErr)
}

func (s *PRServiceTestSuite) TestAddPipelineRunSubtask_Success() {
	addSubTaskParam := model.AddSubTaskParam{
		TaskRunId:      1,
		Cluster:        "cluster",
		Namespace:      "namespace",
		DeployEnv:      "env",
		ConfigId:       1,
		EnvTarget:      "target",
		Senv:           "senv",
		Timeout:        1,
		IsDeploySubEnv: true,
	}
	taskRun := &dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: int64(1)}, Name: "deploy", Type: "type", PipelineRunStageId: int64(1), PipelineRunId: int64(1)}

	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(taskRun, nil)
	s.mockPRepo.EXPECT().CreatePipelineRunSubtask(gomock.Any(), gomock.Any()).Return(nil)

	err := s.pRService.AddPipelineRunSubtask(context.Background(), addSubTaskParam)
	assert.NoError(s.T(), err)
}

func (s *PRServiceTestSuite) TestAddPipelineRunSubtask_FindRunTaskById_Error() {
	expectErr := fmt.Errorf("FindRunTaskById error")
	addSubTaskParam := model.AddSubTaskParam{
		TaskRunId:      1,
		Cluster:        "cluster",
		Namespace:      "namespace",
		DeployEnv:      "env",
		ConfigId:       1,
		EnvTarget:      "target",
		Senv:           "senv",
		Timeout:        1,
		IsDeploySubEnv: true,
	}
	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(nil, expectErr)

	err := s.pRService.AddPipelineRunSubtask(context.Background(), addSubTaskParam)
	assert.ErrorIs(s.T(), err, expectErr)
}

func (s *PRServiceTestSuite) TestAddPipelineRunSubtask_CreatePipelineRunSubtask_Error() {
	expectErr := fmt.Errorf("CreatePipelineRunSubtask error")
	addSubTaskParam := model.AddSubTaskParam{
		TaskRunId:      1,
		Cluster:        "cluster",
		Namespace:      "namespace",
		DeployEnv:      "env",
		ConfigId:       1,
		EnvTarget:      "target",
		Senv:           "senv",
		Timeout:        1,
		IsDeploySubEnv: true,
	}
	taskRun := &dao.PipelineRunTask{
		BaseModel: db.BaseModel{ID: int64(1)}, Name: "deploy", Type: "type", PipelineRunStageId: int64(1), PipelineRunId: int64(1)}

	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), int64(1)).Return(taskRun, nil)
	s.mockPRepo.EXPECT().CreatePipelineRunSubtask(gomock.Any(), gomock.Any()).Return(expectErr)
	err := s.pRService.AddPipelineRunSubtask(context.Background(), addSubTaskParam)
	assert.ErrorIs(s.T(), err, expectErr)
}

func (s *PRServiceTestSuite) TestDeletePipelineRunSubtask_Success() {
	subTaskRun := &dao.PipelineRunSubtask{
		BaseModel: db.BaseModel{ID: int64(1)}, Name: "deploy", Type: "type", PipelineRunTaskId: int64(1), Status: constants.UNHANDLED, PipelineRunTask: &dao.PipelineRunTask{BaseModel: db.BaseModel{ID: 1}, TektonName: "tektonName", PipelineRunStageId: 1, StageRun: &dao.PipelineRunStage{BaseModel: db.BaseModel{ID: 1}, TaskSequence: "1"}}}

	subTaskRuns := []dao.PipelineRunSubtask{
		{BaseModel: db.BaseModel{ID: int64(1)}, Name: "deploy", Type: "type", PipelineRunTaskId: int64(1), Status: constants.SUCCESSFUL},
		{BaseModel: db.BaseModel{ID: int64(2)}, Name: "deploy", Type: "type", PipelineRunTaskId: int64(1), Status: constants.SUCCESSFUL}}
	s.mockPRepo.EXPECT().FindPreloadPipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(subTaskRun, nil)
	s.mockPRepo.EXPECT().DeletePipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(nil)
	s.mockPRepo.EXPECT().FindSubTasksByMultiParams(gomock.Any(), gomock.Any()).Return(subTaskRuns, nil)
	s.mockApprovalClient.EXPECT().FakeDeployPass(gomock.Any(), gomock.Any()).Return(nil)
	s.ExpectTransaction(func() {
		s.mockPRepo.EXPECT().TxBatchUpdatePipelineRunTaskStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		s.mockPRepo.EXPECT().UpdateStageRunStatusAndTime(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	})
	err := s.pRService.DeletePipelineRunSubtask(context.Background(), 1)
	assert.NoError(s.T(), err)
}

//func (s *PRServiceTestSuite) TestCheckAppUpgrade_Success() {
//	pipelineRuns := []dao.PipelineRun{{
//		BaseModel: db.BaseModel{
//			ID: 1,
//		},
//		PipelineId:           1,
//		AppId:                1,
//		BuildNumber:          1,
//		SourceBranch:         "",
//		Branch:               "master",
//		TriggerByChineseName: "张三",
//		TriggerByEmployeeNo:  "T0001",
//	}}
//	taskRun := &dao.PipelineRunTask{
//		PipelineRunId: 1,
//		Type:          constants.TASK_UPGRADE_APPROVAL.String(),
//	}
//	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), gomock.Any()).Return(taskRun, nil)
//	s.mockPRepo.EXPECT().FindPipelineRunInProdStage(gomock.Any(), gomock.Any()).Return(pipelineRuns, nil)
//	req := model.UpgradeCheckReq{
//		PipelineID: 1,
//	}
//	results, err := s.pRService.CheckAppUpgrade(context.Background(), req)
//
//	assert.NoError(s.T(), err)
//	assert.Equal(s.T(), 1, len(results))
//	assert.Equal(s.T(), "master", results[0].Branch)
//}
//
//func (s *PRServiceTestSuite) TestCheckAppUpgrade_TaskRun_NotExisted() {
//	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), gomock.Any()).Return(nil, nil)
//	req := model.UpgradeCheckReq{
//		PipelineID: 1,
//		TaskRunID:  1,
//	}
//	_, err := s.pRService.CheckAppUpgrade(context.Background(), req)
//
//	assert.Equal(s.T(), "pipelineRunTask[1] not existed", err.Error())
//}
//
//func (s *PRServiceTestSuite) TestCheckAppUpgrade_GetTaskRun_Error() {
//	wantErr := errors.New("get task run failed")
//	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), gomock.Any()).Return(nil, wantErr)
//	req := model.UpgradeCheckReq{
//		PipelineID: 1,
//		TaskRunID:  1,
//	}
//	_, err := s.pRService.CheckAppUpgrade(context.Background(), req)
//
//	assert.Equal(s.T(), wantErr.Error(), err.Error())
//}
//
//func (s *PRServiceTestSuite) TestCheckAppUpgrade_WrongTaskRunType() {
//	taskRun := &dao.PipelineRunTask{
//		PipelineRunId: 1,
//		Type:          constants.TASK_TEST_APPROVAL.String(),
//	}
//	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), gomock.Any()).Return(taskRun, nil)
//	req := model.UpgradeCheckReq{
//		PipelineID: 1,
//	}
//	results, err := s.pRService.CheckAppUpgrade(context.Background(), req)
//
//	assert.NoError(s.T(), err)
//	assert.Nil(s.T(), results)
//}
//
//func (s *PRServiceTestSuite) TestCheckAppUpgrade_FindPipelineRunInProdStage_Error() {
//	taskRun := &dao.PipelineRunTask{
//		PipelineRunId: 1,
//		Type:          constants.TASK_UPGRADE_APPROVAL.String(),
//	}
//	s.mockPRepo.EXPECT().FindRunTaskById(gomock.Any(), gomock.Any()).Return(taskRun, nil)
//	wantErr := errors.New("find pipeline run in prod stage failed")
//	s.mockPRepo.EXPECT().FindPipelineRunInProdStage(gomock.Any(), gomock.Any()).Return(nil, wantErr)
//	req := model.UpgradeCheckReq{
//		PipelineID: 1,
//	}
//	_, err := s.pRService.CheckAppUpgrade(context.Background(), req)
//
//	assert.Equal(s.T(), wantErr, err)
//}

func (s *PRServiceTestSuite) Test_GenClientVersionCanaryResp_NoPlanRunNodes_Success() {
	// Arrange
	deployConfigId, pipelineRunId := int64(1), int64(1)
	mockPlan := &pbdep.DeployPlan{
		Name:         "testPlan",
		CanaryPolicy: "TRAFFIC,CLIENT_VERSION",
	}
	mockPlanRun := &pbdep.PlanRunResp{
		Id:            1,
		Nodes:         []*pbdep.PlanRunResp_Node{},
		TimeType:      constants.PlanTimeTypeRelative.String(),
		PlanRunConfig: datatypes.JSON(`{"canaryPolicy": ["CLIENT_VERSION", "TRAFFIC"]}`),
	}
	s.mockPlanClient.EXPECT().GetDeployPlanByID(gomock.Any(), gomock.Any()).Return(mockPlan, nil)
	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(mockPlanRun, nil)

	// Act
	resp, err := s.pRService.genClientVersionCanaryResp(context.Background(), deployConfigId, pipelineRunId)

	// Assert
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), "testPlan", resp.CanaryDeployPlanResp.DeployPlanName)
	assert.Equal(s.T(), int64(1), resp.DeployPlanRunID)
	assert.Equal(s.T(), true, resp.IsShowTrafficControl)
	assert.Contains(s.T(), resp.CanaryPolicy, constants.PolicyTraffic)
	assert.Contains(s.T(), resp.CanaryPolicy, constants.PolicyClientVersion)
}

func (s *PRServiceTestSuite) Test_GenClientVersionCanaryResp_PlanRunSkipped_Success() {
	// Arrange
	deployConfigId, pipelineRunId := int64(1), int64(1)
	mockPlan := &pbdep.DeployPlan{
		Name:         "testPlan",
		CanaryPolicy: "TRAFFIC,CLIENT_VERSION",
	}
	mockPlanRun := &pbdep.PlanRunResp{
		Id: 1,
		Nodes: []*pbdep.PlanRunResp_Node{
			{
				Id:          1,
				Action:      constants.PlanActionControlTraffic.String(),
				StartedTime: 0,
				Status:      constants.PlanRunInitial.String(),
			},
		},
		TimeType:      constants.PlanTimeTypeAbsolute.String(),
		PlanRunConfig: datatypes.JSON(`{"canaryPolicy": ["CLIENT_VERSION", "TRAFFIC"]}`),
		Status:        constants.PlanRunSkip.String(),
	}
	s.mockPlanClient.EXPECT().GetDeployPlanByID(gomock.Any(), gomock.Any()).Return(mockPlan, nil)
	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(mockPlanRun, nil)

	// Act
	resp, err := s.pRService.genClientVersionCanaryResp(context.Background(), deployConfigId, pipelineRunId)

	// Assert
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), true, resp.IsShowTrafficControl)
}

func (s *PRServiceTestSuite) Test_GenClientVersionCanaryResp_PlanRunNotExisted() {
	// Arrange
	deployConfigId, pipelineRunId := int64(1), int64(1)
	mockPlan := &pbdep.DeployPlan{
		Name:         "testPlan",
		CanaryPolicy: "TRAFFIC,CLIENT_VERSION",
	}
	mockPlanRun := &pbdep.PlanRunResp{}
	s.mockPlanClient.EXPECT().GetDeployPlanByID(gomock.Any(), gomock.Any()).Return(mockPlan, nil)
	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(mockPlanRun, nil)

	// Act
	resp, err := s.pRService.genClientVersionCanaryResp(context.Background(), deployConfigId, pipelineRunId)

	// Assert
	assert.NoError(s.T(), err)
	assert.Equal(s.T(), false, resp.IsShowTrafficControl)
}

func (s *PRServiceTestSuite) Test_GenClientVersionCanaryResp_GetDeployPlanByID_Error() {
	// Arrange
	deployConfigId, pipelineRunId := int64(1), int64(1)
	wantErr := errors.New("get deploy plan error")
	s.mockPlanClient.EXPECT().GetDeployPlanByID(gomock.Any(), gomock.Any()).Return(nil, wantErr)

	// Act
	_, err := s.pRService.genClientVersionCanaryResp(context.Background(), deployConfigId, pipelineRunId)

	// Assert
	assert.Equal(s.T(), wantErr, err)
}

func (s *PRServiceTestSuite) Test_GenClientVersionCanaryResp_GetPlanRun_Error() {
	// Arrange
	deployConfigId, pipelineRunId := int64(1), int64(1)
	mockPlan := &pbdep.DeployPlan{
		Name:         "testPlan",
		CanaryPolicy: "TRAFFIC,CLIENT_VERSION",
	}
	wantErr := errors.New("get plan run error")
	s.mockPlanClient.EXPECT().GetDeployPlanByID(gomock.Any(), gomock.Any()).Return(mockPlan, nil)
	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(nil, wantErr)

	// Act
	_, err := s.pRService.genClientVersionCanaryResp(context.Background(), deployConfigId, pipelineRunId)

	// Assert
	assert.Equal(s.T(), wantErr, err)
}

//func (s *PRServiceTestSuite) Test_CheckPassTask_FindTaskRunById_Error() {
//	// Arrange
//	req := model.PassTaskReq{
//		TaskRunId: 1,
//	}
//	wantErr := errors.New("find pipeline run task error")
//	s.mockPRepo.EXPECT().FindTaskRunById(gomock.Any(), gomock.Any()).Return(nil, wantErr)
//
//	// Act
//	_, err := s.pRService.CheckPassTask(context.Background(), req)
//
//	// Assert
//	assert.Equal(s.T(), wantErr.Error(), err.Error())
//}
//
//func (s *PRServiceTestSuite) Test_CheckPassTask_FindTaskRunByIdNotExisted() {
//	// Arrange
//	req := model.PassTaskReq{
//		TaskRunId: 1,
//	}
//	s.mockPRepo.EXPECT().FindTaskRunById(gomock.Any(), gomock.Any()).Return(nil, nil)
//
//	// Act
//	_, err := s.pRService.CheckPassTask(context.Background(), req)
//
//	// Assert
//	assert.Equal(s.T(), "pipelineRunTask[1] not existed", err.Error())
//}
//
//func (s *PRServiceTestSuite) Test_CheckPassTask_GetPlanRun_Error() {
//	// Arrange
//	req := model.PassTaskReq{
//		TaskRunId: 1,
//	}
//	taskRun := &dao.PipelineRunTask{
//		PipelineRunId: 1,
//		Config:        datatypes.JSON(`{"canaryPolicy": "CLIENT_VERSION"}`),
//	}
//	s.mockPRepo.EXPECT().FindTaskRunById(gomock.Any(), gomock.Any()).Return(taskRun, nil)
//	wantErr := errors.New("get plan run error")
//	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(nil, wantErr)
//
//	// Act
//	_, err := s.pRService.CheckPassTask(context.Background(), req)
//
//	// Assert
//	assert.Equal(s.T(), wantErr, err)
//}
//
//func (s *PRServiceTestSuite) Test_CheckPassTask_PlanRunRunning() {
//	// Arrange
//	req := model.PassTaskReq{
//		TaskRunId: 1,
//	}
//	taskRun := &dao.PipelineRunTask{
//		PipelineRunId: 1,
//		Config:        datatypes.JSON(`{"canaryPolicy": "CLIENT_VERSION"}`),
//	}
//	planRun := &pbdep.PlanRunResp{
//		Id:     1,
//		Status: constants.PlanRunRunning.String(),
//	}
//	s.mockPRepo.EXPECT().FindTaskRunById(gomock.Any(), gomock.Any()).Return(taskRun, nil)
//	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(planRun, nil)
//
//	// Act
//	resp, err := s.pRService.CheckPassTask(context.Background(), req)
//
//	// Assert
//	assert.NoError(s.T(), err)
//	assert.Equal(s.T(), false, resp.IsPass)
//}
//
//func (s *PRServiceTestSuite) Test_CheckPassTask_PlanRunSuccess() {
//	// Arrange
//	req := model.PassTaskReq{
//		TaskRunId: 1,
//	}
//	taskRun := &dao.PipelineRunTask{
//		PipelineRunId: 1,
//		Config:        datatypes.JSON(`{"canaryPolicy": "CLIENT_VERSION"}`),
//	}
//	planRun := &pbdep.PlanRunResp{
//		Status: constants.PlanRunSuccessful.String(),
//	}
//	s.mockPRepo.EXPECT().FindTaskRunById(gomock.Any(), gomock.Any()).Return(taskRun, nil)
//	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(planRun, nil)
//
//	// Act
//	resp, err := s.pRService.CheckPassTask(context.Background(), req)
//
//	// Assert
//	assert.NoError(s.T(), err)
//	assert.Equal(s.T(), true, resp.IsPass)
//}
//
//func (s *PRServiceTestSuite) Test_CheckPassTask_TrafficTask_Success() {
//	// Arrange
//	req := model.PassTaskReq{
//		TaskRunId: 1,
//	}
//	taskRun := &dao.PipelineRunTask{
//		PipelineRunId: 1,
//		Config:        datatypes.JSON(`{"canaryPolicy": "TRAFFIC"}`),
//	}
//	s.mockPRepo.EXPECT().FindTaskRunById(gomock.Any(), gomock.Any()).Return(taskRun, nil)
//
//	// Act
//	resp, err := s.pRService.CheckPassTask(context.Background(), req)
//
//	// Assert
//	assert.NoError(s.T(), err)
//	assert.Equal(s.T(), true, resp.IsPass)
//}
//
//func (s *PRServiceTestSuite) Test_CheckPassTask_UnmarshalTaskConfig_Error() {
//	// Arrange
//	req := model.PassTaskReq{
//		TaskRunId: 1,
//	}
//	taskRun := &dao.PipelineRunTask{
//		PipelineRunId: 1,
//		Config:        datatypes.JSON(`{"canaryPolicy": "TRAFFIC"`),
//	}
//	s.mockPRepo.EXPECT().FindTaskRunById(gomock.Any(), gomock.Any()).Return(taskRun, nil)
//
//	// Act
//	_, err := s.pRService.CheckPassTask(context.Background(), req)
//
//	// Assert
//	assert.Error(s.T(), err)
//}

func (s *PRServiceTestSuite) Test_CheckCanaryPassTask_WithPolicyTraffic_Success() {
	// given
	taskRun := &dao.PipelineRunTask{
		Type:   constants.TASK_DEPLOY_CANARY.String(),
		Config: datatypes.JSON(`{"canaryPolicy": "TRAFFIC"}`),
		SubRunTasks: []dao.PipelineRunSubtask{{
			Enabled: true,
			Config:  datatypes.JSON(`{"percentage": 50}`),
		}},
	}

	// when
	err := s.pRService.checkCanaryPassTask(context.Background(), taskRun)

	// then
	assert.NoError(s.T(), err)
}

func (s *PRServiceTestSuite) Test_CheckCanaryPassTask_WithPolicyClientVersion_Success() {
	// given
	taskRun := &dao.PipelineRunTask{
		Type:          constants.TASK_DEPLOY_CANARY.String(),
		Config:        datatypes.JSON(`{"canaryPolicy": "CLIENT_VERSION"}`),
		PipelineRunId: 1,
		SubRunTasks: []dao.PipelineRunSubtask{{
			Enabled: true,
			Config:  datatypes.JSON(`{"percentage": 0}`),
		}},
	}
	planRun := &pbdep.PlanRunResp{
		Id:            1,
		PlanRunConfig: []byte(`{"canaryPolicy": ["CLIENT_VERSION"]}`),
	}
	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(planRun, nil)

	// when
	err := s.pRService.checkCanaryPassTask(context.Background(), taskRun)

	// then
	assert.NoError(s.T(), err)
}

func (s *PRServiceTestSuite) Test_CheckCanaryPassTask_WithPolicyClientVersion_NoTraffic_Success() {
	// given
	taskRun := &dao.PipelineRunTask{
		Type: constants.TASK_DEPLOY_CANARY.String(),
		Config: datatypes.JSON(`{
"canaryPolicy": "CLIENT_VERSION"
}`),
		PipelineRunId: 1,
	}
	planRun := &pbdep.PlanRunResp{
		Id: 1,
		PlanRunConfig: []byte(`{
"canaryPolicy": ["CLIENT_VERSION"]
}`),
	}
	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(planRun, nil)

	// when
	err := s.pRService.checkCanaryPassTask(context.Background(), taskRun)

	// then
	assert.NoError(s.T(), err)
}

func (s *PRServiceTestSuite) Test_CheckCanaryPassTask_WithPolicyTraffic_ZeroPercentage_Failure() {
	// given
	taskRun := &dao.PipelineRunTask{
		Type: constants.TASK_DEPLOY_CANARY.String(),
		Config: datatypes.JSON(`{
"canaryPolicy": "TRAFFIC",
"canaryPercentage": 0
}`),
		SubRunTasks: []dao.PipelineRunSubtask{{
			Enabled: true,
			Config:  datatypes.JSON(`{"percentage": 0}`),
		}},
	}

	// when
	err := s.pRService.checkCanaryPassTask(context.Background(), taskRun)

	// then
	assert.Error(s.T(), err)
	assert.Equal(s.T(), pipelineErr.ErrCanaryPercentageInvalid, err)
}

func (s *PRServiceTestSuite) Test_CheckCanaryPassTask_WithInvalidPolicy_Failure() {
	// given
	taskRun := &dao.PipelineRunTask{
		Type: constants.TASK_DEPLOY_CANARY.String(),
		Config: datatypes.JSON(`{
"canaryPolicy": "INVALID_POLICY",
"canaryPercentage": 50
}`),
	}

	// when
	err := s.pRService.checkCanaryPassTask(context.Background(), taskRun)

	// then
	assert.NoError(s.T(), err)
}

//func (s *PRServiceTestSuite) TestCheckMultiCloudUpgrade_ShouldReturnError_WhenFindSubTaskFails() {
//	// Arrange
//	req := model.MultiCloudUpgradeCheckReq{DeployID: 1}
//	s.mockPRepo.EXPECT().FindPreloadPipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(nil, errors.New("find subtask failed"))
//
//	// Act
//	_, err := s.pRService.CheckMultiCloudUpgrade(context.Background(), req)
//
//	// Assert
//	assert.Error(s.T(), err)
//	assert.Equal(s.T(), "find subtask failed", err.Error())
//}
//
//func (s *PRServiceTestSuite) TestCheckMultiCloudUpgrade_ShouldReturnError_WhenSubTaskNotFound() {
//	// Arrange
//	req := model.MultiCloudUpgradeCheckReq{DeployID: 1}
//	s.mockPRepo.EXPECT().FindPreloadPipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(nil, nil)
//
//	// Act
//	_, err := s.pRService.CheckMultiCloudUpgrade(context.Background(), req)
//
//	// Assert
//	assert.Error(s.T(), err)
//	assert.Equal(s.T(), "pipelineRunSubtask[1] not existed", err.Error())
//}
//
//func (s *PRServiceTestSuite) TestCheckMultiCloudUpgrade_ShouldReturnTrue_WhenNotProdEnvOrNotAutoDeploy() {
//	// Arrange
//	req := model.MultiCloudUpgradeCheckReq{DeployID: 1}
//	subTask := &dao.PipelineRunSubtask{
//		PipelineRunTask: &dao.PipelineRunTask{
//			StageRun: &dao.PipelineRunStage{
//				Type: constants.STAGE_DEPLOY_TEST_ENV.String(),
//			},
//			Type: constants.TASK_AUTOMATION_DEPLOY.String(),
//		},
//	}
//	s.mockPRepo.EXPECT().FindPreloadPipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(subTask, nil)
//
//	// Act
//	resp, err := s.pRService.CheckMultiCloudUpgrade(context.Background(), req)
//
//	// Assert
//	assert.NoError(s.T(), err)
//	assert.True(s.T(), resp.IsPass)
//}
//
//func (s *PRServiceTestSuite) TestCheckMultiCloudUpgrade_ShouldReturnError_WhenFindCanaryTaskError() {
//	// Arrange
//	req := model.MultiCloudUpgradeCheckReq{DeployID: 1}
//	subTask := &dao.PipelineRunSubtask{
//		PipelineRunTask: &dao.PipelineRunTask{
//			StageRun: &dao.PipelineRunStage{
//				Type: constants.STAGE_DEPLOY_PROD_ENV.String(),
//				BaseModel: db.BaseModel{
//					ID: 1,
//				},
//			},
//			Type: constants.TASK_AUTOMATION_DEPLOY.String(),
//		},
//	}
//	s.mockPRepo.EXPECT().FindPreloadPipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(subTask, nil)
//	wantErr := errors.New("find canary task error")
//	s.mockPRepo.EXPECT().FindTaskRunByTypeAndStage(gomock.Any(), constants.TASK_DEPLOY_CANARY, subTask.PipelineRunTask.StageRun.ID).Return(nil, wantErr)
//
//	// Act
//	_, err := s.pRService.CheckMultiCloudUpgrade(context.Background(), req)
//
//	// Assert
//	assert.Equal(s.T(), wantErr, err)
//}
//
//func (s *PRServiceTestSuite) TestCheckMultiCloudUpgrade_ShouldReturnTrue_WhenNoCanaryTask() {
//	// Arrange
//	req := model.MultiCloudUpgradeCheckReq{DeployID: 1}
//	subTask := &dao.PipelineRunSubtask{
//		PipelineRunTask: &dao.PipelineRunTask{
//			StageRun: &dao.PipelineRunStage{
//				Type: constants.STAGE_DEPLOY_PROD_ENV.String(),
//				BaseModel: db.BaseModel{
//					ID: 1,
//				},
//			},
//			Type: constants.TASK_AUTOMATION_DEPLOY.String(),
//		},
//	}
//	s.mockPRepo.EXPECT().FindPreloadPipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(subTask, nil)
//	s.mockPRepo.EXPECT().FindTaskRunByTypeAndStage(gomock.Any(), constants.TASK_DEPLOY_CANARY, subTask.PipelineRunTask.StageRun.ID).Return(nil, nil)
//
//	// Act
//	resp, err := s.pRService.CheckMultiCloudUpgrade(context.Background(), req)
//
//	// Assert
//	assert.NoError(s.T(), err)
//	assert.True(s.T(), resp.IsPass)
//}
//
//func (s *PRServiceTestSuite) TestCheckMultiCloudUpgrade_ShouldReturnTrue_WhenCanaryPolicyNotClientVersion() {
//	// Arrange
//	req := model.MultiCloudUpgradeCheckReq{DeployID: 1}
//	subTask := &dao.PipelineRunSubtask{
//		PipelineRunTask: &dao.PipelineRunTask{
//			StageRun: &dao.PipelineRunStage{
//				Type: constants.STAGE_DEPLOY_PROD_ENV.String(),
//				BaseModel: db.BaseModel{
//					ID: 1,
//				},
//			},
//			Type: constants.TASK_AUTOMATION_DEPLOY.String(),
//		},
//	}
//	canaryTask := &dao.PipelineRunTask{
//		Config: datatypes.JSON(`{"canaryPolicy": "TRAFFIC"}`),
//	}
//	s.mockPRepo.EXPECT().FindPreloadPipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(subTask, nil)
//	s.mockPRepo.EXPECT().FindTaskRunByTypeAndStage(gomock.Any(), constants.TASK_DEPLOY_CANARY, subTask.PipelineRunTask.StageRun.ID).Return(canaryTask, nil)
//
//	// Act
//	resp, err := s.pRService.CheckMultiCloudUpgrade(context.Background(), req)
//
//	// Assert
//	assert.NoError(s.T(), err)
//	assert.True(s.T(), resp.IsPass)
//}
//
//func (s *PRServiceTestSuite) TestCheckMultiCloudUpgrade_ShouldReturnTrue_WhenCanaryConfigUnmarshalError() {
//	// Arrange
//	req := model.MultiCloudUpgradeCheckReq{DeployID: 1}
//	subTask := &dao.PipelineRunSubtask{
//		PipelineRunTask: &dao.PipelineRunTask{
//			StageRun: &dao.PipelineRunStage{
//				Type: constants.STAGE_DEPLOY_PROD_ENV.String(),
//				BaseModel: db.BaseModel{
//					ID: 1,
//				},
//			},
//			Type: constants.TASK_AUTOMATION_DEPLOY.String(),
//		},
//	}
//	canaryTask := &dao.PipelineRunTask{
//		Config: datatypes.JSON(`{"canaryPolicy": "TRAFFIC}`),
//	}
//	s.mockPRepo.EXPECT().FindPreloadPipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(subTask, nil)
//	s.mockPRepo.EXPECT().FindTaskRunByTypeAndStage(gomock.Any(), constants.TASK_DEPLOY_CANARY, subTask.PipelineRunTask.StageRun.ID).Return(canaryTask, nil)
//
//	// Act
//	_, err := s.pRService.CheckMultiCloudUpgrade(context.Background(), req)
//
//	// Assert
//	assert.Error(s.T(), err)
//}
//
//func (s *PRServiceTestSuite) TestCheckMultiCloudUpgrade_ShouldReturnError_WhenGetPlanRunError() {
//	// Arrange
//	req := model.MultiCloudUpgradeCheckReq{DeployID: 1}
//	subTask := &dao.PipelineRunSubtask{
//		PipelineRunTask: &dao.PipelineRunTask{
//			StageRun: &dao.PipelineRunStage{
//				Type: constants.STAGE_DEPLOY_PROD_ENV.String(),
//				BaseModel: db.BaseModel{
//					ID: 1,
//				},
//			},
//			Type: constants.TASK_AUTOMATION_DEPLOY.String(),
//		},
//	}
//	canaryTask := &dao.PipelineRunTask{
//		Config: datatypes.JSON(`{"canaryPolicy": "CLIENT_VERSION"}`),
//	}
//	s.mockPRepo.EXPECT().FindPreloadPipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(subTask, nil)
//	s.mockPRepo.EXPECT().FindTaskRunByTypeAndStage(gomock.Any(), constants.TASK_DEPLOY_CANARY, subTask.PipelineRunTask.StageRun.ID).Return(canaryTask, nil)
//	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(nil, errors.New("get plan run error"))
//
//	// Act
//	_, err := s.pRService.CheckMultiCloudUpgrade(context.Background(), req)
//
//	// Assert
//	assert.Error(s.T(), err)
//	assert.Equal(s.T(), "get plan run error", err.Error())
//}
//
//func (s *PRServiceTestSuite) TestCheckMultiCloudUpgrade_ShouldReturnFalse_WhenDeployOriginIsInProgress() {
//	// Arrange
//	req := model.MultiCloudUpgradeCheckReq{DeployID: 1}
//	subTask := &dao.PipelineRunSubtask{
//		PipelineRunTask: &dao.PipelineRunTask{
//			StageRun: &dao.PipelineRunStage{
//				Type: constants.STAGE_DEPLOY_PROD_ENV.String(),
//				BaseModel: db.BaseModel{
//					ID: 1,
//				},
//			},
//			Type: constants.TASK_AUTOMATION_DEPLOY.String(),
//		},
//	}
//	canaryTask := &dao.PipelineRunTask{
//		Config: datatypes.JSON(`{"canaryPolicy": "CLIENT_VERSION"}`),
//	}
//	timeNow := time.Now().Unix()
//	planRun := &pbdep.PlanRunResp{
//		Id:                 1,
//		DeployPlanId:       1,
//		DeployPlanRecordId: 1,
//		Status:             constants.PlanRunRunning.String(),
//		Nodes:              []*pbdep.PlanRunResp_Node{{Id: 1, Action: constants.PlanActionDeployOrigin.String(), StartedTime: timeNow}},
//	}
//	s.mockPRepo.EXPECT().FindPreloadPipelineRunSubtaskById(gomock.Any(), gomock.Any()).Return(subTask, nil)
//	s.mockPRepo.EXPECT().FindTaskRunByTypeAndStage(gomock.Any(), constants.TASK_DEPLOY_CANARY, subTask.PipelineRunTask.StageRun.ID).Return(canaryTask, nil)
//	s.mockPlanClient.EXPECT().GetPlanRun(gomock.Any(), gomock.Any()).Return(planRun, nil)
//
//	// Act
//	resp, err := s.pRService.CheckMultiCloudUpgrade(context.Background(), req)
//
//	// Assert
//	assert.NoError(s.T(), err)
//	assert.Equal(s.T(), false, resp.IsPass)
//}
