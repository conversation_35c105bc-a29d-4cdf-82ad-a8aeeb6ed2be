-- 迭代子环境 2.0 的数据库变更, 主要改动内容是在部署信息中加入 senv 环境标签
-- DB: deploy - 子环境 2.0 名称
alter table `deploy`.`deploy_change_log` add column senv varchar(63) not null default '' comment '子环境 2.0 名称';

-- DB: deploy - 子环境 2.0 名称
alter table `deploy`.`deploy_metadata` add column senv varchar(63) not null default '' comment '子环境 2.0 名称, 需要符合 kubernetes label value 命名规范';

-- DB: deploy - 子环境版本标识
alter table `deploy`.`sub_env` add column env_target varchar(32) not null default 'sub' comment '版本号, constants.EnvTargetType';

-- DB: deploy - 子环境 2.0 名称, 需要符合 kubernetes label value 命名规范
alter table `deploy`.`sub_env` add column senv varchar(63) not null default '' comment '子环境 2.0 名称, 需要符合 kubernetes label value 命名规范';