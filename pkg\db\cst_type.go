package db

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// DbStrSlice 类型对应 mysql 数据库的 char，varchar，text 类型
// 数据库模型中使用了该类型之后，插入 mysql 数据库会序列化成 json 字符串之后在入库
// 从 mysql 数据库读取会反解析成 []string 类型，例如: []string{"abc", "def"}，
// 入库结果是 ["abc", "def"]，[]string{}，入库结果是 null。
// 使用 gorm DbStrSlice 时可以正常使用的，
// 但是使用 beggo 的 DbStrSlice 类型会报错
type DbStrSlice []string

func (s *DbStrSlice) Scan(value interface{}) error {
	jsonData, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("assertion failed, type: %T, value: %+v", value, value)
	}
	if string(jsonData) == "" {
		return nil
	}
	err := json.Unmarshal(jsonData, s)
	if err != nil {
		return err
	}
	return nil
}

func (s DbStrSlice) Value() (driver.Value, error) {
	if len(s) == 0 {
		return "[]", nil
	}

	jsonData, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(jsonData), nil
}

type DbStrMap map[string]string

func (s *DbStrMap) Scan(value interface{}) error {
	jsonData, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("assertion failed, type: %T, value: %+v", value, value)
	}
	if string(jsonData) == "" {
		return nil
	}
	if string(jsonData) == "{}" {
		return nil
	}
	err := json.Unmarshal(jsonData, s)
	if err != nil {
		return err
	}
	return nil
}

func (s DbStrMap) Value() (driver.Value, error) {
	if len(s) == 0 {
		return "{}", nil
	}

	jsonData, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return string(jsonData), nil
}
