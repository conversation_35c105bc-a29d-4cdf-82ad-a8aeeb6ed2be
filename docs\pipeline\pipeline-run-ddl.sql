CREATE TABLE `pipeline_run` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `pipeline_id` bigint NOT NULL COMMENT '关联流水线id',
  `cancel_by` bigint DEFAULT NULL COMMENT '终止运行操作者',
  `cancel_by_chinese_name` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '终止运行操作者中文名',
  `app_id` bigint NOT NULL COMMENT '关联服务ID',
  `build_number` bigint NOT NULL COMMENT '流水线运行记录号',
  `repo_address` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '仓库地址',
  `source_branch` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触发源分支',
  `branch` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发目标分支',
  `trigger_by` bigint NOT NULL COMMENT '运行触发人id',
  `trigger_by_chinese_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '运行触发人中文名',
  `status` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '运行状态',
  `started_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `workspaces` json DEFAULT NULL COMMENT 'pvc',
  `stage_sequence` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '运行阶段顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `tekton_namespace` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'tekton 命名空间',
  `tekton_name` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'tekton 名称',
  `trigger_by_employee_no` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '运行触发人工号',
  `description` VARCHAR(200) default '' COMMENT '运行流水线描述',
  PRIMARY KEY (`id`),
  KEY `pipeline_run_pipeline_id_index` (`pipeline_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='流水线运行表';

alter table pipeline.pipeline_run
    add column `is_auto_merge` tinyint not null default 0 comment '是否自动合并分支';

-- 是否实际运行的克隆记录
alter table `pipeline`.`pipeline_run` add column `is_clone` tinyint not null default 0 comment '是否实际运行的克隆记录';