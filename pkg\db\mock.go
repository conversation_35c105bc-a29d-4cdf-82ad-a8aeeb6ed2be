package db

import (
	"database/sql/driver"
	"regexp"
	"runtime/debug"
	"strings"
	"time"

	"52tt.com/cicd/pkg/log"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

const mysqlErrName = "DBErr"

type AnyTime struct{}

func (a AnyTime) Match(v driver.Value) bool {
	_, ok := v.(time.Time)
	return ok
}

type Suite struct {
	suite.Suite
	log.SuiteLogger
	AnyTime

	DB   *gorm.DB
	Mock sqlmock.Sqlmock
}

func (suite *Suite) PanicPrinter() func() {
	return func() {
		if r := recover(); r != nil {
			suite.T().Logf("Panic: %v", r)
			suite.T().Logf("Stack: %s", debug.Stack())
		}
	}
}

func (suite *Suite) SetupSuite() {
	//suite.ResetDB()

	suite.SuiteLogger.SetupSuite()
}

func (suite *Suite) SetupTest() {
	suite.ResetDB()
}

func (suite *Suite) TearDownTest() {
	suite.SuiteLogger.TearDownTest()
}

func (suite *Suite) BeforeTest(suiteName, testName string) {
	if strings.Contains(testName, mysqlErrName) {
		suite.CloseDB()
	}
}

func (suite *Suite) AfterTest(_, _ string) {
	require.NoError(suite.T(), suite.Mock.ExpectationsWereMet())
}

func (suite *Suite) ResetDB() {
	//var (
	//	db  *sql.DB
	//	err error
	//)
	sqlDB, mock, err := sqlmock.New()
	require.NoError(suite.T(), err)

	suite.Mock = mock

	db, err := gorm.Open(mysql.New(mysql.Config{
		SkipInitializeWithVersion: true,
		Conn:                      sqlDB,
	}), &gorm.Config{
		// TODO: SkipDefaultTransaction: true, // all Repository mock EXPECT related to transaction neeed to be update.
	},
	)

	require.NoError(suite.T(), err)

	suite.DB = db
	DB = suite.DB
}

func (suite *Suite) CloseDB() {
	sqlDB, _ := suite.DB.DB()
	_ = sqlDB.Close()
}

func (suite *Suite) ExpectExec(sql string, fn func(expectedExec *sqlmock.ExpectedExec)) {
	suite.Mock.ExpectBegin()
	fn(suite.Mock.ExpectExec(regexp.QuoteMeta(sql)))
	suite.Mock.ExpectCommit()
}

func (suite *Suite) ExecRollback(sql string, fn func(exec *sqlmock.ExpectedExec)) {
	suite.Mock.ExpectBegin()
	fn(suite.Mock.ExpectExec(regexp.QuoteMeta(sql)))
	suite.Mock.ExpectRollback()
}

func (suite *Suite) ExpectTransaction(fn func()) {
	suite.Mock.ExpectBegin()
	fn()
	suite.Mock.ExpectCommit()
}

func (suite *Suite) ExecTransactionRollback(fn func()) {
	// typo?
	suite.Mock.ExpectBegin()
	fn()
	suite.Mock.ExpectRollback()
}
