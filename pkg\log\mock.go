package log

import (
	"fmt"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest/observer"
)

type SuiteLogger struct {
	obsLogs *observer.ObservedLogs
}

func (suite *SuiteLogger) GetTestLogs() []observer.LoggedEntry {
	return suite.obsLogs.All()
}

func (suite *SuiteLogger) PrintTestLogs() {
	for _, l := range suite.GetTestLogs() {
		fmt.Printf("%+v", l)
	}
}

func (suite *SuiteLogger) SetupSuite() {
	observedZapCore, obsLogs := observer.New(zap.DebugLevel)
	SugarLogger = zap.New(observedZapCore).Sugar()

	suite.obsLogs = obsLogs
}

func (suite *SuiteLogger) TearDownTest() {
	_ = suite.obsLogs.TakeAll()
}

type Suite struct {
	suite.Suite
	SuiteLogger
}

func (suite *Suite) SetupSuite() {
	suite.SuiteLogger.SetupSuite()
}

func (suite *Suite) TearDownTest() {
	suite.SuiteLogger.TearDownTest()
}
