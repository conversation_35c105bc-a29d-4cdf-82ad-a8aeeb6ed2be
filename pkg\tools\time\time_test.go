package time

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"
	"testing"
	"time"
)

func TestFormat(t *testing.T) {
	utcTime := time.Now().UTC()
	fmt.Println("当前的UTC时间:", utcTime)

	str1 := Format(utcTime)
	fmt.Println("转成CST时间：", str1)

	timestamp := timestamppb.New(utcTime)
	fmt.Println("\n\n----->当前时间戳：", timestamp.AsTime())

	str2 := Format(timestamp.AsTime())
	fmt.Println("转成当地时间：", str2)
	/*
		输出：
		当前的UTC时间: 2023-05-31 04:57:25.1294724 +0000 UTC
		转成当地时间： 2023-05-31 12:57:25 星期三 CST
	*/
}

func Test_ToYearMonth(t *testing.T) {
	utcTime := time.Date(2023, time.June, 27, 10, 30, 0, 0, time.UTC)

	str := ToYearMonth(utcTime)

	assert.Equal(t, "2023-06", str)
}
