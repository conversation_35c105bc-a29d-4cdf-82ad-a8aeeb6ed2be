package gitlab

import (
	"fmt"
	"math"
	"net/url"
	"regexp"
	"strings"
)

func getBranch(ref string) string {
	if strings.HasPrefix(ref, "refs/heads/") {
		branch := strings.ReplaceAll(ref, "refs/heads/", "")
		return branch
	}
	return ref
}

func checkEmail(addr string) bool {
	emailRegex := regexp.MustCompile(`^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$`)
	return emailRegex.MatchString(addr)
}

func floatToInt64(s interface{}) int {
	var n int64
	switch s.(type) {
	case float64:
		n = int64(math.Floor(s.(float64)))
	default:
		n = 0
	}
	return int(n)
}

func getRepoGroupAndRepoName(repoAddr string) (string, error) {
	reg := regexp.MustCompile(`^https?://`)
	matched := reg.MatchString(repoAddr)
	if !matched {
		return "", fmt.Errorf("repo url must be starts with http or https")
	}
	addr, err := url.Parse(repoAddr)
	if err != nil {
		return "", err
	}
	path := strings.Split(addr.Path, "/")
	if len(path) < 2 {
		return "", fmt.Errorf("repo url must be contain repoGroup and repoName")
	}
	repoName := path[len(path)-1]
	repoGroup := strings.TrimSuffix(strings.Replace(strings.Join(path, "/"), "/", "", 1), repoName)
	if strings.HasSuffix(repoName, ".git") {
		// 项目名称带有.会找不到项目
		// dotIndex := strings.IndexRune(repoName, '.')
		// repoName = repoName[0:dotIndex]
		repoName = strings.Split(repoName, ".git")[0]
	}
	return repoGroup + repoName, nil
}
