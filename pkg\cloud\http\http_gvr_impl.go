package http

import (
	"context"
	"errors"

	"52tt.com/cicd/pkg/cloud/http/core"
	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
)

const (
	gvrCreate = "/k8s/resource/any/create"
	gvrUpdate = "/k8s/resource/any/update"
	gvrGet    = "/k8s/resource/any/get"
	gvrList   = "/k8s/resource/any/list"
	gvrDelete = "/k8s/resource/any/delete"
)

func NewGVRHTTPClient(host, token string) core.GVRHTTPClient {
	session := buildSession(token)
	client := NewGVRHTTPClientWithSession(host, token, session)
	return client
}

func NewGVRHTTPClientWithSession(host, token string, session httpclient.Session) core.GVRHTTPClient {
	return &gvrHTTPClient{
		Host:    host,
		Token:   token,
		session: session,
	}
}

type gvrHTTPClient struct {
	Host    string
	HostV2  string
	Token   string
	session httpclient.Session
}

func (c *gvrHTTPClient) CreateResource(ctx context.Context, in *core.CreateResourceRequest) (*core.CreateResourceResponse, error) {
	url := buildURL(c.Host, gvrCreate)

	log.DebugWithCtx(ctx, "[CreateResource] req=%+v", in)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: in})
	if err != nil {
		log.ErrorWithCtx(ctx, "[CreateResource] init cloud ceq, error: %v", err)
		return nil, err
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[CreateResource] send req create resource, error: %v, cluster=%v", err, in.Cluster)
		return nil, err
	}

	var out core.CreateResourceResponse
	if err = httpResp.JsonToStruct(&out); err != nil {
		log.ErrorWithCtx(ctx, "[CreateResource] parse response, error: %v", err)
		return nil, err
	}
	if out.Code != 0 {
		log.ErrorWithCtx(ctx, "[CreateResource] create resource failed, return code is not zero, req=%+v, resp=%+v", in, out)
		return nil, errors.New(out.Message)
	}

	log.DebugWithCtx(ctx, "[CreateResource] resp=%+v", out)
	return &out, nil
}

func (c *gvrHTTPClient) GetResource(ctx context.Context, in *core.GetResourceRequest) (*core.GetResourceResponse, error) {
	url := buildURL(c.Host, gvrGet)

	log.DebugWithCtx(ctx, "[GetResource] req=%+v", in)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: in})
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetResource] init cloud ceq, error: %v, req=%+v", err, in)
		return nil, err
	}
	httpResp, err := c.session.Get(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetResource] send req get resource, error: %v, req=%+v", err, in)
		return nil, err
	}

	var out core.GetResourceResponse
	if err = httpResp.JsonToStruct(&out); err != nil {
		log.ErrorWithCtx(ctx, "[GetResource] parse response, error: %v", err)
		return nil, err
	}
	// code=65 代表资源不存在
	if out.Code != 65 && out.Code != 0 {
		log.ErrorWithCtx(ctx, "[GetResource] get resource failed, return code is not zero, req=%+v, resp=%+v", in, out)
		return nil, errors.New(out.Message)
	}

	log.DebugWithCtx(ctx, "[GetResource] resp=%+v", out)
	return &out, nil
}

func (c *gvrHTTPClient) ListResource(ctx context.Context, in *core.ListResourceRequest) (*core.ListResourceResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (c *gvrHTTPClient) UpdateResource(ctx context.Context, in *core.UpdateResourceRequest) (*core.UpdateResourceResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (c *gvrHTTPClient) DeleteResource(ctx context.Context, in *core.DeleteResourceRequest) (*core.DeleteResourceResponse, error) {
	url := buildURL(c.Host, gvrDelete)

	log.DebugWithCtx(ctx, "[DeleteResource] req=%+v", in)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: in})
	if err != nil {
		log.ErrorWithCtx(ctx, "[DeleteResource] init cloud ceq, error: %v, req=%+v", err, in)
		return nil, err
	}
	httpResp, err := c.session.Delete(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[DeleteResource] send req delete resource, error: %v, req=%+v", err, in)
		return nil, err
	}

	var out core.DeleteResourceResponse
	if err = httpResp.JsonToStruct(&out); err != nil {
		log.ErrorWithCtx(ctx, "[DeleteResource] parse response, error: %v", err)
		return nil, err
	}
	if out.Code != 0 {
		log.ErrorWithCtx(ctx, "[DeleteResource] delete resource failed, return code is not zero, req=%+v, resp=%+v", in, out)
		return nil, errors.New(out.Message)
	}

	log.DebugWithCtx(ctx, "[DeleteResource] resp=%+v", out)
	return &out, nil
}

func (c *gvrHTTPClient) SetV2Host(hostV2 string) {
	c.HostV2 = hostV2
}
func (c *gvrHTTPClient) FindAnyList(ctx context.Context, in core.AnyListReq, out any) (err error) {
	url := buildURL(c.HostV2, gvrList)

	log.DebugWithCtx(ctx, "[GetResource] req=%+v", in)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: in})
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetResource] init cloud ceq, error: %v, req=%+v", err, in)
		return
	}
	httpResp, err := c.session.Post(ctx, httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[GetResource] send req get resource, error: %v, req=%+v", err, in)
		return
	}

	rst := core.AnyListResp{
		Data: core.CloudData{
			Data: out,
		},
	}
	if err = httpResp.JsonToStruct(&rst); err != nil {
		log.ErrorWithCtx(ctx, "[GetResource] parse response, error: %v", err)
		return
	}
	return
}
