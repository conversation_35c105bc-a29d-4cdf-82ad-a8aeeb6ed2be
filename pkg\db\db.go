package db

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"gorm.io/datatypes"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/page"
)

var DB *gorm.DB

func getDSN(c *Config) string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=%v&loc=Local", c.Userna<PERSON>, c.Password, c.Host, c.Port, c.Dbname, c.Parsetime)
}

func New(cfg *Config, opts ...Option) (*gorm.DB, error) {
	dsn := getDSN(cfg)
	gormCfg := &gorm.Config{
		SkipDefaultTransaction: true,
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
		// todo remove hardcode level
		Logger: logger.Default.LogMode(logger.Info),
	}

	cfg.gormConfig = gormCfg
	for _, opt := range opts {
		opt(cfg)
	}

	db, err := gorm.Open(mysql.Open(dsn), gormCfg)
	if err != nil {
		return nil, err
	}

	duration, err := time.ParseDuration(cfg.Maxlifetime)
	if err != nil {
		return nil, err
	}
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}
	if err = sqlDB.Ping(); err != nil {
		return nil, err
	}

	sqlDB.SetMaxIdleConns(cfg.Idleconnection)
	sqlDB.SetMaxOpenConns(cfg.Openconnection)
	sqlDB.SetConnMaxLifetime(duration)
	return db, nil
}

// InitDB Init database
func InitDB(config *Config, opts ...Option) error {
	if config != nil {
		log.Info("init database...")

		db, err := New(config, opts...)
		if err != nil {
			log.Errorf("new db error: %v", err)
			return err
		}

		DB = db
	}
	return nil
}

func Transaction(fn func(tx *gorm.DB) error, opts ...*sql.TxOptions) error {
	return DB.Transaction(fn, opts...)
}

func Paginate(query page.PQuery) func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		pageNum := query.PageNum()
		if pageNum == 0 {
			pageNum = page.DefaultPage
			query.SetPageNum(pageNum)
		}
		pageSize := query.PageSize()
		if pageSize > 500 || pageSize <= 0 {
			if pageSize != page.DefaultUnlimitedSize {
				pageSize = page.DefaultPageSize
			}

			query.SetPageSize(pageSize)
		}
		cond := query.PageCond()
		lastID := query.LastID()
		offset := (pageNum - 1) * pageSize
		tx = tx.Offset(offset).Limit(pageSize)
		if cond != "" {
			tx = tx.Order(cond)
		}
		if lastID != 0 {
			tx = tx.Where("id > ?", lastID)
		}
		return tx
	}
}

func Page(query page.PQuery) *gorm.DB {
	return DB.Scopes(Paginate(query))
}

// Create creates row based on model
func Create(value interface{}) error {
	return DB.Create(value).Error
}

func Clauses(conds ...clause.Expression) *gorm.DB {
	return DB.Clauses(conds...)
}

func Sql(sql string, values ...interface{}) *gorm.DB {
	return DB.Raw(sql, values...)
}

func Model(value interface{}) *gorm.DB {
	return DB.Model(value)
}

func Preload(query string, args ...interface{}) *gorm.DB {
	return DB.Preload(query, args...)
}

func Where(query interface{}, args ...interface{}) *gorm.DB {
	return DB.Where(query, args...)
}

func JSONQuery(fieldName string, keys string) *datatypes.JSONQueryExpression {
	return datatypes.JSONQuery(fieldName).HasKey(keys)
}

type txKey struct{}

func CtxWithTX(ctx context.Context, tx *gorm.DB) context.Context {
	return context.WithValue(ctx, txKey{}, tx)
}

func CtxToTX(ctx context.Context) *gorm.DB {
	txVal := ctx.Value(txKey{})
	tx, ok := txVal.(*gorm.DB)
	if ok {
		return tx
	}

	return nil
}

func CtxTX(ctx context.Context, currentDb *gorm.DB) *gorm.DB {
	txVal := ctx.Value(txKey{})
	tx, ok := txVal.(*gorm.DB)
	if ok {
		return tx.WithContext(ctx)
	}

	return currentDb.WithContext(ctx)
}

func UpdateInBatches(ctx context.Context, values any, uniqueKeys []string, updateFields []string) *gorm.DB {
	tx := CtxTX(ctx, DB)

	columns := make([]clause.Column, len(uniqueKeys))
	for i, key := range uniqueKeys {
		columns[i] = clause.Column{Name: key}
	}

	return tx.Clauses(clause.OnConflict{
		Columns:   columns,
		DoUpdates: clause.AssignmentColumns(updateFields),
	}).Create(values)
}

func ExcludeDeleted(m *gorm.DB) *gorm.DB {
	return m.Where("is_deleted = ?", 0)
}
