package eventcenter

type sendEventData struct {
	Source       string         `json:"source"`
	Name         string         `json:"name"`
	StartTime    string         `json:"start_time"`
	EndTime      string         `json:"end_time"`
	EventData    eventMessage   `json:"event_data"`
	ResourceData []resourceData `json:"resource_data"`
}

type eventMessage struct {
	Description string `json:"description"`
	Name        string `json:"name"`
}

type namespace struct {
	NamespaceName       []string `json:"namespace_name"`
	KubernetesPrivateID []string `json:"kubernetes_private_id"`
	ApplicationID       []string `json:"application_id"`
}

type resourceData struct {
	Namespace namespace `json:"namespace"`
}
