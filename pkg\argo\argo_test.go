package argo

import (
	"context"
	"testing"

	"github.com/golang-jwt/jwt/v4"
)

var password = ``

func getOption() *Options {
	return &Options{
		ServerAddr: "https://localhost:8080",
		SessionRequest: &SessionRequest{
			Password: password,
			Token:    "",
			Username: "admin",
		},
	}
}

func TestGetApplication(t *testing.T) {
	c, err := newClient(getOption())
	if err != nil {
		t.Fatal(err)
	}
	token, err := c.getSessionToken(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	parser := jwt.NewParser(jwt.WithoutClaimsValidation())
	var claims jwt.RegisteredClaims
	_, _, err = parser.ParseUnverified(token, &claims)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(claims)
}

func TestGetVersion(t *testing.T) {
	c, err := newClient(getOption())
	if err != nil {
		t.<PERSON>al(err)
	}
	_ = c.initOauth2Hc()
	t.Log(c.getVersion(context.Background()))
}

func TestListApplication(t *testing.T) {
	c, err := newClient(getOption())
	if err != nil {
		t.Fatal(err)
	}
	_ = c.initOauth2Hc()
	res, err := c.listApplication(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	t.Log(res)
}

func TestDeleteApp(t *testing.T) {
	c, err := newClient(getOption())
	if err != nil {
		t.Fatal(err)
	}
	_ = c.initOauth2Hc()
	_, err = c.DeleteApplication(context.Background(), &ApplicationDeleteRequest{
		Name:              "guestbook",
		Cascade:           true,
		PropagationPolicy: "",
		AppNamespace:      "argocd",
		Project:           "default",
	})
	if err != nil {
		t.Fatal(err)
	}
}
