package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/log"
	"github.com/IBM/sarama"
	"github.com/google/uuid"
)

type SyncProducer struct {
	producer sarama.SyncProducer
	topic    string
}

func NewKfkSender(brokers, topic string) *SyncProducer {
	config := sarama.NewConfig()
	config.Version = sarama.V2_8_1_0
	// 确保写入所有的 kafka 要求的副本数
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Return.Successes = true
	config.Producer.Return.Errors = true
	config.ChannelBufferSize = 1 << 11

	producer, err := sarama.NewSyncProducer(strings.Split(brokers, ","), config)
	if err != nil {
		panic(fmt.Sprintf("kafka topic %s Producer init Err %v", topic, err))
	}

	return &SyncProducer{
		producer: producer,
		topic:    topic,
	}
}

func (s *SyncProducer) Close(ctx context.Context) error {
	return s.producer.Close()
}
func (s *SyncProducer) Send(ctx context.Context, value Event) (err error) {
	if ctx != nil {
		ri, _ := cctx.RequestInfoFromCtx(ctx)
		value.ReqInfo = *ri
	}
	if value.MsgId == "" {
		value.MsgId = uuid.New().String()
	}

	body, err := json.Marshal(value)
	if err != nil {
		return
	}
	msg := &sarama.ProducerMessage{
		Topic: s.topic,
		Key:   sarama.StringEncoder(value.Types),
		Value: sarama.ByteEncoder(body),
	}

	partition, offset, err := s.producer.SendMessage(msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "kafka Sync Send  message topic:%s type:%s msgUid:%s, error: %v",
			s.topic, value.Types, value.MsgId, err)
		return
	}
	log.InfoWithCtx(ctx, "kafka Sync Send message success. topic:%s type:%s msgUid:%s, partition: offset %d:%d",
		s.topic, value.Types, value.MsgId, partition, offset)
	return
}
