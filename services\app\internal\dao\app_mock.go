// Code generated by MockGen. DO NOT EDIT.
// Source: app.go

// Package dao is a generated GoMock package.
package dao

import (
	context "context"
	reflect "reflect"

	constants "52tt.com/cicd/pkg/constants"
	model "52tt.com/cicd/services/app/internal/model"
	gomock "github.com/golang/mock/gomock"
)

// MockAppRepository is a mock of AppRepository interface.
type MockAppRepository struct {
	ctrl     *gomock.Controller
	recorder *MockAppRepositoryMockRecorder
}

// MockAppRepositoryMockRecorder is the mock recorder for MockAppRepository.
type MockAppRepositoryMockRecorder struct {
	mock *MockAppRepository
}

// NewMockAppRepository creates a new mock instance.
func NewMockAppRepository(ctrl *gomock.Controller) *MockAppRepository {
	mock := &MockAppRepository{ctrl: ctrl}
	mock.recorder = &MockAppRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAppRepository) EXPECT() *MockAppRepositoryMockRecorder {
	return m.recorder
}

// BatchUpdateAppCols mocks base method.
func (m *MockAppRepository) BatchUpdateAppCols(ctx context.Context, apps []App, cols ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, apps}
	for _, a := range cols {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchUpdateAppCols", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateAppCols indicates an expected call of BatchUpdateAppCols.
func (mr *MockAppRepositoryMockRecorder) BatchUpdateAppCols(ctx, apps interface{}, cols ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, apps}, cols...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateAppCols", reflect.TypeOf((*MockAppRepository)(nil).BatchUpdateAppCols), varargs...)
}

// BatchUpdateAppEventlink mocks base method.
func (m *MockAppRepository) BatchUpdateAppEventlink(ctx context.Context, appId int64, updateFields map[string]interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateAppEventlink", ctx, appId, updateFields)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateAppEventlink indicates an expected call of BatchUpdateAppEventlink.
func (mr *MockAppRepositoryMockRecorder) BatchUpdateAppEventlink(ctx, appId, updateFields interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateAppEventlink", reflect.TypeOf((*MockAppRepository)(nil).BatchUpdateAppEventlink), ctx, appId, updateFields)
}

// CreateAppEventlink mocks base method.
func (m *MockAppRepository) CreateAppEventlink(ctx context.Context, eventlink *AppEventlink) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppEventlink", ctx, eventlink)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppEventlink indicates an expected call of CreateAppEventlink.
func (mr *MockAppRepositoryMockRecorder) CreateAppEventlink(ctx, eventlink interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppEventlink", reflect.TypeOf((*MockAppRepository)(nil).CreateAppEventlink), ctx, eventlink)
}

// CreateAppPreference mocks base method.
func (m *MockAppRepository) CreateAppPreference(ctx context.Context, preference model.AppPreference) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAppPreference", ctx, preference)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAppPreference indicates an expected call of CreateAppPreference.
func (mr *MockAppRepositoryMockRecorder) CreateAppPreference(ctx, preference interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAppPreference", reflect.TypeOf((*MockAppRepository)(nil).CreateAppPreference), ctx, preference)
}

// Delete mocks base method.
func (m *MockAppRepository) Delete(arg0 *App) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockAppRepositoryMockRecorder) Delete(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockAppRepository)(nil).Delete), arg0)
}

// DeleteAllAppOwners mocks base method.
func (m *MockAppRepository) DeleteAllAppOwners(ctx context.Context, appIds []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAllAppOwners", ctx, appIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAllAppOwners indicates an expected call of DeleteAllAppOwners.
func (mr *MockAppRepositoryMockRecorder) DeleteAllAppOwners(ctx, appIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllAppOwners", reflect.TypeOf((*MockAppRepository)(nil).DeleteAllAppOwners), ctx, appIds)
}

// DeleteAppPreference mocks base method.
func (m *MockAppRepository) DeleteAppPreference(ctx context.Context, preference model.AppPreference) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAppPreference", ctx, preference)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAppPreference indicates an expected call of DeleteAppPreference.
func (mr *MockAppRepositoryMockRecorder) DeleteAppPreference(ctx, preference interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAppPreference", reflect.TypeOf((*MockAppRepository)(nil).DeleteAppPreference), ctx, preference)
}

// FindAll mocks base method.
func (m *MockAppRepository) FindAll(ctx context.Context, query *model.AppQuery, total *int64) ([]App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAll", ctx, query, total)
	ret0, _ := ret[0].([]App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAll indicates an expected call of FindAll.
func (mr *MockAppRepositoryMockRecorder) FindAll(ctx, query, total interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAll", reflect.TypeOf((*MockAppRepository)(nil).FindAll), ctx, query, total)
}

// FindAllAppBasicInfo mocks base method.
func (m *MockAppRepository) FindAllAppBasicInfo(ctx context.Context, query *model.AppQuery) ([]App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAllAppBasicInfo", ctx, query)
	ret0, _ := ret[0].([]App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAllAppBasicInfo indicates an expected call of FindAllAppBasicInfo.
func (mr *MockAppRepositoryMockRecorder) FindAllAppBasicInfo(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAllAppBasicInfo", reflect.TypeOf((*MockAppRepository)(nil).FindAllAppBasicInfo), ctx, query)
}

// FindApp mocks base method.
func (m *MockAppRepository) FindApp(ctx context.Context, id int64) (*App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindApp", ctx, id)
	ret0, _ := ret[0].(*App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindApp indicates an expected call of FindApp.
func (mr *MockAppRepositoryMockRecorder) FindApp(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindApp", reflect.TypeOf((*MockAppRepository)(nil).FindApp), ctx, id)
}

// FindAppBasicInfoById mocks base method.
func (m *MockAppRepository) FindAppBasicInfoById(ctx context.Context, id int64) (*App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAppBasicInfoById", ctx, id)
	ret0, _ := ret[0].(*App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAppBasicInfoById indicates an expected call of FindAppBasicInfoById.
func (mr *MockAppRepositoryMockRecorder) FindAppBasicInfoById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAppBasicInfoById", reflect.TypeOf((*MockAppRepository)(nil).FindAppBasicInfoById), ctx, id)
}

// FindAppBy mocks base method.
func (m *MockAppRepository) FindAppBy(ctx context.Context, projectId int64, name string) (*App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAppBy", ctx, projectId, name)
	ret0, _ := ret[0].(*App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAppBy indicates an expected call of FindAppBy.
func (mr *MockAppRepositoryMockRecorder) FindAppBy(ctx, projectId, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAppBy", reflect.TypeOf((*MockAppRepository)(nil).FindAppBy), ctx, projectId, name)
}

// FindAppsByIds mocks base method.
func (m *MockAppRepository) FindAppsByIds(ctx context.Context, ids []int64, projectId int64) ([]App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAppsByIds", ctx, ids, projectId)
	ret0, _ := ret[0].([]App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAppsByIds indicates an expected call of FindAppsByIds.
func (mr *MockAppRepositoryMockRecorder) FindAppsByIds(ctx, ids, projectId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAppsByIds", reflect.TypeOf((*MockAppRepository)(nil).FindAppsByIds), ctx, ids, projectId)
}

// FindAppsByName mocks base method.
func (m *MockAppRepository) FindAppsByName(ctx context.Context, parms model.FindAppsReq) ([]App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAppsByName", ctx, parms)
	ret0, _ := ret[0].([]App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAppsByName indicates an expected call of FindAppsByName.
func (mr *MockAppRepositoryMockRecorder) FindAppsByName(ctx, parms interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAppsByName", reflect.TypeOf((*MockAppRepository)(nil).FindAppsByName), ctx, parms)
}

// FindAppsByUserAndProject mocks base method.
func (m *MockAppRepository) FindAppsByUserAndProject(ctx context.Context, userId, projectId int64) ([]App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAppsByUserAndProject", ctx, userId, projectId)
	ret0, _ := ret[0].([]App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAppsByUserAndProject indicates an expected call of FindAppsByUserAndProject.
func (mr *MockAppRepositoryMockRecorder) FindAppsByUserAndProject(ctx, userId, projectId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAppsByUserAndProject", reflect.TypeOf((*MockAppRepository)(nil).FindAppsByUserAndProject), ctx, userId, projectId)
}

// FindProjectAppsByLanguage mocks base method.
func (m *MockAppRepository) FindProjectAppsByLanguage(ctx context.Context, query *model.ProjectAppsParam) ([]App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindProjectAppsByLanguage", ctx, query)
	ret0, _ := ret[0].([]App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindProjectAppsByLanguage indicates an expected call of FindProjectAppsByLanguage.
func (mr *MockAppRepositoryMockRecorder) FindProjectAppsByLanguage(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindProjectAppsByLanguage", reflect.TypeOf((*MockAppRepository)(nil).FindProjectAppsByLanguage), ctx, query)
}

// FindRepoByUrl mocks base method.
func (m *MockAppRepository) FindRepoByUrl(ctx context.Context, host string) (*Repo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindRepoByUrl", ctx, host)
	ret0, _ := ret[0].(*Repo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindRepoByUrl indicates an expected call of FindRepoByUrl.
func (mr *MockAppRepositoryMockRecorder) FindRepoByUrl(ctx, host interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindRepoByUrl", reflect.TypeOf((*MockAppRepository)(nil).FindRepoByUrl), ctx, host)
}

// FindUserAppPreferences mocks base method.
func (m *MockAppRepository) FindUserAppPreferences(ctx context.Context, projectId, userId int64) ([]AppPreference, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindUserAppPreferences", ctx, projectId, userId)
	ret0, _ := ret[0].([]AppPreference)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindUserAppPreferences indicates an expected call of FindUserAppPreferences.
func (mr *MockAppRepositoryMockRecorder) FindUserAppPreferences(ctx, projectId, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindUserAppPreferences", reflect.TypeOf((*MockAppRepository)(nil).FindUserAppPreferences), ctx, projectId, userId)
}

// GetAppEventlink mocks base method.
func (m *MockAppRepository) GetAppEventlink(ctx context.Context, appId int64, env constants.EnvType) (*AppEventlink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppEventlink", ctx, appId, env)
	ret0, _ := ret[0].(*AppEventlink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppEventlink indicates an expected call of GetAppEventlink.
func (mr *MockAppRepositoryMockRecorder) GetAppEventlink(ctx, appId, env interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppEventlink", reflect.TypeOf((*MockAppRepository)(nil).GetAppEventlink), ctx, appId, env)
}

// GetProjectsApps mocks base method.
func (m *MockAppRepository) GetProjectsApps(ctx context.Context, query model.ProjectsAppsReq) ([]App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProjectsApps", ctx, query)
	ret0, _ := ret[0].([]App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProjectsApps indicates an expected call of GetProjectsApps.
func (mr *MockAppRepositoryMockRecorder) GetProjectsApps(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectsApps", reflect.TypeOf((*MockAppRepository)(nil).GetProjectsApps), ctx, query)
}

// GetUserApps mocks base method.
func (m *MockAppRepository) GetUserApps(ctx context.Context, query model.UserAppsReq) ([]App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserApps", ctx, query)
	ret0, _ := ret[0].([]App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserApps indicates an expected call of GetUserApps.
func (mr *MockAppRepositoryMockRecorder) GetUserApps(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserApps", reflect.TypeOf((*MockAppRepository)(nil).GetUserApps), ctx, query)
}

// GetUserPreferenceApps mocks base method.
func (m *MockAppRepository) GetUserPreferenceApps(ctx context.Context, query model.UserAppsReq) ([]App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPreferenceApps", ctx, query)
	ret0, _ := ret[0].([]App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPreferenceApps indicates an expected call of GetUserPreferenceApps.
func (mr *MockAppRepositoryMockRecorder) GetUserPreferenceApps(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPreferenceApps", reflect.TypeOf((*MockAppRepository)(nil).GetUserPreferenceApps), ctx, query)
}

// Insert mocks base method.
func (m *MockAppRepository) Insert(arg0 context.Context, arg1 *App) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockAppRepositoryMockRecorder) Insert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAppRepository)(nil).Insert), arg0, arg1)
}

// InsertAppOwner mocks base method.
func (m *MockAppRepository) InsertAppOwner(arg0 context.Context, arg1 []AppUser) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertAppOwner", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertAppOwner indicates an expected call of InsertAppOwner.
func (mr *MockAppRepositoryMockRecorder) InsertAppOwner(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertAppOwner", reflect.TypeOf((*MockAppRepository)(nil).InsertAppOwner), arg0, arg1)
}

// List mocks base method.
func (m *MockAppRepository) List(ctx context.Context, query *model.AppQuery, data interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, query, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// List indicates an expected call of List.
func (mr *MockAppRepositoryMockRecorder) List(ctx, query, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockAppRepository)(nil).List), ctx, query, data)
}

// ListAppEventlink mocks base method.
func (m *MockAppRepository) ListAppEventlink(ctx context.Context, appId int64) ([]AppEventlink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAppEventlink", ctx, appId)
	ret0, _ := ret[0].([]AppEventlink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAppEventlink indicates an expected call of ListAppEventlink.
func (mr *MockAppRepositoryMockRecorder) ListAppEventlink(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAppEventlink", reflect.TypeOf((*MockAppRepository)(nil).ListAppEventlink), ctx, appId)
}

// SearchApps mocks base method.
func (m *MockAppRepository) SearchApps(ctx context.Context, name string) ([]App, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchApps", ctx, name)
	ret0, _ := ret[0].([]App)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchApps indicates an expected call of SearchApps.
func (mr *MockAppRepositoryMockRecorder) SearchApps(ctx, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchApps", reflect.TypeOf((*MockAppRepository)(nil).SearchApps), ctx, name)
}

// UpdateApp mocks base method.
func (m *MockAppRepository) UpdateApp(ctx context.Context, app *App, cols ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, app}
	for _, a := range cols {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateApp", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateApp indicates an expected call of UpdateApp.
func (mr *MockAppRepositoryMockRecorder) UpdateApp(ctx, app interface{}, cols ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, app}, cols...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApp", reflect.TypeOf((*MockAppRepository)(nil).UpdateApp), varargs...)
}

// UpdateAppEventlink mocks base method.
func (m *MockAppRepository) UpdateAppEventlink(ctx context.Context, eventlink *AppEventlink) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAppEventlink", ctx, eventlink)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAppEventlink indicates an expected call of UpdateAppEventlink.
func (mr *MockAppRepositoryMockRecorder) UpdateAppEventlink(ctx, eventlink interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppEventlink", reflect.TypeOf((*MockAppRepository)(nil).UpdateAppEventlink), ctx, eventlink)
}
