-- 产品的公告管理表
CREATE TABLE `notify`.`notice` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
   `title` varchar(512) NOT NULL default "" COMMENT '标题',
   `content` text NOT NULL COMMENT '消息内容',
   `notice_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '发布时间',
   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='产品的公告管理表';

-- 工单添加能否发起下一步审批的标识
alter table `deploy`.`ticket`
    add column `next_approval_flag` tinyint not null default 0 comment '能否发起下一步审批',
    add column `recreate_flag` tinyint not null default 0 comment '能否重新发起工单';
