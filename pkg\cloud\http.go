package cloud

import (
	"52tt.com/cicd/pkg/cloud/http"
	"52tt.com/cicd/pkg/cloud/http/core"
	"52tt.com/cicd/pkg/httpclient"
)

func NewHTTPClient(host, token string) HTTPClient {
	session := httpclient.NewSession(&httpclient.SessionOption{})
	headers := map[string]string{
		"X-TOKEN": token,
	}
	session.SetHeaders(headers)

	a := &hClient{
		session: session,
	}
	a.GVRHTTPClient = http.NewGVRHTTPClientWithSession(host, token, session)
	a.SubNamespaceClient = http.NewSubNamespaceHTTPClientWithSession(host, token, session)
	a.WorkloadClient = http.NewWorkloadHTTPClientWithSession(host, token, session)
	a.ClusterNamespaceClient = http.NewClusterNamespaceHTTPClientWithSession(host, token, session)
	return a
}

type hClient struct {
	core.GVRHTTPClient
	core.SubNamespaceClient
	core.WorkloadClient
	core.ClusterNamespaceClient
	session httpclient.Session
}
