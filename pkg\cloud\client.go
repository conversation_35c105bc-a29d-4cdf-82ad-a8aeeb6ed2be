//go:generate mockgen -destination=client_mock.go -package=cloud -source=client.go
package cloud

import (
	"52tt.com/cicd/pkg/cloud/http/core"
	continuousdeployment "golang.ttyuyin.com/genproto/quwanapis/cloud/continuousdeployment/v1alpha"

	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	mesh "golang.ttyuyin.com/genproto/quwanapis/cloud/mesh/v1alpha"
)

type GRPCClient interface {
	mesh.ServiceRouteServiceClient
	mesh.RouteManagementServiceClient
	constack.WorkloadServiceClient
	constack.ScaledObjectServiceClient
	constack.KubeConfigServiceClient
	constack.AnyResourceServiceClient
	constack.ResourceSyncServiceClient
}

type DeployGRPCClient interface {
	continuousdeployment.ContinuousDeploymentServiceClient
}

type HTTPClient interface {
	core.GVRHTTPClient
	core.SubNamespaceClient
	core.WorkloadClient
	core.ClusterNamespaceClient
}
