// Code generated by MockGen. DO NOT EDIT.
// Source: event_link.go

// Package eventlink is a generated GoMock package.
package eventlink

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockEventLink is a mock of EventLink interface.
type MockEventLink struct {
	ctrl     *gomock.Controller
	recorder *MockEventLinkMockRecorder
}

// MockEventLinkMockRecorder is the mock recorder for MockEventLink.
type MockEventLinkMockRecorder struct {
	mock *MockEventLink
}

// NewMockEventLink creates a new mock instance.
func NewMockEventLink(ctrl *gomock.Controller) *MockEventLink {
	mock := &MockEventLink{ctrl: ctrl}
	mock.recorder = &MockEventLinkMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventLink) EXPECT() *MockEventLinkMockRecorder {
	return m.recorder
}

// CreatCB mocks base method.
func (m *MockEventLink) CreatCB(ctx context.Context, cluster, ns string, req ConsumerBinding) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatCB", ctx, cluster, ns, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatCB indicates an expected call of CreatCB.
func (mr *MockEventLinkMockRecorder) CreatCB(ctx, cluster, ns, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatCB", reflect.TypeOf((*MockEventLink)(nil).CreatCB), ctx, cluster, ns, req)
}

// DelCB mocks base method.
func (m *MockEventLink) DelCB(ctx context.Context, cluster, ns, name string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelCB", ctx, cluster, ns, name)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelCB indicates an expected call of DelCB.
func (mr *MockEventLinkMockRecorder) DelCB(ctx, cluster, ns, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCB", reflect.TypeOf((*MockEventLink)(nil).DelCB), ctx, cluster, ns, name)
}

// DelSB mocks base method.
func (m *MockEventLink) DelSB(ctx context.Context, cluster, ns, name string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSB", ctx, cluster, ns, name)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelSB indicates an expected call of DelSB.
func (mr *MockEventLinkMockRecorder) DelSB(ctx, cluster, ns, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSB", reflect.TypeOf((*MockEventLink)(nil).DelSB), ctx, cluster, ns, name)
}

// GetCB mocks base method.
func (m *MockEventLink) GetCB(ctx context.Context, cluster, ns, name string) (string, ConsumerBinding, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCB", ctx, cluster, ns, name)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(ConsumerBinding)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetCB indicates an expected call of GetCB.
func (mr *MockEventLinkMockRecorder) GetCB(ctx, cluster, ns, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCB", reflect.TypeOf((*MockEventLink)(nil).GetCB), ctx, cluster, ns, name)
}

// UpdSB mocks base method.
func (m *MockEventLink) UpdSB(ctx context.Context, cluster, ns, name string, cols map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdSB", ctx, cluster, ns, name, cols)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdSB indicates an expected call of UpdSB.
func (mr *MockEventLinkMockRecorder) UpdSB(ctx, cluster, ns, name, cols interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdSB", reflect.TypeOf((*MockEventLink)(nil).UpdSB), ctx, cluster, ns, name, cols)
}
