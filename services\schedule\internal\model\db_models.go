package model

import (
	"time"

	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/db"
	"gorm.io/datatypes"
)

type DurationModel struct {
	StartedTime   time.Time `json:"startedTime" gorm:"column:started_time"`     // 开始时间
	CompletedTime time.Time `json:"completedTime" gorm:"column:completed_time"` // 完成时间
}

type ListProjectsReq struct {
	ProjectIDs []int64
	UserGroup  string `json:"userGroup"`
}

// Project 项目表
type Project struct {
	Id                       int64  `json:"id" uri:"id"`
	Name                     string `json:"name" validate:"required"`
	Type                     string `json:"type" validate:"required"`
	Description              string `json:"description"`
	UserGroup                string `json:"userGroup"`
	CanaryDeployNotifyStatus string `json:"canaryDeployNotifyStatus"`
	// ProjectExtraObj          *ProjectExtraObj   `json:"projectExtraObj"`
	// TapdSetting              ProjectTapdSetting `json:"tapdSetting"`
}

type ProjectExtraObj struct {
	CmdbTeamId   string `json:"cmdbTeamId"`
	CmdbTeamName string `json:"cmdbTeamName"`
}

type ProjectTapdSetting struct {
	IsAssociated        bool                         `json:"isAssociated"`                                            // 是否启用关联
	IsRequiredVerify    bool                         `json:"isRequiredVerify"`                                        // 是否启动tapd需求必填校验
	IsDevStatusFlow     bool                         `json:"isDevStatusFlow"`                                         // 是否启动开发状态流转
	IsTestStatusFlow    bool                         `json:"isTestStatusFlow"`                                        // 是否启动测试状态流转
	IsStagingStatusFlow bool                         `json:"isStagingStatusFlow"`                                     // 是否启动 预发布部署后的状态流转
	NotifyInterval      *int                         `json:"notifyInterval" validate:"required_if=IsAssociated true"` // 通知间隔
	NotifyUnit          *string                      `json:"notifyUnit" validate:"required_if=IsAssociated true"`     // 通知单位, M:分钟, H:小时
	NotifyLimit         *int                         `json:"notifyLimit" validate:"required_if=IsAssociated true"`    // 通知上限
	AssociatedNode      constants.TapdAssociatedNode `json:"associatedNode"`                                          // 关联节点
}

func (m *Project) TableName() string {
	return "app.project"
}

// App 应用信息表
type App struct {
	ID                int64          `gorm:"column:id;primary_key;AUTO_INCREMENT"` // 主键Id
	Name              string         `gorm:"column:name;NOT NULL"`                 // 应用名称
	Code              string         `gorm:"column:code;NOT NULL"`                 // 应用代号(唯一标识)
	BuildPath         string         `gorm:"column:build_path"`                    // 构建路径
	RepoAddr          string         `gorm:"column:repo_addr"`                     // 代码仓库地址
	Status            int            `gorm:"column:status;NOT NULL"`               // 状态
	CmdbID            string         `gorm:"column:cmdb_id;NOT NULL"`              // 对应的cmdb的 唯一Id
	ProjectID         int64          `gorm:"column:project_id;NOT NULL"`           // 所属项目id
	CreatedAt         time.Time      `gorm:"column:created_at;NOT NULL"`           // 创建时间
	UpdatedAt         time.Time      `gorm:"column:updated_at;NOT NULL"`           // 更新时间
	Description       string         `gorm:"column:description"`                   // 描述
	LangName          string         `gorm:"column:lang_name"`                     // 语言名称
	LangVersion       string         `gorm:"column:lang_version;NOT NULL"`         // 语言版本
	MatchLabels       datatypes.JSON `gorm:"column:match_labels;type:json"`        // 服务发布时k8s Deployment的 selector MatchLabels
	ServiceLabels     datatypes.JSON `gorm:"column:service_labels;type:json"`      // 服务发布时k8s Service的 selector
	StandLabelEnvs    string         `gorm:"column:stand_label_envs"`              // 使用标准化Labels部署的环境配置。英文,分隔(dev,testing,preview,production)
	SenvStatus        string         `gorm:"column:senv_status"`                   // 是否支持子环境2.0，enabled：支持，disabled：不支持
	NeedExternalRoute bool           `gorm:"column:need_external_route"`           // 是否需要对外路由
	EventlinkType     string         `gorm:"column:eventlink_type"`                // eventlink类型，多个使用,分隔
	Project           Project        `gorm:"foreignKey:ProjectID"`
	AppUsers          []AppUser      `gorm:"foreignKey:AppId"`
}

func (m *App) TableName() string {
	return "app.app"
}

// AppUser 应用用户表
type AppUser struct {
	db.BaseModel
	AppId  int64 `gorm:"column:app_id;NOT NULL"`
	UserId int64 `gorm:"column:user_id;NOT NULL"`
	User   User  `gorm:"foreignKey:UserId"`
}

func (app *AppUser) TableName() string {
	return "app.app_user"
}

type User struct {
	db.BaseModel
	Username    string `gorm:"column:username;NOT NULL"`         // 用户名
	ChineseName string `gorm:"column:chinese_name;NOT NULL"`     // 中文名
	Email       string `gorm:"column:email;NOT NULL"`            // 邮箱
	EmployeeNo  string `gorm:"column:employee_no;NOT NULL"`      // 工号
	LarkUnionID string `gorm:"column:lark_union_id;NOT NULL"`    // 飞书id
	GitlabID    int    `gorm:"column:gitlab_id;default:0"`       // gitlab用户id
	Status      int    `gorm:"column:status;default:1;NOT NULL"` // 状态(0不可用,1正常)
}

func (user *User) TableName() string {
	return "iam.user"
}

// AppEventlink 服务Eventlink表
type AppEventlink struct {
	db.BaseModel
	AppID        int64             `gorm:"column:app_id;NOT NULL"`
	AppName      string            `gorm:"column:app_name;NOT NULL"`
	ProjectID    int64             `gorm:"column:project_id;NOT NULL"`
	Env          constants.EnvType `gorm:"column:env;NOT NULL"`
	ConsumerType string            `gorm:"column:consumer_type;NOT NULL"`
	ProducerType string            `gorm:"column:producer_type;NOT NULL"`
}

func (m *AppEventlink) TableName() string {
	return "app.app_event_link"
}

type ApprovalFlowNode struct {
	db.BaseModel   `mapstructure:",squash"`
	Name           string         `gorm:"column:name" json:"name"`
	IsAssigned     bool           `gorm:"column:is_assigned" json:"isAssigned"`
	ApprovalFlowId int64          `gorm:"column:approval_flow_id" json:"approvalFlowId"`
	Approvers      datatypes.JSON `json:"approvers" mapstructure:"-"`
}

func (m *ApprovalFlowNode) TableName() string {
	return "deploy.approval_flow_node"
}

// ApprovalFlowApprover 审批流节点审核人
type ApprovalFlowApprover struct {
	db.BaseModel `mapstructure:",squash"`
	NodeInstID   int64 `gorm:"column:node_inst_id;NOT NULL"` // 节点实例id
	ApproverID   int64 `gorm:"column:approver_id;NOT NULL"`  // 节点审核人id
}

func (m *ApprovalFlowApprover) TableName() string {
	return "deploy.approval_flow_approver"
}

// ApprovalFlowNodeInstance 审批流节点实例信息表
type ApprovalFlowNodeInstance struct {
	db.BaseModel `mapstructure:",squash"`
	Name         string                 `gorm:"column:name;NOT NULL"`         // 节点名称
	FlowInstID   int64                  `gorm:"column:flow_inst_id;NOT NULL"` // 审批流实例id
	ApprovalTime time.Time              `gorm:"column:approval_time"`         // 审核时间
	Opinions     string                 `gorm:"column:opinions"`              // 审核意见
	ApproverID   int64                  `gorm:"column:approver_id"`           // 当前审核人id
	Approver     string                 `gorm:"column:approver"`              // 当前审核人展示信息(name/employee_no)
	Report       string                 `gorm:"column:report"`                // 审核报告
	Status       string                 `gorm:"column:status;NOT NULL"`       // 状态
	NodeID       int64                  `gorm:"column:node_id;NOT NULL"`      // 审批流节点id
	Approvers    []ApprovalFlowApprover `gorm:"foreignKey:NodeInstID"`
	Node         ApprovalFlowNode       `gorm:"foreignKey:NodeID"`
}

func (m *ApprovalFlowNodeInstance) TableName() string {
	return "deploy.approval_flow_node_instance"
}

// ApprovalFlowInstance 审批流实例信息表
type ApprovalFlowInstance struct {
	db.BaseModel    `mapstructure:",squash"`
	Name            string                     `gorm:"column:name;NOT NULL"`                      // 名称
	FlowType        constants.FlowType         `gorm:"column:flow_type;default:upgrade;NOT NULL"` // 流程类型
	ProjectID       int64                      `gorm:"column:project_id"`                         // 关联组id
	TicketID        int64                      `gorm:"column:ticket_id;NOT NULL;uniqueIndex"`     // 关联的工单
	FlowID          int64                      `gorm:"column:flow_id;NOT NULL"`                   // 关联的流程模板id
	TaskRunID       int64                      `gorm:"column:task_run_id;NOT NULL"`               // 关联的流水线任务id
	Checkers        datatypes.JSON             `gorm:"column:checkers"`                           // 验收人
	ApplicantID     int64                      `gorm:"column:applicant_id"`                       // 申请人(和工单保持一直)
	Status          constants.TicketStatus     `gorm:"column:status;NOT NULL"`                    // 状态
	ChineseName     string                     `gorm:"column:chinese_name;NOT NULL"`              // 中文名
	EmployeeNo      string                     `gorm:"column:employee_no;NOT NULL"`               // 工号
	NodeSequence    string                     `gorm:"column:node_sequence"`                      // 节点顺序
	Nodes           []ApprovalFlowNodeInstance `gorm:"foreignKey:FlowInstID"`
	Ticket          *Ticket                    `gorm:"foreignKey:TicketID"`
	ChangeSetTaskID int64                      `gorm:"column:change_set_task_id"`
}

func (m *ApprovalFlowInstance) TableName() string {
	return "deploy.approval_flow_instance"
}

type ApprovalFlow struct {
	db.BaseModel `mapstructure:",squash"`
	Name         string             `gorm:"column:name" json:"name"`
	FlowType     constants.FlowType `gorm:"column:flow_type" json:"flowType"`
	ProjectId    int64              `gorm:"column:project_id" json:"projectId"`
	Nodes        []ApprovalFlowNode `gorm:"foreignKey:ApprovalFlowId"`
	NodeSequence string             `gorm:"column:node_sequence" json:"nodeSequence"`
}

func (a *ApprovalFlow) TableName() string {
	return "deploy.approval_flow"
}

// Ticket 工单信息表
type Ticket struct {
	db.BaseModel     `mapstructure:",squash"`
	Name             string                `gorm:"column:name;NOT NULL"`                       // 工单名称
	Sn               string                `gorm:"column:sn;NOT NULL"`                         // 工单号
	Type             constants.TicketType  `gorm:"column:type;NOT NULL"`                       // 工单类型
	Reason           string                `gorm:"column:reason;NOT NULL"`                     // 变更原因
	IsChangeSet      int                   `gorm:"column:is_change_set;NOT NULL"`              // 是否变更集(0否,1是)
	ApplicantID      int64                 `gorm:"column:applicant_id;NOT NULL"`               // 申请人
	AppId            int64                 `gorm:"column:app_id;NOT NULL"`                     // 应用id
	ProjectID        int64                 `gorm:"column:project_id;NOT NULL"`                 // 项目id
	TaskRunID        int64                 `gorm:"column:task_run_id;NOT NULL"`                // 流水线运行任务id
	Branch           string                `gorm:"column:branch;NOT NULL"`                     // 运行分支
	ArtifactVersion  string                `gorm:"column:artifact_version;NOT NULL"`           // 产品版本
	DeployConfigID   int64                 `gorm:"column:deploy_config_id;default:0;NOT NULL"` // 部署配置ID
	DeployEnv        datatypes.JSON        `gorm:"column:deploy_env"`                          // 部署环境
	CheckReport      string                `gorm:"column:check_report"`                        // 验收报告地址
	AppName          string                `gorm:"column:app_name"`                            // 服务名称
	ApprovalFlowInst *ApprovalFlowInstance `gorm:"foreignKey:TicketID"`                        // 关联审批流
	EventID          string                `gorm:"column:event_id;uniqueIndex"`                // 工单所属的eventId
	DeployConfigs    datatypes.JSON        `gorm:"column:deploy_configs"`                      // 部署配置信息
	DeployFlag       int                   `gorm:"column:deploy_flag"`                         // 部署标识
	ChangeSetTaskID  int64                 `gorm:"column:change_set_task_id"`                  // 变更集任务id
}

func (m *Ticket) TableName() string {
	return "deploy.ticket"
}
