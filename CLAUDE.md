# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Go 语言的企业级 CI/CD 平台，采用微服务架构设计。项目使用 gRPC 进行服务间通信，对外提供 REST API。

## 常用开发命令

### 环境初始化
```bash
# 安装必要的 protoc 插件
make init
```

### 测试相关
```bash
# 运行所有服务的测试并计算覆盖率（最低要求 70%）
make test

# 仅测试 pkg 目录
make test-pkg
```

### Protocol Buffers 代码生成
```bash
# 生成 gRPC 代码（先执行此命令）
make grpc pn=protocol/app/app

# 生成验证代码（在 grpc 之前执行）
make validate pn=protocol/app/app
```

### Mock 生成
```bash
# 在需要 mock 的接口文件顶部添加：
# //go:generate mockgen -destination=xxx_mock.go -package=dao -source=xxx.go

# 然后运行
make mockgen
```

### 代码格式化
```bash
make fmt
```

## 核心架构设计

### 服务架构

项目采用微服务架构，主要服务包括：

1. **BFF (Backend for Frontend)** - 端口 8090/9000
   - API 网关，统一前端入口
   - 处理权限验证和路由转发
   - WebSocket 支持

2. **Deploy 服务** - 端口 8092/9002
   - 核心部署服务
   - 集成 Kubernetes、ArgoCD、Tekton
   - 处理审批流程和流量染色

3. **App 服务** - 端口 8091/9001
   - 应用和项目管理
   - 镜像和配置管理

4. **其他服务**
   - Pipeline: 流水线管理
   - IAM: 身份认证和权限
   - Notify: 通知服务
   - MCP: 多集群管理

### 服务间通信

1. **内部通信**: 使用 gRPC (端口 9000-9009)
2. **外部 API**: REST API，统一 `/api/v1` 前缀
3. **异步通信**: Kafka 消息队列
   - `cicd-deploy-event-local`
   - `cicd-app-event-local`
   - `cicd-pipeline-event-local`

### 目录结构规范

每个服务遵循标准结构：
```
services/[service-name]/
├── cmd/           # 主程序入口
├── etc/           # 配置文件 (TOML 格式)
├── internal/      # 内部实现
│   ├── conf/      # 配置定义
│   ├── dao/       # 数据访问层
│   ├── model/     # 数据模型
│   ├── server/    # gRPC 服务实现
│   └── service/   # 业务逻辑层
├── rest/          # REST API 实现
└── pkg/           # 服务特定的包
```

### 关键技术栈

- **框架**: Kratos v2 (微服务框架)
- **HTTP**: Gin
- **数据库**: MySQL (使用 GORM)
- **缓存**: Redis
- **消息队列**: Kafka
- **容器编排**: Kubernetes API
- **CI/CD 工具**: ArgoCD, Tekton, Vela

### 权限模型

- 基于角色的访问控制 (RBAC)
- 角色类型: ADMIN, DEV_ADMIN, DEVELOPER, QA, OPS
- OpenAPI token 支持第三方集成

### 事件驱动架构

项目采用事件驱动架构，通过 Kafka 实现服务间解耦。事件处理逻辑主要在各服务的 `service` 层实现。

### 测试策略

- 单元测试使用 `testify/suite`
- Mock 使用 `gomock`
- 测试覆盖率要求 ≥ 70%
- 测试文件遵循 `*_test.go` 命名规范