package argo

type SessionRequest struct {
	Password string `json:"password"`
	Token    string `json:"token"`
	Username string `json:"username"`
}

type sessionResponse struct {
	Token string `json:"token"`
}

type ApplicationDeleteRequest struct {
	Name              string
	Cascade           bool
	PropagationPolicy string
	AppNamespace      string
	Project           string
}

type ApplicationResponse struct{}
