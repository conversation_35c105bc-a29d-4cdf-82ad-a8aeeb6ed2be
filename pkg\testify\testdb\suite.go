package testdb

import (
	"testing"

	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"52tt.com/cicd/pkg/db"
)

type Suite struct {
	suite.Suite
	db     *MemoryMySQL
	gormDB *gorm.DB
}

func (suite *Suite) DatabaseName() string {
	return "test"
}

func (suite *Suite) SetupTest() {
	suite.initMemoryDB()
}

func (suite *Suite) TearDownTest() {
	_ = suite.CloseDB()
}

func (suite *Suite) initMemoryDB() {
	suite.db = New(suite.DatabaseName())
	go func() {
		defer func() {
			if err := recover(); err != nil {
				suite.T().<PERSON><PERSON><PERSON>("SetupSuite panic: %v", err)
				return
			}
		}()

		err := suite.db.Start()
		if err != nil {
			suite.T().Errorf("SetupSuite db.start error: %v", err)
			return
		}
	}()
	suite.gormDBInit()
}

func (suite *Suite) gormDBInit() {
	gormDB, err := db.New(db.NewConfig(db.Port(13306), db.Userna<PERSON>("no_user"), db.Password(""), db.Database(suite.DatabaseName())))
	suite.Require().NoError(err)
	suite.Require().NotNil(gormDB)

	suite.gormDB = gormDB
	db.DB = gormDB
}

func (suite *Suite) AddTable(sql string) {
	err := suite.gormDB.Session(&gorm.Session{Logger: logger.Default.LogMode(logger.Silent)}).Exec(sql).Error
	suite.Require().NoError(err)
}

func (suite *Suite) AddIndex(sql string) {
	err := suite.gormDB.Session(&gorm.Session{Logger: logger.Default.LogMode(logger.Silent)}).Exec(sql).Error
	suite.Require().NoError(err)
}

func (suite *Suite) AddIndexes(sqls ...string) {
	for _, sql := range sqls {
		suite.AddIndex(sql)
	}
}

func (suite *Suite) CloseDebug() {
	suite.gormDB = suite.gormDB.Session(&gorm.Session{Logger: logger.Default.LogMode(logger.Silent)})
	db.DB = suite.gormDB
}

func (suite *Suite) GetDB() *gorm.DB {
	return suite.gormDB
}

func (suite *Suite) CloseDB() error {
	sqlDB, err := suite.gormDB.DB()
	if err != nil {
		return err
	}
	err = sqlDB.Close()
	if err != nil {
		return err
	}

	err = suite.db.Close()
	return err
}

func Run(t *testing.T, s suite.TestingSuite) {
	suite.Run(t, s)
}
