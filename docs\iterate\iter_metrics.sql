alter table `pipeline`.`pipeline_run`
    add column `is_clone` tinyint not null default 0 comment '是否实际运行的克隆记录',
    add column `committed_id` varchar(64) not null default '' comment '提交ID',
    add column `committed_date` timestamp NULL DEFAULT NULL COMMENT '提交时间',
    add column `app_committed_date` timestamp NULL DEFAULT NULL COMMENT '服务变更时间',
    add column `committed_event` json NULL DEFAULT NULL COMMENT '提交事件',
    add column `app_committed_event` json NULL DEFAULT NULL COMMENT '服务变更事件';

-- pipeline_run_stage
alter table `pipeline`.`pipeline_run_stage` add column `is_clone` tinyint not null default 0 comment '是否实际运行的克隆记录';

-- pipeline_run_task
alter table `pipeline`.`pipeline_run_task` add column `is_clone` tinyint not null default 0 comment '是否实际运行的克隆记录';