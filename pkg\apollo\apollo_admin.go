//go:generate mockgen -destination=apollo_admin_mock.go -package=apollo -source=apollo_admin.go
package apollo

import (
	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
	"context"
	"fmt"
)

var _ Service = (*HttpClient)(nil)

type HttpClient struct {
	host    string
	token   string
	session httpclient.Session
}

func NewServiceClient(host string) *HttpClient {
	session := httpclient.NewSession(&httpclient.SessionOption{})

	client := &HttpClient{
		host:    host,
		session: session,
	}
	return client
}

// Service apollo管理端相关接口，接口文档：https://www.apolloconfig.com/#/zh/portal/apollo-open-api-platform?id=_32-api%e6%8e%a5%e5%8f%a3%e5%88%97%e8%a1%a8
type Service interface {
	AddNamespace(ctx context.Context, req *AddNamespaceReq) (*AddNamespaceResp, error)
	AddConfig(ctx context.Context, req *AddConfigReq) (*AddConfigResp, error)
	ModifyConfig(ctx context.Context, req *ModifyConfigReq) error
	ReleaseConfig(ctx context.Context, req *ReleaseConfigReq) (*ReleaseConfigResp, error)
}

func (c *HttpClient) SetToken(token string) {
	headers := map[string]string{
		"Content-Type":  "application/json;charset=UTF-8",
		"Authorization": token,
	}
	c.session.SetHeaders(headers)
}

func (c *HttpClient) AddNamespace(ctx context.Context, req *AddNamespaceReq) (*AddNamespaceResp, error) {
	c.SetToken(req.Token)

	url := fmt.Sprintf("%v/apps/%v/appnamespaces", c.host, req.AppId)
	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "[AddNamespace] init add namespace params: %+v error: %v", req, err)
		return nil, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[AddNamespace] call http request error: %v, req: %+v", err, req)
		return nil, err
	}
	if httpResp.StatusCode != 200 {
		log.ErrorWithCtx(ctx, "[AddNamespace] call http request error: %v, req: %+v", httpResp.String(), req)
		return nil, fmt.Errorf("http request error: %v", httpResp.Status)
	}

	var resp AddNamespaceResp
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.ErrorWithCtx(ctx, "[AddNamespace] parse http response error: %v, req: %+v", err, req)
		return nil, err
	}
	return &resp, nil
}

func (c *HttpClient) AddConfig(ctx context.Context, req *AddConfigReq) (*AddConfigResp, error) {
	c.SetToken(req.Token)

	url := fmt.Sprintf("%v/envs/%v/apps/%v/clusters/%v/namespaces/%v/items", c.host, req.Env, req.AppId, req.ClusterName, req.NamespaceName)
	log.DebugWithCtx(ctx, "[AddConfig] url: %v", url)

	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "[AddConfig] init add config params: %+v error: %v", req, err)
		return nil, err
	}
	httpReq.Headers["Authorization"] = req.Token
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[AddConfig] call http request error: %v, req: %+v", err, req)
		return nil, err
	}
	if httpResp.StatusCode != 200 {
		log.ErrorWithCtx(ctx, "[AddConfig] call http request error: %v, req: %+v", httpResp.String(), req)
		return nil, fmt.Errorf("http request error: %v", httpResp.Status)
	}

	var resp AddConfigResp
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.ErrorWithCtx(ctx, "[AddConfig] parse http response error: %v, req: %+v", err, req)
		return nil, err
	}
	return &resp, nil
}

func (c *HttpClient) ModifyConfig(ctx context.Context, req *ModifyConfigReq) error {
	c.SetToken(req.Token)

	url := fmt.Sprintf("%v/envs/%v/apps/%v/clusters/%v/namespaces/%v/items/%v?createIfNotExists=%v", c.host, req.Env, req.AppId, req.ClusterName, req.NamespaceName, req.Key, req.CreateIfNotExists)
	log.DebugWithCtx(ctx, "[ModifyConfig] url: %v", url)

	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "[ModifyConfig] init modify config params: %+v error: %v", req, err)
		return err
	}
	httpReq.Headers["Authorization"] = req.Token
	httpResp, err := c.session.Put(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[ModifyConfig] call http request error: %v, req: %+v", err, req)
		return err
	}
	if httpResp.StatusCode != 200 {
		log.ErrorWithCtx(ctx, "[ModifyConfig] call http request error: %v, req: %+v", httpResp.String(), req)
		return fmt.Errorf("http request error: %v", httpResp.Status)
	}

	return nil
}

func (c *HttpClient) ReleaseConfig(ctx context.Context, req *ReleaseConfigReq) (*ReleaseConfigResp, error) {
	c.SetToken(req.Token)

	url := fmt.Sprintf("%v/envs/%v/apps/%v/clusters/%v/namespaces/%v/releases", c.host, req.Env, req.AppId, req.ClusterName, req.NamespaceName)
	log.DebugWithCtx(ctx, "[ReleaseConfig] url: %v", url)

	httpReq, err := httpclient.NewRequest(url, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "[ReleaseConfig] init release config params: %+v error: %v", req, err)
		return nil, err
	}
	httpReq.Headers["Authorization"] = req.Token
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "[ReleaseConfig] call http request error: %v, req: %+v", err, req)
		return nil, err
	}
	if httpResp.StatusCode != 200 {
		log.ErrorWithCtx(ctx, "[ReleaseConfig] call http request error: %v, req: %+v", httpResp.String(), req)
		return nil, fmt.Errorf("http request error: %v", httpResp.Status)
	}

	var resp ReleaseConfigResp
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.ErrorWithCtx(ctx, "[ReleaseConfig] parse http response error: %v, req: %+v", err, req)
		return nil, err
	}
	return &resp, nil
}
