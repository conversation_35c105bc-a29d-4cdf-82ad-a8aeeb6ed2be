package model

import (
	"52tt.com/cicd/pkg/constants"
	"52tt.com/cicd/pkg/page"
	"gorm.io/datatypes"
)

const (
	AppOwnerExists = 1
)

type AppQuery struct {
	page.PageModel
	Name                    string                      `json:"name" form:"name"`
	NameMatch               constants.TriggerBranchType `json:"nameMatch" form:"nameMatch"`
	ProjectId               int64                       `json:"projectId" form:"projectId" validate:"required,number"`
	OrderByAppPreferenceIds []string                    `json:"orderByAppPreferenceIds"`
	OwnerId                 int64                       `json:"ownerId" form:"ownerId"` // 研发负责人用户Id
	IDs                     []int64                     `json:"ids"`
	SenvStatus              string                      `json:"senvStatus" form:"senvStatus"`
}

type AppBase struct {
	ID                int64    `json:"id"`
	Name              string   `json:"name,omitempty" validate:"required,max=63"`
	Code              string   `json:"code"`
	Owners            []int64  `json:"owners,omitempty" validate:"required"`
	BuildPath         string   `json:"buildPath"`
	FirstBizModuleId  string   `json:"firstBizModuleId" validate:"required"`  //(cmdb属性)一级业务模块Id
	SecondBizModuleId string   `json:"secondBizModuleId" validate:"required"` //(cmdb属性)二级业务模块Id
	ThirdBizModuleId  string   `json:"thirdBizModuleId"  validate:"required"` //(cmdb属性)三级业务模块Id
	FourthBizModuleId string   `json:"fourthBizModuleId"`                     //(cmdb属性)四级业务模块Id
	RepoAddr          string   `json:"repoAddr,omitempty" validate:"required"`
	LangName          string   `json:"langName"`
	LangVersion       string   `json:"langVersion"`
	ProjectID         int64    `json:"projectID" validate:"required"`
	Description       string   `json:"description,omitempty" validate:"max=400"`
	NeedExternalRoute bool     `json:"needExternalRoute"`
	EventlinkType     []string `json:"eventlinkType"`
}

type App struct {
	AppBase

	Level        string `json:"level"`      //(cmdb属性)应用等级
	AppClass     string `json:"appClass"`   //(cmdb属性)应用分类
	IsBundled    bool   `json:"isBundled" ` //(cmdb属性)是否是捆绑应用
	IsVirtual    int    `json:"isVirtual"`  //(cmdb属性)是否虚拟应用(0否,1是)
	Status       int    `json:"status,omitempty"`
	CmdbId       string `json:"cmdbId,omitempty"`
	PipelineSize int64  `json:"pipelineSize"`
}

type AppDetail struct {
	ID        int64    `json:"id"`
	Name      string   `json:"name,omitempty"  validate:"required"`
	Code      string   `json:"code"`
	Owners    []string `json:"owners,omitempty" validate:"required"`
	BuildPath string   `json:"buildPath"`

	RepoAddr          string   `json:"repoAddr,omitempty" validate:"required"`
	LangName          string   `json:"langName"`
	LangVersion       string   `json:"langVersion"`
	Status            int      `json:"status,omitempty"`
	CmdbId            string   `json:"cmdbId,omitempty"`
	ProjectID         int64    `json:"projectID" validate:"required"`
	Description       string   `json:"description,omitempty"`
	PipelineSize      int64    `json:"pipelineSize"`
	IsPreference      bool     `json:"isPreference"`
	SenvStatus        string   `json:"senvStatus"`
	NeedExternalRoute bool     `json:"needExternalRoute"`
	EventlinkType     []string `json:"eventlinkType"`
}

type UpdateAppReq struct {
	AppBase

	FirstBizModuleName  string `json:"firstBizModuleName"`  //(cmdb属性)一级业务模块名称
	SecondBizModuleName string `json:"secondBizModuleName"` //(cmdb属性)二级业务模块名称
	ThirdBizModuleName  string `json:"thirdBizModuleName"`  //(cmdb属性)三级业务模块名称
	FourthBizModuleName string `json:"fourthBizModuleName"` //(cmdb属性)四级业务模块名称
}

type Owner struct {
	UserID     int64  `json:"userId"`
	Name       string `json:"name"`
	EmployeeNo string `json:"employeeNo"`
}

type AppResp struct {
	ID                  int64             `json:"id"`
	Name                string            `json:"name"`
	Owners              []int64           `json:"owners"`
	BuildPath           string            `json:"buildPath"`
	RepoAddr            string            `json:"repoAddr"`
	FirstBizModuleId    string            `json:"firstBizModuleId"`    //(cmdb属性)一级业务模块Id
	SecondBizModuleId   string            `json:"secondBizModuleId"`   //(cmdb属性)二级业务模块Id
	ThirdBizModuleId    string            `json:"thirdBizModuleId"`    //(cmdb属性)三级业务模块Id
	FourthBizModuleId   string            `json:"fourthBizModuleId"`   //(cmdb属性)四级业务模块Id
	FirstBizModuleName  string            `json:"firstBizModuleName"`  //(cmdb属性)一级业务模块名称
	SecondBizModuleName string            `json:"secondBizModuleName"` //(cmdb属性)二级业务模块名称
	ThirdBizModuleName  string            `json:"thirdBizModuleName"`  //(cmdb属性)三级业务模块名称
	FourthBizModuleName string            `json:"fourthBizModuleName"` //(cmdb属性)四级业务模块名称
	Level               string            `json:"level"`               //(cmdb属性)优先级
	CmdbID              string            `json:"cmdbID"`
	LangName            string            `json:"langName"`
	LangVersion         string            `json:"langVersion"`
	ProjectID           int64             `json:"projectId"`
	Description         string            `json:"description"`
	MatchLabels         map[string]string `json:"matchLabels"`   // 服务发布时k8s Deployment的 selector MatchLabels
	ServiceLabels       map[string]string `json:"serviceLabels"` // 服务发布时k8s Service的 selector
	StandLabelEnvs      string            `json:"standLabelEnvs"`
	NeedExternalRoute   bool              `json:"needExternalRoute"`
	EventlinkType       []string          `json:"eventlinkType"`
}

type LangVersion struct {
	Id      int64  `json:"id"`
	Version string `json:"version"`
}

type AppOwner struct {
	Owner int `json:"owner"`
}

type AppDeployConfigsPara struct {
	Namespace string `form:"namespace" json:"namespace"`
	Cluster   string `form:"cluster" json:"cluster"`
	ID        int64  `uri:"id"`
	Senv      string `form:"senv" json:"senv"`
	EnvTarget string `form:"envTarget" json:"envTarget"`
	Env       string `form:"env" json:"env"`
}

type ProjectAppsParam struct {
	ID       int64  `uri:"id" validate:"required"`
	Language string `form:"language" validate:"required"`
}

type AppPreference struct {
	AppID     int64 `json:"appId"`
	ProjectID int64 `json:"projectId"`
	UserID    int64 `json:"userId"`
}

type AppSenvStatus struct {
	Id         int64  `json:"id" uri:"id"`
	SenvStatus string `json:"senvStatus"`
}

type BatchUpdateParam struct {
	ProjectID         int64   `json:"projectId" validate:"required"`
	AppIDs            []int64 `json:"appIds" validate:"required"`
	Type              string  `json:"type" validate:"required,oneof=owners bizModule repoAddr"`
	Owners            []int64 `json:"owners" validate:"required_if=Type owners"`
	RepoAddr          string  `json:"repoAddr" validate:"required_if=Type repoAddr"`
	FirstBizModuleId  string  `json:"firstBizModuleId" validate:"required_if=Type bizModule"`  //(cmdb属性)一级业务模块Id
	SecondBizModuleId string  `json:"secondBizModuleId" validate:"required_if=Type bizModule"` //(cmdb属性)二级业务模块Id
	ThirdBizModuleId  string  `json:"thirdBizModuleId"  validate:"required_if=Type bizModule"` //(cmdb属性)三级业务模块Id
}

type AppSearchReq struct {
	Keyword string `form:"keyword"`
}

func (s *AppSearchReq) HasKeyword() bool {
	return s.Keyword != ""
}

type AppSearchResult struct {
	AppID         int64  `json:"appId"`
	AppName       string `json:"appName"`
	ProjectID     int64  `json:"projectId"`
	ProjectName   string `json:"projectName"`
	HasPermission bool   `json:"hasPermission"` // 当前用户在项目组中是否有权限
	IsProduction  bool   `json:"isProduction"`  // 服务是否部署在生产环境
}

type AppSearchResp struct {
	Apps []AppSearchResult `json:"apps"`
}

type UserAppsReq struct {
	UserID     int64
	ProjectIDs []int64
	AppName    string
}

type ProjectsAppsReq struct {
	ProjectID  int64  `form:"projectId" validate:"required"`
	UserGroup  string `form:"userGroup"`
	AppName    string `form:"appName"`
	ProjectIDs []int64
}

type AppBasic struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	BuildPath   string `json:"buildPath"`
	RepoAddr    string `json:"repoAddr"`
	ProjectID   int64  `json:"projectId"`
	ProjectName string `json:"projectName"`
	UserGroup   string `json:"userGroup"`
}

type AppEventlinkReq struct {
	ID int64 `uri:"id"`
}

type AppEventlinkResp struct {
	Testing    EventlinkData `json:"testing"`
	Production EventlinkData `json:"production"`
}

type EventlinkData struct {
	ConsumerType string `json:"consumerType"`
	ProducerType string `json:"producerType"`
}

type FindAppsReq struct {
	AppName     string `form:"appName" validate:"required"` // 服务名称,精准匹配
	ProjectID   int64  `form:"projectId"`                   // 项目ID
	ProjectName string `form:"projectName"`                 // 项目名称
}

type AppWithProject struct {
	Name        string `json:"name"`        // 应用名称
	Code        string `json:"code"`        // 应用代号(唯一标识)
	BuildPath   string `json:"buildPath"`   // 构建路径
	LangName    string `json:"langName"`    // 应用语言名称
	LangVersion string `json:"langVersion"` // 应用语言版本
	RepoAddr    string `json:"repoAddr"`    // 源码库地址
	Status      int    `json:"status"`      // 状态
	CmdbId      string `json:"cmdbId"`      // 对应的cmdb的 唯一Id
	ProjectID   int64  `json:"projectID"`   // 所属项目id
	Description string `json:"description"` // 备注

	ProjectName        string         `json:"projectName"`        // 所属项目名称
	ProjectDescription string         `json:"projectDescription"` // 所属项目描述
	ProjectUserGroup   string         `json:"projectUserGroup"`   // 所属项目用户组
	ProjectExtra       datatypes.JSON `json:"projectExtra"`       // 所属项目扩展字段
}

type CreateAppResp struct {
	ID int64 `json:"id"`
}

type CMDBApp struct {
	ID                   string   `json:"id,omitempty"`
	Name                 string   `json:"name,omitempty"`
	Level                string   `json:"level,omitempty"`             //应用等级
	AppClass             string   `json:"app_class,omitempty"`         //
	IsBundled            bool     `json:"is_bundled,omitempty"`        //是否捆绑应用
	Language             string   `json:"language,omitempty"`          //应用语言, java/python/golang
	Category             string   `json:"category,omitempty"`          //应用类别
	AppCreateTime        int      `json:"app_create_time,omitempty"`   //应用创建时间
	DevelopUserList      []string `json:"develop_user_list,omitempty"` //应用负责人列表
	Description          string   `json:"description,omitempty"`
	BusinessLabelL1ObjId string   `json:"_business_label_l1_obj_id,omitempty"`
	BusinessLabelL2ObjId string   `json:"_business_label_l2_obj_id,omitempty"`
	BusinessLabelL3ObjId string   `json:"_business_label_l3_obj_id,omitempty"`
	BusinessLabelL4ObjId string   `json:"_business_label_l4_obj_id,omitempty"`
	BusinessLabelL1Name  string   `json:"business_label_l1_name,omitempty"`
	BusinessLabelL2Name  string   `json:"business_label_l2_name,omitempty"`
	BusinessLabelL3Name  string   `json:"business_label_l3_name,omitempty"`
	BusinessLabelL4Name  string   `json:"business_label_l4_name,omitempty"`
}
