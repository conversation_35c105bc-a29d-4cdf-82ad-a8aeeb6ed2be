//go:generate mockgen -destination=http_sub_namespace_mock.go -package=core -source=http_sub_namespace.go
package core

import (
	"context"
)

type SubNamespaceClient interface {
	CreateSubNamespace(ctx context.Context, in *CreateSubNamespaceRequest) (*CreateSubNamespaceResponse, error)
	GetSubNamespace(ctx context.Context, in *GetSubNamespaceRequest) (*GetSubNamespaceResponse, error)
}

type CreateSubNamespaceRequest struct{}

type CreateSubNamespaceResponse struct{}

type GetSubNamespaceRequest struct {
	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
	Describe  string `json:"describe"`
}

type GetSubNamespaceResponse struct {
	BaseResponse

	Data GetSubNamespaceData `json:"data"`
}

type GetSubNamespaceData struct {
	Namespace   string `json:"namespace"`
	TrafficMark string `json:"trafficMark"`
	Describe    string `json:"describe"`
	Parent      string `json:"parent"`
}

func (d *GetSubNamespaceData) BasicNamespace() string {
	return d.Parent
}

func (d *GetSubNamespaceData) CurrentNamespace() string {
	return d.Namespace
}
