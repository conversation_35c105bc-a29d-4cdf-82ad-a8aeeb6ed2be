// Code generated by MockGen. DO NOT EDIT.
// Source: client.go

// Package aggregate is a generated GoMock package.
package aggregate

import (
	context "context"
	reflect "reflect"

	cloud "52tt.com/cicd/pkg/cloud"
	core "52tt.com/cicd/pkg/cloud/http/core"
	gomock "github.com/golang/mock/gomock"
	constack "golang.ttyuyin.com/genproto/quwanapis/cloud/constack/v1alpha"
	continuousdeployment "golang.ttyuyin.com/genproto/quwanapis/cloud/continuousdeployment/v1alpha"
	mesh "golang.ttyuyin.com/genproto/quwanapis/cloud/mesh/v1alpha"
	grpc "google.golang.org/grpc"
)

// MockAggClient is a mock of AggClient interface.
type MockAggClient struct {
	ctrl     *gomock.Controller
	recorder *MockAggClientMockRecorder
}

// MockAggClientMockRecorder is the mock recorder for MockAggClient.
type MockAggClientMockRecorder struct {
	mock *MockAggClient
}

// NewMockAggClient creates a new mock instance.
func NewMockAggClient(ctrl *gomock.Controller) *MockAggClient {
	mock := &MockAggClient{ctrl: ctrl}
	mock.recorder = &MockAggClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAggClient) EXPECT() *MockAggClientMockRecorder {
	return m.recorder
}

// Apply mocks base method.
func (m *MockAggClient) Apply(ctx context.Context, in *constack.ApplyRequest, opts ...grpc.CallOption) (*constack.ApplyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Apply", varargs...)
	ret0, _ := ret[0].(*constack.ApplyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Apply indicates an expected call of Apply.
func (mr *MockAggClientMockRecorder) Apply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Apply", reflect.TypeOf((*MockAggClient)(nil).Apply), varargs...)
}

// BuildPrimaryRoute mocks base method.
func (m *MockAggClient) BuildPrimaryRoute(ctx context.Context, in *mesh.BuildPrimaryRouteRequest, opts ...grpc.CallOption) (*mesh.BuildPrimaryRouteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuildPrimaryRoute", varargs...)
	ret0, _ := ret[0].(*mesh.BuildPrimaryRouteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildPrimaryRoute indicates an expected call of BuildPrimaryRoute.
func (mr *MockAggClientMockRecorder) BuildPrimaryRoute(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildPrimaryRoute", reflect.TypeOf((*MockAggClient)(nil).BuildPrimaryRoute), varargs...)
}

// BuildSubenvRoute mocks base method.
func (m *MockAggClient) BuildSubenvRoute(ctx context.Context, in *mesh.BuildSubenvRouteRequest, opts ...grpc.CallOption) (*mesh.BuildSubenvRouteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuildSubenvRoute", varargs...)
	ret0, _ := ret[0].(*mesh.BuildSubenvRouteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildSubenvRoute indicates an expected call of BuildSubenvRoute.
func (mr *MockAggClientMockRecorder) BuildSubenvRoute(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildSubenvRoute", reflect.TypeOf((*MockAggClient)(nil).BuildSubenvRoute), varargs...)
}

// BuildSubenvRouteWithMultiTrafficMark mocks base method.
func (m *MockAggClient) BuildSubenvRouteWithMultiTrafficMark(ctx context.Context, in *mesh.BuildSubenvRouteWithMultiTrafficMarkRequest, opts ...grpc.CallOption) (*mesh.BuildSubenvRouteWithMultiTrafficMarkResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuildSubenvRouteWithMultiTrafficMark", varargs...)
	ret0, _ := ret[0].(*mesh.BuildSubenvRouteWithMultiTrafficMarkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildSubenvRouteWithMultiTrafficMark indicates an expected call of BuildSubenvRouteWithMultiTrafficMark.
func (mr *MockAggClientMockRecorder) BuildSubenvRouteWithMultiTrafficMark(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildSubenvRouteWithMultiTrafficMark", reflect.TypeOf((*MockAggClient)(nil).BuildSubenvRouteWithMultiTrafficMark), varargs...)
}

// BuildSubenvRouteWithWeight mocks base method.
func (m *MockAggClient) BuildSubenvRouteWithWeight(ctx context.Context, in *mesh.BuildSubenvRouteWithWeightRequest, opts ...grpc.CallOption) (*mesh.BuildSubenvRouteWithWeightResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuildSubenvRouteWithWeight", varargs...)
	ret0, _ := ret[0].(*mesh.BuildSubenvRouteWithWeightResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuildSubenvRouteWithWeight indicates an expected call of BuildSubenvRouteWithWeight.
func (mr *MockAggClientMockRecorder) BuildSubenvRouteWithWeight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuildSubenvRouteWithWeight", reflect.TypeOf((*MockAggClient)(nil).BuildSubenvRouteWithWeight), varargs...)
}

// ClearPrimaryRoute mocks base method.
func (m *MockAggClient) ClearPrimaryRoute(ctx context.Context, in *mesh.ClearPrimaryRouteRequest, opts ...grpc.CallOption) (*mesh.ClearPrimaryRouteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearPrimaryRoute", varargs...)
	ret0, _ := ret[0].(*mesh.ClearPrimaryRouteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearPrimaryRoute indicates an expected call of ClearPrimaryRoute.
func (mr *MockAggClientMockRecorder) ClearPrimaryRoute(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearPrimaryRoute", reflect.TypeOf((*MockAggClient)(nil).ClearPrimaryRoute), varargs...)
}

// ClearSubenvRoute mocks base method.
func (m *MockAggClient) ClearSubenvRoute(ctx context.Context, in *mesh.ClearSubenvRouteRequest, opts ...grpc.CallOption) (*mesh.ClearSubenvRouteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearSubenvRoute", varargs...)
	ret0, _ := ret[0].(*mesh.ClearSubenvRouteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearSubenvRoute indicates an expected call of ClearSubenvRoute.
func (mr *MockAggClientMockRecorder) ClearSubenvRoute(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearSubenvRoute", reflect.TypeOf((*MockAggClient)(nil).ClearSubenvRoute), varargs...)
}

// ClearSubenvRouteWithWeight mocks base method.
func (m *MockAggClient) ClearSubenvRouteWithWeight(ctx context.Context, in *mesh.ClearSubenvRouteWithWeightRequest, opts ...grpc.CallOption) (*mesh.ClearSubenvRouteWithWeightResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearSubenvRouteWithWeight", varargs...)
	ret0, _ := ret[0].(*mesh.ClearSubenvRouteWithWeightResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearSubenvRouteWithWeight indicates an expected call of ClearSubenvRouteWithWeight.
func (mr *MockAggClientMockRecorder) ClearSubenvRouteWithWeight(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearSubenvRouteWithWeight", reflect.TypeOf((*MockAggClient)(nil).ClearSubenvRouteWithWeight), varargs...)
}

// CopyClusterResource mocks base method.
func (m *MockAggClient) CopyClusterResource(cluster, originNamespace, targetNamespace string, resources []cloud.Resource) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyClusterResource", cluster, originNamespace, targetNamespace, resources)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CopyClusterResource indicates an expected call of CopyClusterResource.
func (mr *MockAggClientMockRecorder) CopyClusterResource(cluster, originNamespace, targetNamespace, resources interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyClusterResource", reflect.TypeOf((*MockAggClient)(nil).CopyClusterResource), cluster, originNamespace, targetNamespace, resources)
}

// Create mocks base method.
func (m *MockAggClient) Create(ctx context.Context, in *constack.CreateRequest, opts ...grpc.CallOption) (*constack.CreateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Create", varargs...)
	ret0, _ := ret[0].(*constack.CreateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockAggClientMockRecorder) Create(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAggClient)(nil).Create), varargs...)
}

// CreateResource mocks base method.
func (m *MockAggClient) CreateResource(ctx context.Context, in *core.CreateResourceRequest) (*core.CreateResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateResource", ctx, in)
	ret0, _ := ret[0].(*core.CreateResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateResource indicates an expected call of CreateResource.
func (mr *MockAggClientMockRecorder) CreateResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateResource", reflect.TypeOf((*MockAggClient)(nil).CreateResource), ctx, in)
}

// CreateRoute mocks base method.
func (m *MockAggClient) CreateRoute(cluster, subNamespace, serviceName string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRoute", cluster, subNamespace, serviceName)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRoute indicates an expected call of CreateRoute.
func (mr *MockAggClientMockRecorder) CreateRoute(cluster, subNamespace, serviceName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRoute", reflect.TypeOf((*MockAggClient)(nil).CreateRoute), cluster, subNamespace, serviceName)
}

// CreateSubEnv mocks base method.
func (m *MockAggClient) CreateSubEnv(cluster, originNamespace, targetNamespace string, userId []string, labels map[string]interface{}) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubEnv", cluster, originNamespace, targetNamespace, userId, labels)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubEnv indicates an expected call of CreateSubEnv.
func (mr *MockAggClientMockRecorder) CreateSubEnv(cluster, originNamespace, targetNamespace, userId, labels interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubEnv", reflect.TypeOf((*MockAggClient)(nil).CreateSubEnv), cluster, originNamespace, targetNamespace, userId, labels)
}

// CreateSubEnvTrafficMark mocks base method.
func (m *MockAggClient) CreateSubEnvTrafficMark(cluster, subNamespace, trafficMark string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubEnvTrafficMark", cluster, subNamespace, trafficMark)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubEnvTrafficMark indicates an expected call of CreateSubEnvTrafficMark.
func (mr *MockAggClientMockRecorder) CreateSubEnvTrafficMark(cluster, subNamespace, trafficMark interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubEnvTrafficMark", reflect.TypeOf((*MockAggClient)(nil).CreateSubEnvTrafficMark), cluster, subNamespace, trafficMark)
}

// CreateSubNamespace mocks base method.
func (m *MockAggClient) CreateSubNamespace(ctx context.Context, in *core.CreateSubNamespaceRequest) (*core.CreateSubNamespaceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubNamespace", ctx, in)
	ret0, _ := ret[0].(*core.CreateSubNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubNamespace indicates an expected call of CreateSubNamespace.
func (mr *MockAggClientMockRecorder) CreateSubNamespace(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubNamespace", reflect.TypeOf((*MockAggClient)(nil).CreateSubNamespace), ctx, in)
}

// Delete mocks base method.
func (m *MockAggClient) Delete(ctx context.Context, in *constack.DeleteRequest, opts ...grpc.CallOption) (*constack.DeleteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Delete", varargs...)
	ret0, _ := ret[0].(*constack.DeleteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockAggClientMockRecorder) Delete(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockAggClient)(nil).Delete), varargs...)
}

// DeleteCollection mocks base method.
func (m *MockAggClient) DeleteCollection(ctx context.Context, in *constack.DeleteCollectionRequest, opts ...grpc.CallOption) (*constack.DeleteCollectionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteCollection", varargs...)
	ret0, _ := ret[0].(*constack.DeleteCollectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCollection indicates an expected call of DeleteCollection.
func (mr *MockAggClientMockRecorder) DeleteCollection(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCollection", reflect.TypeOf((*MockAggClient)(nil).DeleteCollection), varargs...)
}

// DeleteResource mocks base method.
func (m *MockAggClient) DeleteResource(ctx context.Context, in *core.DeleteResourceRequest) (*core.DeleteResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteResource", ctx, in)
	ret0, _ := ret[0].(*core.DeleteResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteResource indicates an expected call of DeleteResource.
func (mr *MockAggClientMockRecorder) DeleteResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteResource", reflect.TypeOf((*MockAggClient)(nil).DeleteResource), ctx, in)
}

// DeleteSubEnvTrafficMark mocks base method.
func (m *MockAggClient) DeleteSubEnvTrafficMark(cluster, subNamespace string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSubEnvTrafficMark", cluster, subNamespace)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSubEnvTrafficMark indicates an expected call of DeleteSubEnvTrafficMark.
func (mr *MockAggClientMockRecorder) DeleteSubEnvTrafficMark(cluster, subNamespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSubEnvTrafficMark", reflect.TypeOf((*MockAggClient)(nil).DeleteSubEnvTrafficMark), cluster, subNamespace)
}

// DeleteWorkloadRelatedHpa mocks base method.
func (m *MockAggClient) DeleteWorkloadRelatedHpa(ctx context.Context, in *constack.DeleteWorkloadRelatedHpaRequest, opts ...grpc.CallOption) (*constack.DeleteWorkloadRelatedHpaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteWorkloadRelatedHpa", varargs...)
	ret0, _ := ret[0].(*constack.DeleteWorkloadRelatedHpaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteWorkloadRelatedHpa indicates an expected call of DeleteWorkloadRelatedHpa.
func (mr *MockAggClientMockRecorder) DeleteWorkloadRelatedHpa(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkloadRelatedHpa", reflect.TypeOf((*MockAggClient)(nil).DeleteWorkloadRelatedHpa), varargs...)
}

// DeployOAM mocks base method.
func (m *MockAggClient) DeployOAM(ctx context.Context, in *continuousdeployment.DeployOAMRequest, opts ...grpc.CallOption) (continuousdeployment.ContinuousDeploymentService_DeployOAMClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeployOAM", varargs...)
	ret0, _ := ret[0].(continuousdeployment.ContinuousDeploymentService_DeployOAMClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeployOAM indicates an expected call of DeployOAM.
func (mr *MockAggClientMockRecorder) DeployOAM(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeployOAM", reflect.TypeOf((*MockAggClient)(nil).DeployOAM), varargs...)
}

// Detail mocks base method.
func (m *MockAggClient) Detail(ctx context.Context, in *constack.DetailRequest, opts ...grpc.CallOption) (*constack.DetailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Detail", varargs...)
	ret0, _ := ret[0].(*constack.DetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Detail indicates an expected call of Detail.
func (mr *MockAggClientMockRecorder) Detail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Detail", reflect.TypeOf((*MockAggClient)(nil).Detail), varargs...)
}

// FindAnyList mocks base method.
func (m *MockAggClient) FindAnyList(ctx context.Context, in core.AnyListReq, out any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAnyList", ctx, in, out)
	ret0, _ := ret[0].(error)
	return ret0
}

// FindAnyList indicates an expected call of FindAnyList.
func (mr *MockAggClientMockRecorder) FindAnyList(ctx, in, out interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAnyList", reflect.TypeOf((*MockAggClient)(nil).FindAnyList), ctx, in, out)
}

// FindCluster mocks base method.
func (m *MockAggClient) FindCluster(ctx context.Context, in *constack.ClusterReq, opts ...grpc.CallOption) (*constack.ClusterResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindCluster", varargs...)
	ret0, _ := ret[0].(*constack.ClusterResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindCluster indicates an expected call of FindCluster.
func (mr *MockAggClientMockRecorder) FindCluster(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindCluster", reflect.TypeOf((*MockAggClient)(nil).FindCluster), varargs...)
}

// FindNS mocks base method.
func (m *MockAggClient) FindNS(ctx context.Context, in *constack.NamespaceReq, opts ...grpc.CallOption) (*constack.NamespaceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindNS", varargs...)
	ret0, _ := ret[0].(*constack.NamespaceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindNS indicates an expected call of FindNS.
func (mr *MockAggClientMockRecorder) FindNS(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindNS", reflect.TypeOf((*MockAggClient)(nil).FindNS), varargs...)
}

// GenerateYaml mocks base method.
func (m *MockAggClient) GenerateYaml(ctx context.Context, in *continuousdeployment.GenerateYamlRequest, opts ...grpc.CallOption) (*continuousdeployment.GenerateYamlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateYaml", varargs...)
	ret0, _ := ret[0].(*continuousdeployment.GenerateYamlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateYaml indicates an expected call of GenerateYaml.
func (mr *MockAggClientMockRecorder) GenerateYaml(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateYaml", reflect.TypeOf((*MockAggClient)(nil).GenerateYaml), varargs...)
}

// Get mocks base method.
func (m *MockAggClient) Get(ctx context.Context, in *constack.GetRequest, opts ...grpc.CallOption) (*constack.GetResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(*constack.GetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockAggClientMockRecorder) Get(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockAggClient)(nil).Get), varargs...)
}

// GetClusterList mocks base method.
func (m *MockAggClient) GetClusterList(usersID []string, env string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterList", usersID, env)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterList indicates an expected call of GetClusterList.
func (mr *MockAggClientMockRecorder) GetClusterList(usersID, env interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterList", reflect.TypeOf((*MockAggClient)(nil).GetClusterList), usersID, env)
}

// GetClusterResources mocks base method.
func (m *MockAggClient) GetClusterResources(cluster, namespace, kind, search string, page, size int64) (*cloud.ClusterResourceDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterResources", cluster, namespace, kind, search, page, size)
	ret0, _ := ret[0].(*cloud.ClusterResourceDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterResources indicates an expected call of GetClusterResources.
func (mr *MockAggClientMockRecorder) GetClusterResources(cluster, namespace, kind, search, page, size interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterResources", reflect.TypeOf((*MockAggClient)(nil).GetClusterResources), cluster, namespace, kind, search, page, size)
}

// GetConfig mocks base method.
func (m *MockAggClient) GetConfig(ctx context.Context, in *constack.GetConfigRequest, opts ...grpc.CallOption) (*constack.GetConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConfig", varargs...)
	ret0, _ := ret[0].(*constack.GetConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfig indicates an expected call of GetConfig.
func (mr *MockAggClientMockRecorder) GetConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfig", reflect.TypeOf((*MockAggClient)(nil).GetConfig), varargs...)
}

// GetNamespaceList mocks base method.
func (m *MockAggClient) GetNamespaceList(cluster string, usersID []string, nsType string) ([]cloud.NamespaceDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNamespaceList", cluster, usersID, nsType)
	ret0, _ := ret[0].([]cloud.NamespaceDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamespaceList indicates an expected call of GetNamespaceList.
func (mr *MockAggClientMockRecorder) GetNamespaceList(cluster, usersID, nsType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamespaceList", reflect.TypeOf((*MockAggClient)(nil).GetNamespaceList), cluster, usersID, nsType)
}

// GetReadyReplicas mocks base method.
func (m *MockAggClient) GetReadyReplicas(ctx context.Context, in *core.GetReadyReplicasRequest) (*core.GetReadyReplicasResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReadyReplicas", ctx, in)
	ret0, _ := ret[0].(*core.GetReadyReplicasResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReadyReplicas indicates an expected call of GetReadyReplicas.
func (mr *MockAggClientMockRecorder) GetReadyReplicas(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReadyReplicas", reflect.TypeOf((*MockAggClient)(nil).GetReadyReplicas), ctx, in)
}

// GetResource mocks base method.
func (m *MockAggClient) GetResource(ctx context.Context, in *core.GetResourceRequest) (*core.GetResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResource", ctx, in)
	ret0, _ := ret[0].(*core.GetResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResource indicates an expected call of GetResource.
func (mr *MockAggClientMockRecorder) GetResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResource", reflect.TypeOf((*MockAggClient)(nil).GetResource), ctx, in)
}

// GetSubNamespace mocks base method.
func (m *MockAggClient) GetSubNamespace(ctx context.Context, in *core.GetSubNamespaceRequest) (*core.GetSubNamespaceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubNamespace", ctx, in)
	ret0, _ := ret[0].(*core.GetSubNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubNamespace indicates an expected call of GetSubNamespace.
func (mr *MockAggClientMockRecorder) GetSubNamespace(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubNamespace", reflect.TypeOf((*MockAggClient)(nil).GetSubNamespace), ctx, in)
}

// GetSubTrafficMarking mocks base method.
func (m *MockAggClient) GetSubTrafficMarking(arg0, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubTrafficMarking", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubTrafficMarking indicates an expected call of GetSubTrafficMarking.
func (mr *MockAggClientMockRecorder) GetSubTrafficMarking(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubTrafficMarking", reflect.TypeOf((*MockAggClient)(nil).GetSubTrafficMarking), arg0, arg1)
}

// GetVirtualServiceRelatedService mocks base method.
func (m *MockAggClient) GetVirtualServiceRelatedService(ctx context.Context, in *mesh.GetVirtualServiceRelatedServiceRequest, opts ...grpc.CallOption) (*mesh.GetVirtualServiceRelatedServiceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVirtualServiceRelatedService", varargs...)
	ret0, _ := ret[0].(*mesh.GetVirtualServiceRelatedServiceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualServiceRelatedService indicates an expected call of GetVirtualServiceRelatedService.
func (mr *MockAggClientMockRecorder) GetVirtualServiceRelatedService(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualServiceRelatedService", reflect.TypeOf((*MockAggClient)(nil).GetVirtualServiceRelatedService), varargs...)
}

// GetWorkloadRelatedHpa mocks base method.
func (m *MockAggClient) GetWorkloadRelatedHpa(ctx context.Context, in *constack.GetWorkloadRelatedHpaRequest, opts ...grpc.CallOption) (*constack.GetWorkloadRelatedHpaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWorkloadRelatedHpa", varargs...)
	ret0, _ := ret[0].(*constack.GetWorkloadRelatedHpaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkloadRelatedHpa indicates an expected call of GetWorkloadRelatedHpa.
func (mr *MockAggClientMockRecorder) GetWorkloadRelatedHpa(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkloadRelatedHpa", reflect.TypeOf((*MockAggClient)(nil).GetWorkloadRelatedHpa), varargs...)
}

// IsNameSpaceExist mocks base method.
func (m *MockAggClient) IsNameSpaceExist(cluster, namespace string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsNameSpaceExist", cluster, namespace)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsNameSpaceExist indicates an expected call of IsNameSpaceExist.
func (mr *MockAggClientMockRecorder) IsNameSpaceExist(cluster, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsNameSpaceExist", reflect.TypeOf((*MockAggClient)(nil).IsNameSpaceExist), cluster, namespace)
}

// List mocks base method.
func (m *MockAggClient) List(ctx context.Context, in *constack.ListRequest, opts ...grpc.CallOption) (*constack.ListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "List", varargs...)
	ret0, _ := ret[0].(*constack.ListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockAggClientMockRecorder) List(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockAggClient)(nil).List), varargs...)
}

// ListCluster mocks base method.
func (m *MockAggClient) ListCluster(ctx context.Context, in *core.ListClusterRequest) (*core.ListClusterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCluster", ctx, in)
	ret0, _ := ret[0].(*core.ListClusterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCluster indicates an expected call of ListCluster.
func (mr *MockAggClientMockRecorder) ListCluster(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCluster", reflect.TypeOf((*MockAggClient)(nil).ListCluster), ctx, in)
}

// ListClusterNamespace mocks base method.
func (m *MockAggClient) ListClusterNamespace(ctx context.Context, in *core.ListClusterNamespaceRequest) (*core.ListClusterNamespaceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClusterNamespace", ctx, in)
	ret0, _ := ret[0].(*core.ListClusterNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClusterNamespace indicates an expected call of ListClusterNamespace.
func (mr *MockAggClientMockRecorder) ListClusterNamespace(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClusterNamespace", reflect.TypeOf((*MockAggClient)(nil).ListClusterNamespace), ctx, in)
}

// ListGatewayHosts mocks base method.
func (m *MockAggClient) ListGatewayHosts(ctx context.Context, in *mesh.ListGatewayHostsRequest, opts ...grpc.CallOption) (*mesh.ListGatewayHostsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListGatewayHosts", varargs...)
	ret0, _ := ret[0].(*mesh.ListGatewayHostsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListGatewayHosts indicates an expected call of ListGatewayHosts.
func (mr *MockAggClientMockRecorder) ListGatewayHosts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGatewayHosts", reflect.TypeOf((*MockAggClient)(nil).ListGatewayHosts), varargs...)
}

// ListResource mocks base method.
func (m *MockAggClient) ListResource(ctx context.Context, in *core.ListResourceRequest) (*core.ListResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListResource", ctx, in)
	ret0, _ := ret[0].(*core.ListResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListResource indicates an expected call of ListResource.
func (mr *MockAggClientMockRecorder) ListResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResource", reflect.TypeOf((*MockAggClient)(nil).ListResource), ctx, in)
}

// ListServiceRelatedVirtualService mocks base method.
func (m *MockAggClient) ListServiceRelatedVirtualService(ctx context.Context, in *mesh.ListServiceRelatedVirtualServiceRequest, opts ...grpc.CallOption) (*mesh.ListServiceRelatedVirtualServiceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListServiceRelatedVirtualService", varargs...)
	ret0, _ := ret[0].(*mesh.ListServiceRelatedVirtualServiceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListServiceRelatedVirtualService indicates an expected call of ListServiceRelatedVirtualService.
func (mr *MockAggClientMockRecorder) ListServiceRelatedVirtualService(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListServiceRelatedVirtualService", reflect.TypeOf((*MockAggClient)(nil).ListServiceRelatedVirtualService), varargs...)
}

// MultiResource mocks base method.
func (m *MockAggClient) MultiResource(ctx context.Context, in *constack.MultiResourceReq, opts ...grpc.CallOption) (*constack.MultiResourceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MultiResource", varargs...)
	ret0, _ := ret[0].(*constack.MultiResourceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiResource indicates an expected call of MultiResource.
func (mr *MockAggClientMockRecorder) MultiResource(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiResource", reflect.TypeOf((*MockAggClient)(nil).MultiResource), varargs...)
}

// Restart mocks base method.
func (m *MockAggClient) Restart(ctx context.Context, in *constack.RestartRequest, opts ...grpc.CallOption) (*constack.RestartResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Restart", varargs...)
	ret0, _ := ret[0].(*constack.RestartResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Restart indicates an expected call of Restart.
func (mr *MockAggClientMockRecorder) Restart(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Restart", reflect.TypeOf((*MockAggClient)(nil).Restart), varargs...)
}

// Save mocks base method.
func (m *MockAggClient) Save(ctx context.Context, in *constack.SaveRequest, opts ...grpc.CallOption) (*constack.SaveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Save", varargs...)
	ret0, _ := ret[0].(*constack.SaveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockAggClientMockRecorder) Save(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockAggClient)(nil).Save), varargs...)
}

// ScaleWorkloadReplicas mocks base method.
func (m *MockAggClient) ScaleWorkloadReplicas(ctx context.Context, in *constack.ScaleWorkloadReq, opts ...grpc.CallOption) (*constack.ScaleWorkloadResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScaleWorkloadReplicas", varargs...)
	ret0, _ := ret[0].(*constack.ScaleWorkloadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScaleWorkloadReplicas indicates an expected call of ScaleWorkloadReplicas.
func (mr *MockAggClientMockRecorder) ScaleWorkloadReplicas(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScaleWorkloadReplicas", reflect.TypeOf((*MockAggClient)(nil).ScaleWorkloadReplicas), varargs...)
}

// SetV2Host mocks base method.
func (m *MockAggClient) SetV2Host(hostV2 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetV2Host", hostV2)
}

// SetV2Host indicates an expected call of SetV2Host.
func (mr *MockAggClientMockRecorder) SetV2Host(hostV2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetV2Host", reflect.TypeOf((*MockAggClient)(nil).SetV2Host), hostV2)
}

// SyncUnifiedClusterSidecar mocks base method.
func (m *MockAggClient) SyncUnifiedClusterSidecar(ctx context.Context, in *constack.SyncUnifiedClusterSidecarReq, opts ...grpc.CallOption) (*constack.SyncUnifiedClusterSidecarResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SyncUnifiedClusterSidecar", varargs...)
	ret0, _ := ret[0].(*constack.SyncUnifiedClusterSidecarResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncUnifiedClusterSidecar indicates an expected call of SyncUnifiedClusterSidecar.
func (mr *MockAggClientMockRecorder) SyncUnifiedClusterSidecar(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncUnifiedClusterSidecar", reflect.TypeOf((*MockAggClient)(nil).SyncUnifiedClusterSidecar), varargs...)
}

// UnDeployOAM mocks base method.
func (m *MockAggClient) UnDeployOAM(ctx context.Context, in *continuousdeployment.UnDeployOAMRequest, opts ...grpc.CallOption) (continuousdeployment.ContinuousDeploymentService_UnDeployOAMClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnDeployOAM", varargs...)
	ret0, _ := ret[0].(continuousdeployment.ContinuousDeploymentService_UnDeployOAMClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnDeployOAM indicates an expected call of UnDeployOAM.
func (mr *MockAggClientMockRecorder) UnDeployOAM(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnDeployOAM", reflect.TypeOf((*MockAggClient)(nil).UnDeployOAM), varargs...)
}

// UndoDeploy mocks base method.
func (m *MockAggClient) UndoDeploy(ctx context.Context, in *continuousdeployment.UndoDeployRequest, opts ...grpc.CallOption) (continuousdeployment.ContinuousDeploymentService_UndoDeployClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UndoDeploy", varargs...)
	ret0, _ := ret[0].(continuousdeployment.ContinuousDeploymentService_UndoDeployClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UndoDeploy indicates an expected call of UndoDeploy.
func (mr *MockAggClientMockRecorder) UndoDeploy(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UndoDeploy", reflect.TypeOf((*MockAggClient)(nil).UndoDeploy), varargs...)
}

// Update mocks base method.
func (m *MockAggClient) Update(ctx context.Context, in *constack.UpdateRequest, opts ...grpc.CallOption) (*constack.UpdateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Update", varargs...)
	ret0, _ := ret[0].(*constack.UpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockAggClientMockRecorder) Update(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAggClient)(nil).Update), varargs...)
}

// UpdateResource mocks base method.
func (m *MockAggClient) UpdateResource(ctx context.Context, in *core.UpdateResourceRequest) (*core.UpdateResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResource", ctx, in)
	ret0, _ := ret[0].(*core.UpdateResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateResource indicates an expected call of UpdateResource.
func (mr *MockAggClientMockRecorder) UpdateResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResource", reflect.TypeOf((*MockAggClient)(nil).UpdateResource), ctx, in)
}

// UpdateSubEnvTrafficMark mocks base method.
func (m *MockAggClient) UpdateSubEnvTrafficMark(cluster, subNamespace, trafficMark string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSubEnvTrafficMark", cluster, subNamespace, trafficMark)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSubEnvTrafficMark indicates an expected call of UpdateSubEnvTrafficMark.
func (mr *MockAggClientMockRecorder) UpdateSubEnvTrafficMark(cluster, subNamespace, trafficMark interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSubEnvTrafficMark", reflect.TypeOf((*MockAggClient)(nil).UpdateSubEnvTrafficMark), cluster, subNamespace, trafficMark)
}
