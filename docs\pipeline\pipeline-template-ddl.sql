CREATE TABLE `template` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '流水线模板名称',
  `type` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '流水线模板类型,包括：系统模板、自定义模板',
  `language` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '语言',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `project_id` bigint DEFAULT NULL COMMENT '项目ID, 可以为0',
  `user_id` bigint NOT NULL COMMENT '创建人',
  `chinese_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
  `stage_sequence` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联的stage顺序,按照逗号分隔',
  `employee_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户工号',
  `tekton_namespace` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'PipelineNamespace',
  `tekton_name` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'PipelineName',
  PRIMARY KEY (`id`),
  UNIQUE KEY `template_unique_key` (`name`,`project_id`,`language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='流水线模板';