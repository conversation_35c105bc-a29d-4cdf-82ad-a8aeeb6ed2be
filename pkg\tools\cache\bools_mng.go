package cache

import "sync"

type BoolsCache struct {
	mu    *sync.RWMutex
	cache map[string]bool
}

func NewBoolsCache() *BoolsCache {
	return &BoolsCache{
		mu:    &sync.RWMutex{},
		cache: make(map[string]bool),
	}
}

func (c *BoolsCache) Add(uid string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.cache[uid] = true
}

func (c *BoolsCache) Get(uid string) (has bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	has = c.cache[uid]
	return
}

func (c *BoolsCache) Del(uid string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	delete(c.cache, uid)
}
