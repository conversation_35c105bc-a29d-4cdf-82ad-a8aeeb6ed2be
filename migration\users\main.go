package main

import (
	"52tt.com/cicd/pkg/gitlab"
	"52tt.com/cicd/pkg/lark"

	"database/sql"
	"fmt"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	"strings"
	"time"
)

/*
对于用户表：iam/user
用于数据一次性全量插入，在重建、迁移用户表时可使用。请求自飞书、Gitlab当前最新的数据

使用方法，运行一次main函数，之后检查数据库
*/

func main() {
	type User struct {
		ID          int       `json:"id" sql:"AUTO_INCREMENT" gorm:"primary_key,column:id"`
		CreatedAt   time.Time `json:"createdAt" gorm:"column:created_at" sql:"DEFAULT:current_timestamp"`
		UpdatedAt   time.Time `json:"updatedAt" gorm:"column:updated_at" sql:"DEFAULT:current_timestamp"`
		Username    string    `gorm:"column:username;NOT NULL"`         // 用户名
		ChineseName string    `gorm:"column:chinese_name;NOT NULL"`     // 中文名
		Email       string    `gorm:"column:email;NOT NULL"`            // 邮箱
		EmployeeNo  string    `gorm:"column:employee_no;NOT NULL"`      // 工号
		LarkUnionID string    `gorm:"column:lark_union_id;NOT NULL"`    // 飞书id
		GitlabID    int       `gorm:"column:gitlab_id;default:0"`       // gitlab用户id
		Status      int       `gorm:"column:status;default:1;NOT NULL"` // 状态(0不可用,1正常)
	}

	feishuClient := lark.NewClient("********************", "m4rAc6pGodqPplQMZOTM30PVl0FBO0NJ")
	deps, err := feishuClient.GetDepsList("od-0a6ee50aa99abaf1f1b711ab75a8d9cf")
	if err != nil {
		fmt.Printf("从飞书获取研发中心部门信息出错：%v", err)
	}
	var fsUsers = make([]*larkcontact.User, 0)
	for _, dep := range deps {
		u, err := feishuClient.GetUsrsList(*dep.OpenDepartmentId)
		if err != nil {
			return
		}
		fsUsers = append(fsUsers, u...)
	}
	gitlabClient := gitlab.NewClient("https://gitlab.ttyuyin.com", "********************")
	users := &[]User{}
	for _, u := range fsUsers {
		user := User{
			Username:    strings.Split(*u.Email, "@")[0],
			ChineseName: *u.Name,
			Email:       *u.Email,
			EmployeeNo:  *u.EmployeeNo,
			LarkUnionID: *u.UnionId,
			GitlabID:    gitlabClient.GetUserIdByEmail(*u.Email),
		}
		*users = append(*users, user)
	}
	fmt.Printf("\n\n获取用户信息成功！共 %d 条用户信息\n\n>>>>>>>开始写入数据库", len(*users))

	//连接数据库
	db, err := sql.Open("mysql", "rd_dev:vRcfj3W#2nGdBeu@@tcp(10.64.240.14:3306)/iam")
	if err != nil {
		fmt.Printf("连接数据库失败！%v", err.Error())
	}
	defer db.Close()

	/*
		//单条记录插入
		countErr := 0
		for _, u := range *users {
			_, err := db.Exec("INSERT INTO user (username,chinese_name,email,employee_no,lark_union_id,gitlab_id) VALUES (?,?,?,?,?,?)",
				u.Username, u.ChineseName, u.Email, u.EmployeeNo, u.LarkUnionID, u.GitlabID)
			if err != nil {
				fmt.Printf(">>>插入出错，记录：%v-->%v\n", u, err)
				countErr++
				continue
			}
		}
		fmt.Printf("\n\n共插入 %d 条记录，其中 %d 条失败", len(*users), countErr)
		return
	*/

	// 批量插入
	var values []interface{}
	sql := "INSERT INTO user (username,chinese_name,email,employee_no,lark_union_id,gitlab_id) VALUES "

	for _, u := range *users {
		sql += "(?,?,?,?,?,?),"
		values = append(values, u.Username, u.ChineseName, u.Email, u.EmployeeNo, u.LarkUnionID, u.GitlabID)
	}

	// 移除最后一个逗号
	sql = sql[:len(sql)-1]

	stmt, err := db.Prepare(sql)
	if err != nil {
		fmt.Printf("准备批量插入失败！%v\n", err)
		return
	}
	defer stmt.Close()

	_, err = stmt.Exec(values...)
	if err != nil {
		fmt.Printf("批量插入失败！%v\n", err)
		return
	}

	fmt.Printf("\n\n共插入 %d 条记录", len(*users))
	return
}
