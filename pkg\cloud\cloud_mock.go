// Code generated by MockGen. DO NOT EDIT.
// Source: cloud.go

// Package cloud is a generated GoMock package.
package cloud

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockOption is a mock of Option interface.
type MockOption struct {
	ctrl     *gomock.Controller
	recorder *MockOptionMockRecorder
}

// MockOptionMockRecorder is the mock recorder for MockOption.
type MockOptionMockRecorder struct {
	mock *MockOption
}

// NewMockOption creates a new mock instance.
func NewMockOption(ctrl *gomock.Controller) *MockOption {
	mock := &MockOption{ctrl: ctrl}
	mock.recorder = &MockOptionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOption) EXPECT() *MockOptionMockRecorder {
	return m.recorder
}

// Apply mocks base method.
func (m *MockOption) Apply(arg0 *httpClient) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Apply", arg0)
}

// Apply indicates an expected call of Apply.
func (mr *MockOptionMockRecorder) Apply(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Apply", reflect.TypeOf((*MockOption)(nil).Apply), arg0)
}

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// CopyClusterResource mocks base method.
func (m *MockService) CopyClusterResource(cluster, originNamespace, targetNamespace string, resources []Resource) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyClusterResource", cluster, originNamespace, targetNamespace, resources)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CopyClusterResource indicates an expected call of CopyClusterResource.
func (mr *MockServiceMockRecorder) CopyClusterResource(cluster, originNamespace, targetNamespace, resources interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyClusterResource", reflect.TypeOf((*MockService)(nil).CopyClusterResource), cluster, originNamespace, targetNamespace, resources)
}

// CreateRoute mocks base method.
func (m *MockService) CreateRoute(cluster, subNamespace, serviceName string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRoute", cluster, subNamespace, serviceName)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRoute indicates an expected call of CreateRoute.
func (mr *MockServiceMockRecorder) CreateRoute(cluster, subNamespace, serviceName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRoute", reflect.TypeOf((*MockService)(nil).CreateRoute), cluster, subNamespace, serviceName)
}

// CreateSubEnv mocks base method.
func (m *MockService) CreateSubEnv(cluster, originNamespace, targetNamespace string, userId []string, labels map[string]interface{}) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubEnv", cluster, originNamespace, targetNamespace, userId, labels)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubEnv indicates an expected call of CreateSubEnv.
func (mr *MockServiceMockRecorder) CreateSubEnv(cluster, originNamespace, targetNamespace, userId, labels interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubEnv", reflect.TypeOf((*MockService)(nil).CreateSubEnv), cluster, originNamespace, targetNamespace, userId, labels)
}

// CreateSubEnvTrafficMark mocks base method.
func (m *MockService) CreateSubEnvTrafficMark(cluster, subNamespace, trafficMark string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubEnvTrafficMark", cluster, subNamespace, trafficMark)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubEnvTrafficMark indicates an expected call of CreateSubEnvTrafficMark.
func (mr *MockServiceMockRecorder) CreateSubEnvTrafficMark(cluster, subNamespace, trafficMark interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubEnvTrafficMark", reflect.TypeOf((*MockService)(nil).CreateSubEnvTrafficMark), cluster, subNamespace, trafficMark)
}

// DeleteSubEnvTrafficMark mocks base method.
func (m *MockService) DeleteSubEnvTrafficMark(cluster, subNamespace string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSubEnvTrafficMark", cluster, subNamespace)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSubEnvTrafficMark indicates an expected call of DeleteSubEnvTrafficMark.
func (mr *MockServiceMockRecorder) DeleteSubEnvTrafficMark(cluster, subNamespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSubEnvTrafficMark", reflect.TypeOf((*MockService)(nil).DeleteSubEnvTrafficMark), cluster, subNamespace)
}

// GetClusterList mocks base method.
func (m *MockService) GetClusterList(usersID []string, env string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterList", usersID, env)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterList indicates an expected call of GetClusterList.
func (mr *MockServiceMockRecorder) GetClusterList(usersID, env interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterList", reflect.TypeOf((*MockService)(nil).GetClusterList), usersID, env)
}

// GetClusterResources mocks base method.
func (m *MockService) GetClusterResources(cluster, namespace, kind, search string, page, size int64) (*ClusterResourceDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterResources", cluster, namespace, kind, search, page, size)
	ret0, _ := ret[0].(*ClusterResourceDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterResources indicates an expected call of GetClusterResources.
func (mr *MockServiceMockRecorder) GetClusterResources(cluster, namespace, kind, search, page, size interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterResources", reflect.TypeOf((*MockService)(nil).GetClusterResources), cluster, namespace, kind, search, page, size)
}

// GetNamespaceList mocks base method.
func (m *MockService) GetNamespaceList(cluster string, usersID []string, nsType string) ([]NamespaceDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNamespaceList", cluster, usersID, nsType)
	ret0, _ := ret[0].([]NamespaceDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamespaceList indicates an expected call of GetNamespaceList.
func (mr *MockServiceMockRecorder) GetNamespaceList(cluster, usersID, nsType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamespaceList", reflect.TypeOf((*MockService)(nil).GetNamespaceList), cluster, usersID, nsType)
}

// GetSubTrafficMarking mocks base method.
func (m *MockService) GetSubTrafficMarking(arg0, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubTrafficMarking", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubTrafficMarking indicates an expected call of GetSubTrafficMarking.
func (mr *MockServiceMockRecorder) GetSubTrafficMarking(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubTrafficMarking", reflect.TypeOf((*MockService)(nil).GetSubTrafficMarking), arg0, arg1)
}

// IsNameSpaceExist mocks base method.
func (m *MockService) IsNameSpaceExist(cluster, namespace string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsNameSpaceExist", cluster, namespace)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsNameSpaceExist indicates an expected call of IsNameSpaceExist.
func (mr *MockServiceMockRecorder) IsNameSpaceExist(cluster, namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsNameSpaceExist", reflect.TypeOf((*MockService)(nil).IsNameSpaceExist), cluster, namespace)
}

// UpdateSubEnvTrafficMark mocks base method.
func (m *MockService) UpdateSubEnvTrafficMark(cluster, subNamespace, trafficMark string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSubEnvTrafficMark", cluster, subNamespace, trafficMark)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSubEnvTrafficMark indicates an expected call of UpdateSubEnvTrafficMark.
func (mr *MockServiceMockRecorder) UpdateSubEnvTrafficMark(cluster, subNamespace, trafficMark interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSubEnvTrafficMark", reflect.TypeOf((*MockService)(nil).UpdateSubEnvTrafficMark), cluster, subNamespace, trafficMark)
}
