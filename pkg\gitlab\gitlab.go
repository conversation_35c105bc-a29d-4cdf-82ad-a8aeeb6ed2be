//go:generate mockgen -destination=gitlab_mock.go -package=gitlab -source=gitlab.go
package gitlab

import (
	"52tt.com/cicd/pkg/tools"
	"context"
	"encoding/json"
	"fmt"
	"regexp"

	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
	"github.com/xanzy/go-gitlab"
)

const (
	ProjectSearch        = "/api/v4/projects?search=%s"
	TagSearch            = "/api/v4/projects/%d/repository/tags"
	MrChanges            = "/api/v4//projects/%d/merge_requests/%d/changes"
	MrDiscussions        = "/api/v4/projects/%d/merge_requests/%d/discussions"
	MrResolveDiscussions = "/api/v4/projects/%d/merge_requests/%d/discussions/%s?resolved=true"
	MrAppendDiscussion   = "/api/v4/projects/%d/merge_requests/%d/discussions/%s/notes"
	MrDiscussionSearch   = "/api/v4/projects/%d/merge_requests/%d/discussions/%s"
	MrRequest            = "/api/v4/projects/%d/merge_requests/%d"
	MergeMrRequest       = "/api/v4/projects/%d/merge_requests/%d/merge"
	CommitsSearch        = "/api/v4/projects/%d/repository/commits"
	SetPipelineOfCommit  = "/api/v4/projects/%d/statuses/%s" //https://docs.gitlab.com/ee/api/commits.html#set-the-pipeline-status-of-a-commit
)

type Options struct {
	SCUserId   string `json:"sc-user-id"`   //sys_cicd userId
	SCAccLevel string `json:"sc-acc-level"` //sys_cicd access_level
}

type Client struct {
	token     string
	host      string
	options   Options
	gitClient *gitlab.Client
}

func (c *Client) getHeaders() map[string]string {
	headers := make(map[string]string, 0)
	headers["PRIVATE-TOKEN"] = c.token
	headers["Content-Type"] = "application/json"
	return headers
}

type Service interface {
	Changes(addr string, iid int64) ([]string, error)
	GetUserInfoById(usrId int64) map[string]interface{}
	GetUserInfoByEmail(email string) []interface{}
	GitMergeChanges(addr string, iid int64) ([]string, error)
	GetUserIdByEmail(email string) int
	GetBranches(repoAddr, search string, regex string) ([]string, error)
	GetBranch(repoAddr, branch string) (*gitlab.Branch, error)
	GetProject(repoAddr string) (*gitlab.Project, error)
	Branches(projectId int, search string, regex string) ([]string, error)
	AddWebhook(projectID int, url string) error
	AddSysUser(projectId, userId int) error
	AddDiscussion(param *DiscussionParameter, body fmt.Stringer) (string, error)
	AppendDiscussion(param *DiscussionParameter, body fmt.Stringer) error
	ResolveDiscussion(param *DiscussionParameter) error
	GetDiscussion(param *DiscussionParameter) (*DiscussionResp, error)
	IsCanBeMerged(projectId int, iid int64) bool
	GetGitlabEventMsg(gitEvents map[string]interface{}) (*GitEventResp, error)
	GetCommits(ctx context.Context, projectId int, params map[string]string) ([]gitlab.Commit, error)
	SetPipelineOfCommit(params *SetPipelineOfCommitParams) error
}

func NewClient(host, token string, opts ...map[string]string) *Client {
	o := &Options{}
	if len(opts) > 0 {
		for _, opt := range opts {
			for k, v := range opt {
				switch k {
				case "sc-user-id":
					o.SCUserId = v
				case "sc-acc-level":
					o.SCAccLevel = v
				default:
				}
			}
		}
	}
	gitClient, _ := gitlab.NewClient(token, gitlab.WithBaseURL(fmt.Sprintf("%s/api/v4", host)))
	c := &Client{
		token:     token,
		host:      host,
		options:   *o,
		gitClient: gitClient,
	}
	return c
}

type GitMrChanges struct {
	changes []Change
}

func (mrChanges *GitMrChanges) getAllChangedPath() []string {
	changes := mrChanges.changes
	allChanges := make([]string, 0)
	noDuplicatedChanges := make(map[string]int, 0)
	for _, change := range changes {
		oldPath, newPath := change.OldPath, change.NewPath
		allChanges = append(allChanges, oldPath, newPath)
	}

	for index, cr := range allChanges {
		if _, ok := noDuplicatedChanges[cr]; !ok {
			noDuplicatedChanges[cr] = index
		}
	}

	keys := make([]string, 0)
	for key := range noDuplicatedChanges {
		keys = append(keys, key)
	}

	return keys
}

func (c *Client) buildUrl(path string, projectId int, iid interface{}) string {
	newUrl := c.host + fmt.Sprintf(path, projectId, iid)
	return newUrl
}

// Changes 获取某个MR事件得changes信息
func (c *Client) Changes(addr string, iid int64) ([]string, error) {
	project, err := c.GetProject(addr)
	if err != nil {
		return nil, err
	}
	changesUrl := c.buildUrl(MrChanges, project.ID, iid)
	var gitMergeChange GitMergeChange

	httpClient := httpclient.NewClient(nil, nil)
	if err := httpClient.DoGet(changesUrl, &gitMergeChange); err != nil {
		fmt.Printf(fmt.Sprintf("获取git仓库%s信息错误", project.Name)+": %v", err)
		return nil, err
	}
	changes := &GitMrChanges{
		gitMergeChange.Changes,
	}
	allChanges := changes.getAllChangedPath()
	return allChanges, nil
}

// GetUserInfoById 通过gitlab userID 获取gitlab用户信息
func (c *Client) GetUserInfoById(usrId int64) map[string]interface{} {
	searchUserUrl := fmt.Sprintf("%s/api/v4/users/%d", c.host, usrId)
	call := httpclient.NewGET(searchUserUrl, nil, nil,
		map[string]string{"PRIVATE-TOKEN": c.token}, nil)

	r, err := call.MakeRequest()
	if err != nil {
		fmt.Printf("获取gitlab用户：%d 信息，发请求错误！%v", usrId, err)
		return nil
	}
	//解析响应
	var res map[string]interface{}
	if err := httpclient.RespAsStrMap(r, &res); err != nil {
		fmt.Printf("读据gitlab用户：%d 信息错误！%v", usrId, err)
		return nil
	}
	//email, ok := res["email"].(string)
	return res
}

func (c *Client) getEmail(userId int64) string {
	result := c.GetUserInfoById(userId)
	var email = ""
	if result["email"] != nil && result["email"] != "" {
		email = result["email"].(string)
	}
	return email
}

// GetUserInfoByEmail 通过gitlab 注册邮箱email（非public email，非commit email），获取用户信息
func (c *Client) GetUserInfoByEmail(email string) []interface{} {
	if !checkEmail(email) {
		return nil
	}
	searchEmailUrl := fmt.Sprintf("%s/api/v4/users", c.host)
	call := httpclient.NewGET(searchEmailUrl, nil, map[string]string{"search": email},
		map[string]string{"PRIVATE-TOKEN": c.token}, nil)

	r, err := call.MakeRequest()
	if err != nil {
		fmt.Printf("获取gitlab用户：%s 信息，发请求错误！%v", email, err)
		return nil
	}
	//解析响应
	var res []interface{}
	if err := httpclient.RespAsArrMap(r, &res); err != nil {
		fmt.Printf("读据gitlab用户：%s 信息错误！%v", email, err)
		return nil
	}
	return res
}

func (c *Client) GitMergeChanges(addr string, iid int64) ([]string, error) {
	project, err := c.GetProject(addr)
	if err != nil {
		return nil, err
	}
	changesUrl := c.buildUrl(MrChanges, project.ID, iid)
	var gitMergeChange GitMergeChange

	httpClient := httpclient.NewClient(nil, nil, c.getHeaders())

	if err := httpClient.DoGet(changesUrl, &gitMergeChange); err != nil {
		fmt.Printf(fmt.Sprintf("获取git仓库%s分支信息错误", project.Name)+": %v", err)
		return nil, err
	}
	changes := &GitMrChanges{
		gitMergeChange.Changes,
	}
	allChanges := changes.getAllChangedPath()
	return allChanges, nil
}

func (c *Client) getMergeChangedFiles(gitHttpUrl string, iID int64) []string {
	mrChanges, err := c.GitMergeChanges(gitHttpUrl, iID)
	if err != nil {
		fmt.Printf("获取merge事件文件变更信息错误: %v\n", err)
		return nil
	}
	return mrChanges
}

func (c *Client) IsCanBeMerged(projectId int, iid int64) bool {
	resp, err := c.GetMrRequest(projectId, iid)
	if err != nil {
		fmt.Printf("获取merge request信息错误: %v\n", err)
		return false
	}
	data, _ := json.Marshal(&resp)
	log.Infof("project[%d]iid[%d]merge request状态信息: %s", projectId, iid, string(data))
	return resp.MergeStatus == "can_be_merged" || resp.MergeStatus == "preparing"
}

// GetUserIdByEmail 通过gitlab 注册邮箱email（非public email，非commit email），获取用户id
func (c *Client) GetUserIdByEmail(email string) int {
	info := c.GetUserInfoByEmail(email)
	if info == nil || len(info) == 0 {
		return 0
	}
	temp := info[0].(map[string]interface{})
	var gitlabId = 0
	if temp["id"] != nil && temp["id"] != "" {
		gitlabId = floatToInt64(temp["id"])
	}
	return gitlabId
}

// GetBranches  获取git仓库的分支
func (c *Client) GetBranches(repoAddr, search string, regex string) ([]string, error) {
	project, err := c.GetProject(repoAddr)
	if err != nil {
		return nil, err
	}
	a, err := c.Branches(project.ID, search, regex)
	return a, err
}

func (c *Client) GetBranch(repoAddr, branch string) (*gitlab.Branch, error) {
	project, err := c.GetProject(repoAddr)
	if err != nil {
		return nil, err
	}
	branchInfo, _, err := c.gitClient.Branches.GetBranch(project.ID, branch)
	if err != nil {
		return nil, err
	}
	return branchInfo, nil
}

// GetProject 获取 gitlab project信息
func (c *Client) GetProject(repoAddr string) (*gitlab.Project, error) {
	repoPrefix, err := getRepoGroupAndRepoName(repoAddr)
	if err != nil {
		log.Errorf("获取代码库的组名和代码库名称错误,错误原因:%v", err)
		return nil, err
	}
	// Get the project search path
	project, _, err := c.gitClient.Projects.GetProject(repoPrefix, nil)
	if err != nil {
		log.Errorf("查询gitlab项目错误,repoPrefix:[%s],错误原因:%v", repoPrefix, err)
		return nil, err
	}
	return project, nil
}

func (c *Client) Branches(projectId int, search string, regex string) ([]string, error) {
	gitBranches := make([]string, 0)
	//默认20页，每页请求50条记录,最多拿2000个分支
	for i := 1; i <= 20; i++ {
		opts := &gitlab.ListBranchesOptions{
			ListOptions: gitlab.ListOptions{Page: i, PerPage: 100}, // 设置每个请求获取的分支数量
			Search:      &search,
		}
		branches, _, err := c.gitClient.Branches.ListBranches(projectId, opts)
		if err != nil {
			log.Errorf("查询id为[%d]的代码库分支错误,原因:%v", projectId, err)
			return nil, err
		}

		branchList := tools.MapTo(branches, func(branch *gitlab.Branch) string {
			return branch.Name
		})
		if len(branchList) > 0 {
			gitBranches = append(gitBranches, branchList...)
		} else {
			break
		}
	}
	if regex != "" {
		regexBranches := make([]string, 0)
		for _, branch := range gitBranches {
			match, _ := regexp.MatchString(regex, branch)
			if match {
				regexBranches = append(regexBranches, branch)
			}
		}
		return regexBranches, nil
	}
	return gitBranches, nil
}

func (c *Client) AddWebhook(projectID int, url string) error {
	hooks, _, err := c.gitClient.Projects.ListProjectHooks(projectID, nil)
	if err != nil {
		log.Errorf("查询代码库webhooks错误: %v", err)
		return err
	}
	for _, hook := range hooks {
		if hook.URL == url {
			log.Warnf("webhook地址 %s 已经存在，不再重复添加", url)
			return nil
		}
	}
	opts := &gitlab.AddProjectHookOptions{
		URL:                 gitlab.String(url),
		PushEvents:          gitlab.Bool(true),
		MergeRequestsEvents: gitlab.Bool(true),
	}
	_, _, err = c.gitClient.Projects.AddProjectHook(projectID, opts)
	if err != nil {
		log.Errorf("给代码库添加webhook地址错误:%v", err)
		return err
	}
	return nil
}

func (c *Client) AddSysUser(projectId, userId int) error {
	accessLevel := gitlab.DeveloperPermissions
	memberOpts := &gitlab.AddProjectMemberOptions{
		UserID:      userId,
		AccessLevel: &accessLevel, // Or any other access level you want to set
	}
	projectMember, _, _ := c.gitClient.ProjectMembers.GetProjectMember(projectId, userId, nil)
	if projectMember != nil {
		return nil
	}
	_, _, err := c.gitClient.ProjectMembers.AddProjectMember(projectId, memberOpts)
	if err != nil {
		log.Errorf("给代码库添加用户sysUser错误:%v", err)
		return err
	}
	return nil
}

func (c *Client) AddDiscussion(param *DiscussionParameter, body fmt.Stringer) (string, error) {
	log.Debugf("添加评论参数:%+v", param)
	discussionUrl := c.buildUrl(MrDiscussions, param.ProjectID, param.MrID)
	discussion := Discussion{
		Body: body.String(),
	}
	var result map[string]interface{}
	data, _ := json.Marshal(&discussion)
	httpClient := httpclient.NewClient(data, nil, c.getHeaders())
	if err := httpClient.DoPost(discussionUrl, &result); err != nil {
		log.Errorf("新增评论错误:%v", err)
		return "", err
	}
	data1, _ := json.Marshal(result)
	log.Debugf("新增评论返回结果:%s", string(data1))
	id, ok := result["id"]
	if !ok {
		return "", fmt.Errorf("新增评论返回结果错误:%s", string(data1))
	}
	return id.(string), nil
}

func (c *Client) AppendDiscussion(param *DiscussionParameter, body fmt.Stringer) error {
	log.Debugf("追加评论参数:%+v, body: %s", param, body)
	discussionUrl := c.host + fmt.Sprintf(MrAppendDiscussion, param.ProjectID, param.MrID, param.ID)
	discussion := Discussion{
		Body: body.String(),
	}
	var result map[string]interface{}
	data, _ := json.Marshal(&discussion)
	httpClient := httpclient.NewClient(data, nil, c.getHeaders())
	if err := httpClient.DoPost(discussionUrl, &result); err != nil {
		log.Errorf("追加评论错误:%v", err)
		return err
	}
	data1, _ := json.Marshal(result)
	log.Debugf("追加评论返回结果:%s", string(data1))
	return nil
}

func (c *Client) ResolveDiscussion(param *DiscussionParameter) error {
	log.Debugf("解决评论参数:%+v", param)
	discussionUrl := c.host + fmt.Sprintf(MrResolveDiscussions, param.ProjectID, param.MrID, param.ID)
	headers := c.getHeaders()
	httpClient := httpclient.NewClient(nil, nil, headers)

	var result map[string]interface{}
	if err := httpClient.DoPut(discussionUrl, &result); err != nil {
		log.Errorf("解决gitlab评论错误:%v", err)
		return err
	}
	data1, _ := json.Marshal(result)
	log.Debugf("解决评论返回结果:%s", string(data1))
	return nil
}

func (c *Client) GetDiscussion(param *DiscussionParameter) (*DiscussionResp, error) {
	discussionUrl := c.host + fmt.Sprintf(MrDiscussionSearch, param.ProjectID, param.MrID, param.ID)
	httpClient := httpclient.NewClient(nil, nil, c.getHeaders())

	var resp *DiscussionResp
	if err := httpClient.DoGet(discussionUrl, &resp); err != nil {
		log.Errorf("解决gitlab评论错误:%v", err)
		return nil, err
	}
	return resp, nil
}

func (c *Client) MergeMrRequest(mr *MergeRequest) error {
	mrUrl := c.buildUrl(MergeMrRequest, mr.ProjectId, mr.Id)
	var result map[string]interface{}
	data, _ := json.Marshal(&mr)
	httpClient := httpclient.NewClient(data, nil, c.getHeaders())
	if err := httpClient.DoPut(mrUrl, &result); err != nil {
		log.Errorf("合并merge request错误:%v", err)
		return err
	}
	if message, ok := result["message"]; ok {
		return fmt.Errorf("合并merge request错误:%v", message)
	}
	return nil
}

func (c *Client) GetMrRequest(projectId int, mrId int64) (*MergeResponse, error) {
	mrUrl := c.buildUrl(MrRequest, projectId, mrId)
	httpClient := httpclient.NewClient(nil, nil, c.getHeaders())

	var result MergeResponse
	if err := httpClient.DoGet(mrUrl, &result); err != nil {
		log.Errorf("获取merge request信息错误:%v", err)
		return nil, err
	}
	return &result, nil
}

func (c *Client) GetCommits(ctx context.Context, projectId int, params map[string]string) ([]gitlab.Commit, error) {
	url := c.host + fmt.Sprintf(CommitsSearch, projectId)
	httpClient := httpclient.NewClient(nil, params, c.getHeaders())

	var result []gitlab.Commit
	if err := httpClient.DoGet(url, &result); err != nil {
		log.ErrorWithCtx(ctx, "[GetCommits] get commits error: %v", err)
		return nil, err
	}

	return result, nil
}

func (c *Client) SetPipelineOfCommit(params *SetPipelineOfCommitParams) error {
	setPipelineOfCommitUrl := c.buildUrl(SetPipelineOfCommit, params.ProjectID, params.ShaID)
	var result map[string]interface{}
	req := map[string]string{
		"name":  params.Name,
		"state": params.State.String(),
		"ref":   params.Ref,
	}
	httpClient := httpclient.NewClient(nil, req, c.getHeaders())
	if err := httpClient.DoPost(setPipelineOfCommitUrl, &result); err != nil {
		log.Errorf("post SetPipelineOfCommit err:%v", err)
		return err
	}
	log.Infof("SetPipelineOfCommit url:%s, params:%v, result: %v", setPipelineOfCommitUrl, params, result)
	if message, ok := result["message"]; ok {
		return fmt.Errorf("SetPipelineOfCommit err:%v", message)
	}
	return nil
}
