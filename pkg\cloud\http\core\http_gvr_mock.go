// Code generated by MockGen. DO NOT EDIT.
// Source: http_gvr.go

// Package core is a generated GoMock package.
package core

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockGVRHTTPClient is a mock of GVRHTTPClient interface.
type MockGVRHTTPClient struct {
	ctrl     *gomock.Controller
	recorder *MockGVRHTTPClientMockRecorder
}

// MockGVRHTTPClientMockRecorder is the mock recorder for MockGVRHTTPClient.
type MockGVRHTTPClientMockRecorder struct {
	mock *MockGVRHTTPClient
}

// NewMockGVRHTTPClient creates a new mock instance.
func NewMockGVRHTTPClient(ctrl *gomock.Controller) *MockGVRHTTPClient {
	mock := &MockGVRHTTPClient{ctrl: ctrl}
	mock.recorder = &MockGVRHTTPClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGVRHTTPClient) EXPECT() *MockGVRHTTPClientMockRecorder {
	return m.recorder
}

// CreateResource mocks base method.
func (m *MockGVRHTTPClient) CreateResource(ctx context.Context, in *CreateResourceRequest) (*CreateResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateResource", ctx, in)
	ret0, _ := ret[0].(*CreateResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateResource indicates an expected call of CreateResource.
func (mr *MockGVRHTTPClientMockRecorder) CreateResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateResource", reflect.TypeOf((*MockGVRHTTPClient)(nil).CreateResource), ctx, in)
}

// DeleteResource mocks base method.
func (m *MockGVRHTTPClient) DeleteResource(ctx context.Context, in *DeleteResourceRequest) (*DeleteResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteResource", ctx, in)
	ret0, _ := ret[0].(*DeleteResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteResource indicates an expected call of DeleteResource.
func (mr *MockGVRHTTPClientMockRecorder) DeleteResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteResource", reflect.TypeOf((*MockGVRHTTPClient)(nil).DeleteResource), ctx, in)
}

// FindAnyList mocks base method.
func (m *MockGVRHTTPClient) FindAnyList(ctx context.Context, in AnyListReq, out any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAnyList", ctx, in, out)
	ret0, _ := ret[0].(error)
	return ret0
}

// FindAnyList indicates an expected call of FindAnyList.
func (mr *MockGVRHTTPClientMockRecorder) FindAnyList(ctx, in, out interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAnyList", reflect.TypeOf((*MockGVRHTTPClient)(nil).FindAnyList), ctx, in, out)
}

// GetResource mocks base method.
func (m *MockGVRHTTPClient) GetResource(ctx context.Context, in *GetResourceRequest) (*GetResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResource", ctx, in)
	ret0, _ := ret[0].(*GetResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResource indicates an expected call of GetResource.
func (mr *MockGVRHTTPClientMockRecorder) GetResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResource", reflect.TypeOf((*MockGVRHTTPClient)(nil).GetResource), ctx, in)
}

// ListResource mocks base method.
func (m *MockGVRHTTPClient) ListResource(ctx context.Context, in *ListResourceRequest) (*ListResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListResource", ctx, in)
	ret0, _ := ret[0].(*ListResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListResource indicates an expected call of ListResource.
func (mr *MockGVRHTTPClientMockRecorder) ListResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResource", reflect.TypeOf((*MockGVRHTTPClient)(nil).ListResource), ctx, in)
}

// SetV2Host mocks base method.
func (m *MockGVRHTTPClient) SetV2Host(hostV2 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetV2Host", hostV2)
}

// SetV2Host indicates an expected call of SetV2Host.
func (mr *MockGVRHTTPClientMockRecorder) SetV2Host(hostV2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetV2Host", reflect.TypeOf((*MockGVRHTTPClient)(nil).SetV2Host), hostV2)
}

// UpdateResource mocks base method.
func (m *MockGVRHTTPClient) UpdateResource(ctx context.Context, in *UpdateResourceRequest) (*UpdateResourceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResource", ctx, in)
	ret0, _ := ret[0].(*UpdateResourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateResource indicates an expected call of UpdateResource.
func (mr *MockGVRHTTPClientMockRecorder) UpdateResource(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResource", reflect.TypeOf((*MockGVRHTTPClient)(nil).UpdateResource), ctx, in)
}
