// Code generated by MockGen. DO NOT EDIT.
// Source: rdlog.go

// Package rdlog is a generated GoMock package.
package rdlog

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// GetPodLog mocks base method.
func (m *MockService) GetPodLog(ctx context.Context, rd SearchRdLog) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodLog", ctx, rd)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodLog indicates an expected call of GetPodLog.
func (mr *MockServiceMockRecorder) GetPodLog(ctx, rd interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodLog", reflect.TypeOf((*MockService)(nil).GetPodLog), ctx, rd)
}
