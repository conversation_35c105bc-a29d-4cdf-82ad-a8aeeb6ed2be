package constants

// EnumEnvTarget 环境目标类型枚举值
type EnumEnvTarget int8

func (e EnumEnvTarget) Value() int8 {
	return int8(e)
}

// String 返回枚举值的字符串，内容为 EnvTargetType 定义的字符串
func (e EnumEnvTarget) String() string {
	return e.ToEnvTargetType().String()
}

func (e EnumEnvTarget) ToEnvTargetType() EnvTargetType {
	v, ok := enumEnvTargetStringMapping[e]
	if ok {
		return v
	}
	return EnvTargetType("unknown")
}

var enumEnvTargetStringMapping = map[EnumEnvTarget]EnvTargetType{
	EnumEnvTargetOrigin: ORIGIN,
	EnumEnvTargetSub:    SUB,
	EnumEnvTargetSubV2:  SUB_V2,
}

const (
	// EnumEnvTargetUnknown 未知环境
	EnumEnvTargetUnknown EnumEnvTarget = 0
	// EnumEnvTargetOrigin 基准环境
	EnumEnvTargetOrigin EnumEnvTarget = 1
	// EnumEnvTargetSub 子环境
	EnumEnvTargetSub EnumEnvTarget = 2
	// EnumEnvTargetSubV2 子环境 2.0
	EnumEnvTargetSubV2 EnumEnvTarget = 3
)

type EnvTargetType string

func (e EnvTargetType) String() string {
	return string(e)
}

func (e EnvTargetType) Value() int8 {
	return e.ToEnumEnvTarget().Value()
}

var envTargetTypeEnumMapping = map[EnvTargetType]EnumEnvTarget{
	ORIGIN: EnumEnvTargetOrigin,
	SUB:    EnumEnvTargetSub,
	SUB_V2: EnumEnvTargetSubV2,
}

func (e EnvTargetType) ToEnumEnvTarget() EnumEnvTarget {
	v, ok := envTargetTypeEnumMapping[e]
	if ok {
		return v
	}
	return EnumEnvTargetUnknown
}

func (e EnvTargetType) IsValid() bool {
	_, ok := envTargetTypeEnumMapping[e]
	return ok
}

func (e EnvTargetType) Chinese() string {
	switch e {
	case ORIGIN:
		return "基准环境"
	case SUB:
		return "子环境"
	case SUB_V2:
		return "子环境 2.0"
	default:
		return ""
	}
}

const (
	ORIGIN EnvTargetType = "origin"
	// Use SUB_V2 instead.
	SUB EnvTargetType = "sub"
	// SUB_V2 子环境 2.0, 具体可查阅 https://q9jvw0u5f5.feishu.cn/wiki/K3Pvwqiv7i82SQk4WtScIlaCnCd
	SUB_V2 EnvTargetType = "sub_v2"
)

// TransferToEnvTargetType 只用于 int8 类型转换为 EnvTargetType 类型
// 如果字段本身是 EnumEnvTarget，则直接调用 EnumEnvTarget.ToEnvTargetType 方法
func TransferToEnvTargetType(value int8) EnvTargetType {
	enumEnvTarget := EnumEnvTarget(value)
	return enumEnvTarget.ToEnvTargetType()
}
