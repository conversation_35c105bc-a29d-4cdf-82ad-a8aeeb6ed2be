package conf

import (
	"52tt.com/cicd/pkg/config"
)

var ScheduleConfig *Config

type Config struct {
	config.Default
	ExDataBase ExDataBase `mapstructure:"exdatabase"`
	HarborCfg  HarborCfg  `mapstructure:"harbor"`
	Schedule   *Schedule  `mapstructure:"schedule"`
}

type Schedule struct {
	Cron     string     `json:"cron"`   // cron表达式
	Name     string     `json:"name"`   // 任务名称
	Active   bool       `json:"active"` //开关
	Triggers []Schedule `mapstructure:"trigger"`
}

type ExDataBase struct {
	DbDeploy string `json:"dbdeploy"` // deploy数据库
	DbApp    string `json:"dbapp"`    // app数据库
}

type HarborCfg struct {
	Host     string `json:"host"`
	AuthName string `json:"authName"`
	AuthPsw  string `json:"authPsw"`
}

func LoadConfig(path string) (*Config, error) {
	var c Config
	if err := config.NewConfig(path, &c); err != nil {
		return nil, err
	}

	ScheduleConfig = &c
	return &c, nil
}
