package gitlab

import (
	"52tt.com/cicd/pkg/httpclient"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGetUserInfoByEmail(t *testing.T) {
	email := "<EMAIL>"
	if !checkEmail(email) {
		return
	}
	url := fmt.Sprintf("https://gitlab.ttyuyin.com/api/v4/users")
	call := httpclient.NewGET(url, nil, map[string]string{"search": email},
		map[string]string{"PRIVATE-TOKEN": "********************"}, nil)

	r, err := call.MakeRequest()
	if err != nil {
		fmt.Printf("获取gitlab用户：%s 信息，发请求出错！%v", email, err)
		return
	}
	//解析响应
	var res []interface{}
	if err := httpclient.RespAsArrMap(r, &res); err != nil {
		fmt.Printf("读据gitlab用户：%s 信息出错！%v", email, err)
		return
	}
	fmt.Print(res)
}

func TestGetUserIdByEmail(t *testing.T) {
	email := "<EMAIL>"
	client := NewClient("https://gitlab.ttyuyin.com", "********************")
	info := client.GetUserInfoByEmail(email)
	if info == nil || len(info) == 0 {
		fmt.Printf("gitlabId = %d", 0)
	}
	temp := info[0].(map[string]interface{})
	var gitlabId = 0
	if temp["id"] != nil && temp["id"] != "" {
		gitlabId = floatToInt64(temp["id"])
	}
	fmt.Print(gitlabId)
}

type Notes struct {
	Body string `json:"body"`
}

func (n Notes) String() string {
	return n.Body
}

// test add discussion to merge request
func TestAddDiscussionToMergeRequest(t *testing.T) {
	client := NewClient("https://gitlab.ttyuyin.com", "********************")
	parameter := DiscussionParameter{ProjectID: 2990, MrID: 201}
	note := Notes{Body: "苹果"}
	discussionId, err := client.AddDiscussion(&parameter, note)
	t.Log("discussion id is ", discussionId)
	if err != nil {
		assert.Error(t, err, "add discussion to merge request error")
	}
	t.Log("add discussion to merge request success")
}

// unit test for GetUserInfoById
func TestGetUserInfoById(t *testing.T) {
	gitlabId := int64(624)
	client := NewClient("https://gitlab.ttyuyin.com", "********************")
	info := client.GetUserInfoById(gitlabId)
	if info == nil || len(info) == 0 {
		fmt.Printf("gitlabId = %d", 0)
	}
	var email = ""
	if info["email"] != nil && info["email"] != "" {
		email = info["email"].(string)
	}
	assert.Equal(t, "<EMAIL>", email, "they should be equal")
}

// unit test for GetProject
func TestGetProject(t *testing.T) {
	client := NewClient("https://gitlab.ttyuyin.com", "********************")
	project, err := client.GetProject("https://gitlab.ttyuyin.com/limsh/personal-test.git")
	assert.NoError(t, err)
	assert.Equal(t, "personal-test", project.Name, "project name should be equal")
}

// test add discussion to merge request
func TestSetPipelineOfCommit(t *testing.T) {
	client := NewClient("https://gitlab.ttyuyin.com", "********************")
	parameter := &SetPipelineOfCommitParams{ProjectID: 3167, ShaID: "5da4ee90a71f808fed3ee6a3ff5181b4c2922c1a", Name: "MR流水线", State: "success", Ref: "feature/hzx", TargetUrl: "https://gitlab.ttyuyin.com/limsh/personal-test/-/pipelines", Description: "test"}
	err := client.SetPipelineOfCommit(parameter)
	if err != nil {
		assert.Error(t, err, "SetPipelineOfCommit error")
	}
	t.Log("SetPipelineOfCommit success")
}
