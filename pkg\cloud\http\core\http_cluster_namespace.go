//go:generate mockgen -destination=http_sub_namespace_mock.go -package=core -source=http_sub_namespace.go
package core

import (
	"context"
)

type ClusterNamespaceClient interface {
	ListCluster(ctx context.Context, in *ListClusterRequest) (*ListClusterResponse, error)
	ListClusterNamespace(ctx context.Context, in *ListClusterNamespaceRequest) (*ListClusterNamespaceResponse, error)
}

type ListClusterRequest struct {
	// 项目管理员邮箱列表，容器云那边把它当成用户唯一标识，但传参字段取名 userId，感觉不好...
	Emails []string `json:"userId"`
	// 枚举类型(production , dev , testing , preview)
	Env string `json:"env"`
}

type ListClusterResponse struct {
	BaseResponse

	Data ClustersData `json:"data"`
}

type ClustersData struct {
	ClusterList []string `json:"clusterList"`
}

type ListClusterNamespaceRequest struct {
	Cluster string `json:"cluster"`
	// 项目管理员邮箱列表，容器云那边把它当成用户唯一标识，但传参字段取名 userId，感觉不好...
	Emails []string `json:"userId"`
	// 命名空间类型, 枚举类型[sub(子环境) , origin(基准环境)]，不传默认为全部类型
	Type string `json:"type"`
}

type ListClusterNamespaceResponse struct {
	BaseResponse

	Data ClusterNamespaceData `json:"data"`
}

type ClusterNamespaceData struct {
	NamespaceList []ClusterNamespace `json:"namespaceList"`
}

type ClusterNamespace struct {
	Namespace   string `json:"namespace"`
	TrafficMark string `json:"trafficMark"`
	Describe    string `json:"describe"`
	Parent      string `json:"parent"`
}

func (d *ClusterNamespace) BasicNamespace() string {
	return d.Parent
}

func (d *ClusterNamespace) CurrentNamespace() string {
	return d.Namespace
}

func (d *ClusterNamespace) IsOrigin() bool {
	return d.Parent == ""
}
