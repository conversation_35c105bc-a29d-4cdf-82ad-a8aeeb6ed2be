package set

import "fmt"

type settable interface {
	~string | ~int | ~int32 | ~int64
}

type Set[T settable] struct {
	container map[T]struct{}
}

func New[T settable]() *Set[T] {
	newSet := &Set[T]{container: make(map[T]struct{})}
	return newSet
}

func With[T settable](elements ...T) *Set[T] {
	newSet := New[T]()
	for _, item := range elements {
		newSet.Add(item)
	}
	return newSet
}

func Of[T settable](arr []T) *Set[T] {
	newSet := New[T]()
	for _, item := range arr {
		newSet.Add(item)
	}
	return newSet
}

func (s *Set[T]) Difference(other *Set[T]) *Set[T] {
	diff := New[T]()
	for elem := range s.container {
		if !other.Exists(elem) {
			diff.Add(elem)
		}
	}
	return diff
}

func (s *Set[T]) Exists(key T) bool {
	_, exists := s.container[key]
	return exists
}

func (s *Set[T]) Contains(key T) bool {
	_, exists := s.container[key]
	return exists
}

func (s *Set[T]) Add(key T) {
	s.container[key] = struct{}{}
}

func (s *Set[T]) Items() map[T]struct{} {
	return s.container
}

func (s *Set[T]) Slice() []T {
	arr := make([]T, 0)
	for key, _ := range s.container {
		arr = append(arr, key)
	}
	return arr
}

func (s *Set[T]) Remove(key T) error {
	_, exists := s.container[key]
	if !exists {
		return fmt.Errorf("item doesn't exist in set")
	}
	delete(s.container, key)
	return nil
}

func (s *Set[T]) Size() int {
	return len(s.container)
}
