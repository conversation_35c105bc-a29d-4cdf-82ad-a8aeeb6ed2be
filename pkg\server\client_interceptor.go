package server

import (
	"context"

	"52tt.com/cicd/pkg/log"
	"google.golang.org/grpc"

	cctx "52tt.com/cicd/pkg/context"
)

var acceptableHeaders = map[string]struct{}{
	"x-qw-traffic-mark": {},
}

func UnaryClientInterceptor(ctx context.Context, method string, req, reply any, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	// 如果调用的上下文中，携带有用户信息，透传到下游
	userInfo := cctx.GetUserinfo(ctx)
	newCtx := cctx.InjectRequestInfo(ctx, &cctx.RequestInfo{
		UserInfo:  *userInfo,
		RequestID: cctx.RequestID(ctx),
	})
	token := cctx.Token(ctx)
	if token != "" {
		newCtx = cctx.InjectToken(newCtx, token)
	}
	mark := cctx.TrafficMark(ctx)
	if mark != "" {
		newCtx = cctx.InjectTrafficMark(newCtx, mark)
	}

	log.InfoWithCtx(newCtx, "gRPC send Request: %+v - To: %+v", req, method)
	err := invoker(newCtx, method, req, reply, cc, opts...) // invoking RPC method
	if err != nil {
		log.ErrorWithCtx(newCtx, "gRPC error : %+v", err)
	}
	return err
}
