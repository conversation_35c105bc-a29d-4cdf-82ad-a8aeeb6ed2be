package testdb

import (
	sqle "github.com/dolthub/go-mysql-server"
	"github.com/dolthub/go-mysql-server/memory"
	"github.com/dolthub/go-mysql-server/server"
)

func New(dbName string) *MemoryMySQL {
	m := &MemoryMySQL{
		dbName: dbName,
	}
	m.init()
	return m
}

type MemoryMySQL struct {
	dbName string
	s      *server.Server
	err    error
}

func (m *MemoryMySQL) init() {
	engine := sqle.NewDefault(memory.NewDBProvider(memory.NewDatabase(m.dbName)))
	config := server.Config{
		Protocol: "tcp",
		Address:  "127.0.0.1:13306",
	}
	m.s, m.err = server.NewDefaultServer(config, engine)
}

func (m *MemoryMySQL) Start() error {
	if m.err != nil {
		return m.err
	}

	err := m.s.Start()
	return err
}

func (m *MemoryMySQL) Close() error {
	return m.s.<PERSON>()
}
