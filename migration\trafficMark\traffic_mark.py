# -*- coding:utf-8 -*-
from configparser import ConfigParser
import logging
import json
import requests
import pymysql

logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s]:[%(levelname)s]%(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)

logger = logging

project_list = [{"new_project": 12, "project_names": ("TT后台(cpp)", "quicksilver")}]


# 映射关系 {新sentinel项目id：旧sentinel项目数组}


def get_test_db_config():
    """ example: conf/sentinel.ini
    [mysql_testing]
    host=
    port=
    user=
    password=
    database=

    [mysql_new_sentinel]
    host=
    port=
    user=
    password=
    database=
    """
    cp = ConfigParser()
    cp.read("../conf/sentinel.ini")
    con_engine = pymysql.connect(host=cp.get("mysql_testing", "host"), user=cp.get("mysql_testing", "user"),
                                 password=cp.get("mysql_testing", "password"), database=cp.get("mysql_testing"
                                                                                               , "database"),
                                 port=int(cp.get("mysql_testing", "port")), charset='utf8')
    # 使用cursor()方法获取游标
    cursor = con_engine.cursor()
    return con_engine, cursor


def get_prod_db_config():
    cp = ConfigParser()
    cp.read("../conf/sentinel.ini")

    con_engine = pymysql.connect(host=cp.get("mysql_new_sentinel", "host"), user=cp.get("mysql_new_sentinel", "user"),
                                 password=cp.get("mysql_new_sentinel", "password"), database=cp.get("mysql_new_sentinel"
                                                                                                    , "database"),
                                 port=int(cp.get("mysql_new_sentinel", "port")), charset='utf8')

    # 使用cursor()方法获取游标
    cursor = con_engine.cursor()
    return con_engine, cursor


def post_cloud_traffic_mark(cluster, subNamespace, trafficMark):
    cloud_url = 'https://cloud.ttyuyin.com/api/v1/openapi/cicd/cluster/namespace/traffic-mark/add'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) '
                      'Chrome/91.0.4472.114 Safari/537.36',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json;charset=UTF-8',
        'X-TOKEN': 'Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663'
    }
    payload = {
        "cluster": cluster,
        "subNamespace": subNamespace,
        'trafficMark': trafficMark
    }
    r = requests.post(f'{cloud_url}', headers=headers, json=payload)
    content = json.loads(r.content)
    if content['code'] != 0:
        logger.error(f"集群：{cluster}，命名空间{subNamespace}，流量标记{trafficMark}请求状态非200，{content['message']}")
    else:
        logger.error(f"集群：{cluster}，命名空间{subNamespace}，流量标记{trafficMark}请求成功")


def main():
    test_db_conn, test_db_cursor = get_test_db_config()
    prod_db_conn, prod_db_cursor = get_prod_db_config()
    for project in project_list:
        get_old_traffic_mark_sql = f"select b.name, d.name, a.namespace, a.mark from sub_env a, org_team b, deploy_k8s_env c, assets_k8scluster d where a.team_id=b.id and a.base_env_id=c.id and c.k8s_cluster_id=d.id and d.name not like '%tt-testing%' and a.enabled=1 and b.name in {project['project_names']} order by a.team_id;"
        test_db_cursor.execute(get_old_traffic_mark_sql)
        results = test_db_cursor.fetchall()
        for row in results:
            project_name = row[0]  # 项目名
            cluster = row[1]  # 集群名
            namespace = row[2]  # 命名空间
            mark = row[3]  # 流量标记
            print(project_name, cluster, namespace, mark)
            # 写入数据库
            post_cloud_traffic_mark(cluster, namespace, mark)

            select_query = "SELECT id FROM traffic_mark WHERE name = '%s' AND env = 2" % mark
            prod_db_cursor.execute(select_query)

            # 获取流量标记查询结果
            result = prod_db_cursor.fetchone()

            if result:
                inserted_id = result[0]  # 获取ID值
                print("获取流量标记查询结果记录存在，ID为：", inserted_id)
            else:
                print("获取流量标记查询结果记录不存在")
                insert_traffic_mark_sql = 'INSERT INTO traffic_mark (`name`, `env`, `project_id`) VALUES ("{}",{}, {})'.format(
                    mark, 2, project['new_project'])
                prod_db_cursor.execute(insert_traffic_mark_sql)

                inserted_id = prod_db_cursor.lastrowid

            select_query = "SELECT id FROM sub_env WHERE cluster = '%s' AND namespace = '%s'" % (cluster, namespace)
            prod_db_cursor.execute(select_query)
            # 获取流量标记查询结果
            result = prod_db_cursor.fetchone()
            if result:
                inserted_id = result[0]  # 获取ID值
                print("获取子环境查询结果记录存在，ID为：", inserted_id)
            else:
                print("获取子环境查询结果记录不存在")
                insert_sub_env_sql = 'INSERT INTO sub_env  (`env`, `cluster`, `namespace`, `project_id`, `traffic_mark_id`) VALUES ({}, "{}","{}", {}, {})'.format(
                    2, cluster, namespace, project['new_project'], inserted_id)
                prod_db_cursor.execute(insert_sub_env_sql)

                # 提交执行结果到数据库
                prod_db_conn.commit()
    test_db_cursor.close()
    test_db_conn.close()
    prod_db_cursor.close()
    prod_db_conn.close()


if __name__ == '__main__':
    main()
