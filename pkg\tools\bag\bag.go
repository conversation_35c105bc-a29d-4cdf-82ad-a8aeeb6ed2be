package bag

import (
	"golang.org/x/exp/constraints"
	"sort"
)

type settable interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | string
}

func New[T settable]() *Bag[T] {
	b := &Bag[T]{
		mapping: make(map[T]struct{}),
		array:   make([]T, 0),
	}
	return b
}

type Bag[T settable] struct {
	mapping map[T]struct{}
	array   []T
}

func (b *Bag[T]) Add(item T) *Bag[T] {
	_, ok := b.mapping[item]
	if ok {
		return b
	}

	b.array = append(b.array, item)
	b.mapping[item] = struct{}{}
	return b
}

func (b *Bag[T]) Adds(items ...T) *Bag[T] {
	for _, item := range items {
		b.Add(item)
	}

	return b
}

func (b *Bag[T]) Items() []T {
	sortSlice(b.array)
	return b.array
}

func (b *Bag[T]) RawItems() []T {
	return b.array
}

func (b *Bag[T]) Size() int {
	return len(b.array)
}

func (b *Bag[T]) IsEmpty() bool {
	return b.Size() == 0
}

func (b *Bag[T]) Existed(item T) bool {
	_, ok := b.mapping[item]
	return ok
}

func sortSlice[T constraints.Ordered](s []T) {
	sort.Slice(s, func(i, j int) bool {
		return s[i] < s[j]
	})
}

func Difference[T settable](a, b *Bag[T]) []T {
	var diff []T

	for _, item := range a.Items() {
		if !b.Existed(item) {
			diff = append(diff, item)
		}
	}

	return diff
}
