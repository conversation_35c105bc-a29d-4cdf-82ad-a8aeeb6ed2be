package dify

import "context"

type ChatRep struct {
	Inputs struct {
	} `json:"inputs"`
	Query          string `json:"query"`
	ResponseMode   string `json:"response_mode"`
	ConversationID string `json:"conversation_id"`
	User           string `json:"user"`
	Files          []struct {
		Type           string `json:"type"`
		TransferMethod string `json:"transfer_method"`
		URL            string `json:"url"`
	} `json:"files"`
}

type ChatResp struct {
	Event          string `json:"event"`
	MessageID      string `json:"message_id"`
	ConversationID string `json:"conversation_id"`
	Mode           string `json:"mode"`
	Answer         string `json:"answer"`
	Metadata       struct {
		Usage struct {
			PromptTokens        int     `json:"prompt_tokens"`
			PromptUnitPrice     string  `json:"prompt_unit_price"`
			PromptPriceUnit     string  `json:"prompt_price_unit"`
			PromptPrice         string  `json:"prompt_price"`
			CompletionTokens    int     `json:"completion_tokens"`
			CompletionUnitPrice string  `json:"completion_unit_price"`
			CompletionPriceUnit string  `json:"completion_price_unit"`
			CompletionPrice     string  `json:"completion_price"`
			TotalTokens         int     `json:"total_tokens"`
			TotalPrice          string  `json:"total_price"`
			Currency            string  `json:"currency"`
			Latency             float64 `json:"latency"`
		} `json:"usage"`
		RetrieverResources []struct {
			Position     int     `json:"position"`
			DatasetID    string  `json:"dataset_id"`
			DatasetName  string  `json:"dataset_name"`
			DocumentID   string  `json:"document_id"`
			DocumentName string  `json:"document_name"`
			SegmentID    string  `json:"segment_id"`
			Score        float64 `json:"score"`
			Content      string  `json:"content"`
		} `json:"retriever_resources"`
	} `json:"metadata"`
	CreatedAt int `json:"created_at"`
}

func (c *client) Chat(ctx context.Context, req ChatRep) (resp ChatResp, err error) {
	urls := c.host + "/chat-messages"
	resp, err = doPost[ChatResp](ctx, c, urls, req)
	return
}
