CREATE TABLE `approval_flow_instance` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `flow_type` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'upgrade' COMMENT '流程类型',
  `project_id` bigint DEFAULT NULL COMMENT '关联组id',
  `flow_id` bigint NOT NULL COMMENT '审批流id',
  `ticket_id` bigint NOT NULL COMMENT '关联的工单',
  `applicant_id` bigint NOT NULL COMMENT '申请人(和工单保持一直)',
  `chinese_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
  `employee_no` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工号',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `node_sequence` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点顺序',
  `checkers` json DEFAULT NULL COMMENT '测试验收人',
  `task_run_id` bigint NOT NULL COMMENT '关联的流水线任务id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_project_id` (`project_id`) USING BTREE,
  KEY `idx_name` (`name`) USING BTREE,
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_ticket_id` (`ticket_id`),
  KEY `idx_task_id` (`task_run_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批流实例信息表';
