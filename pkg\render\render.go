package render

import (
	"fmt"
	"net/http"
)

type Empty struct{}

type Response struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

func Ok(data interface{}) *Response {
	if data == nil {
		data = Empty{}
	}
	resp := &Response{
		Code: http.StatusOK,
		Data: data,
		Msg:  "success",
	}
	return resp
}

func Fail(code int, msg string) *Response {
	return Fatal(code, msg, Empty{})
}

func Fatal(code int, msg string, value interface{}) *Response {
	resp := &Response{
		Code: code,
		Data: value,
		Msg:  msg,
	}
	return resp
}

func Failf(code int, msg string, args ...interface{}) *Response {
	fmtMsg := fmt.Sprintf(msg, args...)
	return Fail(code, fmtMsg)
}
