package lark

import (
	"time"

	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"

	larksdk "github.com/larksuite/oapi-sdk-go/v3"
)

type Config struct {
	AppID     string `mapstructure:"app_id" json:"appID"`
	AppSecret string `mapstructure:"app_secret" json:"appSecret"`
}

// New 初始化一个 lark.Client 实例
func New(cfg *Config, options ...larksdk.ClientOptionFunc) *larksdk.Client {
	defaults := []larksdk.ClientOptionFunc{
		larksdk.WithEnableTokenCache(true),
		// 默认超时 3s
		larksdk.WithReqTimeout(3 * time.Second),
		larksdk.WithLogLevel(larkcore.LogLevelInfo),
	}
	allOptions := make([]larksdk.ClientOptionFunc, 0, len(defaults)+len(options))
	allOptions = append(allOptions, defaults...)
	allOptions = append(allOptions, options...)

	return larksdk.NewClient(cfg.AppID, cfg.AppSecret, allOptions...)
}

func WithHTTPClient(client larkcore.HttpClient) larksdk.ClientOptionFunc {
	return larksdk.WithHttpClient(client)
}

func WithReqTimeout(timeout time.Duration) larksdk.ClientOptionFunc {
	return larksdk.WithReqTimeout(timeout)
}
