/*
 Navicat Premium Data Transfer

 Source Server         : cicd-test
 Source Server Type    : MySQL
 Source Server Version : 80022
 Source Host           : ************:3306
 Source Schema         : idehub

 Target Server Type    : MySQL
 Target Server Version : 80022
 File Encoding         : 65001

 Date: 14/06/2024 10:52:32
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for nhctl_version_management
-- ----------------------------
DROP TABLE IF EXISTS `nhctl_version_management`;
CREATE TABLE `nhctl_version_management`  (
                                             `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `nhctl_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'nhctl版本',
                                             `plugin_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '插件版本',
                                             `is_active` tinyint(0) NOT NULL COMMENT '是否启用: 1启用,0禁用',
                                             PRIMARY KEY (`id`) USING BTREE,
                                             UNIQUE INDEX `unique_nhctl_plugin`(`nhctl_version`, `plugin_version`) USING BTREE COMMENT '唯一键'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
