package lark

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"regexp"

	"52tt.com/cicd/pkg/tools/cond"
)

type Title struct {
	Template string `json:"template"`
	Text     Text   `json:"title"`
}

func NewTitle(title string, colors ...string) *Title {
	color := cond.OrGet(colors, "blue")
	return &Title{
		Text: Text{
			Tag:     "plain_text",
			Content: fmt.Sprintf("🔔 %s", title),
		},
		Template: color,
	}
}

func (t *Title) Fill() string {
	data, _ := json.Marshal(t)
	return string(data)
}

type MessageConfig struct {
	WideScreenMode bool `json:"wide_screen_mode"`
}

func NewMessageConfig() *MessageConfig {
	return &MessageConfig{
		WideScreenMode: true,
	}
}

func (m *MessageConfig) Fill() string {
	data, _ := json.Marshal(m)
	return string(data)
}

// Message Base Model
type Message struct {
	Config      Render   `json:"config"`
	Title       Render   `json:"header"`
	Contents    []Render `json:"elements"`
	MessageType string   `json:"-"`
}

// Text 段落文本 用于文本消息
type Text struct {
	Tag     string `json:"tag"`
	Content string `json:"content"`
}

// Cell 表格单元格
type Cell struct {
	Tag       string `json:"tag"`
	Content   string `json:"content"`
	TextAlign string `json:"text_align"`
}

func NewCell(content string) *Cell {
	return &Cell{
		Tag:       "markdown",
		TextAlign: "center",
		Content:   content,
	}
}

func (c *Cell) Fill() string {
	data, _ := json.Marshal(c)
	return string(data)
}

// Column 表格列
type Column struct {
	Tag           string   `json:"tag"`
	Width         string   `json:"width"`
	Weight        int      `json:"weight"`
	VerticalAlign string   `json:"vertical_align"`
	Elements      []Render `json:"elements"`
}

func (c *Column) Fill() string {
	data, _ := json.Marshal(c)
	return string(data)
}

func NewColumn(cells *Cell) *Column {
	return &Column{
		Tag:           "column",
		Width:         "weighted",
		Weight:        1,
		VerticalAlign: "top",
		Elements:      []Render{cells},
	}
}

// Row 表格行
type Row struct {
	Tag             string   `json:"tag"`
	FlexMode        string   `json:"flex_mode"`
	BackgroundStyle string   `json:"background_style"`
	Elements        []Render `json:"columns"`
}

func (r *Row) Fill() string {
	data, _ := json.Marshal(r)
	return string(data)
}

type Var struct{}

func NewRow(columns ...string) *Row {
	row := &Row{
		Tag:             "column_set",
		FlexMode:        "none",
		BackgroundStyle: "none",
	}
	for _, column := range columns {
		row.Elements = append(row.Elements, NewColumn(NewCell(column)))
	}
	return row
}
func NewRowHeader(columns ...string) *Row {
	row := NewRow(columns...)
	row.BackgroundStyle = "grey"
	return row
}

// Paragraph 段落
type Paragraph struct {
	Tag  string `json:"tag"`
	Text Text   `json:"text"`
}

// HorizontalSection 横向分割区域
type HorizontalSection struct {
	Tag    string   `json:"tag"`
	Fields []Render `json:"fields"`
}

func (hs *HorizontalSection) Fill() string {
	data, _ := json.Marshal(hs)
	return string(data)
}

// SectionField 段落区域
type SectionField struct {
	IsShort bool `json:"is_short"`
	Text    Text `json:"text"`
}

func (p *Paragraph) Fill() string {
	data, _ := json.Marshal(p)
	return string(data)
}

func (sf *SectionField) Fill() string {
	data, _ := json.Marshal(sf)
	return string(data)
}

func NewHorizontalSection(fields ...string) *HorizontalSection {
	section := &HorizontalSection{
		Tag: "div",
	}
	for _, field := range fields {
		section.Fields = append(section.Fields, NewSectionField(field, true))
	}
	return section
}

func NewHorizontalSectionV2(fields ...string) *HorizontalSection {
	section := &HorizontalSection{
		Tag: "div",
	}
	for _, field := range fields {
		section.Fields = append(section.Fields, NewSectionField(field, false))
	}
	return section
}

func NewSectionField(content string, isShort bool) *SectionField {
	return &SectionField{
		IsShort: isShort,
		Text: Text{
			Tag:     "lark_md",
			Content: content,
		},
	}
}

func NewParagraph(content string) *Paragraph {
	return &Paragraph{
		Tag: "div",
		Text: Text{
			Tag:     "lark_md",
			Content: content,
		},
	}
}

// Render 消息渲染
type Render interface {
	Fill() string
}

// Fill message template encode
func (m *Message) Fill() string {
	data, err := json.Marshal(m)

	if err != nil {
		fmt.Printf("json.Marshal(m) error: %v\n", err)
	}
	return string(data)
}

// TextMessage 文本消息
type TextMessage struct {
	Message
}

// RichTextMessage 富文本消息
type RichTextMessage struct {
	Message
}

// CardMessage 卡片消息
type CardMessage struct {
	Message
}

func NewCardMessage(renders ...Render) *CardMessage {
	title := renders[0]
	contents := renders[1:]
	return &CardMessage{
		Message{
			Config:      NewMessageConfig(),
			Title:       title,
			Contents:    contents,
			MessageType: "interactive",
		},
	}
}

func GenUpdateContentByFile(vars map[string]string, filePath string) ([]byte, error) {
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return []byte{}, err
	}

	re := regexp.MustCompile(`\${(.*?)}`)
	result := re.ReplaceAllFunc(data, func(match []byte) []byte {
		key := extractKey(string(match))
		if value, ok := vars[key]; ok {
			return []byte(value)
		}
		return match
	})
	return result, nil
}

func extractKey(str string) string {
	re := regexp.MustCompile(`\${(.*?)}`)
	matches := re.FindStringSubmatch(str)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

func GenUpdateContentByString(vars map[string]string, data string) string {
	re := regexp.MustCompile(`\$\{(.*?)\}`)
	result := re.ReplaceAllStringFunc(data, func(match string) string {
		key := match[2 : len(match)-1]
		if value, ok := vars[key]; ok {
			return value
		}
		return match
	})
	return result
}
