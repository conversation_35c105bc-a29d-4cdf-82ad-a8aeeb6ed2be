package time

import (
	"fmt"
	"regexp"
	"strconv"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
)

const DateLayOut = "2006/01/02"

func GetUnixTime() int {
	unixTime := strconv.FormatInt(time.Now().Unix(), 10)
	i, _ := strconv.Atoi(unixTime)
	return i
}

// Deprecated: 切换到 timex.Format
func Format(t time.Time) string {
	if t.Is<PERSON>ero() {
		return ""
	}
	t = ToCST(t)
	in := t.Format("2006-01-02 15:04:05 Monday -0700 MST")
	regWhatDay := `Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday`
	regTimeZone := `[A-z]+$`

	whatDay := regexp.MustCompile(regWhatDay).FindAllString(in, -1)[0]
	timeZone := regexp.MustCompile(regTimeZone).FindAllString(in, -1)[0]

	switch whatDay {
	case "Monday":
		whatDay = "星期一"
	case "Tuesday":
		whatDay = "星期二"
	case "Wednesday":
		whatDay = "星期三"
	case "Thursday":
		whatDay = "星期四"
	case "Friday":
		whatDay = "星期五"
	case "Saturday":
		whatDay = "星期六"
	case "Sunday":
		whatDay = "星期日"
	}

	return fmt.Sprintf("%s %s %s", in[0:19], whatDay, timeZone)
}

func ToCST(t time.Time) time.Time {
	// 加载中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Println("无法加载时区信息：", err)
		return t
	}
	// 设置为中国标准时间
	return t.In(loc)
}

// Deprecated: 切换到 timex.ToYearMonth
func ToYearMonth(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return ToCST(t).Format("2006-01")
}

// Deprecated: 切换到 timex.ToPointer
func HandleZeroTime(t time.Time) *time.Time {
	if t.IsZero() {
		return nil
	}
	return &t
}

func StampToTime(timestamp *timestamppb.Timestamp) time.Time {
	timestampStr := timestamp.AsTime().Format(time.RFC3339Nano)
	parsedTime, err := time.Parse(time.RFC3339Nano, timestampStr)
	if err != nil {
		return time.Now()
	}
	return parsedTime
}

// Deprecated: 切换到 timex.ElapsedTime
func ElapsedTime(startTime time.Time, completedTime time.Time) time.Duration {
	if !completedTime.IsZero() {
		return completedTime.Sub(startTime)
	}
	return time.Now().Sub(startTime)
}

// format time use pattern yyyy-MM-dd hh:mm:ss
func format(t time.Time) string {
	pattern := "2006-01-02 15:04:05"
	return t.Format(pattern)
}
