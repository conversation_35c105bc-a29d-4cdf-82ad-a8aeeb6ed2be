package token

import (
	"github.com/golang-jwt/jwt/v4"
	"testing"
	"time"
)

// https://cryptotools.net/rsagen
const privateKey = `
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

const publicKey = `
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCuWZeBdWlUkZEgUMSgs6c+Xzu1
6wYNJ0oYoGsd9EmYxWqIAsmDU+PUi1vlTF8OJvf/Qbzws5EkyOalxksS3tBol+V8
FtSbLxGVNxZXN71IumcDsFmHWJDcG9RcI6vPsKoFZakuhZSj0sAyePcuOHnxaf+l
N0J1NfLleyMA/zDAmQIDAQAB
-----END PUBLIC KEY-----
`

var (
	testTimeFunc = time.Now
	testExpire   = 10 * time.Second
)

func TestJWTTokenGen_GenerateToken(t *testing.T) {
	key, err := jwt.ParseRSAPrivateKeyFromPEM([]byte(privateKey))
	if err != nil {
		t.Fatalf("cannot parse private key: %v", err)
	}

	g := NewJWTTokenRSA("test", key)
	g.timeFunc = time.Now

	uid := int64(1)
	tkn, err := g.GenerateToken(&PrivateClaims{Uid: uid}, testExpire)
	if err != nil {
		t.Errorf("cannot generate token: %v\n", err)
	}

	want := ""
	if tkn == want {
		t.Errorf("wrong token generated. \nwant: %q \ngot: %q", want, tkn)
	}
}

func TestJWTTokenVerifier_Verify(t *testing.T) {
	priKey, _ := jwt.ParseRSAPrivateKeyFromPEM([]byte(privateKey))
	g := NewJWTTokenRSA("test", priKey)
	g.timeFunc = testTimeFunc

	uid := int64(1)
	tkn, _ := g.GenerateToken(&PrivateClaims{Uid: uid}, testExpire)

	key, err := jwt.ParseRSAPublicKeyFromPEM([]byte(publicKey))
	if err != nil {
		t.Fatalf("cannot parse public key: %v", err)
	}
	rsaGen := NewJWTTokenRSAVerifier(key)

	cases := []struct {
		name    string
		tkn     string
		now     time.Time
		want    int64
		wantErr bool
	}{
		{
			name:    "valid_token",
			tkn:     tkn,
			now:     testTimeFunc().Add(2 * time.Second),
			want:    uid,
			wantErr: false,
		},
		{
			name:    "token_expired",
			tkn:     tkn,
			now:     testTimeFunc().Add(testExpire + time.Second),
			want:    0,
			wantErr: true,
		},
		{
			name:    "bad_token",
			tkn:     "bad_token",
			now:     testTimeFunc().Add(2 * time.Second),
			want:    0,
			wantErr: true,
		},
		{
			name:    "wrong_signature",
			tkn:     tkn,
			now:     time.Unix(1516239022, 0),
			want:    0,
			wantErr: true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			jwt.TimeFunc = func() time.Time {
				return c.now
			}

			claims, err := rsaGen.Verify(c.tkn)
			if !c.wantErr && err != nil {
				t.Errorf("verification failed: %v", err)
			}

			if c.wantErr && err == nil {
				t.Errorf("want error, got not error")
			}

			if claims != nil && claims.Uid != c.want {
				t.Errorf("wrong uid. want: %q, got: %q", c.want, claims.Uid)
			}
		})
	}
}

func TestJWTTokenHMAC(t *testing.T) {
	jwt.TimeFunc = testTimeFunc

	hmacGen := NewJTWTokenHMAC("test", []byte("abc"))
	hmacGen.timeFunc = testTimeFunc

	uid := int64(1)

	claims := &PrivateClaims{
		Uid: uid,
	}
	tkn, err := hmacGen.GenerateToken(claims, testExpire)
	if err != nil {
		t.Errorf("cannot generate token: %v\n", err)
	}

	verifyClaims, err := hmacGen.Verify(tkn)
	if err != nil {
		t.Errorf("cannot verify token: %v\n", err)
	}

	if verifyClaims.Uid != uid {
		t.Errorf("wrong uid. want: %q, got: %q", uid, claims.Uid)
	}
}

func TestJWTTokenHMAC_Verify(t *testing.T) {
	hmacGen := &JWTTokenHMAC{
		Issuer:   "test",
		timeFunc: testTimeFunc,
		key:      []byte("abc"),
	}

	uid := int64(1)
	tkn, _ := hmacGen.GenerateToken(&PrivateClaims{Uid: uid}, testExpire)

	cases := []struct {
		name    string
		tkn     string
		now     time.Time
		want    int64
		wantErr bool
	}{
		{
			name:    "valid_token",
			tkn:     tkn,
			now:     testTimeFunc().Add(2 * time.Second),
			want:    uid,
			wantErr: false,
		},
		{
			name:    "token_expired",
			tkn:     tkn,
			now:     testTimeFunc().Add(testExpire + time.Second),
			want:    0,
			wantErr: true,
		},
		{
			name:    "bad_token",
			tkn:     "bad_token",
			now:     testTimeFunc().Add(2 * time.Second),
			want:    0,
			wantErr: true,
		},
		{
			name:    "wrong_signature",
			tkn:     tkn,
			now:     time.Unix(1516239022, 0),
			want:    0,
			wantErr: true,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			jwt.TimeFunc = func() time.Time {
				return c.now
			}

			claims, err := hmacGen.Verify(c.tkn)
			if !c.wantErr && err != nil {
				t.Errorf("verification failed: %v", err)
			}

			if c.wantErr && err == nil {
				t.Errorf("want error, got not error")
			}

			if claims != nil && claims.Uid != c.want {
				t.Errorf("wrong uid. want: %q, got: %q", c.want, claims.Uid)
			}
		})
	}
}
