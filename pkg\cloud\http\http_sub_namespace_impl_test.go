package http

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/require"

	"52tt.com/cicd/pkg/cloud/http/core"
)

func TestSubNSHTTPClient_GetSubNamespace(t *testing.T) {
	t.Skip()
	host := os.Getenv("CLOUD_HOST")
	token := os.Getenv("CLOUD_TOKEN")
	client := NewSubNamespaceHTTPClient(host, token)
	resp, err := client.GetSubNamespace(context.Background(), &core.GetSubNamespaceRequest{
		Cluster:   "k8s-tc-bj-cicd-test",
		Namespace: "default-sub-env-cicd",
		Describe:  "",
	})

	require.NoError(t, err)
	t.Logf("%+v", resp)
	t.Logf("%+v", resp.Data.Parent)
}
