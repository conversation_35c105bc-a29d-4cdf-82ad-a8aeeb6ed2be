package context

import (
	"context"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc/metadata"
)

const (
	AuthorizationHeaderKey = "Authorization"
	UseridHeaderKey        = "X-Auth-Userid"
	UsernameHeaderKey      = "X-Auth-Username"
	ChineseNameHeaderKey   = "X-Auth-ChineseName"
	EmployeeNoHeaderKey    = "X-Auth-EmployeeNo"
	RolesHeaderKey         = "X-Auth-Roles"
	ProjectIdHeaderKey     = "X-Auth-ProjectId"
	RequestIDHeaderKey     = "X-Request-ID"
	OpenapiTokenHeaderKey  = "X-Token"

	AuthorizationKey = "authorization"
	useridKey        = "userid"
	usernameKey      = "username"
	chineseNameKey   = "chineseName"
	employeeNoKey    = "employeeNo"
	rolesKey         = "roles"
	mdGinKey         = "mdGinKey"
	projectIdKey     = "projectId"
	requestIDKey     = "requestID"
	trafficMarkKey   = "x-qw-traffic-mark"
)

// Deprecated: 不推荐使用gin.Context
func WithMDFromGin(ctx *gin.Context, md metadata.MD) {
	ctx.Set(mdGinKey, md)
}

// Deprecated: 推荐使用 UserID 或 RequestInfoFromCtx 或 GetUserInfo 等方法获取
func GetUseridFromGin(ctx *gin.Context) int64 {
	userid := ctx.GetString(useridKey)
	if v, err := strconv.ParseInt(userid, 10, 64); err == nil {
		return v
	}
	return 0
}

// Deprecated: 不推荐使用gin.Context
func WithUseridFromGin(ctx *gin.Context) {
	WithUserid(ctx, ctx.GetHeader(UseridHeaderKey))
}

// Deprecated: 不推荐使用gin.Context
func WithUserid(ctx *gin.Context, userid any) {
	ctx.Set(useridKey, userid)
}

// Deprecated: 推荐使用 Username 或 RequestInfoFromCtx 或 GetUserInfo 等方法获取
func GetUsernameFromGin(ctx *gin.Context) string {
	return ctx.GetString(usernameKey)
}

// Deprecated: 不推荐使用gin.Context
func WithUsernameFromGin(ctx *gin.Context) {
	WithUsername(ctx, ctx.GetHeader(UsernameHeaderKey))
}

// Deprecated: 不推荐使用gin.Context
func WithUsername(ctx *gin.Context, username string) {
	ctx.Set(usernameKey, username)
}

// Deprecated: 推荐使用 CName 或 RequestInfoFromCtx 或 GetUserInfo 等方法获取
func GetChineseNameFromGin(ctx *gin.Context) string {
	return ctx.GetString(chineseNameKey)
}

// Deprecated: 不推荐使用gin.Context
func WithChineseNameFromGin(ctx *gin.Context) {
	WithChineseName(ctx, ctx.GetHeader(ChineseNameHeaderKey))
}

// Deprecated: 不推荐使用gin.Context
func WithChineseName(ctx *gin.Context, chineseName string) {
	ctx.Set(chineseNameKey, chineseName)
}

// Deprecated: 推荐使用 EmployeeNo 或 RequestInfoFromCtx 或 GetUserInfo 等方法获取
func GetEmployeeNoFromGin(ctx *gin.Context) string {
	return ctx.GetString(employeeNoKey)
}

// Deprecated: 不推荐使用gin.Context
func WithEmployeeNoFromGin(ctx *gin.Context) {
	WithEmployeeNo(ctx, ctx.GetHeader(EmployeeNoHeaderKey))
}

// Deprecated: 不推荐使用gin.Context
func WithEmployeeNo(ctx *gin.Context, chineseName string) {
	ctx.Set(employeeNoKey, chineseName)
}

// Deprecated: 推荐使用 Roles 或 RequestInfoFromCtx 或 GetUserInfo 等方法获取
func GetRolesFromGin(ctx *gin.Context) string {
	return ctx.GetString(rolesKey)
}

// Deprecated: 不推荐使用gin.Context
func WithRolesFromGin(ctx *gin.Context) {
	WithRoles(ctx, ctx.GetHeader(RolesHeaderKey))
}

// Deprecated: 不推荐使用gin.Context
func WithRoles(ctx *gin.Context, roles string) {
	ctx.Set(rolesKey, roles)
}

// Deprecated: 推荐 Token 方法
func GetTokenFromGin(ctx *gin.Context) string {
	return ctx.GetHeader(AuthorizationKey)
}

func WithUserinfo(parent context.Context, userinfo *UserInfo) context.Context {
	return context.WithValue(parent, userinfoKey{}, userinfo)
}

func GetUserinfo(ctx context.Context) *UserInfo {
	ui := ctx.Value(userinfoKey{})
	if userinfo, ok := ui.(*UserInfo); ok {
		return userinfo
	}

	ri, ok := RequestInfoFromCtx(ctx)
	if ok {
		return &ri.UserInfo
	}

	return &emptyUserinfo
}

// Deprecated: 不推荐使用gin.Context
func WithProjectId(ctx *gin.Context, projectId any) {
	ctx.Set(projectIdKey, projectId)
}

// Deprecated: 不推荐使用gin.Context
func WithProjectIdFromGin(ctx *gin.Context) {
	WithProjectId(ctx, ctx.GetHeader(ProjectIdHeaderKey))
}

// Deprecated: 推荐使用 ProjectID 或 RequestInfoFromCtx 或 GetUserInfo 等方法获取
func GetProjectIdFromGin(ctx *gin.Context) int64 {
	projectId := ctx.GetString(projectIdKey)
	if v, err := strconv.ParseInt(projectId, 10, 64); err == nil {
		return v
	}
	return 0
}

func WithToken(ctx context.Context, token string) context.Context {
	return context.WithValue(ctx, tokenKey{}, token)
}

func Token(ctx context.Context) string {
	t, ok := ctx.Value(tokenKey{}).(string)
	if ok {
		return t
	}

	return ""
}

func RequestID(ctx context.Context) string {
	ri, ok := RequestInfoFromCtx(ctx)
	if ok {
		return ri.RequestID
	}

	return ""
}

func WithRequestInfo(ctx context.Context, ri *RequestInfo) context.Context {
	return context.WithValue(ctx, requestInfoKey{}, ri)
}

func RequestInfoFromCtx(ctx context.Context) (*RequestInfo, bool) {
	ri, ok := ctx.Value(requestInfoKey{}).(*RequestInfo)
	if ok {
		return ri, true
	}
	// http接口的信息注入，只能从gin.Context中获取
	if v, ok := ctx.(*gin.Context); ok {
		return RequestInfoFromGinCtx(v)
	}

	return &emptyRequestInfo, false
}

func MustRequestInfo(ctx context.Context) *RequestInfo {
	ri, _ := RequestInfoFromCtx(ctx)
	return ri
}

func CopyCtxWithTimeout(ctx context.Context, timeout time.Duration) (context.Context, context.CancelFunc) {
	newCtx, timeoutFunc := context.WithTimeout(context.Background(), timeout)
	ri, _ := RequestInfoFromCtx(ctx)
	return WithRequestInfo(newCtx, ri), timeoutFunc
}

func CopyContext(ctx context.Context) context.Context {
	newCtx := context.Background()
	ri, _ := RequestInfoFromCtx(ctx)
	return WithRequestInfo(newCtx, ri)
}

func UserID(ctx context.Context) int64 {
	ri, ok := RequestInfoFromCtx(ctx)
	if ok {
		return ri.UserID
	}

	return 0
}

func Username(ctx context.Context) string {
	ri, ok := RequestInfoFromCtx(ctx)
	if ok {
		return ri.Username
	}

	return ""
}

func CName(ctx context.Context) string {
	ri, ok := RequestInfoFromCtx(ctx)
	if ok {
		return ri.ChineseName
	}

	return ""
}

func EmployeeNo(ctx context.Context) string {
	ri, ok := RequestInfoFromCtx(ctx)
	if ok {
		return ri.EmployeeNo
	}

	return ""
}

func ProjectID(ctx context.Context) int64 {
	ri, ok := RequestInfoFromCtx(ctx)
	if ok {
		return ri.ProjectID
	}

	return 0
}

func Roles(ctx context.Context) []string {
	ri, ok := RequestInfoFromCtx(ctx)
	if ok {
		return ri.Roles
	}

	return nil
}

func WithTrafficMark(ctx context.Context, mark string) context.Context {
	return context.WithValue(ctx, qwTrafficMarkKey{}, mark)
}

func TrafficMark(ctx context.Context) string {
	t, ok := ctx.Value(trafficMarkKey).(string)
	if ok && t != "" {
		return t
	}
	t, ok = ctx.Value(qwTrafficMarkKey{}).(string)
	if ok {
		return t
	}
	return ""
}
