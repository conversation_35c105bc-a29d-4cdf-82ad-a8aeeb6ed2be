// Code generated by MockGen. DO NOT EDIT.
// Source: config_manager.go

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	page "52tt.com/cicd/pkg/page"
	model "52tt.com/cicd/services/app/internal/model"
	gomock "github.com/golang/mock/gomock"
)

// MockConfigManagerService is a mock of ConfigManagerService interface.
type MockConfigManagerService struct {
	ctrl     *gomock.Controller
	recorder *MockConfigManagerServiceMockRecorder
}

// MockConfigManagerServiceMockRecorder is the mock recorder for MockConfigManagerService.
type MockConfigManagerServiceMockRecorder struct {
	mock *MockConfigManagerService
}

// NewMockConfigManagerService creates a new mock instance.
func NewMockConfigManagerService(ctrl *gomock.Controller) *MockConfigManagerService {
	mock := &MockConfigManagerService{ctrl: ctrl}
	mock.recorder = &MockConfigManagerServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConfigManagerService) EXPECT() *MockConfigManagerServiceMockRecorder {
	return m.recorder
}

// ActiveConfigVersion mocks base method.
func (m *MockConfigManagerService) ActiveConfigVersion(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ActiveConfigVersion", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// ActiveConfigVersion indicates an expected call of ActiveConfigVersion.
func (mr *MockConfigManagerServiceMockRecorder) ActiveConfigVersion(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActiveConfigVersion", reflect.TypeOf((*MockConfigManagerService)(nil).ActiveConfigVersion), ctx, id)
}

// BindConfigApp mocks base method.
func (m *MockConfigManagerService) BindConfigApp(ctx context.Context, req *model.BindConfigAppReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindConfigApp", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindConfigApp indicates an expected call of BindConfigApp.
func (mr *MockConfigManagerServiceMockRecorder) BindConfigApp(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindConfigApp", reflect.TypeOf((*MockConfigManagerService)(nil).BindConfigApp), ctx, req)
}

// CreateConfig mocks base method.
func (m *MockConfigManagerService) CreateConfig(ctx context.Context, req *model.CreateConfigParams) (*model.CreateOrUpdateConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateConfig", ctx, req)
	ret0, _ := ret[0].(*model.CreateOrUpdateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateConfig indicates an expected call of CreateConfig.
func (mr *MockConfigManagerServiceMockRecorder) CreateConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateConfig", reflect.TypeOf((*MockConfigManagerService)(nil).CreateConfig), ctx, req)
}

// DeleteConfig mocks base method.
func (m *MockConfigManagerService) DeleteConfig(ctx context.Context, req *model.DeleteConfigParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteConfig", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteConfig indicates an expected call of DeleteConfig.
func (mr *MockConfigManagerServiceMockRecorder) DeleteConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteConfig", reflect.TypeOf((*MockConfigManagerService)(nil).DeleteConfig), ctx, req)
}

// DiffConfigVersion mocks base method.
func (m *MockConfigManagerService) DiffConfigVersion(ctx context.Context, id int64) (*model.DiffConfigVersionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DiffConfigVersion", ctx, id)
	ret0, _ := ret[0].(*model.DiffConfigVersionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DiffConfigVersion indicates an expected call of DiffConfigVersion.
func (mr *MockConfigManagerServiceMockRecorder) DiffConfigVersion(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DiffConfigVersion", reflect.TypeOf((*MockConfigManagerService)(nil).DiffConfigVersion), ctx, id)
}

// FindAppDyCfgs mocks base method.
func (m *MockConfigManagerService) FindAppDyCfgs(ctx context.Context, req *model.FindAppDyCfgsReq) ([]model.DyCfg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAppDyCfgs", ctx, req)
	ret0, _ := ret[0].([]model.DyCfg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAppDyCfgs indicates an expected call of FindAppDyCfgs.
func (mr *MockConfigManagerServiceMockRecorder) FindAppDyCfgs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAppDyCfgs", reflect.TypeOf((*MockConfigManagerService)(nil).FindAppDyCfgs), ctx, req)
}

// GetAllConfig mocks base method.
func (m *MockConfigManagerService) GetAllConfig(ctx context.Context, req *model.ConfigParams) ([]model.ConfigListItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllConfig", ctx, req)
	ret0, _ := ret[0].([]model.ConfigListItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllConfig indicates an expected call of GetAllConfig.
func (mr *MockConfigManagerServiceMockRecorder) GetAllConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllConfig", reflect.TypeOf((*MockConfigManagerService)(nil).GetAllConfig), ctx, req)
}

// GetConfigBasicList mocks base method.
func (m *MockConfigManagerService) GetConfigBasicList(ctx context.Context, req *model.ConfigParams) ([]model.ConfigBasic, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigBasicList", ctx, req)
	ret0, _ := ret[0].([]model.ConfigBasic)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigBasicList indicates an expected call of GetConfigBasicList.
func (mr *MockConfigManagerServiceMockRecorder) GetConfigBasicList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigBasicList", reflect.TypeOf((*MockConfigManagerService)(nil).GetConfigBasicList), ctx, req)
}

// GetConfigDetail mocks base method.
func (m *MockConfigManagerService) GetConfigDetail(ctx context.Context, req *model.GetConfigReq) (*model.ConfigDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigDetail", ctx, req)
	ret0, _ := ret[0].(*model.ConfigDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigDetail indicates an expected call of GetConfigDetail.
func (mr *MockConfigManagerServiceMockRecorder) GetConfigDetail(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigDetail", reflect.TypeOf((*MockConfigManagerService)(nil).GetConfigDetail), ctx, req)
}

// GetConfigList mocks base method.
func (m *MockConfigManagerService) GetConfigList(ctx context.Context, req *model.QueryConfigParams) (page.Paginator, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigList", ctx, req)
	ret0, _ := ret[0].(page.Paginator)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigList indicates an expected call of GetConfigList.
func (mr *MockConfigManagerServiceMockRecorder) GetConfigList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigList", reflect.TypeOf((*MockConfigManagerService)(nil).GetConfigList), ctx, req)
}

// GetConfigVersionList mocks base method.
func (m *MockConfigManagerService) GetConfigVersionList(ctx context.Context, req *model.QueryConfigVersionParams) (page.Paginator, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigVersionList", ctx, req)
	ret0, _ := ret[0].(page.Paginator)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigVersionList indicates an expected call of GetConfigVersionList.
func (mr *MockConfigManagerServiceMockRecorder) GetConfigVersionList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigVersionList", reflect.TypeOf((*MockConfigManagerService)(nil).GetConfigVersionList), ctx, req)
}

// RevisionConfig mocks base method.
func (m *MockConfigManagerService) RevisionConfig(ctx context.Context, req *model.RevisionConfigReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevisionConfig", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// RevisionConfig indicates an expected call of RevisionConfig.
func (mr *MockConfigManagerServiceMockRecorder) RevisionConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevisionConfig", reflect.TypeOf((*MockConfigManagerService)(nil).RevisionConfig), ctx, req)
}

// UnbindConfigApp mocks base method.
func (m *MockConfigManagerService) UnbindConfigApp(ctx context.Context, req *model.UnbindConfigAppReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindConfigApp", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindConfigApp indicates an expected call of UnbindConfigApp.
func (mr *MockConfigManagerServiceMockRecorder) UnbindConfigApp(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindConfigApp", reflect.TypeOf((*MockConfigManagerService)(nil).UnbindConfigApp), ctx, req)
}

// UpdateConfig mocks base method.
func (m *MockConfigManagerService) UpdateConfig(ctx context.Context, req *model.UpdateConfigParams) (*model.CreateOrUpdateConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConfig", ctx, req)
	ret0, _ := ret[0].(*model.CreateOrUpdateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateConfig indicates an expected call of UpdateConfig.
func (mr *MockConfigManagerServiceMockRecorder) UpdateConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfig", reflect.TypeOf((*MockConfigManagerService)(nil).UpdateConfig), ctx, req)
}
