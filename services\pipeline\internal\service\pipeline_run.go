//go:generate mockgen -destination=pipeline_run_mock.go -package=service -source=pipeline_run.go
package service

import (
	"context"
	"encoding/json"
	cmnerrors "errors"
	"fmt"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"time"

	"52tt.com/cicd/pkg/approval"
	"52tt.com/cicd/pkg/cloud"
	cloudagg "52tt.com/cicd/pkg/cloud/aggregate"
	"52tt.com/cicd/pkg/constants"
	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/db"
	"52tt.com/cicd/pkg/event"
	"52tt.com/cicd/pkg/gitlab"
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/page"
	"52tt.com/cicd/pkg/safego"
	"52tt.com/cicd/pkg/timex"
	"52tt.com/cicd/pkg/tools"
	timeutil "52tt.com/cicd/pkg/tools/time"
	"52tt.com/cicd/pkg/tools/vec"
	pbdep "52tt.com/cicd/protocol/deploy"
	pbevent "52tt.com/cicd/protocol/event"
	"52tt.com/cicd/protocol/iam"
	pbiam "52tt.com/cicd/protocol/iam"
	"52tt.com/cicd/services/pipeline/internal/conf"
	"52tt.com/cicd/services/pipeline/internal/dao"
	"52tt.com/cicd/services/pipeline/internal/model"
	pipelineErr "52tt.com/cicd/services/pipeline/pkg/error"
	"52tt.com/cicd/services/pipeline/pkg/tekton"
	v1 "52tt.com/cicd/services/pipeline/pkg/tekton/cdrunner/v1"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	mesh "golang.ttyuyin.com/genproto/quwanapis/cloud/mesh/v1alpha"
	"gorm.io/gorm"
)

var _ PipelineRunService = (*PipelineRunSvc)(nil)

type PipelineRunService interface {
	GetPipelineRunById(ctx context.Context, id int64) (*model.PipelineRun, error)
	CancelPipelineRun(ctx context.Context, run model.PipelineRun) error
	UpdateCancelledByInfo(ctx context.Context, pRun int64, cancelBy int64, name string) error
	GetPipelineRunTaskById(ctx context.Context, id int64) (*dao.PipelineRunTask, error)
	BuildSonarScanResult(ctx context.Context, task *dao.PipelineRunTask) (*model.SonarScanInfo, error)
	GetSonarResults(ctx context.Context, taskRunResult []byte) (*model.SonarScanMetric, string, error)
	// UpdateRunStatus update pipeline run status, from task -> stage -> pipeline
	// if task status is failed, then update stage status to failed，and update pipeline status to failed
	UpdateRunStatus(ctx context.Context, runStatus *model.RunStatus) error
	RunManualDeploy(ctx context.Context, param *model.ManualDeployTaskParam) error
	HandlePrefixPipelineRunDeploy(ctx context.Context, tektonName string, triggerMode constants.TriggerModeType, DeployTektonType string, configId int64) error
	RollbackDeploy(ctx context.Context, tektonName string, Id int64) error
	GetTaskLogExtraInfo(ctx context.Context, extraInfo *model.TaskRunExtraInfo) error
	UpdateTaskRunAndPipelineConfig(ctx context.Context, taskRun dao.PipelineRunTask, manualDeployTaskParam *model.ManualDeployTaskParam) error
	UpdateRetryPipelineStatus(ctx context.Context, taskRun *dao.PipelineRunTask) error
	CheckTaskRunRelatedTicket(ctx context.Context, appId, stageId int64) bool
	GetNewApprovalTask(ctx context.Context, taskRun *dao.PipelineRunTask) (*dao.PipelineRunTask, error)
	GetTaskRunOperator(ctx context.Context, taskRunId int64, pipelineRunId int64) (*iam.UserResponse, error)
	GetTaskRunWithSameBuildNumber(ctx context.Context, prTask dao.PipelineRunTask) []int64
	IsPipelineTerminable(ctx context.Context, id int64) (bool, error)
	BuildApiAutoTestResult(ctx context.Context, task *dao.PipelineRunTask) (*model.ApiAutoTestResp, error)
	GetMrPipelineRunByMrIdAndRepoAddr(ctx context.Context, repoAddr string, MrId int64) ([]dao.PipelineRun, error)
	GetRetryTimeout(ctx context.Context, params *model.RetryCheckTimeout) error
	GetDeployTaskTrafficMark(ctx context.Context, ID int64) (string, error)
	ListSubTasksEnvironment(ctx context.Context, id int64) (*model.SubTaskEnvironment, error)
	ListProductionDeploy(ctx context.Context, req *model.ListDeployReq) (*page.Page, error)
	ListDeploy(ctx context.Context, req *model.ListDeployReq) (*page.Page, error)
	GetNextTaskRunByTaskRunId(ctx context.Context, taskRunId int64, pr dao.PipelineRun) (*dao.PipelineRunTask, error)
	RetryBasicTask(ctx context.Context, taskRun *dao.PipelineRunTask) error
	CheckApprovalTaskBefore(ctx context.Context, pipelineRunTask *dao.PipelineRunTask) bool
	BuildScaScanResult(ctx context.Context, task *dao.PipelineRunTask) (*model.ScaScanResp, error)
	StopTask(ctx context.Context, req model.StopTaskReq) error
	PassTask(ctx context.Context, req model.PassTaskReq) error
	GetCanaryPolicy(ctx context.Context, taskID int64) (*model.CanaryPolicyObj, error)
	ShiftCanaryPercentage(ctx context.Context, params model.CanaryShiftReq) (*model.CanaryPercentageResp, error)
	GetBaseEnvConfigId(ctx context.Context, subEnvTask *dao.PipelineRunSubtask) (int64, error)
	// ClearAfterDeletePipelineRuns 清理删除流水线后的数据(tekton, ticket, etc..)
	ClearAfterDeletePipelineRuns(ctx context.Context, idList []int64) error
	HandleDeleteEvent(ctx context.Context, event *pbevent.DeletePipelineResourceEvent) error
	GetGitRepoStat(ctx context.Context, gitRepoStat model.GitRepoStatReq) (*model.GitRepoStatResp, error)
	DeletePipelineRunSubtask(ctx context.Context, id int64) error
	AddPipelineRunSubtask(ctx context.Context, addSubTaskParam model.AddSubTaskParam) error
	//CheckAppUpgrade(ctx context.Context, req model.UpgradeCheckReq) ([]model.UpgradeCheckPipelineRuns, error)
	//CheckPassTask(ctx context.Context, req model.PassTaskReq) (*model.PassCheckResp, error)
	//CheckMultiCloudUpgrade(ctx context.Context, req model.MultiCloudUpgradeCheckReq) (*model.MultiCloudUpgradeCheckResp, error)
	GetDeployTriggers(ctx context.Context, prId int64, triggerMode constants.TriggerModeType) (int64, string, string)
	HandleTaskRunStatus(ctx context.Context, prTaskId, prSubTaskId int64, status constants.PipelineStatus) error
	ReloadSubTasksWithoutTicket(ctx context.Context, taskRunId int64) error
	CleanExpiredPipelineRun(ctx context.Context) (err error)
	List(ctx context.Context, req model.PipelineRunListReq) ([]dao.PipelineRun, error)
}

type PipelineRunSvc struct {
	tektonClient         tekton.Service
	prRepo               dao.PipelineRunRepository
	configClient         pbdep.DeployConfigServiceClient
	ticketClient         pbdep.TicketServiceClient
	cloudClient          cloud.Service
	deployClient         pbdep.DeployServiceClient
	subEnvClient         pbdep.SubEnvServiceClient
	userClient           iam.UserServiceClient
	pRepo                dao.PipelineRepository
	plService            PipelineService
	deployApprovalClient pbdep.ApprovalServiceClient
	gitClient            gitlab.Service
	sender               event.Sender
	changeSetRepo        dao.ChangeSetRepository
	changeSetService     ChangeSetService
	redisCli             redis.Cmdable
	approvalClient       approval.Service
	canarySvc            *canaryService
	groupRepo            dao.PipelineGroupRepository
	cloudAggClient       cloudagg.AggClient
	deployPlanClient     pbdep.PlanServiceClient
	prOlRepo             dao.PipelineRunOperateLogRepository
}

func NewPipelineRunService(
	tektonClient tekton.Service,
	prRepo dao.PipelineRunRepository,
	cloudClient cloud.Service,
	configClient pbdep.DeployConfigServiceClient,
	ticketClient pbdep.TicketServiceClient,
	deployCline pbdep.DeployServiceClient,
	subEnvClient pbdep.SubEnvServiceClient,
	userClient iam.UserServiceClient,
	pRepo dao.PipelineRepository,
	plService PipelineService,
	deployApprovalClient pbdep.ApprovalServiceClient,
	gitClient gitlab.Service,
	sender event.Sender,
	changeSetRepo dao.ChangeSetRepository,
	changeSetService ChangeSetService,
	redisCli redis.Cmdable, approvalClient approval.Service,
	cloudAggCli cloudagg.AggClient,
	groupRepo dao.PipelineGroupRepository,
	deployPlanClient pbdep.PlanServiceClient,
	prOlRepo dao.PipelineRunOperateLogRepository) *PipelineRunSvc {
	pipelineRunSvc := PipelineRunSvc{
		tektonClient:         tektonClient,
		prRepo:               prRepo,
		configClient:         configClient,
		cloudClient:          cloudClient,
		ticketClient:         ticketClient,
		deployClient:         deployCline,
		subEnvClient:         subEnvClient,
		userClient:           userClient,
		pRepo:                pRepo,
		plService:            plService,
		deployApprovalClient: deployApprovalClient,
		gitClient:            gitClient,
		sender:               sender,
		changeSetRepo:        changeSetRepo,
		changeSetService:     changeSetService,
		redisCli:             redisCli,
		approvalClient:       approvalClient,
		groupRepo:            groupRepo,
		cloudAggClient:       cloudAggCli,
		deployPlanClient:     deployPlanClient,
		prOlRepo:             prOlRepo,
	}
	// new canary service
	pipelineRunSvc.canarySvc = newCanaryService()
	return &pipelineRunSvc
}

func (p *PipelineRunSvc) CancelPipelineRun(ctx context.Context, run model.PipelineRun) error {
	// 排队状态流水线，直接修改数据库状态即可
	if run.Status == constants.PREPARING {
		err := p.prRepo.UpdatePipelineRunStatusAndTime(ctx, run.ID, map[string]interface{}{"status": constants.CANCEL})
		if err != nil {
			return err
		}
		pplRun, err := p.prRepo.GetPipelineRunById(ctx, run.ID)
		if err != nil {
			return err
		}
		if pplRun == nil {
			return nil
		}
		deliverEvent(ctx, p.sender, pplRun, nil)
		return nil
	}

	// 失败状态的流水线只修改数据库状态即可
	if run.Status == constants.FAILED {
		// 遍历出失败的任务
		failStage, has := lo.Find(run.Stages, func(item model.StageRun) bool {
			return item.Status == constants.FAILED
		})
		if has {
			failTask, has := lo.Find(failStage.Tasks, func(item model.TaskRun) bool {
				return item.Status == constants.FAILED
			})
			if has {
				return p.UpdateRunStatus(ctx, &model.RunStatus{
					TaskID: failTask.ID,
					Status: constants.CANCEL,
				})
			}
		}
		return nil
	}

	hasPplRun := true
	pipelineRun, err := p.tektonClient.GetPipelineRun(ctx, run.TektonNamespace, run.TektonName)
	if err != nil {
		if cmnerrors.Is(err, pipelineErr.ErrTektonPipelineRunNotFound) {
			hasPplRun = false
		} else {
			return err
		}
	}

	if hasPplRun {
		pipelineRun.Spec.Status = v1beta1.PipelineRunSpecStatusCancelled
		if cctx.RequestID(ctx) != "" {
			pipelineRun.Annotations[constants.RequestIDKey.String()] = cctx.RequestID(ctx)
		}
		err = p.tektonClient.UpdatePipelineRunStatus(ctx, run.TektonNamespace, pipelineRun)
		if err != nil {
			return err
		}
	} else {
		// 遍历出 正在运行的任务
		failStage, has := lo.Find(run.Stages, func(item model.StageRun) bool {
			return item.Status == constants.PENDING || item.Status == constants.RUNNING || item.Status == constants.UNHANDLED
		})
		if has {
			failTask, has := lo.Find(failStage.Tasks, func(item model.TaskRun) bool {
				return item.Status == constants.PENDING || item.Status == constants.RUNNING || item.Status == constants.UNHANDLED
			})
			if has {
				return p.UpdateRunStatus(ctx, &model.RunStatus{
					TaskID: failTask.ID,
					Status: constants.CANCEL,
				})
			}
		}
	}

	// 流水线 终止 时，可无脑取消 流量计划 支持无害多次调用,前置判定逻辑 放到流量计划模块
	userInfo := cctx.GetUserinfo(ctx)
	_, err = p.deployPlanClient.CancelPlanRun(ctx, &pbdep.PlanRunReq{PipelineRunId: run.ID, CanceledByChineseName: userInfo.ChineseName, CanceledByEmployeeNo: userInfo.EmployeeNo})
	if err != nil {
		err = fmt.Errorf("取消流量计划 %w", err)
	}
	return nil
}

func (p *PipelineRunSvc) UpdateCancelledByInfo(ctx context.Context, pRun int64, cancelBy int64, name string) error {
	err := p.prRepo.UpdateCancelledByInfo(ctx, pRun, cancelBy, name)
	if err != nil {
		return err
	}
	return nil
}

func (p *PipelineRunSvc) GetPipelineRunById(ctx context.Context, id int64) (*model.PipelineRun, error) {
	pipelineRun := model.PipelineRun{}
	pr, err := p.prRepo.GetPipelineRunById(ctx, id)
	if err != nil {
		return nil, err
	}
	if pr == nil {
		return nil, nil
	}
	err = mapstructure.Decode(pr, &pipelineRun)
	if err != nil {
		return nil, err
	}
	return &pipelineRun, nil
}

func (p *PipelineRunSvc) IsPipelineTerminable(ctx context.Context, id int64) (bool, error) {
	pr, err := p.prRepo.GetPipelineRunById(ctx, id)
	if err != nil {
		return false, err
	}
	var taskRunIds []int64
	for _, stage := range pr.Stages {
		for _, task := range stage.Tasks {
			if isBelongApproval(task.GetType()) && task.Status.IsBelongTo(constants.RUNNING) {
				taskRunIds = append(taskRunIds, task.ID)
			}
		}
	}
	if taskRunIds == nil {
		return true, nil
	}
	tr := &pbdep.CheckTicketReq{TaskRunIds: taskRunIds}
	isRunningTicket, err := p.ticketClient.CheckHasRunningApprovedTicket(ctx, tr)
	if err != nil {
		log.ErrorWithCtx(ctx, "grpc调用查看审批任务是否有审批工单失败: %v", err)
		return false, err
	}
	if isRunningTicket.HasRunningTicket {
		return false, nil
	}
	return true, nil
}

func (p *PipelineRunSvc) GetPipelineRunTaskById(ctx context.Context, id int64) (*dao.PipelineRunTask, error) {
	prt, err := p.prRepo.FindRunTaskById(ctx, id)
	if err != nil {
		return nil, err
	}
	return prt, nil
}

func (p *PipelineRunSvc) CheckPipelineRunDeployMsg(ctx context.Context, tektonName string) (*dao.PipelineRunTask, error) {
	if tektonName == "" {
		msg := fmt.Sprintf("tektonName不能为空")
		log.ErrorWithCtx(ctx, msg)
		return nil, pipelineErr.ErrTektonNameNotFound
	}
	pipelineRunTask, err := p.prRepo.FindRunTaskByTektonName(ctx, tektonName)
	if err != nil {
		return nil, err
	}
	if pipelineRunTask == nil {
		msg := fmt.Sprintf("查询不到tektonName[%s]的taskrun任务，无需执行部署", tektonName)
		log.ErrorWithCtx(ctx, msg)
		return nil, pipelineErr.ErrTektonNameNotFound
	}
	taskType := pipelineRunTask.GetType()
	if taskType != constants.TASK_AUTOMATION_DEPLOY && taskType != constants.TASK_DEV_ENV_IMAGE_SYNC && taskType != constants.TASK_TEST_ENV_IMAGE_SYNC {
		msg := fmt.Sprintf("tektonName[%s]对应的taskrun任务类型[%s]不是部署任务", tektonName, pipelineRunTask.Type)
		log.ErrorWithCtx(ctx, msg)
		return nil, fmt.Errorf("%v", msg)
	}
	autoDeployPipelineRunTask, err := p.prRepo.FindTaskRunByMultiParams(ctx, model.PipelineRunSearch{PipelineRunId: pipelineRunTask.PipelineRunId, Type: string(constants.TASK_GENERATE_PUSH_IMAGE)})
	if err != nil {
		return nil, err
	}
	if autoDeployPipelineRunTask == nil {
		msg := fmt.Sprintf("查询不到tektonName[%s]的taskrun 推送镜像任务，无法执行自动化部署", tektonName)
		log.ErrorWithCtx(ctx, msg)
		return nil, fmt.Errorf("%v", msg)
	}
	return pipelineRunTask, nil
}

func (p *PipelineRunSvc) getProjectManagerEmail(ctx context.Context, projectId int64) ([]string, error) {
	search := pbiam.UserSearch{ProjectId: projectId, RoleName: string(constants.DEV_ADMIN)}
	userList, err := p.userClient.GetUserOfProject(ctx, &search)
	if err != nil {
		log.ErrorWithCtx(ctx, "查询项目[%d]成员错误: %v", projectId, err)
		return nil, err
	}
	var emails []string
	for _, user := range userList.Users {
		emails = append(emails, user.Email)
	}
	return emails, nil
}

func getResource(automationDeploy model.AutomationDeploy) []cloud.Resource {
	var resources []cloud.Resource
	if automationDeploy.Secret != nil && len(automationDeploy.Secret) > 0 {
		resources = append(resources, cloud.Resource{
			Kind:          "secret",
			ResourceNames: automationDeploy.Secret,
		})
	}
	if automationDeploy.ConfigMap != nil && len(automationDeploy.ConfigMap) > 0 {
		resources = append(resources, cloud.Resource{
			Kind:          "configmap",
			ResourceNames: automationDeploy.ConfigMap,
		})
	}
	if automationDeploy.ServiceAccount != nil && len(automationDeploy.ServiceAccount) > 0 {
		resources = append(resources, cloud.Resource{
			Kind:          "serviceaccount",
			ResourceNames: automationDeploy.ServiceAccount,
		})
	}
	return resources
}

func (p *PipelineRunSvc) createSubEnvAndBindTrafficMark(ctx context.Context, taskRunId int64, clusterName string, namespace string, env string, projectId int64, envTarget, senv string) error {
	SyncSubeEnvTrafficMarkReq := &pbdep.SyncSubEnvTrafficMarkReq{
		ClusterName: clusterName,
		Namespace:   namespace,
		Env:         env,
		ProjectId:   projectId,
		EnvTarget:   envTarget,
		Senv:        senv,
	}
	syncSubeEnvTrafficMarkResp, err := p.subEnvClient.SyncSubEnvTrafficMark(ctx, SyncSubeEnvTrafficMarkReq)
	if err != nil {
		return err
	}
	if syncSubeEnvTrafficMarkResp != nil {
		log.InfoWithCtx(ctx, "创建流量标记&绑定子环境流量标记成功，数据为:%+v", SyncSubeEnvTrafficMarkReq)
	}

	flag, err := p.cloudClient.CreateSubEnvTrafficMark(clusterName, namespace, syncSubeEnvTrafficMarkResp.TrafficMarkName)
	if err != nil {
		return err
	}
	if !flag {
		return fmt.Errorf("集群[%s]，子环境命名空间[%s]，流量标记[%s]，创建流量标记失败", clusterName, namespace, syncSubeEnvTrafficMarkResp.TrafficMarkName)
	}
	err = p.prRepo.UpdatePipelineRunTaskConfigTrafficMark(ctx, taskRunId, syncSubeEnvTrafficMarkResp.TrafficMarkName)
	if err != nil {
		return err
	}
	return nil
}

func (p *PipelineRunSvc) createSubEnv(ctx context.Context, cluster string, baseNamespace string, namespace string, projectId int64) error {
	emails, userErr := p.getProjectManagerEmail(ctx, projectId)
	if userErr != nil {
		return userErr
	}
	nsDetails, err := p.cloudClient.GetNamespaceList(cluster, emails, constants.SUB.String())
	if err != nil {
		return err
	}
	isSubNamespaceExist := tools.Any(nsDetails, func(nsDetail cloud.NamespaceDetail) bool {
		return strings.EqualFold(nsDetail.Namespace, namespace)
	})
	if !isSubNamespaceExist {
		isSuccess, err := p.cloudClient.CreateSubEnv(cluster, baseNamespace, namespace, emails, map[string]interface{}{})
		if err != nil {
			return err
		}
		if isSuccess {
			log.DebugWithCtx(ctx, "创建子环境成功,基准环境集群+命名空间[%s][%s]子环境命名空间[%s]", cluster, baseNamespace, namespace)
		} else {
			createErr := fmt.Errorf("创建基于基准环境集群+命名空间[%s][%s]的子环境命名空间[%s]失败", cluster, baseNamespace, namespace)
			log.ErrorWithCtx(ctx, createErr.Error())
			return createErr
		}
	}
	return nil
}

// RollbackDeploy 调用 Deploy 服务执行服务回滚部署操作
func (p *PipelineRunSvc) RollbackDeploy(ctx context.Context, tektonName string, Id int64) error {
	rollbackReq := &pbdep.RollbackReq{
		Id:         Id,
		TektonName: tektonName,
	}
	deployResp, err := p.deployClient.Rollback(ctx, rollbackReq)
	if err != nil || deployResp.Code != 0 {
		log.ErrorWithCtx(ctx, "rollback call deploy tektonName[%s] id[%d] err:[%v]", tektonName, Id, err)
		return fmt.Errorf("rollback deploy failed")
	}
	log.InfoWithCtx(ctx, "call rollback deploy tektonName[%s] id[%d] success", tektonName, Id)
	return nil
}

func GenerateNamespace(branch string) string {
	namespace := strings.Replace(branch, "/", "-", -1)
	if len(namespace) > 63 {
		return namespace[:63]
	}
	return namespace
}

// getOriginEnvConfigID 根据子环境任务，查询对应的基准环境任务的部署配置
func (p *PipelineRunSvc) getOriginEnvConfigID(ctx context.Context, subEnvTask *dao.PipelineRunSubtask) (int64, error) {
	pr, err := p.prRepo.FindPipelineRunByIdPreload(ctx, subEnvTask.PipelineRunID)
	if err != nil {
		return 0, errors.Wrap(err, "getOriginEnvSubtask find pipelinerun failed")
	}
	subEnvTaskConfig := model.AutomationDeploy{}
	if err := json.Unmarshal(subEnvTask.Config, &subEnvTaskConfig); err != nil {
		return 0, errors.Wrap(err, "getOriginEnvSubtask failed")
	}

	for _, stage := range pr.Stages {
		for _, t := range stage.Tasks {
			if t.ID <= subEnvTask.PipelineRunTaskId {
				continue
			}

			if t.Type == string(constants.TASK_AUTOMATION_DEPLOY) {
				// 根据 namespace cluster 匹配子任务
				for _, subTask := range t.SubRunTasks {
					taskConfig := model.AutomationDeploy{}
					if err := json.Unmarshal(subTask.Config, &taskConfig); err != nil {
						return 0, errors.Wrap(err, "getOriginEnvSubtask unmarshal origin env task config failed")
					}
					if taskConfig.Namespace == subEnvTaskConfig.Namespace && taskConfig.Cluster == subEnvTaskConfig.Cluster {
						return taskConfig.ConfigId, nil
					}
				}
			}
		}
	}
	return 0, errors.New("could not find origin env subtask")
}

func (p *PipelineRunSvc) HandlePrefixPipelineRunDeploy(ctx context.Context, tektonName string, triggerMode constants.TriggerModeType, DeployTektonType string, configId int64) error {
	var automationDeploy model.AutomationDeploy
	log.InfoWithCtx(ctx, "handlePrefixPipelineRunDeploy 调用 called with tektonName[%s],DeployTektonType[%s]", tektonName, DeployTektonType)
	if DeployTektonType == v1.ApprovalDeployTypeMultiCluster {
		// 多云和子环境 2.0都是走这里的逻辑
		pipelineRunSubtask, err := p.prRepo.FindPipelineRunSubtaskByTektonName(ctx, tektonName)
		if pipelineRunSubtask == nil {
			return fmt.Errorf("get 多云 pipelineRunSubtask[%s] not found", tektonName)
		}
		if err != nil {
			return err
		}
		if err = json.Unmarshal(pipelineRunSubtask.Config, &automationDeploy); err != nil {
			log.ErrorWithCtx(ctx, "get 多云 pipelineRunSubtask[%d] automationDeploy config err:[%v]", pipelineRunSubtask.ID, err.Error())
			return err
		}
		if configId != 0 {
			if err = p.prRepo.UpdatePipelineRunSubtaskConfigId(ctx, pipelineRunSubtask.ID, configId); err != nil {
				return err
			}
		} else {
			if automationDeploy.ConfigId == 0 {
				if automationDeploy.EnvTarget == string(constants.SUB_V2) {
					// 子环境 2.0 需要从后面的基准任务获取配置
					configId, err = p.getOriginEnvConfigID(ctx, pipelineRunSubtask)
					if err != nil {
						return errors.Wrap(err, "get origin env subtask failed")
					}
				} else {
					// 没有部署配置信息，需要根据集群+命名空间拿取最新配置，并保存
					appConfigReq := &pbdep.GetAppConfigReq{
						AppId:     pipelineRunSubtask.PipelineRunTask.PipelineRun.AppId,
						Namespace: automationDeploy.Namespace,
						Cluster:   automationDeploy.Cluster,
						EnvTarget: automationDeploy.EnvTarget,
						Senv:      automationDeploy.Senv,
					}
					config, err := p.configClient.GetAppConfig(ctx, appConfigReq)
					if err != nil {
						log.ErrorWithCtx(ctx, "应用id{%d}集群{%s}命名空间{%s}部署配置自动部署配置异常:%v", pipelineRunSubtask.PipelineRunTask.PipelineRun.AppId, automationDeploy.Cluster, automationDeploy.Namespace, err)
						return err
					}
					if config == nil || config.ConfigId == 0 {
						return fmt.Errorf("应用id{%d}集群{%s}命名空间{%s}部署配置自动部署配置为空，无法执行多云部署", pipelineRunSubtask.PipelineRunTask.PipelineRun.AppId, automationDeploy.Cluster, automationDeploy.Namespace)
					}
					configId = config.ConfigId
				}
				if err = p.prRepo.UpdatePipelineRunSubtaskConfigId(ctx, pipelineRunSubtask.ID, configId); err != nil {
					return err
				}
			}
		}

		// 找到正确的 configId 之后，替换原来的 0
		if automationDeploy.ConfigId == 0 {
			automationDeploy.ConfigId = configId
		}

		return p.pipelineRunDeploy(ctx, tektonName, pipelineRunSubtask.PipelineRunTask, automationDeploy, triggerMode, pipelineRunSubtask.ID)

	} else {
		pipelineRunTask, err := p.CheckPipelineRunDeployMsg(ctx, tektonName)
		if err != nil {
			return err
		}
		if err = json.Unmarshal(pipelineRunTask.Config, &automationDeploy); err != nil {
			log.ErrorWithCtx(ctx, "get 单云pipelineRunTask[%d] automationDeploy config err:[%v]", pipelineRunTask.ID, err.Error())
			return err
		}

		taskType := pipelineRunTask.GetType()
		if taskType == constants.TASK_DEV_ENV_IMAGE_SYNC || taskType == constants.TASK_TEST_ENV_IMAGE_SYNC {
			// 纯纯打补丁， 同步镜像 补上 基准环境类型
			if automationDeploy.EnvTarget == "" {
				automationDeploy.EnvTarget = constants.ORIGIN.String()
			}
			// 镜像同步需要获取最新环境配置，把 configId 更新为 0，让后面的逻辑重新获取
			automationDeploy.ConfigId = 0
		}

		return p.pipelineRunDeploy(ctx, tektonName, pipelineRunTask, automationDeploy, triggerMode, 0)
	}
}

func (p *PipelineRunSvc) pipelineRunDeploy(ctx context.Context, tektonName string, pipelineRunTask *dao.PipelineRunTask, automationDeploy model.AutomationDeploy, triggerMode constants.TriggerModeType, subtaskRunId int64) error {
	pushImagePipelineRunTask, err := p.prRepo.FindTaskRunByMultiParams(ctx, model.PipelineRunSearch{PipelineRunId: pipelineRunTask.PipelineRunId, Type: string(constants.TASK_GENERATE_PUSH_IMAGE)})
	if err != nil {
		log.ErrorWithCtx(ctx, "[pipelineRunDeploy] failed to FindTaskRunByMultiParams, err: %v, pipelineRunID: %d", err, pipelineRunTask.PipelineRunId)
		return err
	}
	imageUrl := pushImagePipelineRunTask.GetImageUrlOnTR()
	if imageUrl == "" {
		log.ErrorWithCtx(ctx, "[pipelineRunDeploy] failed to get image url, tektonName: %s", tektonName)
		return fmt.Errorf("tektonName[%s]的taskrun镜像版本为空，无法执行自动化部署", tektonName)
	}
	deployConfigID := automationDeploy.ConfigId
	namespace := automationDeploy.Namespace
	if automationDeploy.IsCreateSubEnv && constants.EnvTargetType(automationDeploy.EnvTarget) == constants.SUB {
		resPrepareSubEnvToDeploy, err := p.prepareSubEnvToDeploy(ctx, pipelineRunTask, &automationDeploy)
		if err != nil {
			log.ErrorWithCtx(ctx, "[pipelineRunDeploy] failed to prepareSubEnvToDeploy, err: %v, pipelineRunID: %d", err, pipelineRunTask.PipelineRunId)
			return err
		}
		deployConfigID = resPrepareSubEnvToDeploy.deployConfigID
		namespace = resPrepareSubEnvToDeploy.namespace
	} else {
		// 手动界面触发存在config_id 即拿config_id对应的配置
		// task config 存在config_id，优先级最高，否则拿服务+集群+命名空间下的配置
		namespace = automationDeploy.Namespace
		if automationDeploy.ConfigId == 0 {
			// 子环境、基准环境部署配置自动部署配置
			config, err := p.configClient.GetAppConfig(ctx, &pbdep.GetAppConfigReq{
				AppId:     pipelineRunTask.PipelineRun.AppId,
				Namespace: automationDeploy.Namespace,
				Cluster:   automationDeploy.Cluster,
				EnvTarget: automationDeploy.EnvTarget,
				Senv:      automationDeploy.Senv,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "[pipelineRunDeploy] 子环境、基准环境部署配置自动部署，获取配置异常: %v", err)
				return err
			}
			deployConfigID = config.GetConfigId()
		}
	}

	if deployConfigID == 0 {
		log.ErrorWithCtx(ctx, "[pipelineRunDeploy] failed to get configId, appName=%s, cluster=%s, namespace=%s", pipelineRunTask.PipelineRun.Pipeline.AppName, automationDeploy.Cluster, namespace)
		return fmt.Errorf("部署服务[%s]集群[%s]镜像[%s]命名空间[%s]失败，错误内容为:服务配置不存在,请配置", pipelineRunTask.PipelineRun.Pipeline.AppName, automationDeploy.Cluster, imageUrl, namespace)
	}
	err = p.prRepo.UpdatePipelineRunTaskConfigId(ctx, pipelineRunTask.ID, deployConfigID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[pipelineRunDeploy] autoDeploy update configId error:%v", err)
		return fmt.Errorf("autoDeploy update configId error:%v", err)
	}
	switch constants.EnvTargetType(automationDeploy.EnvTarget) {
	case constants.SUB:
		err := p.prRepo.UpdatePipelineRunTaskConfigSubNamespace(ctx, pipelineRunTask.ID, namespace)
		if err != nil {
			log.ErrorWithCtx(ctx, "[pipelineRunDeploy] autoDeploy update subNamespace error: %v", err)
			return err
		}
	case constants.SUB_V2:
		if _, err = p.prepareSubEnvV2ToDeploy(ctx, pipelineRunTask, subtaskRunId, &automationDeploy); err != nil {
			log.ErrorWithCtx(ctx, "[pipelineRunDeploy] failed to prepareSubEnvV2ToDeploy, err: %v, pipelineRunID: %d", err, pipelineRunTask.PipelineRunId)
			return err
		}
	}
	log.InfoWithCtx(ctx, "[pipelineRunDeploy] tektonName[%s],流水线自动化表单信息:[%+v],镜像名[%s]最终获取的配置ID[%d]", tektonName, automationDeploy, imageUrl, deployConfigID)
	// 调用增强tekton deploys/name
	triggeredBy, chineseName, employeeNo := p.getDeployTriggers(ctx, pushImagePipelineRunTask.PipelineRunId, triggerMode)
	var approvalTaskId int64
	taskSeq := pipelineRunTask.StageRun.GetTaskSequence()
	if len(taskSeq) > 1 {
		approvalTaskId = taskSeq[0]
	}

	deployParam := &pbdep.DeployParam{
		ConfigId:               deployConfigID,
		AppName:                pipelineRunTask.PipelineRun.Pipeline.AppName,
		Cluster:                automationDeploy.Cluster,
		Namespace:              namespace,
		HelmRepo:               "",
		Branch:                 pipelineRunTask.PipelineRun.Branch,
		Version:                imageUrl,
		TektonName:             tektonName,
		TaskRunId:              pipelineRunTask.ID,
		OperatedBy:             triggeredBy,
		ChineseName:            chineseName,
		EmployeeNo:             employeeNo,
		AppId:                  pipelineRunTask.PipelineRun.Pipeline.AppID,
		TaskRunName:            pipelineRunTask.Name,
		TaskRunType:            pipelineRunTask.Type,
		TicketRelatedTaskRunId: approvalTaskId,
		SubtaskRunId:           subtaskRunId,
		DeployEnv:              automationDeploy.DeployEnv,
	}
	// 子环境2.0（包含金丝雀）Senv 由发布时传递
	if constants.EnvTargetType(automationDeploy.EnvTarget) == constants.SUB_V2 {
		deployParam.Senv = automationDeploy.Senv
	}

	deployMsg, err := p.deployClient.Deploy(ctx, deployParam)
	if err != nil || deployMsg.Code != 0 {
		log.ErrorWithCtx(ctx, "[pipelineRunDeploy] 部署服务[%s]集群[%s]镜像[%s]命名空间[%s]失败，错误内容为:%s", deployParam.AppName, deployParam.Cluster, deployParam.Version, deployParam.Namespace, err)
		return fmt.Errorf("部署服务[%s]集群[%s]镜像[%s]命名空间[%s]失败，错误内容为:%s", deployParam.AppName, deployParam.Cluster, deployParam.Version, deployParam.Namespace, err)
	}
	return nil
}

func (p *PipelineRunSvc) GetDeployTriggers(ctx context.Context, prId int64, triggerMode constants.TriggerModeType) (int64, string, string) {
	return p.getDeployTriggers(ctx, prId, triggerMode)
}

func (p *PipelineRunSvc) getDeployTriggers(ctx context.Context, prId int64, triggerMode constants.TriggerModeType) (int64, string, string) {
	triggeredBy := int64(0)
	chineseName, employeeNo := "", ""
	if triggerMode == constants.MANUAL {
		userInfo := cctx.GetUserinfo(ctx)
		triggeredBy = userInfo.UserID
		chineseName = userInfo.ChineseName
		employeeNo = userInfo.EmployeeNo
	} else {
		var pipelineRun dao.PipelineRun
		err := p.prRepo.FindPipelineRunById(ctx, prId, &pipelineRun)
		if err != nil {
			log.ErrorWithCtx(ctx, "根据流水线id[%d]查询流水线信息失败:%v", prId, err)
		} else {
			triggeredBy = pipelineRun.TriggerBy
			chineseName = pipelineRun.TriggerByChineseName
			employeeNo = pipelineRun.TriggerByEmployeeNo
		}
	}
	return triggeredBy, chineseName, employeeNo
}

func interfaceToInt64(value interface{}) int64 {
	result, _ := strconv.ParseInt(strings.Replace(strings.Replace(value.(string), "\"", "", -1), "\n", "", -1), 10, 64)
	return result
}

func interfaceToString(value interface{}) string {
	return strings.TrimSuffix(value.(string), "\n")
}

func (p *PipelineRunSvc) BuildSonarScanResult(ctx context.Context, task *dao.PipelineRunTask) (*model.SonarScanInfo, error) {
	taskCfg := model.SonarScan{}
	cfgErr := json.Unmarshal(task.Config, &taskCfg)
	if cfgErr != nil {
		return nil, cfgErr
	}

	scanResult := model.SonarScanInfo{}
	taskStatus := task.Status
	if taskStatus == constants.SUCCESSFUL || taskStatus == constants.FAILED || taskStatus == constants.CANCEL {
		scanResult.CompletedTime = timex.ToPointer(task.CompletedTime)
		scanResult.SonarStatus = taskStatus
		metricResult, sonarUrl, err := p.GetSonarResults(ctx, task.Result)
		if err != nil {
			return nil, err
		}
		scanResult.MetricResult = metricResult
		scanResult.SonarUrl = sonarUrl
	}

	// 扫描阈值需要开启卡点
	if taskCfg.TerminatePipeline {
		metricQuality := model.SonarScanMetric{}
		for _, threshold := range taskCfg.ScanThreshold {
			switch threshold.Name {
			case constants.BUG:
				metricQuality.Bug = threshold.Int64()
			case constants.VULNERABILITY:
				metricQuality.Vulnerability = threshold.Int64()
			case constants.BAD_SMELL:
				metricQuality.BadSmell = threshold.Int64()
			case constants.TEST_COVERAGE:
				metricQuality.TestCoverage = threshold.Float64()
			}
		}
		scanResult.MetricQuality = &metricQuality
	}

	return &scanResult, nil
}

func (p *PipelineRunSvc) GetSonarResults(ctx context.Context, taskRunResult []byte) (*model.SonarScanMetric, string, error) {
	taskResult, sonarUrl := model.TaskRunResult{}, ""
	if len(taskRunResult) > 0 {
		resErr := json.Unmarshal(taskRunResult, &taskResult)
		if resErr != nil {
			return nil, "", resErr
		}
		metricResult := model.SonarScanMetric{}
		var failReason string
		results := taskResult.Results
		for _, resultItem := range results {
			switch resultItem.Name {
			case "bug-num":
				metricResult.Bug = interfaceToInt64(resultItem.Value)
			case "code-smell-num":
				metricResult.BadSmell = interfaceToInt64(resultItem.Value)
			case "vulnerability-num":
				metricResult.Vulnerability = interfaceToInt64(resultItem.Value)
			case "fail-reason":
				failReason = interfaceToString(resultItem.Value)
			case "sonar-report":
				sonarUrl = interfaceToString(resultItem.Value)
			case "coverage":
				baseResult := strings.Replace(resultItem.Value.(string), "%", "", -1)
				result, _ := strconv.ParseFloat(strings.Replace(strings.Replace(baseResult, "\"", "", -1), "\n", "", -1), 64)
				metricResult.TestCoverage = result
			}
		}

		// sonar任务成功或任务失败且失败原因是"quality"才有值
		if failReason == "quality" || failReason == "" {
			return &metricResult, sonarUrl, nil
		}
	}
	return nil, "", nil
}

type (
	StatusFunc func(context.Context, int64, map[string]interface{}) error
	StatusNode struct {
		id             int64
		exec           StatusFunc
		parent         *StatusNode
		status         constants.PipelineStatus
		needUpdate     bool
		originalStatus constants.PipelineStatus
		pipelineRunId  int64
	}
)

func (p *PipelineRunSvc) UpdateRunStatus(ctx context.Context, runStatus *model.RunStatus) error {
	status := runStatus.Status
	task, err := p.prRepo.FindRunTaskById(ctx, runStatus.TaskID)
	log.DebugWithCtx(ctx, "update run status, taskrun id: %d, status: %s", runStatus.TaskID, status)
	if err != nil {
		return err
	}
	if task == nil {
		errInfo := fmt.Sprintf("找不到任务记录，taskrun id: %d", runStatus.TaskID)
		log.ErrorWithCtx(ctx, errInfo)
		return fmt.Errorf(errInfo)
	}
	if task.Status == status {
		return nil
	}

	if task.Status.IsFinished() && !status.IsCancel() {
		if !task.Status.IsFailed() || (!task.IsApprovalTask() && !task.IsApiAutomationTestTask()) {
			errInfo := fmt.Sprintf("任务已经结束，taskrun id: %d, taskrun status: %v", runStatus.TaskID, task.Status)
			log.ErrorWithCtx(ctx, errInfo)
			return fmt.Errorf(errInfo)
		}
	}
	stageNewStatus, needUpdateStage := getStageStatus(status, task)
	pipelineNewStatus, needUpdatePipeline := getPipelineStatus(stageNewStatus, task)
	log.DebugWithCtx(ctx, "pipelineNewStatus: %v, needUpdatePipeline: %v, originalStatus: %v", pipelineNewStatus, needUpdatePipeline, task.PipelineRun.Status)
	log.DebugWithCtx(ctx, "stageNewStatus: %v, needUpdateStage: %v, originalStatus: %v", stageNewStatus, needUpdateStage, task.StageRun.Status)
	pipelineStatus := StatusNode{
		id:             task.PipelineRunId,
		exec:           p.prRepo.UpdatePipelineRunStatusAndTime,
		status:         pipelineNewStatus,
		needUpdate:     needUpdatePipeline,
		originalStatus: task.PipelineRun.Status,
	}

	stageStatus := StatusNode{
		id:             task.PipelineRunStageId,
		exec:           p.prRepo.UpdateStageRunStatusAndTime,
		parent:         &pipelineStatus,
		status:         stageNewStatus,
		needUpdate:     needUpdateStage,
		originalStatus: task.StageRun.Status,
	}

	taskStatus := StatusNode{
		id:             task.ID,
		exec:           p.prRepo.UpdateTaskRunStatusAndTime,
		parent:         &stageStatus,
		status:         status,
		needUpdate:     true,
		originalStatus: task.Status,
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		dbCtx := db.CtxWithTX(ctx, tx)
		prId := task.PipelineRunId
		pr, err := p.prRepo.TxFindPipelineRunLocked(dbCtx, prId)
		if err != nil || pr == nil {
			log.ErrorWithCtx(ctx, "update run status, taskrun id: %d, lock pipelinerun failed: %s", task.ID, err)
			return err
		}

		if err := updateOfStatusGroup(dbCtx, &taskStatus); err != nil {
			return err
		}

		return nil
	})

	if err == nil {
		newTaskRun, _ := p.prRepo.FindRunTaskById(ctx, task.ID)
		log.DebugWithCtx(ctx, "new task run after update status taskrun: %v, stageRun:%v, pipelineRun: %v", newTaskRun, newTaskRun.StageRun, newTaskRun.PipelineRun)
		deliverEvent(ctx, p.sender, newTaskRun, nil)
		if newTaskRun.StageRun != nil {
			deliverEvent(ctx, p.sender, newTaskRun.StageRun, nil)
		}
		if newTaskRun.PipelineRun != nil {
			deliverEvent(ctx, p.sender, newTaskRun.PipelineRun, nil)
		}
	}

	return err
}

func getStageStatus(taskStatus constants.PipelineStatus, taskRun *dao.PipelineRunTask) (constants.PipelineStatus, bool) {
	if taskStatus == constants.FAILED || taskStatus == constants.CANCEL || taskStatus == constants.UNHANDLED {
		return taskStatus, true
	}

	if taskStatus == constants.SUCCESSFUL && taskRun.StageRun.IsLastTask(taskRun.ID) {
		return constants.SUCCESSFUL, true
	}

	if taskStatus == constants.RUNNING && taskRun.StageRun.Status.CanUpdateToRunning() {
		return taskStatus, true
	}
	return taskRun.StageRun.Status, false
}

func getPipelineStatus(stageStatus constants.PipelineStatus, taskRun *dao.PipelineRunTask) (constants.PipelineStatus, bool) {
	if stageStatus == constants.FAILED || stageStatus == constants.CANCEL || stageStatus == constants.UNHANDLED {
		return stageStatus, true
	}

	if stageStatus == constants.SUCCESSFUL && taskRun.PipelineRun.IsLastStage(taskRun.PipelineRunStageId) {
		return constants.SUCCESSFUL, true
	}
	if stageStatus == constants.RUNNING && taskRun.PipelineRun.Status.CanUpdateToRunning() {
		return stageStatus, true
	}
	return taskRun.PipelineRun.Status, false
}

func updateOfStatusGroup(ctx context.Context, node *StatusNode) error {
	log.DebugWithCtx(ctx, "node status update: %v", node)
	if !node.needUpdate {
		return nil
	}
	updatedFields := map[string]interface{}{}
	status := node.status
	updatedFields["status"] = status
	if status.IsFinished() {
		updatedFields["completed_time"] = time.Now()
	} else if status == constants.PENDING {
		updatedFields["started_time"] = time.Time{}
		updatedFields["completed_time"] = time.Time{}
	} else if status == constants.RUNNING && node.originalStatus != constants.UNHANDLED {
		updatedFields["started_time"] = time.Now()
		updatedFields["completed_time"] = time.Time{}
	}
	if err := node.exec(ctx, node.id, updatedFields); err != nil {
		return err
	}
	if node.parent != nil {
		if err := updateOfStatusGroup(ctx, node.parent); err != nil {
			return err
		}
	}
	return nil
}

func (p *PipelineRunSvc) RunManualDeploy(ctx context.Context, manualDeployTaskParam *model.ManualDeployTaskParam) error {
	// 检查是否有在途工单
	treq := &pbdep.CheckHasTicketByReq{
		AppId:     manualDeployTaskParam.AppId,
		Namespace: manualDeployTaskParam.Namespace,
		Cluster:   manualDeployTaskParam.Cluster,
		EnvTarget: string(manualDeployTaskParam.EnvTarget),
		Senv:      manualDeployTaskParam.Senv,
	}
	ticketStatus, err := p.ticketClient.CheckTicketIsApproved(ctx, treq)
	if err != nil {
		log.ErrorWithCtx(ctx, "查询当前服务[%d],集群[%s],命名空间[%s],子环境名称[%s]下是否有在途工单出错:%v", treq.AppId, treq.Cluster, treq.Namespace, treq.GetSenv(), err)
		return err
	}
	if ticketStatus.IsApproved {
		return pipelineErr.ErrHasTicketIsRunning
	}
	// 更新运行时任务配置以及流水线配置信息
	var taskRun *dao.PipelineRunTask
	taskRun, err = p.prRepo.FindRunTaskById(ctx, manualDeployTaskParam.TaskRunId)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据taskRunId[%d]查询taskRun出错: %v", manualDeployTaskParam.TaskRunId, err)
		return err
	}
	if taskRun == nil {
		return fmt.Errorf("查询不到自动化部署的taskRunId[%d]，请检查数据", manualDeployTaskParam.TaskRunId)
	}
	if taskRun.Type != string(constants.TASK_AUTOMATION_DEPLOY) {
		return fmt.Errorf("任务[%d]不是自动化部署任务，请检查数据", manualDeployTaskParam.TaskRunId)
	}
	err = p.UpdateTaskRunAndPipelineConfig(ctx, *taskRun, manualDeployTaskParam)
	if err != nil {
		return err
	}
	if taskRun.Status == constants.FAILED {
		// 任务失败的话，走重试逻辑
		err = p.RetryBasicTask(ctx, taskRun)
		if err != nil {
			log.ErrorWithCtx(ctx, "重试触发自动化部署[task_run_id:%d]失败:%v", taskRun.ID, err)
			return err
		}
	} else {
		// 创建子环境以及部署
		err = p.HandlePrefixPipelineRunDeploy(ctx, taskRun.TektonName, constants.MANUAL, v1.ApprovalDeployTypeSingleCluster, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "手动触发自动化部署[tektonName:%s]失败:%v", taskRun.TektonName, err)
			return err
		}
	}
	return nil
}

func (p *PipelineRunSvc) UpdateTaskRunAndPipelineConfig(ctx context.Context, taskRun dao.PipelineRunTask, manualDeployTaskParam *model.ManualDeployTaskParam) error {
	var config model.AutomationDeploy
	err := json.Unmarshal(taskRun.Config, &config)
	if err != nil {
		return err
	}
	pipelineRunId, err := p.prRepo.GetPipelineRunIdByTaskRunId(ctx, manualDeployTaskParam.TaskRunId)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据taskRunId[%d]查询pipelineRunId出错: %v", manualDeployTaskParam.TaskRunId, err)
		return err
	}
	pipeline, err := p.pRepo.GetPipelineByPipelineRunId(ctx, pipelineRunId)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据pipelineRunId[%d]查询pipeline出错: %v", pipelineRunId, err)
		return err
	}
	if pipeline == nil {
		return fmt.Errorf("pipleinRunId[%d]对应的流水线不存在，请检查数据", pipelineRunId)
	}
	config.Cluster = manualDeployTaskParam.Cluster
	config.Namespace = manualDeployTaskParam.Namespace
	config.IsCreateSubEnv = manualDeployTaskParam.IsCreateSubEnv
	config.TrafficMark = manualDeployTaskParam.TrafficMark
	config.ConfigId = manualDeployTaskParam.ConfigId
	config.ConfigMap = manualDeployTaskParam.ConfigMap
	config.Secret = manualDeployTaskParam.Secret
	config.ServiceAccount = manualDeployTaskParam.ServiceAccount
	config.Senv = manualDeployTaskParam.Senv
	configByte, err := json.Marshal(config)
	updateConfigParams := &model.UpdatePipelineConfigParams{
		TaskId: taskRun.TaskId, PipelineRunId: pipelineRunId,
		TaskRunId: manualDeployTaskParam.TaskRunId, UpdatedPipelineConfig: configByte,
		UpdatedTaskRunConfig: configByte,
	}
	err = p.plService.UpdatePipelineConfig(ctx, updateConfigParams)
	if err != nil {
		log.ErrorWithCtx(ctx, "更新taskRun配置以及流水线配置失败:%v", err)
		return err
	}
	return nil
}

func (p *PipelineRunSvc) GetTaskLogExtraInfo(ctx context.Context, extraInfo *model.TaskRunExtraInfo) error {
	addClusterInfo := func(data []byte, extraInfo *model.TaskRunExtraInfo) error {
		if len(data) > 0 {
			var cfg model.AutomationDeploy
			err := json.Unmarshal(data, &cfg)
			if err != nil {
				return err
			}
			extraInfo.Cluster = cfg.Cluster
			extraInfo.Namespace = cfg.Namespace
		}
		return nil
	}
	setCompletedTime := func(t time.Time, extraInfo *model.TaskRunExtraInfo) {
		if t.IsZero() {
			extraInfo.CompletedTime = ""
		} else {
			extraInfo.CompletedTime = timeutil.Format(t)[0:19]
		}
	}
	switch extraInfo.Type {
	case "task":
		taskRun, errT := p.prRepo.FindTaskRunById(ctx, extraInfo.Tid)
		if errT != nil {
			log.ErrorWithCtx(ctx, "根据taskRunId查询pipelineRunTask错误:%v", errT)
			return errT
		}
		if err := addClusterInfo(taskRun.Config, extraInfo); err != nil {
			return err
		}
		user, err := p.GetTaskRunOperator(ctx, taskRun.ID, extraInfo.Pid)
		if err != nil || user == nil {
			// 考虑重复创建流水线的情况
			sameTaskRuns, err := p.prRepo.FindTaskRunWithSameBuildNumberBy(ctx, taskRun.ID)
			if err != nil {
				return err
			}
			for _, run := range sameTaskRuns {
				if taskRun.TektonName == run.TektonName && run.ID != taskRun.ID {
					user, err = p.GetTaskRunOperator(ctx, run.ID, run.PipelineRunId)
					if err != nil {
						return fmt.Errorf("根据taskRunId[%d]查询任务操作人错误", taskRun.ID)
					}
					break
				}
			}
		}
		pipeline, err := p.pRepo.GetPipelineByPipelineRunId(ctx, taskRun.PipelineRunId)
		if err != nil {
			return err
		}
		if pipeline == nil {
			return fmt.Errorf("根据pipelineRunId[%d]查询pipeline错误", taskRun.PipelineRunId)
		}
		extraInfo.AppName = pipeline.AppName
		extraInfo.Operator = user.User.ChineseName
		setCompletedTime(taskRun.CompletedTime, extraInfo)
	case "subTask":
		// TODO: 同步任务的兼容支持
		subTask, err := p.prRepo.FindSubTaskById(ctx, extraInfo.Tid)
		if err != nil || subTask == nil {
			return fmt.Errorf("根据subTaskId[%d]查询子任务错误,未找到对应的子任务", extraInfo.Tid)
		}
		if err := addClusterInfo(subTask.Config, extraInfo); err != nil {
			return err
		}
		changeLog, err := p.deployClient.FindChangeLogBySubTaskRunID(context.Background(), &pbdep.SubTaskRunId{Id: subTask.ID})
		if err != nil {
			return fmt.Errorf("根据taskRunId[%d],查询子任务操作人错误,未找到部署变更记录", subTask.ID)
		}
		if changeLog.Id == 0 {
			return fmt.Errorf("部署记录不存在")
		}
		operator, err := p.userClient.GetUserById(context.TODO(), &iam.UserParam{UserId: changeLog.OperatorBy})
		if err != nil || operator == nil {
			return fmt.Errorf("根据taskRunId[%d]查询子任务操作人错误,未找到对应的操作人信息", subTask.ID)
		}
		pipeline, err := p.pRepo.GetPipelineByPipelineRunId(ctx, subTask.PipelineRunID)
		if err != nil {
			return err
		}
		if pipeline == nil {
			return fmt.Errorf("根据pipelineRunId[%d]查询pipeline错误", subTask.PipelineRunID)
		}
		extraInfo.AppName = pipeline.AppName
		extraInfo.Operator = operator.User.ChineseName
		setCompletedTime(subTask.CompletedTime, extraInfo)
	}
	return nil
}

func (p *PipelineRunSvc) getCreatedAtById(ctx context.Context, pipelineRunId int64) (time.Time, error) {
	var pipelineRun dao.PipelineRun
	err := p.prRepo.FindPipelineRunById(ctx, pipelineRunId, &pipelineRun)
	if err != nil {
		return time.Time{}, err
	}
	return pipelineRun.CreatedAt, nil
}

func (p *PipelineRunSvc) GetRetryTimeout(ctx context.Context, params *model.RetryCheckTimeout) error {
	startDate := ""   // 触发日期
	durationDays := 0 // 重试、部署操作期限还剩x天
	expireDate := ""  // 操作截止日期

	var startTime time.Time

	params.RetryTimeout = conf.AppConfig.Retry.TimeoutDays
	if params.PipelineRunId != 0 {
		createdAt, err := p.getCreatedAtById(ctx, params.PipelineRunId)
		if err != nil {
			return err
		}
		startTime = createdAt
	} else if params.TaskRunId != 0 {
		pipelineRunId, err := p.prRepo.GetPipelineRunIdByTaskRunId(ctx, params.TaskRunId)
		if err != nil {
			return err
		}
		createdAt, err := p.getCreatedAtById(ctx, pipelineRunId)
		if err != nil {
			return err
		}
		startTime = createdAt
	} else if params.ChangeSetId != 0 {
		changeSet, err := p.changeSetRepo.FindChSetById(ctx, params.ChangeSetId)
		if err != nil || changeSet == nil {
			errInfo := fmt.Errorf("根据变更集id=%d查询变更集信息错误", params.ChangeSetId)
			log.ErrorWithCtx(ctx, errInfo.Error())
			return errInfo
		}
		startTime = changeSet.StartedTime
		if startTime.IsZero() {
			startTime = time.Now()
		}
	} else if params.TicketId != 0 {
		ticket, err := p.ticketClient.GetTicketBy(ctx, &pbdep.GetTicketReq{Id: params.TicketId})
		if err != nil {
			log.ErrorWithCtx(ctx, "查询工单发生错误: %v", err)
			return err
		}
		if ticket.Type == constants.ReDeployTicket.String() ||
			ticket.Type == constants.RouteDeployTicket.String() ||
			ticket.Type == constants.EventlinkDeployTicket.String() {
			startTime = ticket.CreatedAt.AsTime()
		} else {
			if ticket.IsChangeSet == 0 {
				taskRunId := ticket.TaskRunId
				pipelineRunId, err := p.prRepo.GetPipelineRunIdByTaskRunId(ctx, taskRunId)
				if err != nil {
					return err
				}
				createdAt, err := p.getCreatedAtById(ctx, pipelineRunId)
				if err != nil {
					return err
				}
				startTime = createdAt
			} else {
				changeSetTaskId := ticket.ChangeSetTaskId
				changeSetTask, err := p.changeSetRepo.FindChSetTaskByIdPreload(ctx, changeSetTaskId)
				if err != nil {
					return err
				}
				startTime = changeSetTask.ChangeSet.StartedTime
				if startTime.IsZero() {
					startTime = time.Now()
				}
			}
		}
	} else {
		return fmt.Errorf("没有查询条件")
	}

	isRetry := p.isRetry(startTime, &durationDays, &expireDate, &startDate)
	params.Days = durationDays
	params.ExpireDate = expireDate
	params.StartDate = startDate
	if isRetry {
		params.CanRetry = true
	}

	return nil
}

func (p *PipelineRunSvc) isRetry(startedTime time.Time, durationDays *int, expireDate, startDateStr *string) bool {
	if startedTime.IsZero() {
		return false
	}
	startDate := startedTime.Truncate(24 * time.Hour) // 只保留日期部分，将时间部分置零
	*startDateStr = startDate.Format(timeutil.DateLayOut)

	now := time.Now().Truncate(24 * time.Hour)
	duration := now.Sub(startDate)
	*durationDays = int(duration.Hours() / 24)
	*expireDate = startDate.AddDate(0, 0, conf.AppConfig.Retry.TimeoutDays).Format(timeutil.DateLayOut)
	return *durationDays < conf.AppConfig.Retry.TimeoutDays
}

func (p *PipelineRunSvc) IsInChangeSet(ctx context.Context, taskRun *dao.PipelineRunTask, changeSetId int64) bool {
	log.DebugWithCtx(ctx, "IsInChangeSet:taskRunName=%s,taskRunType=%s,changeSetId=%d", taskRun.Name, taskRun.Type, changeSetId)
	changeSet, err := p.changeSetRepo.FindChSetByIdPreload(ctx, changeSetId)
	if err != nil {
		log.ErrorWithCtx(ctx, "check IsInChangeSet err: %v", err)
		return false
	}
	changeSetStageIndex := NotFound
	for index, stage := range changeSet.Stages {
		if isManagedByChangeSet(stage.Type.String()) {
			changeSetStageIndex = index
			break
		}
	}
	var inChangeSet bool
	if changeSetStageIndex != NotFound {
		changeSetStages := changeSet.Stages[changeSetStageIndex:]
		log.DebugWithCtx(ctx, "IsInChangeSet:changeSetStages=%v", changeSetStages)
		inChangeSet = tools.Any(changeSetStages, func(stage dao.ChangeSetStage) bool {
			return stage.StageID == taskRun.StageRun.StageId
		})
	}
	log.DebugWithCtx(ctx, "IsInChangeSet:inChangeSet=%v", inChangeSet)
	return inChangeSet
}

func (p *PipelineRunSvc) cloneNewPipelineRun(ctx context.Context, taskRun *dao.PipelineRunTask) (*dao.PipelineRun, *dao.PipelineRunTask, error) {
	log.DebugWithCtx(ctx, "克隆流水线实例[pr-%d tr-%d]，开始加载待克隆流水线实例数据", taskRun.PipelineRunId, taskRun.ID)
	pipelineRun, err := p.prRepo.FindPipelineRunWithPreload(ctx, taskRun.PipelineRunId)
	if err != nil {
		log.ErrorWithCtx(ctx, "克隆流水线实例[pr-%d tr-%d]，加载待克隆流水线实例数据，发生异常: %v", taskRun.PipelineRunId, taskRun.ID, err)
		return nil, nil, err
	}
	// 克隆旧的流水线，更改流水线状态、流水线操作人、更新时间、流水线stage task顺序置空
	retryPipelineRun := pipelineRun.CloneOnRetryTask(*taskRun)
	// 流水线的触发人保持为原始触发人
	retryPipelineRun.SetTriggeredByInfo(pipelineRun.TriggerBy, pipelineRun.TriggerByChineseName, pipelineRun.TriggerByEmployeeNo)
	pipeline, err := p.pRepo.GetPipeline(ctx, retryPipelineRun.PipelineId)
	if err != nil || pipeline == nil {
		log.ErrorWithCtx(ctx, "克隆流水线实例[pr-%d tr-%d]，加载流水线数据，发生异常: %v", taskRun.PipelineRunId, taskRun.ID, err)
		return nil, nil, err
	}
	// 包含金丝雀或者预发布的 且是生产升级工单的任务 重试需要全部清空状态，因为重提单会下线金丝雀
	forceChangeStatusFlag := pipelineRun.HasCanaryOrSatingTask() && taskRun.Type == constants.TASK_UPGRADE_APPROVAL.String()
	fn := func(tx *gorm.DB) error {
		txCtx := db.CtxWithTX(ctx, tx)
		if err := p.prRepo.CreatePipelineRun(txCtx, retryPipelineRun, forceChangeStatusFlag); err != nil {
			return err
		}
		return nil
	}
	err = db.Transaction(fn)
	if err != nil {
		log.ErrorWithCtx(ctx, "克隆流水线[pr-%d tr-%d]，保存新流水线数据，发生异常: %v", taskRun.PipelineRunId, taskRun.ID, err)
		return nil, nil, err
	}
	// 获取生成的task任务内 重试任务所在的id
	var retryNewPipelineRunTask dao.PipelineRunTask
	for _, stage := range retryPipelineRun.Stages {
		for _, task := range stage.Tasks {
			if task.TaskId == taskRun.TaskId {
				retryNewPipelineRunTask = task
				break
			}
		}
	}

	retryPipelineRun.Pipeline = pipeline
	return retryPipelineRun, &retryNewPipelineRunTask, nil
}

func (p *PipelineRunSvc) getTicketMap(ctx context.Context, oldPipelineRunId int64, retryPipelineRunId int64) (map[int64]int64, error) {
	// sync ticket pipeline runs
	ticketTypes := []string{string(constants.TASK_TEST_ACCEPTANCE), string(constants.TASK_TEST_APPROVAL), string(constants.TASK_GRAY_UPGRADE_APPROVAL), string(constants.TASK_UPGRADE_APPROVAL)}
	oldPipelineRunSearch := &model.PipelineRunTaskSearch{PipelineRunId: oldPipelineRunId, Types: ticketTypes}
	newPipelineRunSearch := &model.PipelineRunTaskSearch{PipelineRunId: retryPipelineRunId, Types: ticketTypes}
	oldPipelineRunTasks, err := p.prRepo.QueryPipelineRunTasksByMultiParams(ctx, oldPipelineRunSearch)
	if err != nil {
		return nil, err
	}
	newPipelineRunTasks, err := p.prRepo.QueryPipelineRunTasksByMultiParams(ctx, newPipelineRunSearch)
	if err != nil {
		return nil, err
	}
	pipelineRunTaskMap := make(map[int64]int64)
	for _, oldPipelineRunTask := range oldPipelineRunTasks {
		for _, newPipelineRunTask := range newPipelineRunTasks {
			if oldPipelineRunTask.Type == newPipelineRunTask.Type {
				pipelineRunTaskMap[oldPipelineRunTask.ID] = newPipelineRunTask.ID
			}
		}
	}
	return pipelineRunTaskMap, nil
}

func (p *PipelineRunSvc) syncTicketTaskRun(ctx context.Context, oldPipelineRunId int64, retryPipelineRunId int64) error {
	// sync ticket pipeline runs
	if pipelineRunTaskMap, err := p.getTicketMap(ctx, oldPipelineRunId, retryPipelineRunId); err == nil && len(pipelineRunTaskMap) > 0 {
		// sync grpc ticket flow_instance task_run_id
		_, err = p.ticketClient.SyncTicketPipelineRunTask(ctx, &pbdep.SyncTicketPipelineRunTaskReq{TaskRunMap: pipelineRunTaskMap})
		if err != nil {
			msg := fmt.Sprintf("[syncTicketTaskRun]更新工单&工单实例的task_run_id,内容[%+v]失败.err:%v", pipelineRunTaskMap, err)
			log.ErrorWithCtx(ctx, msg)
			return fmt.Errorf("%v", msg)
		}
	}
	return nil
}

func (p *PipelineRunSvc) setNewestPipelineRunIdToChangeSet(ctx context.Context, taskRun *dao.PipelineRunTask, pipelineRunId, changeSetId int64, isInChangeSet bool) error {
	log.DebugWithCtx(ctx, "setNewestPipelineRunIdToChangeSet: taskRunName=%s,taskRunType=%s,pipelineRunId=%d,changeSetId=%d,isInChangeSet=%v", taskRun.Name, taskRun.Type, pipelineRunId, changeSetId, isInChangeSet)
	var pipelineRun dao.PipelineRun
	if err := p.prRepo.FindPipelineRunById(ctx, pipelineRunId, &pipelineRun); err != nil {
		log.ErrorWithCtx(ctx, "find pipeline run by id=%d err: %v", pipelineRunId, err)
		return err
	}
	query := model.RetryPipelineRunQuery{
		PipelineId:  pipelineRun.PipelineId,
		BuildNumber: pipelineRun.BuildNumber,
		Order:       "DESC",
	}
	plRun, err := p.prRepo.FindRetryPipelineRun(ctx, &query)
	if err != nil || plRun == nil {
		log.ErrorWithCtx(ctx, "retry taskRun=%s err: %v", taskRun.Name, err)
		return errors.Wrapf(err, "重试任务%s发生错误", taskRun.Name)
	}
	updateInfo := UpdateFields{
		"pipeline_run_id": plRun.ID,
	}
	if err := p.changeSetRepo.UpdateChSetPipelineByChSetByAndPipeId(ctx, changeSetId, plRun.PipelineId, updateInfo); err != nil {
		log.ErrorWithCtx(ctx, "set the newest pipeline_run_id to change_set_pipeline err: %v", err)
		return fmt.Errorf("设置重试任务%s的PipelineRunId到变更集错误: %w", taskRun.Name, err)
	}
	return nil
}

func (p *PipelineRunSvc) RetryBasicTask(ctx context.Context, taskRun *dao.PipelineRunTask) error {
	key := fmt.Sprintf("retry-pipeline-run-%d-%d-task-%d", taskRun.PipelineRunId, taskRun.PipelineRun.BuildNumber, taskRun.ID)
	// 互斥锁，防止重复重试
	// 存储需要克隆的重试流水线任务id
	cmd := p.redisCli.SetNX(ctx, key, "1", 10*time.Second)
	if cmd.Err() != nil {
		log.ErrorWithCtx(ctx, "[RetryBasicTask]SetNX pipeline:RetryPipeline key[%s], error", key)
		return cmd.Err()
	}
	if !cmd.Val() {
		log.ErrorWithCtx(ctx, "[RetryBasicTask]当前流水线[%d]任务[%s]已经开始重试，key[%s]", taskRun.PipelineRunId, taskRun.Name, key)
		return pipelineErr.ErrRetryIsRunning
	}

	log.DebugWithCtx(ctx, "[RetryBasicTask]重试任务key[%s]实例[%d]，开始克隆流水线", key, taskRun.ID)
	retryPipelineRun, retryNewPipelineRunTask, err := p.cloneNewPipelineRun(ctx, taskRun)
	if err != nil {
		return err
	}
	// sync ticket old taskrun into new taskrun
	err = p.syncTicketTaskRun(ctx, taskRun.PipelineRunId, retryPipelineRun.ID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[RetryBasicTask]retryBasicTask sync ticket old taskrun into new taskrun err: %v", err)
		return err
	}
	pipelineRunTaskMap, err := p.getTicketMap(ctx, taskRun.PipelineRunId, retryPipelineRun.ID)
	if err != nil {
		return err
	}
	// sync ticket pipeline runs && sync grpc ticket flow_instance task_run_id
	_, err = p.ticketClient.SyncTicketPipelineRunTask(ctx, &pbdep.SyncTicketPipelineRunTaskReq{TaskRunMap: pipelineRunTaskMap})
	if err != nil {
		msg := fmt.Sprintf("更新工单&工单实例的task_run_id,内容[%+v]失败.err:%v", pipelineRunTaskMap, err)
		log.ErrorWithCtx(ctx, msg)
		return fmt.Errorf("%v", msg)
	}
	// 重试 同步发布计划相关的表 修改pipeline_run_id
	_, err = p.deployPlanClient.UpdatePlanPrIDByRetryPr(ctx, &pbdep.PlanRunByRetryPrReq{PipelineIdMap: map[int64]int64{taskRun.PipelineRunId: retryPipelineRun.ID}})
	if err != nil {
		log.ErrorWithCtx(ctx, "retryBasicTask sync deploy plan pr id err: %v, old pipeline_run_id:%d,new pipeline_run_id:%d", err, taskRun.PipelineRunId, retryPipelineRun.ID)
		return err
	}
	ciConfigStages := conf.GetCiStagesConfig()
	isCiStage := tools.Any(ciConfigStages, func(r string) bool {
		return strings.EqualFold(r, taskRun.StageRun.Type)
	})
	if !isCiStage {
		cdPipelineRunTask, err := p.prRepo.FindRunTaskById(ctx, retryNewPipelineRunTask.ID)
		if err != nil {
			return err
		}
		// cd 任务重试
		if retryPipelineRun.IsManagedByChangeSet() {
			// 更新变更集最新的pipelinerun&taskrun信息 changeset_run_task 表
			chSet, err := p.changeSetRepo.GetChangeSetPreloadsById(ctx, retryPipelineRun.ChangeSetId)
			if err != nil {
				return err
			}
			if chSet == nil {
				return nil
			}
			err = p.changeSetRepo.UpdateChSetPipelineFields(ctx, chSet.ID, taskRun.PipelineRunId, map[string]interface{}{
				"pipeline_run_id": retryPipelineRun.ID,
				"task_run_id":     cdPipelineRunTask.ID,
				"task_run_status": constants.RUNNING,
				"tip":             "已更新",
			})
			changeSetRunTask, err := p.plService.GenerateChangeSetRunTaskByRetry(ctx, retryPipelineRun, chSet, cdPipelineRunTask)
			if err != nil {
				log.ErrorWithCtx(ctx, "retry deploy generate dao changeSetTask run failed %v", err)
				return err
			}
			err = p.changeSetRepo.CreateChangeSetRunTask(ctx, changeSetRunTask)
			if err != nil {
				log.ErrorWithCtx(ctx, "retry deploy create dao changeSetTask run failed %v", err)
				return err
			}
			err = p.changeSetService.RetryChangeSetByPrRunTask(ctx, retryPipelineRun.ID, chSet.ID, cdPipelineRunTask)
			if err != nil {
				return err
			}
			// 终止重试前的流水线 避免影响其他变更集下的流水线
			err = p.CancelPipelineRun(ctx, model.PipelineRun{TektonNamespace: taskRun.PipelineRun.TektonNamespace, TektonName: taskRun.PipelineRun.TektonName, Status: taskRun.PipelineRun.Status})
			if err != nil {
				log.ErrorWithCtx(ctx, "变更集id[%d] cancel PipelineRun id: %d 失败: %v", chSet.ID, taskRun.PipelineRunId, err)
				return nil
			}
			// 更新工单对应的taskrunid
			_, err = p.ticketClient.SyncChangeSetTicketPipelineRunTask(ctx, &pbdep.SyncChangeSetTicketPipelineRunTaskReq{ChangeSetId: chSet.ID, TaskRunMap: pipelineRunTaskMap})
			if err != nil {
				return err
			}
		} else {
			err = p.retryCdTask(ctx, *cdPipelineRunTask, *retryPipelineRun, true)
			if err != nil {
				return err
			}
		}
	} else {
		// 获取重试pipeline_run_task 之后的节点
		retryCiPipelineRun, err := p.getRetryCiPipelineRun(ctx, retryPipelineRun, retryNewPipelineRunTask)
		if err != nil {
			log.ErrorWithCtx(ctx, "getRetryCiPipelineRun: %+v", err)
			return fmt.Errorf("获取重试流水线数据失败，请联系管理员处理！")
		}
		v, _ := json.Marshal(retryCiPipelineRun)
		log.InfoWithCtx(ctx, "[RetryBasicTask]重试的任务id:%d,获取序列化的重试CiPipelineRun:%d,内容:%s", taskRun.ID, retryNewPipelineRunTask.ID, string(v))
		// CI 阶段执行创建pipelinerun任务（仅传入包含重试节点之后的任务）
		if err := p.retryCiTask(ctx, *retryNewPipelineRunTask, *retryCiPipelineRun); err != nil {
			log.ErrorWithCtx(ctx, "retry ci task failed: %v", err)
			return err
		}
	}
	if retryPipelineRun.IsManagedByChangeSet() {
		isInChangeSet := p.IsInChangeSet(ctx, taskRun, retryPipelineRun.ChangeSetId)
		_ = p.setNewestPipelineRunIdToChangeSet(ctx, taskRun, retryPipelineRun.ID, retryPipelineRun.ChangeSetId, isInChangeSet)
	}
	pl, err := p.pRepo.GetPipeline(ctx, retryPipelineRun.PipelineId)
	if err == nil && pl != nil {
		if discussionId, err := p.tryAppendDiscussion(ctx, pl, retryPipelineRun); err == nil && discussionId != retryPipelineRun.DiscussID {
			updateFields := map[string]interface{}{
				"discuss_id": discussionId,
			}
			_ = p.prRepo.UpdatePipelineRunFields(ctx, retryPipelineRun.ID, updateFields)
		}
	}

	return nil
}

func (p *PipelineRunSvc) retryCdTask(ctx context.Context, taskRun dao.PipelineRunTask, pipelineRun dao.PipelineRun, isRetry bool) error {
	firstPipelineRun, e := p.prRepo.FindFirstPipelineRunBy(ctx, pipelineRun.PipelineId, pipelineRun.BuildNumber)
	if e != nil {
		log.ErrorWithCtx(ctx, "[retryCdTask]流水线重试taskrun任务[%s]: find first pipeline run failed: %v", taskRun.Name, e)
		return e
	}

	runner := v1.New(&taskRun, &pipelineRun, isRetry, timeutil.ToYearMonth(firstPipelineRun.CreatedAt))
	tpr, err := p.tektonClient.CreateCDPipelineRun(ctx, runner)
	if err != nil {
		log.ErrorWithCtx(ctx, "[retryCdTask]流水线重试taskrun任务[%s]: retry create CD PipelineRun failed: %v", taskRun.Name, err)
		return err
	}
	// update pipeline last run id
	if err := p.pRepo.UpdatePipelineLastRunIdWithoutTransaction(ctx, pipelineRun.PipelineId, pipelineRun.ID); err != nil {
		return err
	}
	log.DebugWithCtx(ctx, "[retryCdTask]流水线重试taskrun任务[%s]:retry create CD PipelineRun[%s] success for pipelinerun[%d]", taskRun.Name, tpr.Name, pipelineRun.ID)
	return nil
}

func (p *PipelineRunSvc) retryCiTask(ctx context.Context, taskRun dao.PipelineRunTask, pipelineRun dao.PipelineRun) error {
	// 获取拉取源码 commit结果
	commit := ""
	pullCodeTask, err := p.prRepo.FindTaskRunByMultiParams(ctx, model.PipelineRunSearch{PipelineRunId: pipelineRun.ID, Type: string(constants.TASK_PULL_CODE)})
	if err != nil {
		return err
	}
	if pullCodeTask == nil {
		log.InfoWithCtx(ctx, "流水线重试taskrun任务[%s]: pipeline run[%d] not found pull code task", taskRun.Name, pipelineRun.ID)
	} else {
		commit = pullCodeTask.GetCommitOnPR()
	}
	firstPipelineRun, err := p.prRepo.FindFirstPipelineRunBy(ctx, pipelineRun.PipelineId, pipelineRun.BuildNumber)
	if err != nil {
		return err
	}
	args := model.PipelineRunArgs{
		Args:         map[string]string{"commit": commit},
		IsRetry:      true,
		RequestDate:  firstPipelineRun.CreatedAt,
		Branch:       firstPipelineRun.Branch,
		SourceBranch: firstPipelineRun.SourceBranch,
	}

	err = p.plService.RetryCiPipeline(ctx, &pipelineRun, args)
	if err != nil {
		return err
	}
	return nil
}

func (p *PipelineRunSvc) UpdateRetryPipelineStatus(ctx context.Context, taskRun *dao.PipelineRunTask) error {
	pipelineStatus := StatusNode{
		id:             taskRun.PipelineRunId,
		exec:           p.prRepo.UpdatePipelineRunStatusAndTime,
		status:         constants.RUNNING,
		needUpdate:     true,
		originalStatus: constants.FAILED,
	}
	stageStatus := StatusNode{
		id:             taskRun.PipelineRunStageId,
		exec:           p.prRepo.UpdateStageRunStatusAndTime,
		parent:         &pipelineStatus,
		status:         constants.RUNNING,
		needUpdate:     true,
		originalStatus: constants.FAILED,
	}
	taskStatus := StatusNode{
		id:             taskRun.ID,
		exec:           p.prRepo.UpdateTaskRunStatusAndTime,
		parent:         &stageStatus,
		status:         constants.RUNNING,
		needUpdate:     true,
		originalStatus: constants.FAILED,
	}
	if err := updateOfStatusGroup(ctx, &taskStatus); err != nil {
		return err
	}
	newTaskRun, _ := p.prRepo.FindRunTaskById(ctx, taskRun.ID)
	deliverEvent(ctx, p.sender, newTaskRun, nil)
	if newTaskRun.StageRun != nil {
		deliverEvent(ctx, p.sender, newTaskRun.StageRun, nil)
	}
	if newTaskRun.PipelineRun != nil {
		deliverEvent(ctx, p.sender, newTaskRun.PipelineRun, nil)
	}
	return nil
}

type UpdateFunc func(context.Context, int64, map[string]interface{}) error

func (p *PipelineRunSvc) ResetCompleteTimeToEmpty(ctx context.Context, taskRun *dao.PipelineRunTask) error {
	updateFields := map[string]any{
		"completed_time": time.Time{},
		"updated_at":     time.Now(),
	}
	resetToEmptyTime := map[int64]UpdateFunc{
		taskRun.ID:                 p.prRepo.UpdateTaskRunStatusAndTime,
		taskRun.PipelineRunStageId: p.prRepo.UpdateStageRunStatusAndTime,
	}
	for id, reset := range resetToEmptyTime {
		err := reset(ctx, id, updateFields)
		if err != nil {
			return err
		}
	}
	return nil
}

func (p *PipelineRunSvc) GetTaskRunWithSameBuildNumber(ctx context.Context, prTask dao.PipelineRunTask) []int64 {
	taskRuns, err := p.prRepo.FindTaskRunWithSameBuildNumberBy(ctx, prTask.ID)
	if err != nil {
		log.ErrorWithCtx(ctx, "根据任务运行记录Id %d查找相同构建号下所有同类型任务运行记录失败: %v", prTask.ID, err)
		return nil
	}
	if taskRuns != nil && len(taskRuns) > 0 {
		taskRunIds := tools.MapTo(taskRuns, func(taskRun dao.PipelineRunTask) int64 {
			return taskRun.ID
		})
		return taskRunIds
	}
	return nil
}

func (p *PipelineRunSvc) CheckTaskRunRelatedTicket(ctx context.Context, appId, stageId int64) bool {
	taskRuns, err := p.prRepo.FindTaskRunByStageRunId(ctx, stageId)
	if err != nil {
		log.ErrorWithCtx(ctx, "[CheckTaskRunRelatedTicket]根据stage运行记录Id %d查找所有任务运行记录失败: %v", stageId, err)
		return false
	}
	// check has approval task
	hasApprovalTask := tools.Any(taskRuns, func(taskRun dao.PipelineRunTask) bool {
		return taskRun.GetType().IsBelongTo(constants.TASK_TEST_APPROVAL, constants.TASK_GRAY_UPGRADE_APPROVAL, constants.TASK_UPGRADE_APPROVAL)
	})
	if hasApprovalTask {
		sequence := tools.MapTo(tools.Filter(taskRuns, func(taskRun dao.PipelineRunTask) bool {
			return taskRun.GetType().IsBelongTo(constants.TASK_TEST_APPROVAL, constants.TASK_GRAY_UPGRADE_APPROVAL, constants.TASK_UPGRADE_APPROVAL)
		}), func(taskRun dao.PipelineRunTask) int64 {
			return taskRun.ID
		})
		hasTicket, err := p.ticketClient.CheckHasApprovedTicket(ctx, &pbdep.CheckHasTicketByReq{AppId: appId, TaskRunIds: sequence})
		if err != nil {
			log.ErrorWithCtx(ctx, "[CheckTaskRunRelatedTicket]根据stage运行记录Id %d查找所有任务运行记录失败: %v", stageId, err)
			return false
		}
		return hasTicket.CheckHasTicket
	}
	return true
}

func (p *PipelineRunSvc) GetNewApprovalTask(ctx context.Context, taskRun *dao.PipelineRunTask) (*dao.PipelineRunTask, error) {
	log.DebugWithCtx(ctx, "生成工单任务实例[%d]新实例，开始克隆流水线", taskRun.ID)
	retryPipelineRun, retryPRTask, err := p.cloneNewPipelineRun(ctx, taskRun)
	if err != nil {
		return nil, err
	}
	// sync ticket old taskrun into new taskrun
	err = p.syncTicketTaskRun(ctx, taskRun.PipelineRunId, retryPipelineRun.ID)
	if err != nil {
		log.ErrorWithCtx(ctx, "sync ticket old taskrun into new taskrun err: %v", err)
		return nil, err
	}
	// clone任务后，创建tekton任务前将状态更新为running
	params := &model.RunStatus{
		TaskID: retryPRTask.ID,
		Status: constants.RUNNING,
	}
	if err := p.UpdateRunStatus(ctx, params); err != nil {
		return nil, err
	}
	cdPipelineRunTask, err := p.prRepo.FindRunTaskById(ctx, retryPRTask.ID)
	if err != nil {
		return nil, err
	}
	// 审批任务重试
	err = p.retryCdTask(ctx, *cdPipelineRunTask, *retryPipelineRun, true)
	if err != nil {
		return nil, err
	}

	return retryPRTask, nil
}

func (p *PipelineRunSvc) GetTaskRunOperator(ctx context.Context, taskRunId int64, pipelineRunId int64) (*iam.UserResponse, error) {
	taskRun, err := p.prRepo.FindTaskRunById(ctx, taskRunId)
	if err != nil {
		return nil, fmt.Errorf("取任务操作人错误：查找任务运行记录出错>>>id=%d", taskRunId)
	}

	var user *iam.UserResponse

	if isBelongApproval(taskRun.GetType()) {
		flowInst, err := p.deployApprovalClient.FindFlowInstByTaskId(context.TODO(), &pbdep.TaskRun{Id: taskRunId})
		if err != nil {
			return nil, err
		}
		if flowInst.Id == 0 {
			return nil, fmt.Errorf("审批流不存在")
		}
		user, err = p.userClient.GetUserById(context.TODO(), &iam.UserParam{UserId: flowInst.ApplicantId})
		if err != nil || user == nil {
			return nil, fmt.Errorf("取任务操作人错误：查找用户出错>>>id=%d", flowInst.ApplicantId)
		}
	} else if isBelongDeploy(taskRun.GetType()) {
		changeLog, err := p.deployClient.FindChangeLogByTaskRunID(context.TODO(), &pbdep.TaskRunId{Id: taskRunId})
		if err != nil {
			return nil, err
		}
		if changeLog.Id == 0 {
			return nil, fmt.Errorf("部署记录不存在")
		}
		user, err = p.userClient.GetUserById(context.TODO(), &iam.UserParam{UserId: changeLog.OperatorBy})
		if err != nil || user == nil {
			return nil, fmt.Errorf("取任务操作人错误：查找用户出错>>>id=%d", changeLog.OperatorBy)
		}
	} else {
		var pipeRun dao.PipelineRun
		err := p.prRepo.FindPipelineRunById(ctx, pipelineRunId, &pipeRun)
		if err != nil {
			return nil, err
		}
		user, err = p.userClient.GetUserById(context.TODO(), &iam.UserParam{UserId: pipeRun.TriggerBy})
		if err != nil || user == nil {
			return nil, fmt.Errorf("取任务操作人错误：查找用户出错>>>id=%d", pipeRun.TriggerBy)
		}
	}
	return user, nil
}

func (p *PipelineRunSvc) BuildApiAutoTestResult(ctx context.Context, task *dao.PipelineRunTask) (*model.ApiAutoTestResp, error) {
	taskResult := model.ApiAutoTestResult{}
	if task.Result != nil {
		err := json.Unmarshal(task.Result, &taskResult)
		if err != nil {
			return nil, err
		}
	}
	resp := &model.ApiAutoTestResp{
		CompletedTime: task.CompletedTime,
		Result:        string(task.Status),
		TaskID:        taskResult.TaskID,
		ExecuteID:     taskResult.ExecuteID,
	}

	// result: {"results": [{"name": "total_case", "value": "1"}, {"name": "success_case", "value": "1"}, {"name": "failure_case", "value": "0"}, {"name": "report_path", "value": "https://testing-dev-quality.ttyuyin.com/#/api-test/testplan/reporter?plan_id=plan_id:7pQYvZL-VIAVZqyB8NiNT&task_id=task_id:JN5gbY2z_D2qS5ak5fZDb&plan_execute_id=execute_id:6y0KMlHqNOnem0S3CaZ2C&project_id=project_id:wpmjOS5bAjeNDSitM51bN&execute_id=execute_id:6y0KMlHqNOnem0S3CaZ2C"}]}
	var resultStatus string
	for _, resultItem := range taskResult.Results {
		switch resultItem.Name {
		case "total_case":
			resp.TotalCase = interfaceToInt64(resultItem.Value)
		case "success_case":
			resp.SuccessCase = interfaceToInt64(resultItem.Value)
		case "failure_case":
			resp.FailureCase = interfaceToInt64(resultItem.Value)
		case "report_path":
			resp.ReportPath = interfaceToString(resultItem.Value)
		case "status":
			resultStatus = interfaceToString(resultItem.Value)
		}
	}
	if resultStatus != "" {
		resp.Result = resultStatus
	} else {
		if task.Status.IsSuccess() {
			if resp.FailureCase > 0 {
				resp.Result = constants.FAILED.String()
			} else {
				resp.Result = constants.SUCCESSFUL.String()
			}
		}
	}
	if resp.TotalCase == 0 {
		resp.Rate = "0.0"
	} else {
		rate := float64(resp.SuccessCase) / float64(resp.TotalCase) * 100
		resp.Rate = fmt.Sprintf("%.1f", rate)
	}

	return resp, nil
}

func (p *PipelineRunSvc) GetMrPipelineRunByMrIdAndRepoAddr(ctx context.Context, repoAddr string, mrId int64) ([]dao.PipelineRun, error) {
	plRuns, err := p.prRepo.FindPipelineRunByMrIdAndRepoAddr(ctx, repoAddr, mrId)
	if err != nil {
		errInfo := fmt.Errorf("根据MrId查询流水线运行记录失败：%w", err)
		log.ErrorWithCtx(ctx, errInfo.Error())
		return nil, errInfo
	}
	return plRuns, nil
}

func (p *PipelineRunSvc) tryAppendDiscussion(ctx context.Context, pipeline *dao.Pipeline, plRun *dao.PipelineRun) (string, error) {
	if constants.IsPreMergePipeline(pipeline.Type) && plRun.IID != 0 {
		log.DebugWithCtx(ctx, "重试流水线运行，追加discussion: %s", plRun.DiscussID)
		// then append to the discussion
		var events map[string]interface{}
		if err := json.Unmarshal(plRun.GitlabEvents, &events); err != nil {
			log.ErrorWithCtx(ctx, "解析GitlabEvent失败: %v", err)
			return "", err
		}
		gitEvent, err := p.gitClient.GetGitlabEventMsg(events)
		if err != nil {
			return "", err
		}
		parameter := gitlab.DiscussionParameter{
			ID:        plRun.DiscussID,
			MrID:      plRun.IID,
			ProjectID: gitEvent.ProjectID,
		}

		discussionResp, err := p.gitClient.GetDiscussion(&parameter)
		note := model.NewPipelineNote(pipeline.ID, plRun.BuildNumber)
		if err != nil || discussionResp.Message != "" || !discussionResp.ResolvedAt.IsZero() {
			// create new discussion
			discussionId, err := p.gitClient.AddDiscussion(&parameter, note)
			if err != nil {
				return "", err
			}
			// append to the new discussion
			_ = p.gitClient.AppendDiscussion(&gitlab.DiscussionParameter{
				ID:        discussionId,
				MrID:      plRun.IID,
				ProjectID: gitEvent.ProjectID,
			}, note)
			return discussionId, nil
		}
		_ = p.gitClient.AppendDiscussion(&parameter, note)
	}
	return plRun.DiscussID, nil
}

func isBelongApproval(t constants.Task) bool {
	return t == constants.TASK_TEST_ACCEPTANCE ||
		t == constants.TASK_TEST_APPROVAL ||
		t == constants.TASK_GRAY_UPGRADE_APPROVAL ||
		t == constants.TASK_UPGRADE_APPROVAL
}

func isBelongDeploy(t constants.Task) bool {
	return t.IsDeployTask()
}

func isManagedByChangeSet(stageType string) bool {
	stages := conf.GetInChangeSetStageConfig()
	_, ok := stages[stageType]
	return ok
}

func (p *PipelineRunSvc) GetDeployTaskTrafficMark(ctx context.Context, ID int64) (string, error) {
	task, err := p.prRepo.FindTaskWithSubTask(ctx, ID)
	if err != nil {
		return "", errors.Wrapf(err, "find task run by %d error", ID)
	}
	if task == nil {
		return "", errors.Errorf("task run %d not found", ID)
	}

	if task.GetType() != constants.TASK_DEPLOY_SUB {
		return "", errors.Errorf("task %s not support", task.Type)
	}

	subTask := task.GetLatestSubtask()
	if subTask == nil {
		log.ErrorWithCtx(ctx, "ApiAutoTest related Task %d latest SubTask nil", ID)
		return "", errors.Errorf("subtask nil")
	}
	conf, err := subTask.UnmarshalConfig()
	if err != nil {
		return "", errors.Wrapf(err, "get subTask %d config failed", subTask.ID)
	}

	// 子环境 2.0 需要 project 信息
	pipeline, err := p.pRepo.GetPipelineByPipelineRunId(ctx, task.PipelineRunId)
	if err != nil {
		return "", errors.Wrapf(err, "find pipeline by pipelinerun %d error", task.PipelineRunId)
	}
	if pipeline == nil {
		return "", errors.Errorf("pipeline by pipelinerun %d not found", task.PipelineRunId)
	}

	req := pbdep.GetSubEnvReq{
		ClusterName: conf.Cluster,
		Namespace:   conf.Namespace,
		Env:         conf.DeployEnv,
		Senv:        conf.Senv,
		ProjectId:   pipeline.ProjectID,
	}
	resp, err := p.subEnvClient.GetSubEnvByResource(ctx, &req)
	if err != nil {
		return "", errors.Wrapf(err, "call deploy service with %s error", req.String())
	}
	log.DebugWithCtx(ctx, "ApiAutoTest req: %s get sub env from deploy service: %+v", req.String(), resp)
	return resp.TrafficMarkName, nil
}

func (p *PipelineRunSvc) ListSubTasksEnvironment(ctx context.Context, id int64) (*model.SubTaskEnvironment, error) {
	subTaskSearch := model.SubTaskSearch{
		TaskRunId:   id,
		EnabledList: []int64{1},
	}
	subTasks, err := p.prRepo.FindSubTasksByMultiParams(ctx, subTaskSearch)
	if err != nil {
		log.ErrorWithCtx(ctx, "find sub tasks by main task id %d error: %v", id, err)
		return nil, err
	}
	//查询当前任务所有重试过的子任务
	retrySubTaskSearch := model.SubTaskSearch{
		TaskRunId:   id,
		EnabledList: []int64{0},
	}
	retrySubTasks, err := p.prRepo.FindSubTasksByMultiParams(ctx, retrySubTaskSearch)
	if err != nil {
		return nil, err
	}
	retrySubTaskIdAndPrefixIdMap := make(map[int64]int64)
	for _, rs := range retrySubTasks {
		retrySubTaskIdAndPrefixIdMap[rs.ID] = rs.PrefixID
	}

	tasks := make([]model.SubTaskEnvItem, 0)
	for _, subTask := range subTasks {
		var automationDeploy model.AutomationDeploy
		_ = json.Unmarshal(subTask.Config, &automationDeploy)
		prstIds := []int64{subTask.ID}
		prefixId := subTask.PrefixID
		for prefixId != 0 {
			prstIds = append(prstIds, prefixId)
			prefixId = retrySubTaskIdAndPrefixIdMap[prefixId]
		}
		sort.Slice(prstIds, func(i, j int) bool {
			return prstIds[i] < prstIds[j]
		})
		tasks = append(tasks, model.SubTaskEnvItem{
			ID:         subTask.ID,
			Name:       fmt.Sprintf("子任务%d-%s", subTask.ID, subTask.Name),
			Env:        fmt.Sprintf("环境%d", subTask.ID),
			Cluster:    automationDeploy.Cluster,
			Namespace:  automationDeploy.Namespace,
			SubTaskIds: prstIds,
		})
	}
	tasks = sortSubTasks(ctx, tasks)
	environment := &model.SubTaskEnvironment{
		SubTasks:  tasks,
		TaskRunId: id,
	}
	return environment, nil
}

func sortSubTasks(ctx context.Context, tasks []model.SubTaskEnvItem) []model.SubTaskEnvItem {
	// 按照 集群、命名空间 字段从小到大排序
	sort.Slice(tasks, func(i, j int) bool {
		if tasks[i].Cluster != tasks[j].Cluster {
			return tasks[i].Cluster < tasks[j].Cluster
		}
		return tasks[i].Namespace < tasks[j].Namespace
	})
	return tasks
}

func (p *PipelineRunSvc) ListProductionDeploy(ctx context.Context, req *model.ListDeployReq) (*page.Page, error) {
	changeSetTask, err := p.changeSetRepo.FindPipelineRunByChangeSetTaskId(ctx, req.ChangeSetTaskId, req.ChangeSetId)
	if err != nil {
		log.ErrorWithCtx(ctx, "find taskRun  associated with task and subtask by id %d error: %v", req.ChangeSetTaskId, err)
		return nil, err
	}
	var res model.ProductionAutomationDeployList
	var pipelineRunIds []int64
	for _, cst := range changeSetTask {
		pipelineRunIds = append(pipelineRunIds, cst.PipelineRunID)
	}
	chSetPipeline, err := p.changeSetRepo.ListRelatedPipelinesByPrId(ctx, pipelineRunIds)
	if err != nil {
		return nil, err
	}
	articleVersionMap := make(map[int64]string)
	for _, csp := range chSetPipeline {
		articleVersionMap[csp.PipelineRunID] = csp.ArtifactVersion
	}
	// 检查是否有审批任务
	isHasApproval := false
	if len(changeSetTask) > 0 && p.CheckApprovalTaskBefore(ctx, changeSetTask[0].PipelineRunTask) {
		isHasApproval = true
	}
	for _, cst := range changeSetTask {
		for _, env := range cst.PipelineRunTask.SubRunTasks {
			var multiEnvConfig model.TaskRunMultiCloudEnvConfig
			err = json.Unmarshal(env.Config, &multiEnvConfig)
			if err != nil {
				return nil, err
			}
			configId := multiEnvConfig.ConfigId
			var configVersion int64
			if configId != 0 && isHasApproval {
				// 有部署配置信息(部署任务前有审批任务且还没有部署过),查询配置版本编号
				appConfigReq := &pbdep.GetAppConfigByIdReq{Id: configId}
				config, err := p.configClient.GetAppConfigById(ctx, appConfigReq)
				if err != nil {
					log.ErrorWithCtx(ctx, "根据configId[%d]查询部署配置异常:%v", configId, err)
					return nil, err
				}
				if config != nil && config.ConfigId != 0 {
					configVersion = config.ConfigVersion
				}
			} else {
				// 需要根据集群+命名空间拿取最新配置的情况
				// 1 自动化部署任务前面没有审批任务，且当前自动化部署任务没有运行过（configId还是0）
				// 2 自动化部署任务前面没有审批任务，configId不等于0（部署过失败了，这时的部署配置可能会被编辑，需要去重新获取最新的）
				appConfigReq := &pbdep.GetAppConfigReq{AppId: cst.Pipeline.AppID, Namespace: multiEnvConfig.Namespace, Cluster: multiEnvConfig.Cluster}
				config, err := p.configClient.GetAppConfig(ctx, appConfigReq)
				if err != nil {
					log.ErrorWithCtx(ctx, "应用id{%d}集群{%s}命名空间{%s}部署配置自动部署配置异常:%v", cst.Pipeline.AppID, multiEnvConfig.Cluster, multiEnvConfig.Namespace, err)
					return nil, err
				}
				if config != nil && config.ConfigId != 0 {
					configId = config.ConfigId
					configVersion = config.ConfigVersion
				}
			}
			if articleVersion, ok := articleVersionMap[cst.PipelineRunID]; !ok || articleVersion == "" {
				return nil, fmt.Errorf("找不到当前运行记录[%d]的制品版本", cst.PipelineRunID)
			}
			articleVersion := articleVersionMap[cst.PipelineRunID]
			res = append(res, model.ProductionAutomationDeploy{
				AppId:           cst.Pipeline.AppID,
				AppName:         cst.Pipeline.AppName,
				Cluster:         multiEnvConfig.Cluster,
				Namespace:       multiEnvConfig.Namespace,
				IsCreateSubEnv:  multiEnvConfig.IsCreateSubEnv,
				EnvTarget:       multiEnvConfig.EnvTarget,
				TrafficMark:     multiEnvConfig.TrafficMark,
				ConfigID:        configId,
				ConfigVersion:   configVersion,
				Status:          env.Status.String(),
				ArtifactVersion: articleVersion,
				Branch:          cst.PipelineRun.Branch,
				SubtaskRunId:    env.ID,
				IsHasApproval:   isHasApproval,
				PipelineId:      cst.PipelineID,
				BuildNumber:     cst.PipelineRun.BuildNumber,
			})
		}
	}
	sort.Sort(res)
	start := (req.PageNum() - 1) * req.PageSize()
	end := start + req.PageSize()
	if end > len(res) {
		end = len(res)
	}
	finalRes := res[start:end]
	for i := 0; i < len(finalRes); i++ {
		var ids []int64
		prs, err := p.prRepo.FindSubtaskWithRetryBy(ctx, finalRes[i].BuildNumber, finalRes[i].PipelineId, finalRes[i].Cluster, finalRes[i].Namespace)
		if err != nil {
			return nil, err
		}
		for _, pr := range prs {
			for _, subtask := range pr.SubRunTasks {
				ids = append(ids, subtask.ID)
			}
		}
		sort.Slice(ids, func(i, j int) bool {
			return ids[i] < ids[j]
		})
		finalRes[i].RetrySubTaskIds = ids
	}
	resp := &page.Page{
		PageNum:     req.PageNum(),
		PageSize:    req.PageSize(),
		List:        finalRes,
		TotalRecord: int64(len(res)),
	}
	return resp, nil
}

func (p *PipelineRunSvc) ListDeploy(ctx context.Context, req *model.ListDeployReq) (*page.Page, error) {
	changeSetTask, err := p.changeSetRepo.FindPipelineRunByChangeSetTaskId(ctx, req.ChangeSetTaskId, req.ChangeSetId)
	if err != nil {
		log.ErrorWithCtx(ctx, "find taskRun  associated with task and subtask by id %d error: %v", req.ChangeSetTaskId, err)
		return nil, err
	}
	var res model.ProductionAutomationDeployList
	var pipelineRunIds []int64
	for _, cst := range changeSetTask {
		pipelineRunIds = append(pipelineRunIds, cst.PipelineRunID)
	}
	chSetPipeline, err := p.changeSetRepo.ListRelatedPipelinesByPrId(ctx, pipelineRunIds)
	if err != nil {
		return nil, err
	}
	articleVersionMap := make(map[int64]string)
	for _, csp := range chSetPipeline {
		articleVersionMap[csp.PipelineRunID] = csp.ArtifactVersion
	}
	// 检查是否有审批任务
	isHasApproval := false
	if len(changeSetTask) > 0 && p.CheckApprovalTaskBefore(ctx, changeSetTask[0].PipelineRunTask) {
		isHasApproval = true
	}
	taskRunForPipelineRunMap := make(map[int64]*dao.PipelineRun)
	for _, cst := range changeSetTask {
		var automationDeploy model.AutomationDeploy
		err = json.Unmarshal(cst.PipelineRunTask.Config, &automationDeploy)
		if err != nil {
			return nil, err
		}
		// 获取自动化部署任务部署配置
		configId := automationDeploy.ConfigId
		var configVersion int64
		if configId != 0 && isHasApproval {
			// 有部署配置信息(部署任务前有审批任务且还没有部署过),查询配置版本编号
			appConfigReq := &pbdep.GetAppConfigByIdReq{Id: automationDeploy.ConfigId}
			config, err := p.configClient.GetAppConfigById(ctx, appConfigReq)
			if err != nil {
				log.ErrorWithCtx(ctx, "根据configId[%d]查询部署配置异常:%v", automationDeploy.ConfigId, err)
				return nil, err
			}
			if config != nil && config.ConfigId != 0 {
				configVersion = config.ConfigVersion
			}
		} else {
			// 需要根据集群+命名空间拿取最新配置的情况
			// 1 自动化部署任务前面没有审批任务，且当前自动化部署任务没有运行过（configId还是0）
			// 2 自动化部署任务前面没有审批任务，configId不等于0（部署过失败了，这时的部署配置可能会被编辑，需要去重新获取最新的）
			appConfigReq := &pbdep.GetAppConfigReq{AppId: cst.Pipeline.AppID, Namespace: automationDeploy.Namespace, Cluster: automationDeploy.Cluster}
			config, err := p.configClient.GetAppConfig(ctx, appConfigReq)
			if err != nil {
				log.ErrorWithCtx(ctx, "应用id{%d}集群{%s}命名空间{%s}部署配置自动部署配置异常:%v", cst.Pipeline.AppID, automationDeploy.Cluster, automationDeploy.Namespace, err)
				return nil, err
			}
			if config != nil && config.ConfigId != 0 {
				configId = config.ConfigId
				configVersion = config.ConfigVersion
			}
		}
		if articleVersion, ok := articleVersionMap[cst.PipelineRunID]; !ok || articleVersion == "" {
			return nil, fmt.Errorf("找不到当前运行记录[%d]的制品版本", cst.PipelineRunID)
		}
		articleVersion := articleVersionMap[cst.PipelineRunID]
		res = append(res, model.ProductionAutomationDeploy{
			AppId:           cst.Pipeline.AppID,
			AppName:         cst.Pipeline.AppName,
			Cluster:         automationDeploy.Cluster,
			Namespace:       automationDeploy.Namespace,
			IsCreateSubEnv:  automationDeploy.IsCreateSubEnv,
			TrafficMark:     automationDeploy.TrafficMark,
			EnvTarget:       automationDeploy.EnvTarget,
			ConfigID:        configId,
			ConfigVersion:   configVersion,
			Status:          cst.PipelineRunTask.Status.String(),
			TaskRunId:       cst.PipelineRunTask.ID,
			TaskId:          cst.PipelineRunTask.TaskId,
			ArtifactVersion: articleVersion,
			Branch:          cst.PipelineRun.Branch,
			IsHasApproval:   isHasApproval,
			PipelineId:      cst.PipelineID,
			BuildNumber:     cst.PipelineRun.BuildNumber,
		})
		taskRunForPipelineRunMap[cst.PipelineRunTask.ID] = cst.PipelineRun
	}
	sort.Sort(res)
	start := (req.PageNum() - 1) * req.PageSize()
	end := start + req.PageSize()
	if end > len(res) {
		end = len(res)
	}
	finalRes := res[start:end]
	// 查找每个任务之前重试的任务
	for i := 0; i < len(finalRes); i++ {
		var ids []int64
		prs, err := p.prRepo.FindDeployTaskRunWithRetryBy(ctx, finalRes[i].BuildNumber, finalRes[i].PipelineId)
		if err != nil {
			return nil, err
		}
		taskRun, err := p.getSameTaskRun(taskRunForPipelineRunMap[finalRes[i].TaskRunId], finalRes[i].TaskRunId, prs)
		for _, tr := range taskRun {
			ids = append(ids, tr.ID)
		}
		sort.Slice(ids, func(i, j int) bool {
			return ids[i] < ids[j]
		})
		finalRes[i].RetryTaskRunIds = ids
	}
	resp := &page.Page{
		PageNum:     req.PageNum(),
		PageSize:    req.PageSize(),
		List:        finalRes,
		TotalRecord: int64(len(res)),
	}
	return resp, nil
}

func (p *PipelineRunSvc) getSameTaskRun(pipelineRun *dao.PipelineRun, taskRunId int64, pipelineRunList []dao.PipelineRun) ([]dao.PipelineRunTask, error) {
	index := 0
	var indexTask *dao.PipelineRunTask
	for _, stage := range pipelineRun.Stages {
		for _, task := range stage.Tasks {
			index++
			if task.ID == taskRunId {
				indexTask = &task
				break
			}
		}
		if indexTask != nil {
			break
		}
	}
	if indexTask == nil {
		return nil, nil
	}
	var res []dao.PipelineRunTask
	for _, pr := range pipelineRunList {
		cur := 0
		for _, stage := range pr.Stages {
			for _, task := range stage.Tasks {
				cur++
				if cur == index && task.Type == indexTask.Type {
					res = append(res, task)
				}
			}
		}
	}
	return res, nil
}

func (p *PipelineRunSvc) GetNextTaskRunByTaskRunId(ctx context.Context, taskRunId int64, pr dao.PipelineRun) (*dao.PipelineRunTask, error) {
	nextTaskRunId := int64(0)
	taskRunArr := make([]string, 0)
	for _, stage := range pr.Stages {
		taskRunArr = append(taskRunArr, strings.Split(stage.TaskSequence, ",")...)
	}
	for index, arrId := range taskRunArr {
		int64arrId, _ := strconv.ParseInt(arrId, 10, 64)
		if taskRunId == int64arrId && index+1 < len(taskRunArr) {
			nextTaskRunId, _ = strconv.ParseInt(taskRunArr[index+1], 10, 64)
		}
	}
	if nextTaskRunId == 0 {
		return nil, fmt.Errorf("nextTaskRunId is null")
	}
	cdPipelineRunTask, err := p.prRepo.FindRunTaskById(ctx, nextTaskRunId)
	return cdPipelineRunTask, err
}

func (p *PipelineRunSvc) CheckApprovalTaskBefore(ctx context.Context, pipelineRunTask *dao.PipelineRunTask) bool {
	pr, err := p.prRepo.GetPipelineRunById(ctx, pipelineRunTask.PipelineRunId)
	if err != nil {
		log.ErrorWithCtx(ctx, "query pipelineRun by pipelineId %d fail: %v", pipelineRunTask.PipelineRunId, err)
		return true
	}
	if pr == nil {
		return true
	}
	for _, stage := range pr.Stages {
		if stage.ID != pipelineRunTask.PipelineRunStageId {
			continue
		}
		for j, task := range stage.Tasks {
			if task.ID != pipelineRunTask.ID {
				continue
			}
			if j-1 < 0 {
				return false
			}
			if stage.Tasks[j-1].GetType().IsBelongTo(constants.TASK_TEST_APPROVAL, constants.TASK_UPGRADE_APPROVAL, constants.TASK_GRAY_UPGRADE_APPROVAL) {
				return true
			}
		}
	}
	return true
}

func (p *PipelineRunSvc) BuildScaScanResult(ctx context.Context, task *dao.PipelineRunTask) (*model.ScaScanResp, error) {
	taskResult := model.TaskRunResult{}
	// result: {"results": [{"name": "status", "value": "1"}, {"name": "result_url", "value": "http://test.com"}]}
	resp := &model.ScaScanResp{
		CompletedTime: task.CompletedTime,
		Result:        task.Status.String(),
	}

	if task.Result != nil {
		err := json.Unmarshal(task.Result, &taskResult)
		if err != nil {
			return nil, err
		}
		for _, item := range taskResult.Results {
			switch item.Name {
			case "result_url":
				resp.ReportPath = interfaceToString(item.Value)
			}
		}
	}
	return resp, nil
}

func (p *PipelineRunSvc) StopTask(ctx context.Context, req model.StopTaskReq) error {
	pr, err := p.prRepo.GetPipelineRunById(ctx, req.PipelineRunId)
	if err != nil {
		log.ErrorWithCtx(ctx, "find pipelineRun[%d] failed: %v", req.PipelineRunId, err)
		return err
	}
	if pr == nil {
		log.ErrorWithCtx(ctx, "pipelineRun[%d] not found", req.PipelineRunId)
		return pipelineErr.ErrPipelineRunRecordNotExisted
	}
	curStage, curTask := p.findStageAndTasksBy(pr, req.TaskRunId)
	if curStage == nil || curTask == nil {
		log.ErrorWithCtx(ctx, "stage and task not found,taskRunId:%d", req.TaskRunId)
		return pipelineErr.ErrPipelineRunTaskNotExisted
	}

	updateStages := make(map[int64]constants.PipelineStatus)
	updatePipelineRunStatus := struct {
		prId   int64
		Status constants.PipelineStatus
	}{
		prId:   0,
		Status: constants.SUCCESSFUL,
	}
	var updateTaskIds []int64
	var skipTask []string

	offCanaryTask := p.GetFirstTaskBy(curStage, constants.TASK_OFFLINE_CANARY)
	deployCanaryTask := p.GetFirstTaskBy(curStage, constants.TASK_DEPLOY_CANARY)
	followStages := p.getFollowingStages(curStage, pr.Stages)

	flag, isExistSuccessDeploy := false, false
	for _, task := range curStage.Tasks {
		if task.Type == constants.TASK_OFFLINE_CANARY.String() {
			break
		}
		if flag {
			updateTaskIds = append(updateTaskIds, task.ID)
			skipTask = append(skipTask, constants.GenerateSkipParamName(task.ID))
			continue
		}
		if task.ID == curTask.ID {
			flag = true
		}
	}
	tools.Do(followStages, func(stage *dao.PipelineRunStage) {
		updateStages[stage.ID] = constants.SKIPPED
		for _, task := range stage.Tasks {
			updateTaskIds = append(updateTaskIds, task.ID)
			skipTask = append(skipTask, constants.GenerateSkipParamName(task.ID))
		}
	})
	if deployCanaryTask != nil {
		isExistSuccessDeploy, err = p.isExistSuccessDeploy(ctx, deployCanaryTask.ID)
		if err != nil {
			log.ErrorWithCtx(ctx, "Check whether any cluster has been successfully deployed failed:%v", err)
			return err
		}
	}
	if offCanaryTask == nil {
		updateStages[curStage.ID] = constants.SUCCESSFUL
	} else if offCanaryTask != nil && !isExistSuccessDeploy {
		updateTaskIds = append(updateTaskIds, offCanaryTask.ID)
		skipTask = append(skipTask, constants.GenerateSkipParamName(offCanaryTask.ID))
		updatePipelineRunStatus.prId, updatePipelineRunStatus.Status = req.PipelineRunId, constants.SUCCESSFUL
		updateStages[curStage.ID] = constants.SUCCESSFUL
	} else {
		updatePipelineRunStatus.prId, updatePipelineRunStatus.Status = req.PipelineRunId, constants.RUNNING
		updateStages[curStage.ID] = constants.RUNNING
	}

	txUpdate := func(tx *gorm.DB) error {
		txCtx := db.CtxWithTX(ctx, tx)
		err = p.prRepo.TxUpdatePipelineRunStatusAndTime(txCtx, curTask.ID,
			map[string]interface{}{"status": constants.SKIPPED, "completed_time": time.Now(), "updated_at": time.Now()})
		if err != nil {
			log.ErrorWithCtx(ctx, "stop task, update pipelineRunTask[%d] status failed:%v", curTask.ID, err)
			return err
		}
		err = p.prRepo.TxBatchUpdatePipelineRunTaskStatus(txCtx, updateTaskIds, constants.SKIPPED.String())
		if err != nil {
			log.ErrorWithCtx(ctx, "stop task, update pipelineRunTaskIds[%+v] status failed:%v", updateTaskIds, err)
			return err
		}
		for stageId, status := range updateStages {
			if stageId == curStage.ID {
				// 当前终止的阶段均需要是成功，需要更新完成时间
				err := p.prRepo.UpdateStageRunStatusAndTime(txCtx, curStage.ID, map[string]interface{}{"status": status, "completed_time": time.Now(), "updated_at": time.Now()})
				if err != nil {
					return err
				}
			} else {
				// 之后的stage阶段，都需要更新为skipped
				err = p.prRepo.TxBatchUpdatePipelineRunStageStatus(txCtx, tx, []int64{stageId}, status.String())
				if err != nil {
					log.ErrorWithCtx(ctx, "stop task, update pipelineRunStage[%d] status failed:%v", stageId, err)
					return err
				}
			}
		}
		if updatePipelineRunStatus.prId != 0 {
			err = p.prRepo.TxBatchUpdatePipelineRunStatus(txCtx, tx, []int64{updatePipelineRunStatus.prId}, updatePipelineRunStatus.Status.String())
			if err != nil {
				log.ErrorWithCtx(ctx, "stop task, update pipelineRun[%d] status failed:%v", updatePipelineRunStatus.prId, err)
				return err
			}
		}
		return nil
	}
	dbErr := db.Transaction(txUpdate)
	if dbErr != nil {
		log.ErrorWithCtx(ctx, "update stop task status failed: %v", dbErr)
		return dbErr
	}
	err = p.approvalClient.FakeDeploySkip(ctx, pr.TektonName, curTask.TektonName, skipTask)
	if err != nil {
		log.ErrorWithCtx(ctx, "taskRunId[%d]调用 FakeDeploySkip err:%v", req.TaskRunId, err)
		return err
	}
	// 终止验证 时，可无脑取消 流量计划 支持无害多次调用,前置判定逻辑 放到流量计划模块
	userInfo := cctx.GetUserinfo(ctx)
	_, err = p.deployPlanClient.CancelPlanRun(ctx, &pbdep.PlanRunReq{PipelineRunId: pr.ID, CanceledByChineseName: userInfo.ChineseName, CanceledByEmployeeNo: userInfo.EmployeeNo})
	if err != nil {
		err = fmt.Errorf("取消流量计划 %w", err)
	}
	// send event
	curTask.Status = constants.SKIPPED
	deliverEvent(ctx, p.sender, curTask, nil)
	curStage.Status = constants.SKIPPED
	deliverEvent(ctx, p.sender, curStage, nil)
	if updatePipelineRunStatus.prId != 0 {
		pr.Status = updatePipelineRunStatus.Status
		deliverEvent(ctx, p.sender, pr, nil)
	}
	return nil
}

func (p *PipelineRunSvc) findStageAndTasksBy(pr *dao.PipelineRun, taskRunId int64) (*dao.PipelineRunStage, *dao.PipelineRunTask) {
	for _, stage := range pr.Stages {
		for _, task := range stage.Tasks {
			if task.ID == taskRunId {
				return &stage, &task
			}
		}
	}
	return nil, nil
}

func (p *PipelineRunSvc) GetFirstTaskBy(stage *dao.PipelineRunStage, taskType constants.Task) *dao.PipelineRunTask {
	for _, task := range stage.Tasks {
		if task.Type == taskType.String() {
			return &task
		}
	}
	return nil
}

func (p *PipelineRunSvc) getFollowingStages(curStage *dao.PipelineRunStage, allStage []dao.PipelineRunStage) []*dao.PipelineRunStage {
	flag := false
	var res []*dao.PipelineRunStage
	for _, stage := range allStage {
		if flag {
			res = append(res, &stage)
			continue
		}
		if stage.ID == curStage.ID {
			flag = true
		}
	}
	return res
}

func (p *PipelineRunSvc) isExistSuccessDeploy(ctx context.Context, taskRunId int64) (bool, error) {
	subTasks, err := p.prRepo.FindSubTasksByMultiParams(ctx, model.SubTaskSearch{TaskRunId: taskRunId})
	if err != nil {
		log.ErrorWithCtx(ctx, "find subtask by taskRunId failed: %v", err)
		return false, err
	}
	for _, subtask := range subTasks {
		if subtask.Status == constants.SUCCESSFUL {
			return true, nil
		}
	}
	return false, nil
}

func (p *PipelineRunSvc) PassTask(ctx context.Context, req model.PassTaskReq) error {
	var (
		taskRun *dao.PipelineRunTask
		err     error
	)
	if err := db.Transaction(func(tx *gorm.DB) error {
		if taskRun, err = p.prRepo.TxFindRunTask(ctx, tx, req.TaskRunId); err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "[PassTask]根据taskRunId查询pipelineRunTask错误:%v", err)
		return err
	}
	if taskRun == nil {
		log.ErrorWithCtx(ctx, "[PassTask]根据taskRunId查询pipelineRunTask错误:%v", err)
		return err
	}
	userInfo := cctx.GetUserinfo(ctx)

	if taskRun.Type == constants.TASK_PAUSE.String() {
		return p.passPauseTask(ctx, taskRun, userInfo)
	}

	if taskRun.Status != constants.SUCCESSFUL {
		return pipelineErr.ErrPipelineRunTaskStatusInvalid
	}
	if taskRun.Type == constants.TASK_DEPLOY_CANARY.String() {
		if err = p.checkCanaryPassTask(ctx, taskRun); err != nil {
			return err
		}
	}
	if (taskRun.Type == constants.TASK_DEPLOY_STAGING.String() ||
		taskRun.Type == constants.TASK_DEPLOY_CANARY.String()) && taskRun.TektonName != "" {
		err = p.approvalClient.FakeDeployPass(ctx, []string{taskRun.TektonName})
		if err != nil {
			log.ErrorWithCtx(ctx, "taskRunId[%d]调用 FakeDeployPass err:%v", taskRun.ID, err)
			return err
		}
	}
	// 协程记录通过验证的操作日志，避免找茬
	safego.Go(func() {
		if err = p.prOlRepo.Create(ctx, &dao.PipelineRunOperateLog{
			Action:              constants.OpLogActionPassCanary,
			PipelineRunID:       taskRun.PipelineRunId,
			TaskRunID:           taskRun.ID,
			TaskRunType:         taskRun.Type,
			Operator:            userInfo.UserID,
			OperatorChineseName: userInfo.ChineseName,
			OperatorEmployeeNo:  userInfo.EmployeeNo,
		}); err != nil {
			log.ErrorWithCtx(ctx, "[PassTask]记录通过验证操作日志失败: %v, req: %+v", err, req)
		}
	})

	// 协程取消放量计划执行，可无脑多次取消 放量计划模块判定是否可以取消
	safego.Go(func() {
		// 前端界面通过金丝雀验证的时候，如果有放量计划执行，则取消
		if req.NeedCancelPlanRun && taskRun.Type == constants.TASK_DEPLOY_CANARY.String() {
			_, errIn := p.deployPlanClient.CancelPlanRun(ctx, &pbdep.PlanRunReq{PipelineRunId: taskRun.PipelineRunId, CanceledByChineseName: userInfo.ChineseName, CanceledByEmployeeNo: userInfo.EmployeeNo})
			if errIn != nil {
				log.ErrorWithCtx(ctx, "[PassTask]取消流量计划失败: %v", errIn)
			}
		}
	})
	return nil
}

func (p *PipelineRunSvc) passPauseTask(ctx context.Context, taskRun *dao.PipelineRunTask, userInfo *cctx.UserInfo) (err error) {
	if taskRun.TektonName == "" {
		return
	}
	rst := map[string]any{
		"name":         "通过卡点",
		"approver":     userInfo.ChineseName + "/" + userInfo.EmployeeNo,
		"approvalTime": time.Now(),
	}

	jsonStr, _ := json.Marshal(rst)
	taskRun.Result = jsonStr
	p.prRepo.UpdatePipelineRunTaskCols(ctx, taskRun, "result")

	err = p.approvalClient.FakeDeployPass(ctx, []string{taskRun.TektonName})
	if err != nil {
		log.ErrorWithCtx(ctx, "taskRunId[%d]调用 FakeDeployPass err:%v", taskRun.ID, err)
		return err
	}

	return
}

func (p *PipelineRunSvc) checkCanaryPassTask(ctx context.Context, taskRun *dao.PipelineRunTask) error {
	canaryConfig, err := taskRun.GetCanaryConfig()
	if canaryConfig == nil {
		log.ErrorWithCtx(ctx, "[checkCanaryPassTask]获取金丝雀配置异常: %v", err)
		return err
	}
	canaryPolicy := canaryConfig.CanaryPolicy
	if err != nil {
		log.ErrorWithCtx(ctx, "[checkCanaryPassTask]获取金丝雀策略异常: %v", err)
		return err
	}
	var isHasTraffic bool
	if canaryPolicy == constants.PolicyTraffic {
		isHasTraffic = true
	} else if canaryPolicy == constants.PolicyClientVersion {
		planRun, err := p.deployPlanClient.GetPlanRun(ctx, &pbdep.PlanRunReq{PipelineRunId: taskRun.PipelineRunId})
		if err != nil {
			log.ErrorWithCtx(ctx, "[checkCanaryPassTask] get plan run failed: %v", err)
			return err
		}
		if planRun.Id != 0 {
			var planRunCfg planRunConfig
			if err = json.Unmarshal(planRun.PlanRunConfig, &planRunCfg); err != nil {
				log.ErrorWithCtx(ctx, "[checkCanaryPassTask] unmarshal plan run config failed, err: %v", err)
				return err
			}
			isHasTraffic = tools.Any(planRunCfg.CanaryPolicy, func(policy string) bool {
				return policy == constants.PolicyTraffic.String()
			})
		}
	} else {
		log.WarnWithCtx(ctx, "[checkCanaryPassTask]金丝雀策略类型为%s，不需要校验，直接通过验证", canaryPolicy)
	}
	// 金丝雀策略为流量比例类型，或者发布计划带有流量比例
	if isHasTraffic {
		canaryPercentage, err := taskRun.GetCanaryPercentage()
		if err != nil {
			log.ErrorWithCtx(ctx, "[checkCanaryPassTask]获取金丝雀放量百分比异常: %v", err)
			return err
		}
		if canaryPercentage == 0 {
			log.ErrorWithCtx(ctx, "[checkCanaryPassTask]金丝雀放量为0，无法通过验证")
			return pipelineErr.ErrCanaryPercentageInvalid
		}
	}

	return nil
}

func (p *PipelineRunSvc) GetCanaryPolicy(ctx context.Context, taskID int64) (*model.CanaryPolicyObj, error) {
	var (
		task *dao.PipelineRunTask
		err  error
	)
	if err := db.Transaction(func(tx *gorm.DB) error {
		if task, err = p.prRepo.TxFindRunTask(ctx, tx, taskID); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, errors.Wrapf(err, "get task %d failed", taskID)
	}

	if task == nil {
		return nil, pipelineErr.ErrPipelineRunTaskNotExisted
	}
	if task.Type != constants.TASK_DEPLOY_CANARY.String() {
		return nil, errors.Wrapf(pipelineErr.ErrPipelineRunTaskNotExisted, "task %d is not canary task", taskID)
	}
	var deployConfig model.TaskRunMultiCloudEnvConfig
	if err := json.Unmarshal(task.Config, &deployConfig); err != nil {
		log.ErrorWithCtx(ctx, "[GetCanaryPolicy]序列化金丝雀配置信息错误, taskRunId %d: %v", task.ID, err)
		return nil, err
	}
	percentage, err := task.GetCanaryPercentage()
	if err != nil {
		return nil, errors.Wrapf(err, "get task %d canary percentage failed", taskID)
	}

	canaryConfig := p.canarySvc.getConfig()
	canaryPercentageResp := &model.CanaryPercentageResp{
		Current: percentage,
		Step:    canaryConfig["step"],
		Min:     canaryConfig["min"],
		Max:     canaryConfig["max"],
	}
	// 发布计划策略
	if deployConfig.CanaryPolicy == constants.PolicyClientVersion {
		canaryResp, err := p.genClientVersionCanaryResp(ctx, deployConfig.CanaryDeployPlanID, task.PipelineRunId)
		if err != nil {
			return nil, err
		}
		canaryResp.CanaryPercentageResp = canaryPercentageResp
		return canaryResp, nil
	}
	// 流量比例策略/升级阶段没有工单
	trafficResp := model.CanaryPolicyObj{
		CanaryPolicy:         []constants.EnumCanaryPolicy{constants.PolicyTraffic},
		IsShowTrafficControl: true,
		CanaryPercentageResp: canaryPercentageResp,
	}
	return &trafficResp, nil
}

type planRunConfig struct {
	CanaryPolicy []string `json:"canaryPolicy"`
}

func (p *PipelineRunSvc) genClientVersionCanaryResp(ctx context.Context, deployConfigId, pipelineRunId int64) (*model.CanaryPolicyObj, error) {
	var (
		canaryPolicyName     string
		canaryVersions       []string
		isShowTrafficControl bool
		isHasTrafficControl  bool
	)
	canaryPolicy := make([]constants.EnumCanaryPolicy, 0, 2)
	// 获取发布计划名称
	plan, err := p.deployPlanClient.GetDeployPlanByID(ctx, &pbdep.GetDeployPlanReq{DeployPlanId: deployConfigId})
	if err != nil {
		log.ErrorWithCtx(ctx, "[genClientVersionCanaryResp] get deploy plan failed, err: %v", err)
		return nil, err
	}
	if plan != nil {
		canaryPolicyName = plan.GetName()
		canaryVersions = plan.GetVersions()
	}
	// 查询发布计划实例的数据
	planRun, err := p.deployPlanClient.GetPlanRun(ctx, &pbdep.PlanRunReq{PipelineRunId: pipelineRunId})
	if err != nil {
		log.ErrorWithCtx(ctx, "[genClientVersionCanaryResp] get plan run failed, err: %v", err)
		return nil, err
	}
	// 兼容旧数据
	if planRun.Id == 0 {
		canaryPolicy = []constants.EnumCanaryPolicy{constants.PolicyClientVersion}
	} else {
		var planRunCfg planRunConfig
		if err = json.Unmarshal(planRun.PlanRunConfig, &planRunCfg); err != nil {
			log.ErrorWithCtx(ctx, "[genClientVersionCanaryResp] unmarshal plan run config failed, err: %v", err)
			return nil, err
		}

		for _, policy := range planRunCfg.CanaryPolicy {
			policyResult := constants.EnumCanaryPolicy(policy)
			if policyResult == constants.PolicyTraffic {
				isHasTrafficControl = true
			}
			canaryPolicy = append(canaryPolicy, policyResult)
		}

		if isHasTrafficControl {
			if len(planRun.Nodes) > 0 {
				firstNode := planRun.Nodes[0]
				// 发布计划实例跳过了执行
				// 发布计划实例执行失败
				// 第一个节点不是流量控制节点
				if planRun.Status == constants.PlanRunSkip.String() ||
					planRun.Status == constants.PlanRunFail.String() ||
					firstNode.Action != constants.PlanActionControlTraffic.String() {
					isShowTrafficControl = true
				} else {
					isShowTrafficControl = false
				}
			} else {
				// 没有放量计划也显示流量控制
				isShowTrafficControl = true
			}
		}
		// 有初始化流量比例，不给调整流量比例的按钮了
		if planRun.HasInitialTrafficPercentage {
			isShowTrafficControl = false
		}
	}

	return &model.CanaryPolicyObj{
		CanaryPolicy:         canaryPolicy,
		IsShowTrafficControl: isShowTrafficControl,
		CanaryDeployPlanResp: &model.CanaryDeployPlanResp{
			DeployPlanID:       deployConfigId,
			DeployPlanName:     canaryPolicyName,
			DeployPlanRunID:    planRun.Id,
			DeployPlanVersions: canaryVersions,
			IsHasPlanRunNodes:  len(planRun.Nodes) > 0,
		},
	}, nil
}

func (p *PipelineRunSvc) ShiftCanaryPercentage(ctx context.Context, params model.CanaryShiftReq) (*model.CanaryPercentageResp, error) {
	lockKey := fmt.Sprintf("canary-shift-%d", params.ID)
	lockResult := p.redisCli.SetNX(ctx, lockKey, "", 3*time.Second)
	if lockResult.Err() != nil {
		return nil, errors.Wrapf(lockResult.Err(), "set redis lock failed")
	}
	if !lockResult.Val() {
		return nil, errors.Wrapf(pipelineErr.ErrCanaryParamInvalid, "task %d is locked", params.ID)
	}

	var (
		hasShift   bool
		canaryData model.CanaryPercentageResp
		task       *dao.PipelineRunTask
		err        error
	)
	// 发送事件
	defer func() {
		if !hasShift {
			return
		}
		eventpb := &pbevent.CanaryShiftEvent{
			TaskRunId: params.ID,
			Current:   canaryData.Current,
			Step:      canaryData.Step,
			Max:       canaryData.Max,
			Min:       canaryData.Min,
		}
		e := event.NewPipelineRelatedEvent(eventpb, event.WithEventType("canary"))
		result := p.sender.Send(ctx, e)
		if event.IsUndelivered(result) {
			log.ErrorWithCtx(ctx, "send canary shift event failed")
		}
		if event.IsACK(result) {
			log.DebugWithCtx(ctx, "send canary shift event success")
		}
	}()

	// release lock
	defer func() {
		if err := p.redisCli.Del(ctx, lockKey).Err(); err != nil {
			log.ErrorWithCtx(ctx, "delete redis lock failed: %v", err)
		}
	}()

	if err := db.Transaction(func(tx *gorm.DB) error {
		if task, err = p.prRepo.TxFindRunTask(ctx, tx, params.ID); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, errors.Wrapf(err, "get task %d failed", params.ID)
	}
	if task == nil {
		return nil, pipelineErr.ErrPipelineRunTaskNotExisted
	}
	// validation
	if task.Type != constants.TASK_DEPLOY_CANARY.String() {
		return nil, errors.Wrapf(pipelineErr.ErrPipelineRunTaskNotExisted, "task %d is not canary task", params.ID)
	}
	if task.Status != constants.SUCCESSFUL && !params.IsInitialShift {
		return nil, errors.Wrapf(pipelineErr.ErrCanaryParamInvalid, "task %d is not successful", params.ID)
	}
	expectPercentage := params.Value
	currentPercentage, err := task.GetCanaryPercentage()
	if err != nil {
		return nil, errors.Wrapf(err, "get task %d canary percentage failed", params.ID)
	}
	canaryConfig := p.canarySvc.getConfig()
	canaryData = model.CanaryPercentageResp{
		Current: currentPercentage,
		Step:    canaryConfig["step"],
		Min:     canaryConfig["min"],
		Max:     canaryConfig["max"],
	}
	if expectPercentage < canaryConfig["min"] || expectPercentage > canaryConfig["max"] {
		return nil, errors.Wrapf(pipelineErr.ErrCanaryParamInvalid, "percentage %d is out of range [%d, %d]", expectPercentage, canaryConfig["min"], canaryConfig["max"])
	}
	if expectPercentage == currentPercentage {
		log.InfoWithCtx(ctx, "[ShiftCanaryPercentage] task %d canary percentage is already %d", params.ID, expectPercentage)
		return &canaryData, nil
	}
	// execute shift
	if err = p.rolloutCanary(ctx, task, expectPercentage, params.CanaryPolicy); err != nil {
		return nil, errors.Wrapf(err, "rollout canary failed")
	}

	canaryData.Current = expectPercentage
	hasShift = true
	return &canaryData, nil
}

func (p *PipelineRunSvc) rolloutCanary(ctx context.Context, task *dao.PipelineRunTask, percentage int64, canaryPolicy []string) error {
	var canaryConfig model.DeployCanaryConfig
	if err := task.UnmarshalConfig(&canaryConfig); err != nil {
		log.Errorf("unmarshal pipelineRunTask[%d] config error: %v", task.ID, err)
		return err
	}

	var (
		updatedSubTasks []dao.PipelineRunSubtask
		successNumber   int64
		err             error
	)
	subtasks := make([]dao.PipelineRunSubtask, 0, len(task.SubRunTasks))
	// 过滤掉重试的子任务
	for _, t := range task.SubRunTasks {
		if t.Enabled {
			subtasks = append(subtasks, t)
		}
	}
	// 发布计划的调整流量比例
	var cancelFnc context.CancelFunc
	ctx, cancelFnc = cctx.CopyCtxWithTimeout(ctx, 3*time.Minute) // 三分钟啥都干完了
	defer cancelFnc()
	if canaryConfig.CanaryDeployPlanId != 0 {
		updatedSubTasks, successNumber, err = p.shiftCanaryWithDeployPlan(ctx, task, canaryPolicy, canaryConfig, subtasks, percentage)
	} else {
		switch canaryConfig.TrafficControlMethod {
		case constants.EnumTrafficControlDyeing:
			updatedSubTasks, successNumber, err = p.shiftCanaryWithDyeing(ctx, task, subtasks, percentage)
		case constants.EnumTrafficControlWeight:
			updatedSubTasks, successNumber, err = p.shiftCanaryWithWeight(ctx, task, subtasks, percentage)
		}
	}
	if err != nil {
		return err
	}
	// update subtask config percentage
	for _, sTask := range updatedSubTasks {
		if err := p.prRepo.UpdatePipelineRunSubtaskConfigPercentage(ctx, sTask.ID, percentage); err != nil {
			return errors.Wrapf(pipelineErr.ErrCanaryShiftingFailed, "update subtask %d config percentage %d failed", sTask.ID, percentage)
		}
	}

	if int(successNumber) != len(subtasks) {
		return errors.Wrapf(pipelineErr.ErrCanaryShiftingFailed, "rollout canary failed expect %d success %d", len(subtasks), successNumber)
	}

	return nil
}

// 发布计划 调整流量比例
func (p *PipelineRunSvc) shiftCanaryWithDeployPlan(ctx context.Context, task *dao.PipelineRunTask, canaryPolicyList []string, canaryConfig model.DeployCanaryConfig, subtasks []dao.PipelineRunSubtask, percentage int64) ([]dao.PipelineRunSubtask, int64, error) {
	var (
		updatedSubTasks []dao.PipelineRunSubtask
		successNumber   int64
		err             error
	)
	deployPlanObj, err := p.deployPlanClient.GetDeployPlanByID(ctx, &pbdep.GetDeployPlanReq{DeployPlanId: canaryConfig.CanaryDeployPlanId})
	if err != nil {
		log.ErrorWithCtx(ctx, "get deploy plan by id %d failed: %v", canaryConfig.CanaryDeployPlanId, err)
		return nil, 0, err
	}
	if deployPlanObj == nil {
		log.ErrorWithCtx(ctx, "deploy plan %d not found", canaryConfig.CanaryDeployPlanId)
		return nil, 0, errors.Wrapf(pipelineErr.ErrCanaryShiftingFailed, "deploy plan %d not found", canaryConfig.CanaryDeployPlanId)
	}
	if deployPlanObj.TrafficMark == "" {
		log.ErrorWithCtx(ctx, "deploy plan %d traffic mark is empty", canaryConfig.CanaryDeployPlanId)
		return nil, 0, errors.Wrapf(pipelineErr.ErrCanaryShiftingFailed, "deploy plan %d traffic mark is empty", canaryConfig.CanaryDeployPlanId)
	}
	deployPlanObjCanaryPolicy := strings.Split(deployPlanObj.CanaryPolicy, ",")
	isTrafficControlDyeing := canaryConfig.TrafficControlMethod == constants.EnumTrafficControlDyeing
	isOnlyTraffic := tools.OnlyContains(canaryPolicyList, constants.PolicyTraffic.String())
	isTrafficAndClientVersion := tools.OnlyContains(canaryPolicyList, constants.PolicyTraffic.String(), constants.PolicyClientVersion.String())
	if isTrafficControlDyeing {
		// 调度里或者工单里放量计划里仅有调整流量比例+流水线里配置流量染色&切片
		if isOnlyTraffic || tools.OnlyContains(deployPlanObjCanaryPolicy, constants.PolicyTraffic.String()) {
			updatedSubTasks, successNumber, err = p.shiftCanaryWithPlan(ctx, task, subtasks, deployPlanObj, percentage)
		} else if isTrafficAndClientVersion || tools.OnlyContains(deployPlanObjCanaryPolicy, constants.PolicyTraffic.String(), constants.PolicyClientVersion.String()) {
			// 调度里或者工单里放量计划里有调整流量比例+客户端版本号+流水线里配置流量染色&切片
			updatedSubTasks, successNumber, err = p.shiftCanaryWithCanaryTrafficAndClientVersion(ctx, task, deployPlanObj, subtasks, percentage)
		}
	}
	return updatedSubTasks, successNumber, err
}

// 构建子环境路由 支持多流量标记（当前仅加客户端版本的流量标记+以及金丝雀切片策略）
func (p *PipelineRunSvc) shiftCanaryWithCanaryTrafficAndClientVersion(ctx context.Context, task *dao.PipelineRunTask, dplPlan *pbdep.DeployPlan, subtasks []dao.PipelineRunSubtask, percentage int64) ([]dao.PipelineRunSubtask, int64, error) {
	var canaryRegex string
	if percentage > 0 {
		canaryRegex = p.canarySvc.generateRegexForDeployPlan(percentage, dplPlan.Id)
		if canaryRegex == "" {
			return nil, 0, errors.Wrapf(pipelineErr.ErrCanaryParamInvalid, "generate regex failed")
		}
	}

	var (
		config          model.DeployCanaryConfig
		updatedSubTasks []dao.PipelineRunSubtask
		successNumber   int64
	)
	for _, sTask := range subtasks {
		if err := json.Unmarshal(sTask.Config, &config); err != nil {
			return nil, 0, errors.Wrapf(err, "unmarshal subtask %d canary config failed", sTask.ID)
		}
		if config.Percentage == percentage {
			log.InfoWithCtx(ctx, "[shiftCanaryWithCanaryTrafficAndClientVersion] subtask %d already rollout to %d, skip", sTask.ID, percentage)
			successNumber += 1
			continue
		}

		appName := task.StageRun.PipelineRun.Pipeline.AppName
		if percentage > 0 {
			// 大于0添加路由
			if err := p.buildCanaryRouteWithCanaryTrafficAndClientVersion(ctx, config, appName, canaryRegex, []string{dplPlan.TrafficMark, constants.DefaultTrafficMark}); err != nil {
				log.ErrorWithCtx(ctx, "[shiftCanaryWithCanaryTrafficAndClientVersion] task %d build canary trafficMark failed, %v", sTask.ID, err)
				break
			}
		} else {
			// 等于0清除路由
			if err := p.clearCanaryRouteWithDyeing(ctx, config, appName); err != nil {
				log.ErrorWithCtx(ctx, "[shiftCanaryWithCanaryTrafficAndClientVersion] task %d clear canary trafficMark failed, %v", sTask.ID, err)
				break
			}
		}
		log.InfoWithCtx(ctx, "rollout canary success, task %d subtask %d cluster %s namespace %s regex %s percent %d", task.ID, sTask.ID, config.Cluster, config.Namespace, canaryRegex, percentage)
		updatedSubTasks = append(updatedSubTasks, sTask)
		successNumber += 1
	}

	return updatedSubTasks, successNumber, nil
}

// shiftCanaryWithDyeing 流量染色
func (p *PipelineRunSvc) shiftCanaryWithDyeing(ctx context.Context, task *dao.PipelineRunTask, subtasks []dao.PipelineRunSubtask, percentage int64) ([]dao.PipelineRunSubtask, int64, error) {
	var canaryRegex string
	if percentage > 0 {
		canaryRegex = p.canarySvc.generateRegex(percentage, task.PipelineRunId)
		if canaryRegex == "" {
			return nil, 0, errors.Wrapf(pipelineErr.ErrCanaryParamInvalid, "generate regex failed")
		}
	}

	return p.shiftCanary(ctx, task, subtasks, percentage, canaryRegex)
}

func (p *PipelineRunSvc) shiftCanaryWithPlan(ctx context.Context, task *dao.PipelineRunTask, subtasks []dao.PipelineRunSubtask, dplPlan *pbdep.DeployPlan, percentage int64) ([]dao.PipelineRunSubtask, int64, error) {
	var canaryRegex string
	if percentage > 0 {
		canaryRegex = p.canarySvc.generateRegex(percentage, dplPlan.Id)
		if canaryRegex == "" {
			return nil, 0, errors.Wrapf(pipelineErr.ErrCanaryParamInvalid, "generate regex failed")
		}
	}

	return p.shiftCanary(ctx, task, subtasks, percentage, canaryRegex)
}

func (p *PipelineRunSvc) shiftCanary(ctx context.Context, task *dao.PipelineRunTask, subtasks []dao.PipelineRunSubtask, percentage int64, canaryRegex string) ([]dao.PipelineRunSubtask, int64, error) {
	var (
		config          model.DeployCanaryConfig
		updatedSubTasks []dao.PipelineRunSubtask
		successNumber   int64
	)
	for _, sTask := range subtasks {
		if err := json.Unmarshal(sTask.Config, &config); err != nil {
			return nil, 0, errors.Wrapf(err, "unmarshal subtask %d canary config failed", sTask.ID)
		}
		if config.Percentage == percentage {
			log.InfoWithCtx(ctx, "subtask %d already rollout to %d, skip", sTask.ID, percentage)
			successNumber += 1
			continue
		}

		appName := task.StageRun.PipelineRun.Pipeline.AppName
		if percentage > 0 {
			// 大于0添加路由
			if err := p.buildCanaryRouteWithDyeing(ctx, config, appName, canaryRegex); err != nil {
				log.ErrorWithCtx(ctx, "[shiftCanaryWithDyeing] task %d build canary trafficMark failed, %v", sTask.ID, err)
				break
			}
		} else {
			// 等于0清除路由
			if err := p.clearCanaryRouteWithDyeing(ctx, config, appName); err != nil {
				log.ErrorWithCtx(ctx, "[shiftCanaryWithDyeing] task %d clear canary trafficMark failed, %v", sTask.ID, err)
				break
			}
		}
		log.InfoWithCtx(ctx, "rollout canary success, task %d subtask %d cluster %s namespace %s regex %s percent %d", task.ID, sTask.ID, config.Cluster, config.Namespace, canaryRegex, percentage)
		updatedSubTasks = append(updatedSubTasks, sTask)
		successNumber += 1
	}

	return updatedSubTasks, successNumber, nil
}

func (p *PipelineRunSvc) buildCanaryRouteWithDyeing(ctx context.Context, taskConfig model.DeployCanaryConfig, appName, canaryRegex string) error {
	req := mesh.BuildSubenvRouteWithMultiTrafficMarkRequest{
		Service: &mesh.Service{
			Cluster:   taskConfig.Cluster,
			Namespace: taskConfig.Namespace,
			Name:      appName,
		},
		SubenvName: taskConfig.Senv,
		TrafficMarks: &mesh.TrafficMarkWithMulti{
			Identifier: "canary", // NOTICE: 和容器云约定，金丝雀场景下这个值固定写死
			StringMatchs: []*mesh.StringMatch{{
				MatchType: &mesh.StringMatch_Regex{
					Regex: canaryRegex,
				}},
			},
		},
	}
	_, err := p.cloudAggClient.BuildSubenvRouteWithMultiTrafficMark(ctx, &req)
	if err != nil {
		// 可能容器云有故障，退出之后等用户重试
		log.ErrorWithCtx(ctx, "[buildCanaryRouteWithDyeing] failed to BuildSubenvRoute, err: %v, req: %+v", err, req.String())
		return err
	}

	return nil
}

func (p *PipelineRunSvc) buildCanaryRouteWithCanaryTrafficAndClientVersion(ctx context.Context, taskConfig model.DeployCanaryConfig, appName, canaryRegex string, trafficMarks []string) error {
	var stringMatchs []*mesh.StringMatch
	// 保险一点 先匹配客户端的流量标记 再匹配金丝雀切片策略
	for _, trafficMark := range trafficMarks {
		stringMatchs = append(stringMatchs, &mesh.StringMatch{
			MatchType: &mesh.StringMatch_Exact{
				Exact: trafficMark,
			}})
	}
	stringMatchs = append(stringMatchs, &mesh.StringMatch{
		MatchType: &mesh.StringMatch_Regex{
			Regex: canaryRegex,
		}})
	req := mesh.BuildSubenvRouteWithMultiTrafficMarkRequest{
		Service: &mesh.Service{
			Cluster:   taskConfig.Cluster,
			Namespace: taskConfig.Namespace,
			Name:      appName,
		},
		SubenvName: taskConfig.Senv,
		TrafficMarks: &mesh.TrafficMarkWithMulti{
			Identifier:   "canary", // NOTICE: 和容器云约定，金丝雀场景下这个值固定写死
			StringMatchs: stringMatchs,
		},
	}
	_, err := p.cloudAggClient.BuildSubenvRouteWithMultiTrafficMark(ctx, &req)
	if err != nil {
		// 可能容器云有故障，退出之后等用户重试
		log.ErrorWithCtx(ctx, "[buildCanaryRouteWithCanaryAndClientVersion] failed to BuildSubenvRoute, err: %v, req: %+v", err, req.String())
		return err
	}

	return nil
}

func (p *PipelineRunSvc) clearCanaryRouteWithDyeing(ctx context.Context, taskConfig model.DeployCanaryConfig, appName string) error {
	req := &mesh.ClearSubenvRouteRequest{
		Service: &mesh.Service{
			Cluster:   taskConfig.Cluster,
			Namespace: taskConfig.Namespace,
			Name:      appName,
		},
		SubenvName: taskConfig.Senv,
	}
	_, err := p.cloudAggClient.ClearSubenvRoute(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "[clearCanaryRouteWithDyeing] failed to ClearSubEnvRoute, err: %v, req: %+v", err, req)
		return err
	}

	return nil
}

// shiftCanaryWithWeight istio权重路由
func (p *PipelineRunSvc) shiftCanaryWithWeight(ctx context.Context, task *dao.PipelineRunTask, subtasks []dao.PipelineRunSubtask, percentage int64) ([]dao.PipelineRunSubtask, int64, error) {
	var (
		config          model.DeployCanaryConfig
		updatedSubTasks []dao.PipelineRunSubtask
		successNumber   int64
	)
	for _, sTask := range subtasks {
		if err := json.Unmarshal(sTask.Config, &config); err != nil {
			return nil, 0, errors.Wrapf(err, "unmarshal subtask %d canary config failed", sTask.ID)
		}
		if config.Percentage == percentage {
			log.InfoWithCtx(ctx, "subtask %d already rollout to %d, skip", sTask.ID, percentage)
			successNumber += 1
			continue
		}

		appName := task.StageRun.PipelineRun.Pipeline.AppName
		weight := uint32(percentage)
		if weight > 0 {
			// 大于0添加路由
			if err := p.buildCanaryRouteWithWeight(ctx, config, appName, weight); err != nil {
				log.ErrorWithCtx(ctx, "[shiftCanaryWithWeight] task %d build canary trafficMark failed, %v", sTask.ID, err)
				break
			}
		} else {
			// 等于0清除路由
			if err := p.clearCanaryRouteWithWeight(ctx, config, appName); err != nil {
				log.ErrorWithCtx(ctx, "[shiftCanaryWithDyeing] task %d clear canary trafficMark failed, %v", sTask.ID, err)
				break
			}
		}
		log.InfoWithCtx(ctx, "rollout canary success, task %d subtask %d cluster %s namespace %s percent %d", task.ID, sTask.ID, config.Cluster, config.Namespace, percentage)
		updatedSubTasks = append(updatedSubTasks, sTask)
		successNumber += 1
	}

	return updatedSubTasks, successNumber, nil
}

func (p *PipelineRunSvc) buildCanaryRouteWithWeight(ctx context.Context, taskConfig model.DeployCanaryConfig, appName string, percentage uint32) error {
	req := mesh.BuildSubenvRouteWithWeightRequest{
		Service: &mesh.Service{
			Cluster:   taskConfig.Cluster,
			Namespace: taskConfig.Namespace,
			Name:      appName,
		},
		SubenvName: taskConfig.Senv,
		Weight:     percentage,
	}
	_, err := p.cloudAggClient.BuildSubenvRouteWithWeight(ctx, &req)
	if err != nil {
		// 可能容器云有故障，退出之后等用户重试
		log.ErrorWithCtx(ctx, "[buildCanaryRouteWithWeight] failed to BuildSubenvRouteWithWeight, err: %v, req: %+v", err, req.String())
		return err
	}

	// 添加默认流量标记
	if err := p.buildDefaultCanaryRoute(ctx, taskConfig.Cluster, taskConfig.Namespace, taskConfig.Senv, appName); err != nil {
		log.ErrorWithCtx(ctx, "[buildCanaryRouteWithWeight] failed to buildDefaultCanaryRoute, err: %v, req: %+v", err, req.String())
		return err
	}

	return nil
}

func (p *PipelineRunSvc) buildDefaultCanaryRoute(ctx context.Context, cluster, namespace, senv, appName string) error {
	stringMatchs := []*mesh.StringMatch{{
		MatchType: &mesh.StringMatch_Exact{
			Exact: constants.DefaultTrafficMark, // 运营后台要求添加的流量标记
		}},
	}
	meshService := mesh.Service{Cluster: cluster, Namespace: namespace, Name: appName}
	buildRouteReq := mesh.BuildSubenvRouteWithMultiTrafficMarkRequest{
		Service:    &meshService,
		SubenvName: senv,
		TrafficMarks: &mesh.TrafficMarkWithMulti{
			Identifier:   "canary",
			StringMatchs: stringMatchs,
		},
	}
	_, err := p.cloudAggClient.BuildSubenvRouteWithMultiTrafficMark(ctx, &buildRouteReq)
	if err != nil {
		return err
	}

	return nil
}

func (p *PipelineRunSvc) clearCanaryRouteWithWeight(ctx context.Context, taskConfig model.DeployCanaryConfig, appName string) error {
	req := &mesh.ClearSubenvRouteWithWeightRequest{
		Service: &mesh.Service{
			Cluster:   taskConfig.Cluster,
			Namespace: taskConfig.Namespace,
			Name:      appName,
		},
		SubenvName: taskConfig.Senv,
	}
	_, err := p.cloudAggClient.ClearSubenvRouteWithWeight(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "[clearCanaryRouteWithWeight] failed to ClearSubenvRouteWithWeight, err: %v, req: %+v", err, req)
		return err
	}

	return nil
}

func (p *PipelineRunSvc) GetBaseEnvConfigId(ctx context.Context, subEnvTask *dao.PipelineRunSubtask) (int64, error) {
	pipelineRunId := subEnvTask.PipelineRunID
	pr, err := p.prRepo.FindPipelineRunByIdPreload(ctx, pipelineRunId)
	if err != nil {
		return 0, err
	}
	subEnvTaskConfig := model.AutomationDeploy{}
	if err := json.Unmarshal(subEnvTask.Config, &subEnvTaskConfig); err != nil {
		return 0, errors.Wrap(err, "getOriginEnvSubtask failed")
	}
	for _, stage := range pr.Stages {
		for _, t := range stage.Tasks {
			if t.ID <= subEnvTask.PipelineRunTaskId {
				continue
			}
			if t.Type == string(constants.TASK_AUTOMATION_DEPLOY) {
				// 根据 namespace cluster 匹配子任务
				for _, subTask := range t.SubRunTasks {
					taskConfig := model.AutomationDeploy{}
					if err := json.Unmarshal(subTask.Config, &taskConfig); err != nil {
						return 0, errors.Wrap(err, "getOriginEnvSubtask unmarshal origin env task config failed")
					}
					if taskConfig.Namespace == subEnvTaskConfig.Namespace && taskConfig.Cluster == subEnvTaskConfig.Cluster {
						return taskConfig.ConfigId, nil
					}
				}
			}
		}
	}
	return 0, nil
}

// ClearAfterDeletePipelineRuns 有多个业务逻辑：删除 tekton 资源，调用工单 grpc 等等，需要做好数据一致性的处理。
// 一致性的保障：只有全部PipelineRun 的清理动作都成功，这个调用才是成功的。这个函数应该具有幂等性。
func (p *PipelineRunSvc) ClearAfterDeletePipelineRuns(ctx context.Context, pipelineRunIds []int64) error {
	if len(pipelineRunIds) == 0 {
		log.WarnWithCtx(ctx, "pipelineRunIds is empty")
		return nil
	}

	var (
		pipelineRuns  []*dao.PipelineRun
		approvalTasks []dao.PipelineRunTask
		subTasks      []*dao.PipelineRunSubtask
	)

	allCleared := true // use to control return error or nil

	handleReturn := func() error {
		if allCleared {
			log.InfoWithCtx(ctx, "ClearAfterDeletePipelineRuns all pipelineRun cleared")
			return nil
		} else {
			log.ErrorWithCtx(ctx, "ClearAfterDeletePipelineRuns not all pipelineRun cleared")
			return errors.New("ClearAfterDeletePipelineRuns not all pipelineRun cleared")
		}
	}

	// find all approval tasks and abandon ticket
	for _, id := range pipelineRunIds {
		tasks, err := p.prRepo.FindApprovalTasksByPipelineRunID(ctx, id)
		if err != nil {
			log.ErrorWithCtx(ctx, "ClearAfterDeletePipelineRuns find approval tasks by pipelineRun id %d failed: %v", id, err)
			allCleared = false
			continue
		}
		approvalTasks = append(approvalTasks, tasks...)
	}

	// call DeployService to handle related tickets
	if len(approvalTasks) != 0 {
		log.InfoWithCtx(ctx, "ClearAfterDeletePipelineRuns approvalTasks is not empty, need to handle ticket")
		ticketParam := &pbdep.TicketAbandonParams{
			TaskRunIds: make([]int64, len(approvalTasks)),
		}
		for i, task := range approvalTasks {
			ticketParam.TaskRunIds[i] = task.ID
		}
		if _, err := p.ticketClient.AbandonTicketBy(ctx, ticketParam); err != nil {
			allCleared = false
			log.ErrorWithCtx(ctx, "ClearAfterDeletePipelineRuns abandon ticket %s failed %v", ticketParam.String(), err)
		}
	} else {
		log.InfoWithCtx(ctx, "ClearAfterDeletePipelineRuns approvalTasks is empty, no need to handle ticket")
	}

	// find tekton resources
	for _, id := range pipelineRunIds {
		pr, err := p.prRepo.FindPipelineRunPreloadSubRunTasks(ctx, id)
		if err != nil {
			log.ErrorWithCtx(ctx, "ClearAfterDeletePipelineRuns find pipelineRun by id %d failed: %v", id, err)
			// 出于幂等性考虑，这里不返回错误，继续处理下一个 pipelineRun
			allCleared = false
			continue
		}
		if pr == nil {
			log.WarnWithCtx(ctx, "ClearAfterDeletePipelineRuns pipelineRun id %d not found", id)
			continue
		}
		if pr.TektonNamespace == "" || pr.TektonName == "" {
			log.WarnWithCtx(ctx, "ClearAfterDeletePipelineRuns PipelineRun %d missing Namespace[%s] Name[%s]", pr.ID, pr.TektonNamespace, pr.TektonName)
			continue
		}
		pipelineRuns = append(pipelineRuns, pr)

		// find all subtasks
		for i, subtask := range pr.SubRunTasks {
			if subtask.TektonNamespace == "" || subtask.TektonName == "" {
				log.WarnWithCtx(ctx, "ClearAfterDeletePipelineRuns PipelineRun %d SubTask %d missing Namespace[%s] Name[%s]", pr.ID, subtask.ID, subtask.TektonNamespace, subtask.TektonName)
				continue
			}
			subTasks = append(subTasks, &pr.SubRunTasks[i])
		}
	}

	// delete PipelineRun tekton resources
	for _, pr := range pipelineRuns {
		if err := p.tektonClient.DeletePipelineRun(ctx, pr.TektonNamespace, pr.TektonName); err != nil {
			if errors.Is(err, pipelineErr.ErrTektonPipelineRunNotFound) {
				log.InfoWithCtx(ctx, "ClearAfterDeletePipelineRuns PipelineRun %d tekton pipelinerun %s deleted already", pr.ID, pr.TektonName)
			} else {
				allCleared = false
				log.ErrorWithCtx(ctx, "ClearAfterDeletePipelineRuns PipelineRun %d delete tekton pipelinerun %s failed: %v", pr.ID, pr.TektonName, err)
			}
		}
		log.InfoWithCtx(ctx, "ClearAfterDeletePipelineRuns PipelineRun %d tekton pipelinerun %s deleted", pr.ID, pr.TektonName)
	}

	// delete SubTask tekton resources
	for _, subTask := range subTasks {
		if err := p.tektonClient.DeletePipelineRun(ctx, subTask.TektonNamespace, subTask.TektonName); err != nil {
			if errors.Is(err, pipelineErr.ErrTektonPipelineRunNotFound) {
				log.InfoWithCtx(ctx, "ClearAfterDeletePipelineRuns PipelineRun %d SubTask %d tekton pipelinerun %s deleted already", subTask.PipelineRunID, subTask.ID, subTask.TektonName)
			} else {
				allCleared = false
				log.ErrorWithCtx(ctx, "ClearAfterDeletePipelineRuns PipelineRun %d SubTask %d delete tekton pipelinerun %s failed: %v", subTask.PipelineRunID, subTask.ID, subTask.TektonName, err)
			}
		}
		log.InfoWithCtx(ctx, "ClearAfterDeletePipelineRuns PipelineRun %d SubTask %d tekton pipelinerun %s deleted", subTask.PipelineRunID, subTask.ID, subTask.TektonName)
	}

	return handleReturn()
}

func (p *PipelineRunSvc) HandleDeleteEvent(ctx context.Context, event *pbevent.DeletePipelineResourceEvent) error {
	var (
		deletedPipelineRunList []int64
		pipelineRuns           []dao.PipelineRun
		releasePipelineRuns    []dao.PipelineRun
		err                    error
	)
	log.InfoWithCtx(ctx, "HandleDeleteEvent %s", event.String())
	switch event.From {
	case pbevent.DeletePipelineResourceEvent_PIPELINE:
		log.InfoWithCtx(ctx, "DeleteEvent from pipeline %d", event.PipelineId)
		pipelineRuns, err = p.prRepo.FindPipelineRunsByPipelines(ctx, []int64{event.PipelineId})
		if err != nil {
			return errors.Wrapf(err, "find pipelinerun by pipeline %d failed", event.PipelineId)
		}
	case pbevent.DeletePipelineResourceEvent_PIPELINE_GROUP:
		log.InfoWithCtx(ctx, "DeleteEvent from group %d", event.PipelineGroupId)
		pipelines, err := p.groupRepo.GetAllPipelines(ctx, event.PipelineGroupId)
		if err != nil {
			return errors.Wrapf(err, "find pipelines by group %d failed", event.PipelineGroupId)
		}
		pipelineIds := make([]int64, len(pipelines))
		for i, p := range pipelines {
			pipelineIds[i] = p.ID
		}
		pipelineRuns, err = p.prRepo.FindPipelineRunsByPipelines(ctx, pipelineIds)
		if err != nil {
			return errors.Wrapf(err, "find pipelinerun by pipelines %v failed", pipelineIds)
		}
	case pbevent.DeletePipelineResourceEvent_TEMPLATE:
		log.InfoWithCtx(ctx, "DeleteEvent from template %d", event.TemplateId)
		pipelines, err := p.pRepo.GetAllPipelinesByTemplateID(ctx, event.TemplateId)
		if err != nil {
			return errors.Wrapf(err, "find pipelines by template %d failed", event.TemplateId)
		}
		pipelineIds := make([]int64, len(pipelines))
		for i, p := range pipelines {
			pipelineIds[i] = p.ID
		}
		pipelineRuns, err = p.prRepo.FindPipelineRunsByPipelines(ctx, pipelineIds)
		if err != nil {
			return errors.Wrapf(err, "find pipelinerun by pipelines %v failed", pipelineIds)
		}
	default:
		log.ErrorWithCtx(ctx, "DeleteEvent %s unknown from field", event.String())
		return errors.Errorf("unknown delete event from %s", event.From.String())
	}

	if len(pipelineRuns) == 0 {
		log.InfoWithCtx(ctx, "DeleteEvent %s has not related PipelineRun", event.String())
		return nil
	}
	for _, pipelineRun := range pipelineRuns {
		deletedPipelineRunList = append(deletedPipelineRunList, pipelineRun.ID)
	}
	// 获取所有服务，清除所有服务关联的发布计划
	for _, pipelineRun := range pipelineRuns {
		if pipelineRun.Pipeline.Type == constants.RELEASE.String() {
			releasePipelineRuns = append(releasePipelineRuns, pipelineRun)
		}
	}
	if len(releasePipelineRuns) > 0 {
		appIds := make([]int64, 0, len(releasePipelineRuns))
		pipelineRunIds := make([]int64, 0, len(releasePipelineRuns))
		for _, pr := range pipelineRuns {
			appIds = append(appIds, pr.AppId)
			pipelineRunIds = append(pipelineRunIds, pr.ID)
		}
		appIds = vec.RemoveDuplicate(appIds)
		if len(appIds) > 0 && len(pipelineRunIds) > 0 {
			log.InfoWithCtx(ctx, "DeleteEvent [%s] unbind deploy plan by appIds %v, pipelineRunIds %v", event.String(), appIds, pipelineRunIds)
			req := &pbdep.UnbindDeployPlanRecordsReq{
				AppIds:         appIds,
				PipelineRunIds: pipelineRunIds,
			}
			_, err = p.deployPlanClient.UnbindDeployPlanRecordsBy(ctx, req)
			if err != nil {
				log.ErrorWithCtx(ctx, "DeleteEvent [%s] unbind deploy plan failed: %v, req: %v", event.String(), err, req)
			}
		}
	}
	return p.ClearAfterDeletePipelineRuns(ctx, deletedPipelineRunList)
}

func (p *PipelineRunSvc) GetGitRepoStat(ctx context.Context, gitRepoStat model.GitRepoStatReq) (*model.GitRepoStatResp, error) {
	return p.prRepo.ListTopNGitbranchs(ctx, gitRepoStat)
}

func (p *PipelineRunSvc) DeletePipelineRunSubtask(ctx context.Context, id int64) error {
	subTask, err := p.prRepo.FindPreloadPipelineRunSubtaskById(ctx, id)
	if err != nil {
		return err
	}
	if subTask.Status != constants.PENDING && subTask.Status != constants.UNHANDLED && subTask.Status != constants.FAILED {
		return fmt.Errorf("部署任务不是待处理、失败状态，无法删除")
	}
	if err = p.prRepo.DeletePipelineRunSubtaskById(ctx, id); err != nil {
		return err
	}
	// 如果部署子任务都已经完成，驱动流水线往下运行
	search := model.SubTaskSearch{
		TaskRunId:   subTask.PipelineRunTaskId,
		EnabledList: []int64{1},
	}
	subTasks, err := p.prRepo.FindSubTasksByMultiParams(ctx, search)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeletePipelineRunSubtask find sub task by main task id[%d] failed, err: %v", subTask.PipelineRunTaskId, err)
		return err
	}
	isPipelineRunSubTasksAllSuccess := func(subTaskRuns []dao.PipelineRunSubtask) bool {
		for _, tkRun := range subTaskRuns {
			if tkRun.Status != constants.SUCCESSFUL {
				return false
			}
		}
		return true
	}(subTasks)
	if len(subTasks) > 0 && isPipelineRunSubTasksAllSuccess && subTask.PipelineRunTask.TektonName != "" {
		err = p.approvalClient.FakeDeployPass(ctx, []string{subTask.PipelineRunTask.TektonName})
		if err != nil {
			log.ErrorWithCtx(ctx, "DeletePipelineRunSubtask isPipelineRunSubTasksAllSuccess success taskRunId[%d]调用 FakeDeployPass err:%v", subTask.PipelineRunTaskId, err)
			return err
		}
		// 更新状态
		return db.Transaction(func(tx *gorm.DB) error {
			txCtx := db.CtxWithTX(ctx, tx)
			if err := p.prRepo.TxBatchUpdatePipelineRunTaskStatus(txCtx, []int64{subTask.PipelineRunTaskId}, constants.SUCCESSFUL.String()); err != nil {
				return err
			}
			status := constants.PENDING.String()
			if subTask.PipelineRunTask.StageRun.IsLastTask(subTask.PipelineRunTaskId) {
				// 当前任务是当前阶段的最后一个，需要将stage状态更新为成功，否则代表还有后面的任务执行，stage为pending
				status = constants.SUCCESSFUL.String()
			}
			if err := p.prRepo.UpdateStageRunStatusAndTime(txCtx, subTask.PipelineRunTask.PipelineRunStageId, map[string]interface{}{
				"status":         status,
				"completed_time": time.Now(),
				"updated_at":     time.Now(),
			}); err != nil {
				log.ErrorWithCtx(ctx, "[DeletePipelineRunSubtask] update pipelineRunStage[%d] error: %v", subTask.PipelineRunTask.PipelineRunStageId, err)
				return err
			}
			return nil
		})
	}
	return nil
}

func (p *PipelineRunSvc) AddPipelineRunSubtask(ctx context.Context, addSubTaskParam model.AddSubTaskParam) error {
	taskRun, err := p.prRepo.FindRunTaskById(ctx, addSubTaskParam.TaskRunId)
	if err != nil || taskRun == nil {
		log.Errorf("AddPipelineRunSubtask 根据taskRunId查询pipelineRunTask出错:%v", err)
		return err
	}

	var deployConfig model.AutomationDeploy
	deployConfig.ConfigId, deployConfig.Cluster, deployConfig.Namespace, deployConfig.Senv, deployConfig.EnvTarget, deployConfig.DeployEnv, deployConfig.TriggerMode, deployConfig.Timeout = addSubTaskParam.ConfigId, addSubTaskParam.Cluster, addSubTaskParam.Namespace, addSubTaskParam.Senv, addSubTaskParam.EnvTarget, addSubTaskParam.DeployEnv, constants.MANUAL.String(), addSubTaskParam.Timeout
	configByte, _ := json.Marshal(deployConfig)
	newSubTask := &dao.PipelineRunSubtask{
		Name:              taskRun.Name,
		Type:              taskRun.Type,
		Status:            constants.PENDING,
		PipelineRunTaskId: addSubTaskParam.TaskRunId,
		Enabled:           true,
		PipelineRunID:     taskRun.PipelineRunId,
		Config:            configByte,
	}
	if err = p.prRepo.CreatePipelineRunSubtask(ctx, newSubTask); err != nil {
		log.ErrorWithCtx(ctx, "AddPipelineRunSubtask 新增部署子任务失败, err: %v", err)
		return err
	}
	return nil
}

func (p *PipelineRunSvc) GetPipelineRunQuota() (int, error) {
	// calculate the pipelinerun quota of project
	return 0, nil
}

// CheckAppUpgrade 检查应用升级，提单前判断当前流水线是否有在途生产流程流水线
//func (p *PipelineRunSvc) CheckAppUpgrade(ctx context.Context, req model.UpgradeCheckReq) ([]model.UpgradeCheckPipelineRuns, error) {
//	taskRun, err := p.prRepo.FindRunTaskById(ctx, req.TaskRunID)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "[CheckAppUpgrade] find pipeline run task by ID[%d] error: %v", req.TaskRunID, err)
//		return nil, err
//	}
//	if taskRun == nil {
//		log.ErrorWithCtx(ctx, "[CheckAppUpgrade] find pipeline run task by ID[%d] not existed", req.TaskRunID)
//		return nil, fmt.Errorf("pipelineRunTask[%d] not existed", req.TaskRunID)
//	}
//	if taskRun.Type != constants.TASK_UPGRADE_APPROVAL.String() {
//		return nil, nil
//	}
//	req.PipelineRunID = taskRun.PipelineRunId
//	req.MaxPipelineRunID = conf.AppConfig.UpgradeCheck.MaxPipelineRunID
//	pipelineRuns, err := p.prRepo.FindPipelineRunInProdStage(ctx, req)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "[CheckAppUpgrade] find pipeline run in prod stage error: %v", err)
//		return nil, err
//	}
//	var checkResults []model.UpgradeCheckPipelineRuns
//	for _, pr := range pipelineRuns {
//		checkResults = append(checkResults, model.UpgradeCheckPipelineRuns{
//			ID:                   pr.ID,
//			PipelineId:           pr.PipelineId,
//			BuildNumber:          pr.BuildNumber,
//			StartedTime:          pr.CreatedAt,
//			TriggerByChineseName: pr.TriggerByChineseName,
//			TriggerByEmployeeNo:  pr.TriggerByEmployeeNo,
//			Branch:               pr.Branch,
//		})
//	}
//
//	return checkResults, nil
//}
//
//// CheckPassTask 检查任务是否可以通过
//func (p *PipelineRunSvc) CheckPassTask(ctx context.Context, req model.PassTaskReq) (*model.PassCheckResp, error) {
//	taskRun, err := p.prRepo.FindTaskRunById(ctx, req.TaskRunId)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "[CheckPassTask] find pipeline run task by ID[%d] error: %v", req.TaskRunId, err)
//		return nil, err
//	}
//	if taskRun == nil {
//		log.ErrorWithCtx(ctx, "[CheckPassTask] find pipeline run task by ID[%d] not existed", req.TaskRunId)
//		return nil, fmt.Errorf("pipelineRunTask[%d] not existed", req.TaskRunId)
//	}
//
//	var taskConfig model.TaskRunCanaryConfig
//	if err = json.Unmarshal(taskRun.Config, &taskConfig); err != nil {
//		log.ErrorWithCtx(ctx, "[CheckPassTask] unmarshal task run config error: %v", err)
//		return nil, err
//	}
//	// 金丝雀发布策略为客户端版本号，才需要做发布计划校验
//	if taskConfig.CanaryPolicy != constants.PolicyClientVersion {
//		return &model.PassCheckResp{IsPass: true}, nil
//	}
//	planRun, err := p.deployPlanClient.GetPlanRun(ctx, &pbdep.PlanRunReq{PipelineRunId: taskRun.PipelineRunId})
//	if err != nil {
//		log.ErrorWithCtx(ctx, "[CheckPassTask] get plan run by pipeline run id[%d] error: %v", taskRun.PipelineRunId, err)
//		return nil, err
//	}
//	var isPass bool
//	if planRun.Id != 0 {
//		// 发布计划处于运行中/失败状态，发出提示
//		isPass = planRun.Status != constants.PlanRunRunning.String() && planRun.Status != constants.PlanRunFail.String()
//	} else {
//		isPass = true
//	}
//	return &model.PassCheckResp{IsPass: isPass}, nil
//}
//
//func (p *PipelineRunSvc) CheckMultiCloudUpgrade(ctx context.Context, req model.MultiCloudUpgradeCheckReq) (*model.MultiCloudUpgradeCheckResp, error) {
//	subTask, err := p.prRepo.FindPreloadPipelineRunSubtaskById(ctx, req.DeployID)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "[CheckMultiCloudUpgrade] find pipeline run subtask by ID[%d] error: %v", req.DeployID, err)
//		return nil, err
//	}
//	if subTask == nil {
//		log.ErrorWithCtx(ctx, "[CheckMultiCloudUpgrade] find pipeline run subtask by ID[%d] not existed", req.DeployID)
//		return nil, fmt.Errorf("pipelineRunSubtask[%d] not existed", req.DeployID)
//	}
//	// 只有生产环境的部署基准任务才需要检查
//	if subTask.PipelineRunTask.StageRun.Type != constants.STAGE_DEPLOY_PROD_ENV.String() ||
//		subTask.PipelineRunTask.Type != constants.TASK_AUTOMATION_DEPLOY.String() {
//		return &model.MultiCloudUpgradeCheckResp{IsPass: true}, nil
//	}
//
//	// 部署生产环境存在金丝雀任务才需要检查
//	canaryTask, err := p.prRepo.FindTaskRunByTypeAndStage(ctx, constants.TASK_DEPLOY_CANARY, subTask.PipelineRunTask.StageRun.ID)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "[CheckMultiCloudUpgrade] find canary task run by stage ID[%d] error: %v", subTask.PipelineRunTask.StageRun.ID, err)
//		return nil, err
//	}
//	if canaryTask == nil {
//		return &model.MultiCloudUpgradeCheckResp{IsPass: true}, nil
//	}
//
//	var taskConfig model.TaskRunCanaryConfig
//	if err = json.Unmarshal(canaryTask.Config, &taskConfig); err != nil {
//		log.ErrorWithCtx(ctx, "[CheckMultiCloudUpgrade] unmarshal task run config error: %v", err)
//		return nil, err
//	}
//	// 金丝雀发布策略为客户端版本号，才需要做发布计划校验
//	if taskConfig.CanaryPolicy != constants.PolicyClientVersion {
//		return &model.MultiCloudUpgradeCheckResp{IsPass: true}, nil
//	}
//	planRun, err := p.deployPlanClient.GetPlanRun(ctx, &pbdep.PlanRunReq{PipelineRunId: canaryTask.PipelineRunId})
//	if err != nil {
//		log.ErrorWithCtx(ctx, "[CheckMultiCloudUpgrade] get plan run by pipeline run id[%d] error: %v", canaryTask.PipelineRunId, err)
//		return nil, err
//	}
//
//	var (
//		deployOriginTime time.Time
//	)
//	isPass := true
//	if planRun.Id != 0 {
//		// 发布计划处于运行中且发布计划存在全量部署任务
//		if planRun.Status == constants.PlanRunRunning.String() {
//			for _, node := range planRun.Nodes {
//				if node.Action == constants.PlanActionDeployOrigin.String() {
//					isPass = false
//					deployOriginTime = time.Unix(node.StartedTime, 0)
//					break
//				}
//			}
//		}
//	}
//
//	return &model.MultiCloudUpgradeCheckResp{
//		IsPass:           isPass,
//		PipelineRunID:    subTask.PipelineRunID,
//		DeployOriginTime: &deployOriginTime,
//	}, nil
//}

func (p *PipelineRunSvc) HandleTaskRunStatus(ctx context.Context, prTaskId, prSubTaskId int64, status constants.PipelineStatus) error {
	var err error
	tx := db.DB.Begin()
	txCtx := db.CtxWithTX(ctx, tx)
	defer func() {
		if r := recover(); r != nil {
			log.ErrorWithCtx(ctx, "[handleDeployStatus] panic %v: stack %s, subTaskId[%d] status:%v", r, debug.Stack(), prSubTaskId, status)
			tx.Rollback()
		}
		if err != nil {
			log.ErrorWithCtx(ctx, "[handleDeployStatus] error: %v, rollbacked, subTaskId[%d] status:%v", err, prSubTaskId, status)
			tx.Rollback()
		} else {
			log.InfoWithCtx(ctx, "[handleDeployStatus] success: committed, subTaskId[%d] status:%v", prSubTaskId, status)
			tx.Commit()
		}
	}()

	rt, err := p.prRepo.TxFindRunTask(ctx, tx, prTaskId)
	if err != nil || rt == nil {
		log.ErrorWithCtx(ctx, "[handleDeployStatus] find run task failed, err: %v, taskRunId: %d", err, prTaskId)
		return fmt.Errorf("find run task failed, err: %v, taskRunId: %d", err, prTaskId)
	}

	pr, err := p.prRepo.TxFindPipelineRunLocked(txCtx, rt.PipelineRunId)
	if err != nil || pr == nil {
		log.ErrorWithCtx(ctx, "[handleDeployStatus] find pipeline run failed, err: %v, prId: %d", err, rt.PipelineRunId)
		return fmt.Errorf("find pipeline run failed, err: %v, prId: %d", err, rt.PipelineRunId)
	}

	// select rows from database by mainTaskID
	subTaskSearch := model.SubTaskSearch{
		TaskRunId:   prTaskId,
		EnabledList: []int64{1},
	}
	subTasks, err := p.prRepo.FindSubTasksByMultiParams(ctx, subTaskSearch)
	if err != nil {
		log.ErrorWithCtx(ctx, "[handleDeployStatus] find sub tasks failed, err: %v, taskRunId: %d", err, prTaskId)
		return fmt.Errorf("find sub tasks failed, err: %v, taskRunId: %d", err, prTaskId)
	}
	stateMachine := newSubTaskStateMachine(pr, rt, subTasks, prSubTaskId)
	// update sub task status, result, tekname etc.
	eventStatus := status
	// 手动停止部署子任务或者终止状态变为失败后，不处理
	if prSubTaskId > 0 && stateMachine.trigger != nil && stateMachine.trigger.Status == constants.FAILED {
		log.InfoWithCtx(ctx, "[handleDeployStatus] sub task ID[%d] TektonName[%s] is failed, ignore", stateMachine.trigger.ID, stateMachine.trigger.TektonName)
		return nil
	}
	switch eventStatus {
	case constants.RUNNING:
		err = stateMachine.cloudSubTaskRun()
	case constants.SUCCESSFUL:
		err = stateMachine.cloudSubTaskSuccess()
	case constants.FAILED:
		err = stateMachine.cloudSubTaskFail()
	case constants.SKIPPED:
		err = stateMachine.cloudSubTaskSkip()
	default:
		return fmt.Errorf("subtask[%d] status %s is not match", prSubTaskId, eventStatus)
	}
	if err != nil {
		return errors.Wrapf(err, "subtask[%d] state machine failed", prSubTaskId)
	}

	// update task run
	log.InfoWithCtx(ctx, "[handleDeployStatus] update task run to %+v", *rt)
	updated, err := p.prRepo.TxUpdatePipelineRunTask(ctx, tx, rt)
	if err != nil {
		return errors.Wrapf(err, "run task update run task failed")
	}
	if updated == 0 {
		log.InfoWithCtx(ctx, "[handleDeployStatus] run task is already updated: %#v", rt)
	}
	deliverEvent(ctx, p.sender, rt, nil)

	if prSubTaskId > 0 {
		// update sub task run
		subTask := stateMachine.getCallbackTask()
		log.InfoWithCtx(ctx, "[handleDeployStatus] update sub task run to %+v", *subTask)
		updated, err = p.prRepo.TxUpdateTaskRunMultiCloud(ctx, tx, subTask)
		if err != nil {
			return errors.Wrapf(err, "sub task update run task failed")
		}
		if updated == 0 {
			log.InfoWithCtx(ctx, "[handleDeployStatus] sub task is already updated: %#v", rt)
		}
		deliverEvent(ctx, p.sender, subTask, nil)
	}

	// update PipelineRunStage and send MQ event
	err = p.processStageRun(txCtx, *rt, rt.PipelineRunStageId)
	if err != nil {
		return errors.Wrap(err, "sub task process stage run failed")
	}

	log.InfoWithCtx(ctx, "[handleDeployStatus] sub task update pipeline run to %+v", *pr)
	updated, err = p.prRepo.TxUpdatePipelineRun(ctx, tx, pr)
	if err != nil {
		log.ErrorWithCtx(ctx, "[handleDeployStatus] update PipelineRun failed: %v", err)
		return errors.Wrapf(err, "update PipelineRun failed")
	}
	if updated == 0 {
		log.InfoWithCtx(ctx, "[handleDeployStatus] pipeline run is already updated: %#v", pr)
	}
	deliverEvent(ctx, p.sender, pr, nil)
	return nil
}

func (p *PipelineRunSvc) processStageRun(ctx context.Context, runTask dao.PipelineRunTask, runStageId int64) error {
	runTaskStatus := runTask.Status
	tx := db.CtxTX(ctx, nil)
	rs, err := p.prRepo.TxFindRunStage(ctx, tx, runStageId)
	if err != nil || rs == nil {
		log.ErrorWithCtx(ctx, "[processStageRun] find run stage failed: %v, runStageId[%d]", err, runStageId)
		return fmt.Errorf("find run stage failed: %v", err)
	}
	if rs.IsFirstTask(runTask.ID) {
		rs.StartedTime = runTask.StartedTime
	}
	if rs.IsLastTask(runTask.ID) && runTaskStatus.IsFinished() {
		rs.CompletedTime = runTask.CompletedTime
	}
	if rs.IsLastTask(runTask.ID) && runTaskStatus == constants.SKIPPED {
		rs.CompletedTime = time.Now()
	}
	// update stage status by task status
	switch runTaskStatus {
	case constants.PENDING:
		log.DebugWithCtx(ctx, "[processStageRun] ignore status, runTaskStatus: %s", runTaskStatus)
	case constants.SUCCESSFUL, constants.SKIPPED:
		if rs.IsLastTask(runTask.ID) {
			rs.Status = constants.SUCCESSFUL
		} else {
			log.DebugWithCtx(ctx, "[processStageRun] ignore not last task successful status")
		}
	default:
		// RUNNING, FAILED, CANCEL, UNHANDLED
		rs.Status = runTaskStatus
	}
	log.InfoWithCtx(ctx, "[processStageRun] update run stage to %v", *rs)
	updated, err := p.prRepo.TxUpdatePipelineRunStage(ctx, tx, rs)
	if err != nil {
		return errors.Wrapf(err, "update run stage failed")
	}
	if updated == 0 {
		log.InfoWithCtx(ctx, "[processStageRun] run stage is already updated: %#v", rs)
	}
	deliverEvent(ctx, p.sender, rs, nil)

	return nil
}

func (p *PipelineRunSvc) getRetryCiPipelineRun(ctx context.Context, pr *dao.PipelineRun, prt *dao.PipelineRunTask) (*dao.PipelineRun, error) {
	// 并行任务，重试的时候，需要重试当前并行阶段所有非成功的任务
	firstParallelStageTaskRunId := int64(0)
	if pr.IsTaskIdInParallelStage(prt.ID) {
		for _, stage := range pr.Stages {
			if stage.Type == constants.STAGE_PARALLEL.String() {
				firstParallelStageTaskRunId = stage.Tasks[0].ID
				break
			}
		}
		if firstParallelStageTaskRunId != 0 {
			retryCiPipelineRun, err := p.prRepo.GetRetryPipelineRunPreloadsFromParallelStageFirstTask(ctx, pr.ID, firstParallelStageTaskRunId)
			if err == nil {
				return retryCiPipelineRun, err
			}
		}
	} else {
		retryCiPipelineRun, err := p.prRepo.GetPipelineRunPreloadsAfterTaskId(ctx, pr.ID, prt.ID, prt.TaskId)
		if err == nil {
			return retryCiPipelineRun, err
		}
	}
	return nil, nil
}

// ReloadSubTasksWithoutTicket 重新加载子任务
func (p *PipelineRunSvc) ReloadSubTasksWithoutTicket(ctx context.Context, taskRunId int64) error {
	taskRun, err := p.prRepo.FindRunTaskById(ctx, taskRunId)
	if err != nil {
		return err
	}
	var deployBase model.AutomationDeploy
	if err = json.Unmarshal(taskRun.Config, &deployBase); err != nil {
		return errors.Wrapf(err, "ReloadSubTasksWithoutTicket unmarshal task %d automation deploy config failed", taskRunId)
	}
	if deployBase.ModifySubEnv == "true" {
		return nil
	}
	if taskRun.Type != constants.TASK_DEPLOY_SUB.String() {
		return nil
	}
	if taskRun.Status != constants.UNHANDLED {
		return nil
	}
	pr, err := p.pRepo.GetPipelineRelated(ctx, taskRun.PipelineRun.PipelineId)
	if err != nil {
		return err
	}
	// 忽略有审批的
	for _, stage := range pr.Template.Stages {
		if stage.ID == taskRun.StageRun.StageId {
			for _, task := range stage.Tasks {
				if task.Type == constants.TASK_UPGRADE_APPROVAL.String() || task.Type == constants.TASK_GRAY_UPGRADE_APPROVAL.String() || task.Type == constants.TASK_TEST_APPROVAL.String() {
					return nil
				}
			}
		}
	}
	// 计算7天前的时间
	sevenDaysAgo := time.Now().Add(-7 * 24 * time.Hour)
	prs, err := p.prRepo.FindPipelineRunsBy(ctx, model.PipelineRunSearch{Status: constants.SUCCESSFUL.String(), PipelineRunBeforeTime: &sevenDaysAgo, PipelineId: taskRun.PipelineRun.PipelineId})
	if err != nil {
		return err
	}
	pipelineRunIds := tools.MapTo(prs, func(pr dao.PipelineRun) int64 {
		return pr.ID
	})
	if len(pipelineRunIds) > 0 {
		subTasks, err := p.prRepo.FindSubTasksByMultiParams(ctx, model.SubTaskSearch{EnabledList: []int64{1}, PipelineRunIdList: pipelineRunIds, Status: constants.SUCCESSFUL.String(), Type: constants.TASK_DEPLOY_SUB.String()})
		if err != nil {
			return err
		}
		// 只找出当前模板中的task出来
		subTasks = tools.Filter(subTasks, func(subTask dao.PipelineRunSubtask) bool {
			return subTask.PipelineRunTask.TaskId == taskRun.TaskId
		})
		if len(subTasks) <= 0 {
			return nil
		}
		// 按照pipelineRun id从大到小排序
		sort.Slice(subTasks, func(i, j int) bool {
			return subTasks[i].PipelineRunID > subTasks[j].PipelineRunID
		})
		var (
			latestPipelineRunId int64
			latestSubTasks      []dao.PipelineRunSubtask
		)
		for _, subTask := range subTasks {
			if subTask.PipelineRunID != taskRun.PipelineRunId {
				if latestPipelineRunId == 0 {
					latestPipelineRunId = subTask.PipelineRunID
				}
				if latestPipelineRunId != 0 && latestPipelineRunId == subTask.PipelineRunID {
					subEnvTaskConfig := model.AutomationDeploy{}
					if err := json.Unmarshal(subTask.Config, &subEnvTaskConfig); err != nil {
						return errors.Wrap(err, "ReloadSubTasksWithoutTicket Unmarshal subTask config failed")
					}
					subEnvTaskConfig.ConfigId = 0
					configByte, err := json.Marshal(subEnvTaskConfig)
					if err != nil {
						return err
					}
					newSubTask := dao.PipelineRunSubtask{
						Status:            constants.PENDING,
						PipelineRunID:     taskRun.PipelineRunId,
						PipelineRunTaskId: taskRunId,
						Config:            configByte,
						Name:              subTask.Name,
						Type:              subTask.Type,
						Enabled:           true,
					}
					latestSubTasks = append(latestSubTasks, newSubTask)
				}
			}
		}
		// 删除数据 + 新增数据
		if len(latestSubTasks) > 0 {
			if dbErr := db.Transaction(func(tx *gorm.DB) error {
				dbCtx := db.CtxWithTX(ctx, tx)
				err = p.prRepo.DeletePipelineRunSubtaskByTaskRunId(dbCtx, taskRunId)
				if err != nil {
					return err
				}
				err = p.prRepo.BatchCreatePipelineRunSubtask(dbCtx, latestSubTasks)
				if err != nil {
					return err
				}
				// 更新加个已修改子环境的标识
				err := p.prRepo.UpdatePipelineRunTaskConfigModifySubEnv(dbCtx, taskRunId, true)
				if err != nil {
					return err
				}
				return nil
			}); dbErr != nil {
				return dbErr
			}
		}
	}
	return nil
}

func (p *PipelineRunSvc) CleanExpiredPipelineRun(ctx context.Context) (err error) {
	err = p.prRepo.ExpiredPipelineRuns(ctx, conf.AppConfig.Retry.TimeoutDays)
	if err != nil {
		log.ErrorWithCtx(ctx, "CleanExpiredPipelineRun err: %v", err)
		return err
	}
	return
}

func (p *PipelineRunSvc) List(ctx context.Context, req model.PipelineRunListReq) (objs []dao.PipelineRun, err error) {
	var total int64
	objs, err = p.prRepo.GetPipelineRunsBy(ctx, &model.PipelineRunQuery{
		PageModel:  req.PageModel,
		PipelineId: req.PipelineId,
	}, &total)

	return
}
