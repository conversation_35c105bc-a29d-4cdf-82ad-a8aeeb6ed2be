-- 服务设置
alter table `app`.`app`
    add column `eventlink_type` varchar(64) DEFAULT "" COMMENT 'eventlink类型';

-- 服务Eventlink配置
CREATE TABLE `app`.`app_event_link` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `app_id` bigint NOT NULL COMMENT '服务id',
    `app_name` varchar(128) NOT NULL COMMENT '服务名',
    `project_id` bigint NOT NULL COMMENT '项目id',
    `env` varchar(64) NOT NULL COMMENT '环境类型',
    `consumer_type` varchar(64) NOT NULL COMMENT '消费者类型,如:kafka,mix,eventlink',
    `producer_type` varchar(64) NOT NULL COMMENT '生产者类型,如:false,true',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_app` (`app_id`) USING BTREE
) ENGINE=InnoDB COMMENT='服务Eventlink配置';

-- 部署eventlink记录表
CREATE TABLE `deploy`.`event_link_apply` (
    `id`            bigint          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `env`           varchar(64)     NOT NULL COMMENT '环境类型',
    `consumer_type` varchar(64)     NOT NULL COMMENT '消费者类型,如:kafka,mix,eventlink',
    `producer_type` varchar(64)     NOT NULL COMMENT '生产者类型,如:false,true',
    `project_id`   bigint          NOT NULL COMMENT '项目id',
    `app_id`        bigint          NOT NULL COMMENT '服务id',
    `app_name`      varchar(128)    NOT NULL COMMENT '服务名',
    `is_applied`    tinyint(1)   NOT NULL DEFAULT 0 COMMENT '1:已应用 0:未应用,工单审批中',
    `ticket_id`     bigint       NOT NULL DEFAULT 0 COMMENT '工单id',
    `created_by`    varchar(128) NOT NULL COMMENT '操作人',
    `created_by_chinese_name` varchar(128) NOT NULL COMMENT '操作人中文名',
    `created_by_employee_no`  varchar(128) NOT NULL COMMENT '操作人工号',
    `created_at`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_app` (`app_id`,`is_applied`),
    KEY `idx_ticket` (`ticket_id`)
) ENGINE=InnoDB  COMMENT='部署eventlink记录表';


--  EventLink 资源部署记录
CREATE TABLE `deploy`.`event_link_cd_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  -- 关联的服务
  `app_id` bigint NOT NULL COMMENT '服务id',
  `app_name` varchar(128) NOT NULL COMMENT '服务名称',
  -- 资源数据
  `crd_type` varchar(128) NOT NULL default '' COMMENT '类型,ConsumerBinding、',
  `crd_name` varchar(128) NOT NULL default '' COMMENT '名称',
  `cluster` varchar(256)  NOT NULL COMMENT '部署集群',
  `namespace` varchar(63) NOT NULL COMMENT '部署命名空间',
  `crd_extra` json comment '资源关联的扩展信息，与类型相关',
  -- 操作数据
  `release_by` bigint NOT NULL COMMENT '部署触发人id',
  `release_by_name` varchar(128) NOT NULL COMMENT '部署触发人中文名',
  `release_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '部署时间',
  `offline_by` bigint NOT NULL COMMENT '下线触发人id',
  `offline_by_name` varchar(128) NOT NULL COMMENT '下线触发人中文名',
  `offline_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下线时间',
  -- 公共部分
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1:删除',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_crd_type` (`crd_type`) USING BTREE,
  KEY `idx_crd_name` (`crd_name`) USING BTREE,
  KEY `idx_app` (`app_id`) USING BTREE,
  KEY `idx_del` (`is_del`) USING BTREE
) ENGINE=InnoDB COMMENT='EventLink 资源部署记录';
