// Code generated by MockGen. DO NOT EDIT.
// Source: approval.go

// Package approval is a generated GoMock package.
package approval

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// Deploy mocks base method.
func (m *MockService) Deploy(arg0 context.Context, arg1 *DeployOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Deploy", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Deploy indicates an expected call of Deploy.
func (mr *MockServiceMockRecorder) Deploy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Deploy", reflect.TypeOf((*MockService)(nil).Deploy), arg0, arg1)
}

// FakeDeployPass mocks base method.
func (m *MockService) FakeDeployPass(ctx context.Context, names []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FakeDeployPass", ctx, names)
	ret0, _ := ret[0].(error)
	return ret0
}

// FakeDeployPass indicates an expected call of FakeDeployPass.
func (mr *MockServiceMockRecorder) FakeDeployPass(ctx, names interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FakeDeployPass", reflect.TypeOf((*MockService)(nil).FakeDeployPass), ctx, names)
}

// FakeDeploySkip mocks base method.
func (m *MockService) FakeDeploySkip(ctx context.Context, pipelineTektonName, currTaskName string, skipTask []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FakeDeploySkip", ctx, pipelineTektonName, currTaskName, skipTask)
	ret0, _ := ret[0].(error)
	return ret0
}

// FakeDeploySkip indicates an expected call of FakeDeploySkip.
func (mr *MockServiceMockRecorder) FakeDeploySkip(ctx, pipelineTektonName, currTaskName, skipTask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FakeDeploySkip", reflect.TypeOf((*MockService)(nil).FakeDeploySkip), ctx, pipelineTektonName, currTaskName, skipTask)
}

// InvokeVirtualTask mocks base method.
func (m *MockService) InvokeVirtualTask(ctx context.Context, label string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InvokeVirtualTask", ctx, label)
	ret0, _ := ret[0].(error)
	return ret0
}

// InvokeVirtualTask indicates an expected call of InvokeVirtualTask.
func (mr *MockServiceMockRecorder) InvokeVirtualTask(ctx, label interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvokeVirtualTask", reflect.TypeOf((*MockService)(nil).InvokeVirtualTask), ctx, label)
}

// SimpleDeploy mocks base method.
func (m *MockService) SimpleDeploy(arg0 context.Context, arg1 *DeployOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleDeploy", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SimpleDeploy indicates an expected call of SimpleDeploy.
func (mr *MockServiceMockRecorder) SimpleDeploy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleDeploy", reflect.TypeOf((*MockService)(nil).SimpleDeploy), arg0, arg1)
}

// TicketApprovedCallback mocks base method.
func (m *MockService) TicketApprovedCallback(ctx context.Context, tektonNameList []string, deployConfigs []DeployConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TicketApprovedCallback", ctx, tektonNameList, deployConfigs)
	ret0, _ := ret[0].(error)
	return ret0
}

// TicketApprovedCallback indicates an expected call of TicketApprovedCallback.
func (mr *MockServiceMockRecorder) TicketApprovedCallback(ctx, tektonNameList, deployConfigs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TicketApprovedCallback", reflect.TypeOf((*MockService)(nil).TicketApprovedCallback), ctx, tektonNameList, deployConfigs)
}
