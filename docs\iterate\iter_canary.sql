-- 项目添加规范金丝雀部署提醒状态字段
alter table `app`.`project`
    add column `canary_deploy_notify_status` varchar(64) default "disabled" comment '规范金丝雀部署提醒，enabled：开启，disabled：关闭';

alter table `app`.`app`
    add column `senv_status` varchar(64) default "" comment '是否支持子环境2.0，enabled：支持，disabled：不支持';

-- 新增项目额外信息字段
alter table `app`.`project`
    add column `extra` json DEFAULT NULL COMMENT '用于额外json信息存储，包罗万象';