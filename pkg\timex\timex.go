package timex

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"52tt.com/cicd/pkg/log"
)

type TimeFunc func() time.Time

var timeFunc TimeFunc = time.Now

func DefaultTimeFunc() time.Time {
	return timeFunc()
}

func SetTimeFunc(fn TimeFunc) {
	timeFunc = fn
}

// MockTimeFunc is used for testing. It can be used to mock the time.
func MockTimeFunc(t time.Time) {
	timeFunc = func() time.Time {
		return t
	}
}

func Format(t time.Time) string {
	if t.<PERSON><PERSON>ero() {
		return ""
	}

	t = toCST(t)
	weekday := t.Weekday().String()
	weekdayChinese := getChineseWeekday(weekday)
	timeZone := strings.TrimSpace(t.Format(" MST"))

	return fmt.Sprintf("%s %s %s", t.Format("2006-01-02 15:04:05"), weekdayChinese, timeZone)
}

func toCST(t time.Time) time.Time {
	// 加载中国时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Errorf("无法加载时区信息, err: %v", err)
		return t
	}
	// 设置为中国标准时间
	return t.In(loc)
}

func getChineseWeekday(weekday string) string {
	switch weekday {
	case "Monday":
		return "星期一"
	case "Tuesday":
		return "星期二"
	case "Wednesday":
		return "星期三"
	case "Thursday":
		return "星期四"
	case "Friday":
		return "星期五"
	case "Saturday":
		return "星期六"
	case "Sunday":
		return "星期日"
	default:
		return ""
	}
}

func ToYearMonth(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return toCST(t).Format("2006-01")
}

func ToPointer(t time.Time) *time.Time {
	if t.IsZero() {
		return nil
	}
	return &t
}

func ElapsedTime(start, end time.Time) time.Duration {
	if !end.IsZero() {
		return end.Sub(start)
	}
	return timeFunc().Sub(start)
}

type EmptyTime time.Time

func (ct *EmptyTime) UnmarshalJSON(data []byte) error {
	// 这里可以自定义反序列化逻辑，如果需要的话
	return nil
}

func (ct EmptyTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	if t.IsZero() {
		// 如果是零值时间，返回空字符串
		return []byte(`""`), nil
	}
	return json.Marshal(t)
}
