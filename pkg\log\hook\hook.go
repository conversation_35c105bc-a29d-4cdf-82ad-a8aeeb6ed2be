package hook

import (
	"context"
	"strconv"

	"go.uber.org/zap"

	cctx "52tt.com/cicd/pkg/context"
	"52tt.com/cicd/pkg/log/core"
)

func ExtraFieldFromContext(logger core.LoggerFormat, lv core.Level, ctx context.Context, format string, args []any, next core.Handle) {
	if ctx != nil {
		info, _ := cctx.RequestInfoFromCtx(ctx)
		mark := cctx.TrafficMark(ctx)
		if sugaredLogger, ok := logger.(*zap.SugaredLogger); ok {
			if info.UserID != 0 {
				sugaredLogger = sugaredLogger.With(zap.String("uid", strconv.Itoa(int(info.UserID))))
			}
			if info.ProjectID != 0 {
				sugaredLogger = sugaredLogger.With(zap.String("pid", strconv.Itoa(int(info.ProjectID))))
			}
			if info.ChineseName != "" {
				sugaredLogger = sugaredLogger.With(zap.String("cname", info.ChineseName))
			}
			if info.RequestID != "" {
				sugaredLogger = sugaredLogger.With(zap.String("reqid", info.RequestID))
			}
			if mark != "" {
				sugaredLogger = sugaredLogger.With(zap.String("tfcMark", mark))
			}

			logger = sugaredLogger
		}
	}
	next(logger, lv, ctx, format, args...)
}
