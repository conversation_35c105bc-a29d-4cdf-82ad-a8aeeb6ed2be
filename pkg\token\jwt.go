package token

import (
	"crypto/rsa"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

type CustomClaims struct {
	jwt.RegisteredClaims
	*PrivateClaims
}

func newStandardClaims(issuer string, timeFunc func() time.Time, expire time.Duration) jwt.RegisteredClaims {
	return jwt.RegisteredClaims{
		Issuer:    issuer,                                     // 签发者
		IssuedAt:  jwt.NewNumericDate(timeFunc()),             // 签发时间
		ExpiresAt: jwt.NewNumericDate(timeFunc().Add(expire)), // 失效时间
	}
}

// NewJWTTokenRSA Create a new JWTTokenRSA. Takes an issuer and key
func NewJWTTokenRSA(issuer string, privateKey *rsa.PrivateKey) *JWTTokenRSA {
	return &JWTTokenRSA{
		Issuer:     issuer,
		timeFunc:   time.Now,
		privateKey: privateKey,
	}
}

// JWTTokenRSA implements GenerateToken method.
type JWTTokenRSA struct {
	Issuer     string
	timeFunc   func() time.Time
	privateKey *rsa.PrivateKey
}

func (g *JWTTokenRSA) GenerateToken(claims *PrivateClaims, expire time.Duration) (string, error) {
	customClaims := &CustomClaims{
		RegisteredClaims: newStandardClaims(g.Issuer, g.timeFunc, expire),
		PrivateClaims:    claims,
	}

	return generateToken(jwt.SigningMethodRS512, customClaims, g.privateKey)
}

func generateToken(method jwt.SigningMethod, claims jwt.Claims, key interface{}) (string, error) {
	tkn := jwt.NewWithClaims(method, claims)
	return tkn.SignedString(key)
}

func NewJWTTokenRSAVerifier(publicKey *rsa.PublicKey) *JWTTokenRSAVerifier {
	return &JWTTokenRSAVerifier{
		PublicKey: publicKey,
	}
}

// JWTTokenRSAVerifier verifies access tokens.
type JWTTokenRSAVerifier struct {
	PublicKey *rsa.PublicKey
}

// Verify implements the Verify method from Verifier.
func (v *JWTTokenRSAVerifier) Verify(token string) (*PrivateClaims, error) {
	chm, err := verify(token, func(token *jwt.Token) (interface{}, error) {
		return v.PublicKey, nil
	})

	if err != nil {
		return nil, err
	}
	return chm.PrivateClaims, nil
}

func verify(token string, keyfunc jwt.Keyfunc) (*CustomClaims, error) {
	tkn, err := jwt.ParseWithClaims(token, &CustomClaims{}, keyfunc)

	if err != nil {
		return nil, fmt.Errorf("cannot parse token: %v", err)
	}

	if !tkn.Valid {
		return nil, fmt.Errorf("token not vaild")
	}

	clm, ok := tkn.Claims.(*CustomClaims)
	if !ok {
		return nil, fmt.Errorf("token claim is not CustomClaims")
	}

	if err := clm.Valid(); err != nil {
		return nil, fmt.Errorf("claim not valid: %v", err)
	}

	return clm, nil
}

func NewJTWTokenHMAC(issuer string, key []byte) *JWTTokenHMAC {
	return &JWTTokenHMAC{
		Issuer:   issuer,
		timeFunc: time.Now,
		key:      key,
	}
}

type JWTTokenHMAC struct {
	Issuer   string
	timeFunc func() time.Time
	key      []byte
}

func (g *JWTTokenHMAC) GenerateToken(claims *PrivateClaims, expire time.Duration) (string, error) {
	customClaims := &CustomClaims{
		RegisteredClaims: newStandardClaims(g.Issuer, g.timeFunc, expire),
		PrivateClaims:    claims,
	}

	return generateToken(jwt.SigningMethodHS512, customClaims, g.key)
}

func (g *JWTTokenHMAC) Verify(token string) (*PrivateClaims, error) {
	chm, err := verify(token, func(token *jwt.Token) (interface{}, error) {
		return g.key, nil
	})

	if err != nil {
		return nil, err
	}
	return chm.PrivateClaims, nil
}
