package constants

type DebugStatus int8

func (s DebugStatus) Value() int8 {
	return int8(s)
}

func (s DebugStatus) String() string {
	switch s {
	case DebugStatusDeploying:
		return "DEBUG_STATUS_DEPLOYING"
	case DebugStatusSuccessful:
		return "DEBUG_STATUS_SUCCESSFUL"
	case DebugStatusFailed:
		return "DEBUG_STATUS_FAILED"
	default:
		return "DEBUG_STATUS_PENDING"
	}
}

const (
	DebugStatusPending    DebugStatus = 0 // 未开始
	DebugStatusDeploying  DebugStatus = 1 // 部署中
	DebugStatusSuccessful DebugStatus = 2 // 成功
	DebugStatusFailed     DebugStatus = 3 // 失败
)

var (
	DebugStatus_Value = map[string]DebugStatus{
		"DEBUG_STATUS_PENDING":    DebugStatusPending,
		"DEBUG_STATUS_DEPLOYING":  DebugStatusDeploying,
		"DEBUG_STATUS_SUCCESSFUL": DebugStatusSuccessful,
		"DEBUG_STATUS_FAILED":     DebugStatusFailed,
	}

	DebugStatus_Name = map[DebugStatus]string{
		DebugStatusPending:    "DEBUG_STATUS_PENDING",
		DebugStatusDeploying:  "DEBUG_STATUS_DEPLOYING",
		DebugStatusSuccessful: "DEBUG_STATUS_SUCCESSFUL",
		DebugStatusFailed:     "DEBUG_STATUS_FAILED",
	}
)
