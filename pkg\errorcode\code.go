package errorcode

// Project related error code
const (
	ProjectNameExisted = 10000
	ProjectNotExisted  = 10001
	UserNotExisted     = 10004
	UserBindResource   = 10005
	ProjectUserExisted = 10006
)

// App related error code
const (
	AppNameExisted  = 12000
	AppCheckError   = 12001
	AppBizModExists = 12002
	AppNotExisted   = 12003
	AppNumError     = 12004
)

const (
	ImageIdentityNameDuplicated = 12200
	ImageAddressInvalid         = 12201
)

// ApprovalFlowNotExisted ApprovalFlow related error code
const (
	ApprovalFlowNotExisted = 13000
)

// Template related error code
const (
	TemplateNotExisted                   = 14000
	TemplateNameExisted                  = 14001
	TemplatePreCheckFailed               = 14002
	TemplateUpdateFailed                 = 14003
	TemplateImpactOnPipes                = 14004
	TemplateUpdatePermissionLost         = 14005
	TemplateStageNotExisted              = 14006
	TemplateTaskNotExisted               = 14007
	TemplateDeletePermissionUnAuthorized = 14008
)

const (
	PipelineNotExisted             = 16000
	PipelineIsExistedInApp         = 16002
	PipelineAppNotExisted          = 16003
	PipelineAppBranchNotExisted    = 16004
	PipelineRunRecordNotExisted    = 16005
	PipelineRunTaskNotExisted      = 16006
	PipelineAppLanguageNotPatch    = 16007
	PipelineBranchNotExisted       = 16008
	PipelineRunNotExisted          = 16009
	PipelineCanNotCancel           = 16010
	PipelineRunTaskTypeError       = 16011
	PipelineRetryStatus            = 16012
	PipelineRetryTaskType          = 16013
	PipelineRetryStartedTime       = 16014
	PipelineRetryTicketRelated     = 16015
	PipelineRetryIsRunning         = 16016
	PipelineDeployTektonNotRunning = 16017
	PipelineSubTaskIsNotRunning    = 16018
	PipelineRunTaskStatusInvalid   = 16019
	PipelineMustRunAfterLast       = 16020
)

const (
	ServiceEnvAlreadyHasConfig = 17010
	ServiceEnvAlreadyHasTicket = 17011
)

const (
	PipelineGroupNameIsExisted        = 18000
	PipelineQualityGroupAppDuplicated = 18001
	PipelineGroupNotExisted           = 18002
	PipelineQualityGroupHasExisted    = 18003
	PipelineQualityGroupNameLength    = 18004
	PipelineQualityGroupNotFound      = 18005
)

// DeployConfigNotExisted Deploy related error code
const (
	DeployConfigNotExisted    = 19001
	ChangeLogCannotBeRollback = 19002
	// DeployChangeLogNotFound deploy change log not found
	DeployChangeLogNotFound = 19003
)

const (
	ApprovalFlowNameExisted       = 11000
	OnlineTypeApprovalFlowExisted = 11001
	TicketRelatedAppIsExisted     = 20000
	NoApprovalPermission          = 20001
	FlowInstNotExists             = 20002
	ExceptionFlowStatus           = 20003
	UnexpectedFlowStatus          = 20003
	TicketIsRunning               = 20004
	TicketApproversInValid        = 20005
	TicketCheckersInvalid         = 20006
	UnreadyResource               = 20007
)

// Deploy Config Template related error code
const (
	DeployConfigTemplateNameExisted = 21000
	DeployConfigTemplateNotExisted  = 21001
)

const (
	DuplicateDyeingRuleValues = 22000
	UnableToUseDyeingConfig   = 22001
)

const (
	TrafficMarkNameExisted    = 23000
	TrafficMarkNotFound       = 23001
	TrafficMarkHasBoundSubEnv = 23002
)

const (
	DeployConfigTemplateNotExists = 30001
)

const (
	DeploymentNotExisted = 31000
	K8sYamlRenderFailed  = 31001
)

const (
	SubEnvNotExists           = 24000
	SubEnvIsInvalid           = 24001
	SubEnvHasExists           = 24002
	CanaryShiftFailed         = 24003
	SubEnvHasBoundTrafficMark = 24004
	CanaryPercentageError     = 24005
)

const (
	ChSetRelatedPipelineNotAllRelease    = 25000
	ChSetRelatedPipelineNoSameTemplate   = 25001
	ChSetRelatedPipelineNoSameDeployCfg  = 25002
	ChSetRelatedPipelineNoSameAutoTest   = 25003
	ChSetRelatedPipelineJoinedOtherChSet = 25004
	ChSetNotExist                        = 25005
	ChSetFinished                        = 25006
	ReleasePipelineNotFound              = 25007
	ReleasePipelineTemplateNoSame        = 25008
	ReleasePipelineHasBoundChanSet       = 25009
	ChSetExists                          = 25010
	ChSetCantNotCancel                   = 25011
)

// 给发布计划用
const (
	DeployPlanExists                                = 26000
	DeployPlanVersionExists                         = 26001
	DeployPlanAbsoluteTimeError                     = 26002
	DeployPlanRelativeTimeError                     = 26003
	DeployPlanTrafficPercentageError                = 26004
	DeployPlanActionOrderError                      = 26005
	DeployPlanHasRelatedRecords                     = 26006
	DeployPlanHasDeleted                            = 26007
	DeployPlanHasExpired                            = 26008
	DeployPlanTrafficPercentageLessThanInitialError = 26009
)

const (
	RedisSetNxTimeout = 40000
)

// 2700x for jetdev
const (
	JetdevUserConfigNotExisted = 27000
	JetdevClusterNotExisted    = 27001
	JetdevAppNotFound          = 27002
	JetdevStatusParamInvalid   = 27003
)

const (
	GatewayNameExisted                = 28000
	GatewayNotExisted                 = 28001
	RouteCreateFailed                 = 28002
	RouteCorsPolicyValidateFailed     = 28003
	RouteApplyRouteGenerationNotMatch = 28004
	RouteNotExisted                   = 28005
)

const (
	RunningLogNotFound = 29000
	ErrEventLink       = 29001
)

const (
	ConfigNameExisted       = 30000
	ConfigNotFound          = 30001
	ConfigVersionNotFound   = 30002
	ConfigAppAlreadyUnbind  = 30003
	ConfigEnvNotExisted     = 30004
	ConfigNameSuffixInvalid = 30005
)

const (
	ProjectMigrateTaskNotExisted = 31000
)
