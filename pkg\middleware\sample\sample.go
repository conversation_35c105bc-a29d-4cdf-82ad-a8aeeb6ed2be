package sample

import (
	"52tt.com/cicd/pkg/log"
	"52tt.com/cicd/pkg/token"
	"github.com/gin-gonic/gin"
	"net/http"
)

const (
	uidKey      = "uid"
	usernameKey = "username"
)

var hmacGen = token.NewJTWTokenHMAC("cicd-v2", []byte("test"))

func EasyContext() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == http.MethodOptions {
			return
		}

		tkn := c.<PERSON>("Authorization")
		claims, err := hmacGen.Verify(tkn)
		if err != nil {
			log.Errorf("EasyContext解析token异常: %v\n", err)
			c.Next()
			return
		}

		c.Set(uid<PERSON><PERSON>, claims.Uid)
		c.Set(username<PERSON>ey, claims.Username)
		c.Next()
	}
}

func GetUid(c *gin.Context) int64 {
	return c.GetInt64(uid<PERSON><PERSON>)
}

func GetUsername(c *gin.Context) string {
	return c.GetString(usernameKey)
}
