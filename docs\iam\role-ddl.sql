CREATE TABLE `role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `descr` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色信息表';

INSERT INTO `iam`.`role`(`id`, `name`, `descr`) VALUES (1, 'DEVELOPER', '开发人员');
INSERT INTO `iam`.`role`(`id`, `name`, `descr`) VALUES (2, 'QA', '测试人员');
INSERT INTO `iam`.`role`(`id`, `name`, `descr`) VALUES (3, 'OPS', '运维人员');
INSERT INTO `iam`.`role`(`id`, `name`, `descr`) VALUES (4, 'DEV_ADMIN', '研发管理者');
INSERT INTO `iam`.`role`(`id`, `name`, `descr`) VALUES (5, 'ADMIN', '系统管理员');