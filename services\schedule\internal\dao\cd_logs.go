package dao

import (
	"context"

	models "52tt.com/cicd/services/schedule/internal/model"
)

type deployRunRepo struct {
}

func NewDeployRunRepo() *deployRunRepo {
	return &deployRunRepo{}
}

func (r *deployRunRepo) FindChangeLogs(ctx context.Context, parm models.ListChangeLogReq) (objs []models.DeployChangeLog, err error) {
	objs = make([]models.DeployChangeLog, 0)
	tx := DeployDb.Model(&models.DeployChangeLog{}).Order("id desc")

	// 现有的筛选条件
	if parm.IsCurrent != nil {
		tx = tx.Where("is_current = ?", *parm.IsCurrent)
	}
	if parm.EnvTarget != 0 {
		tx = tx.Where("env_target = ?", parm.EnvTarget)
	}
	if parm.Env != 0 {
		tx = tx.Where("env = ?", parm.Env)
	}
	if parm.AppId > 0 {
		tx = tx.Where("app_id = ?", parm.AppId)
	}
	if parm.ProjectId > 0 {
		tx = tx.Where("project_id = ?", parm.ProjectId)
	}
	if parm.Action != 0 {
		tx = tx.Where("action = ?", parm.Action)
	}
	if !parm.OperatedBefore.IsZero() {
		tx = tx.Where("operated_at < ?", parm.OperatedBefore)
	}

	err = tx.Find(&objs).Error
	return
}
