package vec

import (
	"reflect"
	"testing"
)

// unit test for HasSameElement
func TestHasSameElement(t *testing.T) {
	a := []int{1, 7}
	b := []int{7}

	has := HasSameElement(a, b)
	if has {
		t.<PERSON><PERSON><PERSON>("HasSameElement(%v, %v) = %v; want false", a, b, has)
		return
	}
	t.Logf("HasSameElement(%v, %v) = %v", a, b, has)
}

func TestRemoveDuplicateInt(t *testing.T) {
	slices := []int{1, 2, 2, 3, 3}
	expectedResult := []int{1, 2, 3}

	result := RemoveDuplicate(slices)

	if !reflect.DeepEqual(result, expectedResult) {
		t.<PERSON>("Expected %v, but got %v", expectedResult, result)
	}
}

func TestRemoveDuplicateString(t *testing.T) {
	slices := []string{"apple", "banana", "banana", "cherry", "cherry"}
	expectedResult := []string{"apple", "banana", "cherry"}

	result := RemoveDuplicate(slices)

	if !reflect.DeepEqual(result, expectedResult) {
		t.<PERSON><PERSON>("Expected %v, but got %v", expectedResult, result)
	}
}
