package server

import (
	"context"
	"fmt"
	"time"

	"52tt.com/cicd/pkg/config"
	"52tt.com/cicd/pkg/engine"
	"52tt.com/cicd/pkg/log"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/penglongli/gin-metrics/ginmetrics"
)

type Server struct {
	engine *engine.LocalEngine
	Rpc    *grpc.Server
	c      config.Configuration
	inst   *kratos.App
}

func (svr *Server) Run() error {
	if err := svr.inst.Run(); err != nil {
		return err
	}
	return nil
}

func loggingMiddleware(next middleware.Handler) middleware.Handler {
	return func(ctx context.Context, req interface{}) (interface{}, error) {
		tr, ok := transport.FromServerContext(ctx)
		request := ""
		if ok {
			request = tr.Operation()
			log.InfoWithCtx(ctx, "gRPC received Request: %+v - Header: %+v - From: %+v", request, tr.RequestHeader(), tr.Endpoint())
		}
		reply, err := next(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "gRPC  Request: %+v , error: %v", request, err)
		}
		log.InfoWithCtx(ctx, "gRPC  Request: %+v , end ... ", request)
		return reply, err
	}
}

func NewServer(engine *engine.LocalEngine, c config.Configuration) *Server {
	//运行
	appConfig := c.GetAppConfig()

	httpPort := fmt.Sprintf(":%d", appConfig.Http.Port)
	//log
	log.Debugf("start http server with port %s", httpPort)

	httpSrv := http.NewServer(
		http.Address(httpPort),
		http.Timeout(parseTimeout(appConfig.Http.Timeout, time.Second)),
	)
	httpSrv.HandlePrefix("/", engine.Engine)

	rpcPort := fmt.Sprintf(":%d", appConfig.Rpc.Port)

	// server
	grpcSrv := grpc.NewServer(
		grpc.Address(rpcPort),
		grpc.Timeout(parseTimeout(appConfig.Rpc.Timeout, time.Second)),
		grpc.UnaryInterceptor(UnaryServerInterceptor),
		grpc.Middleware(loggingMiddleware),
	)

	ser := kratos.New(
		kratos.Version(appConfig.Version),
		kratos.Name(appConfig.Name),
		kratos.Server(
			httpSrv,
			grpcSrv,
		),
	)

	svr := &Server{
		engine: engine,
		Rpc:    grpcSrv,
		c:      c,
		inst:   ser,
	}
	return svr
}

func parseTimeout(timeout string, d time.Duration) time.Duration {
	timeoutDuration := d
	if timeout != "" {
		parsed, err := time.ParseDuration(timeout)
		if err != nil {
			panic(err)
		}
		timeoutDuration = parsed
	}
	return timeoutDuration
}

func MetricsHandler(engine *engine.LocalEngine) {
	m := ginmetrics.GetMonitor()

	// +optional set metric path, default /debug/metrics
	m.SetMetricPath("/metrics")
	// +optional set slow time, default 5s
	m.SetSlowTime(10)
	// +optional set request duration, default {0.1, 0.3, 1.2, 5, 10}
	// used to p95, p99
	m.SetDuration([]float64{0.1, 0.3, 1.2, 5, 10})

	// set middleware for gin
	m.Use(engine.Engine)
}
