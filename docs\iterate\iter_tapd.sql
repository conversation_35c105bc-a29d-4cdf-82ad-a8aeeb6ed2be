-- 项目设置
alter table `app`.`project`
    add column `tapd_setting` json DEFAULT NULL COMMENT 'tapd关联设置';


alter table `deploy`.`ticket`
    add column `ticket_story_id` bigint NOT NULL COMMENT '工单需求计划id';

CREATE TABLE `deploy`.`ticket_story` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  -- 工单计划数据
  `project_id` bigint NOT NULL COMMENT '项目id',
  `app_id` bigint NOT NULL COMMENT '服务id',
  `plan_name` varchar(256)  NOT NULL default '' COMMENT '名称',
  `story_run_cfg` json comment '需求与流水线运行配置',
  -- 公共部分
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_app` (`app_id`) USING BTREE
) ENGINE=InnoDB COMMENT='工单与需求关联';


-- 需求和流水线运行关联
CREATE TABLE `deploy`.`story_run` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  -- 来源数据
  `ticket_story_id` bigint NOT NULL COMMENT '需求关联id',
  -- 需求数据
  `story_id` varchar(64) NOT NULL default '' COMMENT '需求id',
  `iteration_id` varchar(32) NOT NULL default '' COMMENT '迭代id',
  `workspace_id` varchar(32) NOT NULL default '' COMMENT 'Tapd项目id',
  `story_name` varchar(256) NOT NULL default '' COMMENT '需求标题',
   -- 关联的服务
  `project_id` bigint NOT NULL COMMENT '项目id',
  `app_id` bigint NOT NULL COMMENT '服务id',
  `app_name` varchar(128) NOT NULL COMMENT '服务名称',
  `task_run_id`       bigint      NOT NULL COMMENT '任务运行id,后续任务重试时会变',
  `pipeline_id`       bigint          NOT NULL COMMENT '流水线id',
  `pipeline_run_number` bigint NOT NULL COMMENT '流水线运行记录号',
  `trigger_by` bigint NOT NULL COMMENT 'pipeling运行触发人id',
  `trigger_by_chinese_name` varchar(128) NOT NULL COMMENT 'Pipeline运行触发人中文名',
  `pipeline_run_step`   bigint NOT NULL COMMENT '流水线已经运行成功的步骤点(提测、升级工单、预发布等)',
  -- 公共部分
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1:删除',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_story` (`story_id`) USING BTREE,
  KEY `idx_workspace` (`workspace_id`,`iteration_id`) USING BTREE,
  KEY `idx_app` (`app_id`) USING BTREE,
  KEY `idx_del` (`is_del`) USING BTREE,
  KEY `idx_pipe_run` (`pipeline_id`,`pipeline_run_number`) USING BTREE
) ENGINE=InnoDB COMMENT='需求与流水线运行关系表';

-- 需求发送记录
CREATE TABLE `deploy`.`story_notify_record` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `story_id` varchar(1024) NOT NULL default '' COMMENT '需求id',
    `workspace_id` varchar(32) NOT NULL default '' COMMENT 'Tapd项目id',
    `story_name` varchar(256) NOT NULL default '' COMMENT '需求标题',
    `notify_interval` int NOT NULL default 0 COMMENT '通知间隔时间',
    `notify_unit` varchar(32) NOT NULL default '' COMMENT '通知间隔单位,M:分钟,H:小时',
    `notify_limit` int NOT NULL default 0 COMMENT '通知限制次数',
    `notify_count` int NOT NULL default 0 COMMENT '已经通知次数',
    `next_notify_time` timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '下次通知时间',
    `notify_status` tinyint(1) NOT NULL default 0 COMMENT '通知状态,0:未通知,1:通知中,2:通知结束',
    `trigger_pipeline_id` bigint NOT NULL COMMENT '触发通知的流水线id',
    `trigger_pipeline_run_number` bigint NOT NULL COMMENT '触发通知的流水线运行记录号',
    `trigger_ticket_id` bigint NOT NULL COMMENT '触发通知的工单id',
    `trigger_app_id` bigint NOT NULL COMMENT '触发通知的服务id',
    `trigger_app_name` varchar(128) NOT NULL COMMENT '触发通知的服务名称',
    `trigger_by` bigint NOT NULL COMMENT '触发通知的人id',
    `trigger_by_chinese_name` varchar(128) NOT NULL COMMENT '触发通知的人中文名',
    `remark` varchar(1024) NOT NULL DEFAULT '' COMMENT '备注',
    `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已删除',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_pipeline_run` (`trigger_pipeline_id`,`trigger_pipeline_run_number`) USING BTREE,
    KEY `idx_ticket` (`trigger_ticket_id`) USING BTREE,
    KEY `idx_app` (`trigger_app_id`) USING BTREE,
    KEY `idx_notify_status` (`notify_status`) USING BTREE
) ENGINE=InnoDB COMMENT='需求通知记录表';





