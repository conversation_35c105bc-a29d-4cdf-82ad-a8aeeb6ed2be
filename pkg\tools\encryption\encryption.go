package encryption

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
)

const KEY = "BywgEXIYVd5fKWnv1w1SFRfsmT7kF3Kq" //加密密钥

func Encrypt(plaintext []byte) (string, error) {
	block, err := aes.NewCipher([]byte(KEY))
	if err != nil {
		return "", err
	}

	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	stream := cipher.NewCTR(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], plaintext)

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func Decrypt(ciphertext string) (string, error) {
	ct, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("decrypt error: %s", err)
	}

	block, err := aes.NewCipher([]byte(KEY))
	if err != nil {
		return "", err
	}

	if len(ct) < aes.BlockSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	iv := ct[:aes.BlockSize]
	ct = ct[aes.BlockSize:]

	stream := cipher.NewCTR(block, iv)
	stream.XORKeyStream(ct, ct)

	return string(ct), nil
}
