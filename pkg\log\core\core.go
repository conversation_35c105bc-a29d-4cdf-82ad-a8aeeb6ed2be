package core

import (
	"context"
)

type LoggerFormat interface {
	Debugf(format string, args ...interface{})
	Infof(format string, args ...interface{})
	Warnf(format string, args ...interface{})
	Errorf(format string, args ...interface{})
	Panicf(format string, args ...interface{})
	Fatalf(format string, args ...interface{})
}

type Logger interface {
	DebugWithCtx(ctx context.Context, format string, args ...any)
	InfoWithCtx(ctx context.Context, format string, args ...any)
	WarnWithCtx(ctx context.Context, format string, args ...any)
	ErrorWithCtx(ctx context.Context, format string, args ...any)
	PanicWithCtx(ctx context.Context, format string, args ...any)
	FatalWithCtx(ctx context.Context, format string, args ...any)
}

type Handle func(logger LoggerFormat, lv Level, ctx context.Context, format string, args ...any)
type Hook func(logger LoggerFormat, lv Level, ctx context.Context, format string, args []any, next Handle)

type HookManager interface {
	RegisterHook(hooks ...Hook)
	Hooks() []Hook
}

type LoggerManager interface {
	CurrentHookManager() HookManager
	CurrentLevelManager() LevelManager
	CurrentLogger() Logger
}
