// Code generated by MockGen. DO NOT EDIT.
// Source: cmdb.go

// Package cmdb is a generated GoMock package.
package cmdb

import (
	context "context"
	http "net/http"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// BatchUpdateApp mocks base method.
func (m *MockService) BatchUpdateApp(ctx context.Context, apps *BatchUpdateCmdbApp) (*BatchUpdateCmdbResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateApp", ctx, apps)
	ret0, _ := ret[0].(*BatchUpdateCmdbResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateApp indicates an expected call of BatchUpdateApp.
func (mr *MockServiceMockRecorder) BatchUpdateApp(ctx, apps interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateApp", reflect.TypeOf((*MockService)(nil).BatchUpdateApp), ctx, apps)
}

// CheckAppCanDel mocks base method.
func (m *MockService) CheckAppCanDel(ctx context.Context, uid string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAppCanDel", ctx, uid)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAppCanDel indicates an expected call of CheckAppCanDel.
func (mr *MockServiceMockRecorder) CheckAppCanDel(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAppCanDel", reflect.TypeOf((*MockService)(nil).CheckAppCanDel), ctx, uid)
}

// Common mocks base method.
func (m *MockService) Common(ctx context.Context, httpRequest *http.Request, url string) (any, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Common", ctx, httpRequest, url)
	ret0, _ := ret[0].(any)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Common indicates an expected call of Common.
func (mr *MockServiceMockRecorder) Common(ctx, httpRequest, url interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Common", reflect.TypeOf((*MockService)(nil).Common), ctx, httpRequest, url)
}

// CreateApp mocks base method.
func (m *MockService) CreateApp(arg0 context.Context, arg1 *App) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateApp", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateApp indicates an expected call of CreateApp.
func (mr *MockServiceMockRecorder) CreateApp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateApp", reflect.TypeOf((*MockService)(nil).CreateApp), arg0, arg1)
}

// DelApp mocks base method.
func (m *MockService) DelApp(ctx context.Context, uid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelApp", ctx, uid)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelApp indicates an expected call of DelApp.
func (mr *MockServiceMockRecorder) DelApp(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelApp", reflect.TypeOf((*MockService)(nil).DelApp), ctx, uid)
}

// GetAlias mocks base method.
func (m *MockService) GetAlias(arg0 context.Context, arg1 *App) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAlias", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAlias indicates an expected call of GetAlias.
func (mr *MockServiceMockRecorder) GetAlias(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAlias", reflect.TypeOf((*MockService)(nil).GetAlias), arg0, arg1)
}

// GetApps mocks base method.
func (m *MockService) GetApps(arg0 context.Context, arg1 []string) ([]CmdbApp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApps", arg0, arg1)
	ret0, _ := ret[0].([]CmdbApp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApps indicates an expected call of GetApps.
func (mr *MockServiceMockRecorder) GetApps(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApps", reflect.TypeOf((*MockService)(nil).GetApps), arg0, arg1)
}

// GetModelList mocks base method.
func (m *MockService) GetModelList(ctx context.Context, modelCode string, params map[string]string) ([]map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetModelList", ctx, modelCode, params)
	ret0, _ := ret[0].([]map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetModelList indicates an expected call of GetModelList.
func (mr *MockServiceMockRecorder) GetModelList(ctx, modelCode, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetModelList", reflect.TypeOf((*MockService)(nil).GetModelList), ctx, modelCode, params)
}

// GetModulesSubTree mocks base method.
func (m *MockService) GetModulesSubTree(arg0 context.Context, arg1 string) (*CmdbModelTreeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetModulesSubTree", arg0, arg1)
	ret0, _ := ret[0].(*CmdbModelTreeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetModulesSubTree indicates an expected call of GetModulesSubTree.
func (mr *MockServiceMockRecorder) GetModulesSubTree(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetModulesSubTree", reflect.TypeOf((*MockService)(nil).GetModulesSubTree), arg0, arg1)
}

// GetModulesTree mocks base method.
func (m *MockService) GetModulesTree(arg0 context.Context) (*CmdbModelTreeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetModulesTree", arg0)
	ret0, _ := ret[0].(*CmdbModelTreeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetModulesTree indicates an expected call of GetModulesTree.
func (mr *MockServiceMockRecorder) GetModulesTree(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetModulesTree", reflect.TypeOf((*MockService)(nil).GetModulesTree), arg0)
}

// UpdateApp mocks base method.
func (m *MockService) UpdateApp(arg0 context.Context, arg1 *App) (*Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateApp", arg0, arg1)
	ret0, _ := ret[0].(*Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateApp indicates an expected call of UpdateApp.
func (mr *MockServiceMockRecorder) UpdateApp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApp", reflect.TypeOf((*MockService)(nil).UpdateApp), arg0, arg1)
}
