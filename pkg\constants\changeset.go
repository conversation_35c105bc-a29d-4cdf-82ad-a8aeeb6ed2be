package constants

type changeSetStreamName string

const (
	ChangesetList         changeSetStreamName = "CHANGESET_LIST"
	ChangesetDetail       changeSetStreamName = "CHANGESET_DETAIL"
	ChangesetPipelineList changeSetStreamName = "CHANGESET_PIPELINE_LIST"
)

func (name changeSetStreamName) String() string {
	return string(name)
}

type changeSetButtonName string

const (
	DEPLOY     changeSetButtonName = "DEPLOY"     // 部署
	APPROVAL   changeSetButtonName = "APPROVAL"   // 提测
	REDEPLOY   changeSetButtonName = "REDEPLOY"   // 重新部署
	REAPPROVAL changeSetButtonName = "REAPPROVAL" // 重新提测
	RUN        changeSetButtonName = "RUN"        // 继续运行
	END        changeSetButtonName = "END"        // 终止
)

func (name changeSetButtonName) String() string {
	return string(name)
}

type changeSetButtonStatus string

const (
	ALLSUCCESS changeSetButtonStatus = "ALLSUCCESS" // 所有流水线都成功
	ANYFAILED  changeSetButtonStatus = "ANYFAILED"  // 流水线存在失败
	OTHER      changeSetButtonStatus = "OTHER"      // 其他情况
)

func (name changeSetButtonStatus) String() string {
	return string(name)
}
