CREATE TABLE `approval_flow` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '审批流名称',
  `flow_type` varchar(24) COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'upgrade' COMMENT '流程类型',
  `project_id` bigint DEFAULT NULL COMMENT '关联组id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `node_sequence` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点顺序',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id_index` (`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批流表';