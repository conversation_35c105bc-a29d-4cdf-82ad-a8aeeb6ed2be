package protocol

import (
	"fmt"
	"strconv"
	"strings"
)

// ClientVersion ...
type ClientVersion uint32

// Major returns major version
func (cv ClientVersion) Major() uint8 { return uint8((cv >> 24) & 0x00FF) }

// Minor returns minor version
func (cv ClientVersion) Minor() uint8 { return uint8((cv >> 16) & 0x00FF) }

// Patch returns patch
func (cv ClientVersion) Patch() uint16 { return uint16(cv & 0xFFFF) }

// String returns the `major`.`minor`.`patch` formatted string
func (cv ClientVersion) String() string {
	return fmt.Sprintf("%d.%d.%d", cv.Major(), cv.Minor(), cv.Patch())
}

// FormatClientVersion returns the 32-bits representation of the specified major-minor-patch version
func FormatClientVersion(major, minor uint8, patch uint16) uint32 {
	return (uint32(major) << 24) + (uint32(minor) << 16) + uint32(patch)
}

func ParseApkVersionToInts(str string) (int, int, int, error) {
	parts := strings.Split(str, ".")
	if len(parts) != 3 {
		return 0, 0, 0, fmt.Errorf("版本号格式不正确，必须包含三个部分，例如1.1.1")
	}

	var nums [3]int
	for i, part := range parts {
		num, err := strconv.Atoi(part)
		if err != nil {
			return 0, 0, 0, fmt.Errorf("无法将字符串转换为整数：%s", err)
		}
		nums[i] = num
	}

	return nums[0], nums[1], nums[2], nil
}

// ClientVersions 将 x.y.z 的版本号转成唯一的 32 位值
func ClientVersions(deployPlanVersions []string) ([]string, error) {
	versionSlice := deployPlanVersions
	if len(versionSlice) == 0 {
		return nil, nil
	}

	versions := make([]string, 0, len(versionSlice))
	for _, v := range versionSlice {
		vMajor, vMinor, vPatch, err := ParseApkVersionToInts(v)
		if err != nil {
			return nil, err
		}

		vUint32 := FormatClientVersion(uint8(vMajor), uint8(vMinor), uint16(vPatch))
		versions = append(versions, fmt.Sprintf("%d", vUint32))
	}
	return versions, nil
}

func GetInt64ClientVersion(version string) (int64, error) {
	vMajor, vMinor, vPatch, err := ParseApkVersionToInts(version)
	if err != nil {
		return 0, err
	}

	vUint32 := FormatClientVersion(uint8(vMajor), uint8(vMinor), uint16(vPatch))
	return int64(vUint32), nil
}
