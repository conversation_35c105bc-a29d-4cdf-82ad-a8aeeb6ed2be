## 说明

该库的封装仅用于单测使用

- `testdb` 用于 dao 层单测，提供了一个内存数据库，用于测试 dao 层的数据库操作
- `testgin` 用于 http 层单测，提供了一个 http server，用于测试 http 接口

## 示例

### 1. testdb

以 `services/pipeline/dao/pipeline_quality_test.go` 为说明

```go
import 	"52tt.com/cicd/pkg/testify/testdb"

// 测试定义的 suite
func TestPipelineQualitySuite(t *testing.T) {
    testdb.Run(t, new(pipelineQualitySuite))
}

// 定义测试的表和索引
const (
    testPipelineQualitySQL = ``
    testPipelineQualityAppSQL = ``
    testPipelineQualityIdx1 = ``
    testPipelineQualityIdx2 = ``
)

// 定义 suite
type pipelineQualitySuite struct {
    testdb.Suite
    
    repo PipelineQualityRepository
}

// 每个测试用例执行前的初始化
func (s *pipelineQualitySuite) SetupTest() {
    // 调用父类的初始化
    s.Suite.SetupTest()
    
    // 导入表和索引
    s.AddTable(testPipelineQualitySQL)
    s.AddTable(testPipelineQualityAppSQL)
    s.AddIndexs(testPipelineQualityIdx1, testPipelineQualityIdx2)
    
    // 初始化 repo
    s.repo = NewPipelineQualityRepository(s.GetDB())
}

// 测试用例 Test_interface_description
func (s *pipelineQualitySuite) Test_BatchSavePipelineQuality_ShouldSuccess() {
    // given
    
    // when
    
    // then
}
```

### 2. testgin

以 `services/pipeline/rest/pipeline_quality_test.go` 为说明

```go
import 	"52tt.com/cicd/pkg/testify/testgin"

// 测试定义的 suite
func TestPipelineQualityControllerSuite(t *testing.T) {
    testgin.Run(t, new(pipelineQualityControllerSuite))
}

// 定义 suite
type pipelineQualityControllerSuite struct {
    testgin.Suite
    
    controller             *PipelineQualityController
    mockPipelineQualitySvc *service.MockPipelineQualityService
}

func (s *pipelineQualityControllerSuite) SetupTest() {
    // 调用父类的初始化
    s.Setup()
    
    s.mockPipelineQualitySvc = service.NewMockPipelineQualityService(s.Controller())
    s.controller = NewPipelineQualityController(s.mockPipelineQualitySvc)
    // 注册路由
    s.controller.Route(s.RouterGroup())
}

// 测试用例 Test_interface_description
func (s *pipelineQualityControllerSuite) Test_UpdateQualityGroup_ShouldSuccess() {
    // given
    req := model.PipelineQuality{
        ProjectId: 1,
        Groups:    []model.QualityGroup{},
    }
    s.mockPipelineQualitySvc.EXPECT().UpdateQuality(gomock.Any(), &req).Return(nil)
    
    // when
    // 构造 body，可用 testgin.MarshalRequestBody 来构造
    // 或者直接定义 json 字符串，如 body=`{"project_id": 1, "groups": []}`
    body, _ := testgin.MarshalRequestBody(&req)
    // 发送请求 s.MockCallHTTPInterface, 第一个参数是请求方法，第二个参数是请求路径，第三个参数是请求体
    // 返回值第一个是请求的响应，第二个是 error，该 error 是 http.NewRequest 时的错误，一般不会有
    resp, _ := s.MockCallHTTPInterface(http.MethodPost, "/pipelines/quality/groups", body)
    
    // then
    // 响应结构是 render.Response，可以通过 resp.Code 获取状态码，resp.Body.String() 获取响应体
    s.Equal(http.StatusOK, resp.Code)
    // 可以通过 testgin.UnmarshalResponse 来解析响应体为 render.Response
    result, _ := testgin.UnmarshalResponse(resp.Body.Bytes())
    s.Equal(http.StatusOK, result.Code)
}

func (s *pipelineQualityControllerSuite) Test_ListQualityGroup_ShouldSuccess() {
    // given
    //...
    
    // when 
    //...
    
    // then
    s.Equal(http.StatusOK, resp.Code)
    result, _ := testgin.UnmarshalResponse(resp.Body.Bytes())
    
    // 可通过 testgin.UnmarshalResponseWithList 来解析 data 字段为列表
    var items []model.QualityGroup
    _ = testgin.UnmarshalResponseWithList(&result.Data, &items)
    s.Equal(want, items)
}
```