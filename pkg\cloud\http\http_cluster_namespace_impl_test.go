package http

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/require"

	"52tt.com/cicd/pkg/cloud/http/core"
)

func TestClusterNamespaceHTTPClient_ListCluster(t *testing.T) {
	t.Skip()
	host := os.Getenv("CLOUD_HOST")
	token := os.Getenv("CLOUD_TOKEN")
	client := NewClusterNamespaceHTTPClient(host, token)
	resp, err := client.ListCluster(context.Background(), &core.ListClusterRequest{
		Emails: []string{"<EMAIL>"},
		Env:    "testing",
	})

	require.NoError(t, err)
	t.Logf("%+v", resp)
}

func TestClusterNamespaceHTTPClient_ListClusterNamespace(t *testing.T) {
	t.Skip()
	host := os.Getenv("CLOUD_HOST")
	token := os.Getenv("CLOUD_TOKEN")
	client := NewClusterNamespaceHTTPClient(host, token)
	resp, err := client.ListClusterNamespace(context.Background(), &core.ListClusterNamespaceRequest{
		Cluster: "k8s-tc-bj-cicd-test",
		Emails:  []string{"<EMAIL>"},
	})

	require.NoError(t, err)
	t.Logf("%+v", resp)
}
