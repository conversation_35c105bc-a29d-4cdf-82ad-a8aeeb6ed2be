// Code generated by MockGen. DO NOT EDIT.
// Source: project.go

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	page "52tt.com/cicd/pkg/page"
	model "52tt.com/cicd/services/app/internal/model"
	gomock "github.com/golang/mock/gomock"
)

// MockProjectService is a mock of ProjectService interface.
type MockProjectService struct {
	ctrl     *gomock.Controller
	recorder *MockProjectServiceMockRecorder
}

// MockProjectServiceMockRecorder is the mock recorder for MockProjectService.
type MockProjectServiceMockRecorder struct {
	mock *MockProjectService
}

// NewMockProjectService creates a new mock instance.
func NewMockProjectService(ctrl *gomock.Controller) *MockProjectService {
	mock := &MockProjectService{ctrl: ctrl}
	mock.recorder = &MockProjectServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProjectService) EXPECT() *MockProjectServiceMockRecorder {
	return m.recorder
}

// CheckMemberIsBindResource mocks base method.
func (m *MockProjectService) CheckMemberIsBindResource(ctx context.Context, projectId, userId int64) (*model.CheckMember, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckMemberIsBindResource", ctx, projectId, userId)
	ret0, _ := ret[0].(*model.CheckMember)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMemberIsBindResource indicates an expected call of CheckMemberIsBindResource.
func (mr *MockProjectServiceMockRecorder) CheckMemberIsBindResource(ctx, projectId, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMemberIsBindResource", reflect.TypeOf((*MockProjectService)(nil).CheckMemberIsBindResource), ctx, projectId, userId)
}

// CheckProject mocks base method.
func (m *MockProjectService) CheckProject(project *model.Project) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckProject", project)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckProject indicates an expected call of CheckProject.
func (mr *MockProjectServiceMockRecorder) CheckProject(project interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProject", reflect.TypeOf((*MockProjectService)(nil).CheckProject), project)
}

// CheckProjectIsExist mocks base method.
func (m *MockProjectService) CheckProjectIsExist(ctx context.Context, project *model.Project) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckProjectIsExist", ctx, project)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckProjectIsExist indicates an expected call of CheckProjectIsExist.
func (mr *MockProjectServiceMockRecorder) CheckProjectIsExist(ctx, project interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProjectIsExist", reflect.TypeOf((*MockProjectService)(nil).CheckProjectIsExist), ctx, project)
}

// CheckProjectUserInfo mocks base method.
func (m *MockProjectService) CheckProjectUserInfo(userInfo *model.ProjectUserInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckProjectUserInfo", userInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckProjectUserInfo indicates an expected call of CheckProjectUserInfo.
func (mr *MockProjectServiceMockRecorder) CheckProjectUserInfo(userInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckProjectUserInfo", reflect.TypeOf((*MockProjectService)(nil).CheckProjectUserInfo), userInfo)
}

// CheckUserIsInProject mocks base method.
func (m *MockProjectService) CheckUserIsInProject(ctx context.Context, projectId, userId int64) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserIsInProject", ctx, projectId, userId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserIsInProject indicates an expected call of CheckUserIsInProject.
func (mr *MockProjectServiceMockRecorder) CheckUserIsInProject(ctx, projectId, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserIsInProject", reflect.TypeOf((*MockProjectService)(nil).CheckUserIsInProject), ctx, projectId, userId)
}

// CreateProjectMember mocks base method.
func (m *MockProjectService) CreateProjectMember(ctx context.Context, query *model.ProjectUserInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateProjectMember", ctx, query)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateProjectMember indicates an expected call of CreateProjectMember.
func (mr *MockProjectServiceMockRecorder) CreateProjectMember(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateProjectMember", reflect.TypeOf((*MockProjectService)(nil).CreateProjectMember), ctx, query)
}

// DeleteProjectUserBy mocks base method.
func (m *MockProjectService) DeleteProjectUserBy(ctx context.Context, projectId, userId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteProjectUserBy", ctx, projectId, userId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteProjectUserBy indicates an expected call of DeleteProjectUserBy.
func (mr *MockProjectServiceMockRecorder) DeleteProjectUserBy(ctx, projectId, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteProjectUserBy", reflect.TypeOf((*MockProjectService)(nil).DeleteProjectUserBy), ctx, projectId, userId)
}

// GetProjectApps mocks base method.
func (m *MockProjectService) GetProjectApps(ctx context.Context, query *model.ProjectAppsParam) ([]model.AppDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProjectApps", ctx, query)
	ret0, _ := ret[0].([]model.AppDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProjectApps indicates an expected call of GetProjectApps.
func (mr *MockProjectServiceMockRecorder) GetProjectApps(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectApps", reflect.TypeOf((*MockProjectService)(nil).GetProjectApps), ctx, query)
}

// GetProjectBasicInfo mocks base method.
func (m *MockProjectService) GetProjectBasicInfo(ctx context.Context, pQuery *model.ProjectInfoQuery) (*model.Project, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProjectBasicInfo", ctx, pQuery)
	ret0, _ := ret[0].(*model.Project)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProjectBasicInfo indicates an expected call of GetProjectBasicInfo.
func (mr *MockProjectServiceMockRecorder) GetProjectBasicInfo(ctx, pQuery interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectBasicInfo", reflect.TypeOf((*MockProjectService)(nil).GetProjectBasicInfo), ctx, pQuery)
}

// GetProjectList mocks base method.
func (m *MockProjectService) GetProjectList(ctx context.Context, pQuery page.PQuery) (*page.Page, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProjectList", ctx, pQuery)
	ret0, _ := ret[0].(*page.Page)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProjectList indicates an expected call of GetProjectList.
func (mr *MockProjectServiceMockRecorder) GetProjectList(ctx, pQuery interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectList", reflect.TypeOf((*MockProjectService)(nil).GetProjectList), ctx, pQuery)
}

// GetProjectMembersList mocks base method.
func (m *MockProjectService) GetProjectMembersList(ctx context.Context, query *model.ProjectQuery) (page.Paginator, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProjectMembersList", ctx, query)
	ret0, _ := ret[0].(page.Paginator)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProjectMembersList indicates an expected call of GetProjectMembersList.
func (mr *MockProjectServiceMockRecorder) GetProjectMembersList(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProjectMembersList", reflect.TypeOf((*MockProjectService)(nil).GetProjectMembersList), ctx, query)
}

// GetUserApprovalsBy mocks base method.
func (m *MockProjectService) GetUserApprovalsBy(ctx context.Context, projectId, userId int64) ([]model.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserApprovalsBy", ctx, projectId, userId)
	ret0, _ := ret[0].([]model.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserApprovalsBy indicates an expected call of GetUserApprovalsBy.
func (mr *MockProjectServiceMockRecorder) GetUserApprovalsBy(ctx, projectId, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserApprovalsBy", reflect.TypeOf((*MockProjectService)(nil).GetUserApprovalsBy), ctx, projectId, userId)
}

// GetUserAppsBy mocks base method.
func (m *MockProjectService) GetUserAppsBy(ctx context.Context, projectId, userId int64) ([]model.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAppsBy", ctx, projectId, userId)
	ret0, _ := ret[0].([]model.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAppsBy indicates an expected call of GetUserAppsBy.
func (mr *MockProjectServiceMockRecorder) GetUserAppsBy(ctx, projectId, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAppsBy", reflect.TypeOf((*MockProjectService)(nil).GetUserAppsBy), ctx, projectId, userId)
}

// GetUserById mocks base method.
func (m *MockProjectService) GetUserById(ctx context.Context, userId int64) (*model.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserById", ctx, userId)
	ret0, _ := ret[0].(*model.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserById indicates an expected call of GetUserById.
func (mr *MockProjectServiceMockRecorder) GetUserById(ctx, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserById", reflect.TypeOf((*MockProjectService)(nil).GetUserById), ctx, userId)
}

// GetUserProjectList mocks base method.
func (m *MockProjectService) GetUserProjectList(ctx context.Context, userId int64, userRole string) ([]model.ProjectInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProjectList", ctx, userId, userRole)
	ret0, _ := ret[0].([]model.ProjectInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProjectList indicates an expected call of GetUserProjectList.
func (mr *MockProjectServiceMockRecorder) GetUserProjectList(ctx, userId, userRole interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProjectList", reflect.TypeOf((*MockProjectService)(nil).GetUserProjectList), ctx, userId, userRole)
}

// GetUserProjectsBy mocks base method.
func (m *MockProjectService) GetUserProjectsBy(ctx context.Context, req *model.UserProjectsByReq) ([]model.ProjectInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProjectsBy", ctx, req)
	ret0, _ := ret[0].([]model.ProjectInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProjectsBy indicates an expected call of GetUserProjectsBy.
func (mr *MockProjectServiceMockRecorder) GetUserProjectsBy(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProjectsBy", reflect.TypeOf((*MockProjectService)(nil).GetUserProjectsBy), ctx, req)
}

// NewProject mocks base method.
func (m *MockProjectService) NewProject(ctx context.Context, project *model.Project) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewProject", ctx, project)
	ret0, _ := ret[0].(error)
	return ret0
}

// NewProject indicates an expected call of NewProject.
func (mr *MockProjectServiceMockRecorder) NewProject(ctx, project interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewProject", reflect.TypeOf((*MockProjectService)(nil).NewProject), ctx, project)
}

// UpdateProject mocks base method.
func (m *MockProjectService) UpdateProject(ctx context.Context, project *model.Project) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateProject", ctx, project)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateProject indicates an expected call of UpdateProject.
func (mr *MockProjectServiceMockRecorder) UpdateProject(ctx, project interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateProject", reflect.TypeOf((*MockProjectService)(nil).UpdateProject), ctx, project)
}

// UpdateProjectCanaryDeployNotifyStatus mocks base method.
func (m *MockProjectService) UpdateProjectCanaryDeployNotifyStatus(ctx context.Context, project model.ProjectCanaryDeployNotifyStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateProjectCanaryDeployNotifyStatus", ctx, project)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateProjectCanaryDeployNotifyStatus indicates an expected call of UpdateProjectCanaryDeployNotifyStatus.
func (mr *MockProjectServiceMockRecorder) UpdateProjectCanaryDeployNotifyStatus(ctx, project interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateProjectCanaryDeployNotifyStatus", reflect.TypeOf((*MockProjectService)(nil).UpdateProjectCanaryDeployNotifyStatus), ctx, project)
}

// UpdateProjectMember mocks base method.
func (m *MockProjectService) UpdateProjectMember(ctx context.Context, query *model.ProjectUserInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateProjectMember", ctx, query)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateProjectMember indicates an expected call of UpdateProjectMember.
func (mr *MockProjectServiceMockRecorder) UpdateProjectMember(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateProjectMember", reflect.TypeOf((*MockProjectService)(nil).UpdateProjectMember), ctx, query)
}

// UpdateProjectTapdSetting mocks base method.
func (m *MockProjectService) UpdateProjectTapdSetting(ctx context.Context, params *model.UpdateProjectTapdSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateProjectTapdSetting", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateProjectTapdSetting indicates an expected call of UpdateProjectTapdSetting.
func (mr *MockProjectServiceMockRecorder) UpdateProjectTapdSetting(ctx, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateProjectTapdSetting", reflect.TypeOf((*MockProjectService)(nil).UpdateProjectTapdSetting), ctx, params)
}

// UpdateUserLatestProjectId mocks base method.
func (m *MockProjectService) UpdateUserLatestProjectId(ctx context.Context, pQuery *model.ProjectInfoQuery, userId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserLatestProjectId", ctx, pQuery, userId)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserLatestProjectId indicates an expected call of UpdateUserLatestProjectId.
func (mr *MockProjectServiceMockRecorder) UpdateUserLatestProjectId(ctx, pQuery, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserLatestProjectId", reflect.TypeOf((*MockProjectService)(nil).UpdateUserLatestProjectId), ctx, pQuery, userId)
}

// VerifyParamRight mocks base method.
func (m *MockProjectService) VerifyParamRight(ctx context.Context, projectId, userId int64) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyParamRight", ctx, projectId, userId)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyParamRight indicates an expected call of VerifyParamRight.
func (mr *MockProjectServiceMockRecorder) VerifyParamRight(ctx, projectId, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyParamRight", reflect.TypeOf((*MockProjectService)(nil).VerifyParamRight), ctx, projectId, userId)
}
