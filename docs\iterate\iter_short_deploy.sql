CREATE TABLE `deploy_record` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `deploy_change_log_id` bigint NOT NULL COMMENT '变更记录ID',
  `number` bigint NOT NULL COMMENT '部署次数',
  `log_key` json DEFAULT NULL COMMENT '日志',
  `tekton_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `result` json DEFAULT NULL COMMENT '任务执行结果',
  PRIMARY KEY (`id`),
  UNIQUE KEY `number` (`number`,`deploy_change_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;