CREATE TABLE `deploy_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `metadata_id` bigint NOT NULL COMMENT '元数据ID',
  `config_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称',
  `config_type` tinyint NOT NULL DEFAULT '1' COMMENT '应用类型：1通用应用、2定时任务、3statefulset',
  `version` int NOT NULL DEFAULT '1' COMMENT '版本',
  `app_basic_config` json COMMENT '应用基础配置',
  `app_advanced_config` json COMMENT '应用高级配置',
  `trait_config` json COMMENT '运维特征',
  `created_by` bigint NOT NULL COMMENT '创建人id',
  `created_by_chinese_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人中文名',
  `created_by_employee_no` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人工号',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部署配置';

CREATE TABLE `deploy_metadata` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `env` tinyint NOT NULL COMMENT '部署环境: 1开发、2测试、3灰度、4生产',
  `env_target` tinyint NOT NULL COMMENT '部署环境目标：1基准环境、2子环境',
  `cluster` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署集群',
  `namespace` varchar(63) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部署命名空间',
  `app_id` bigint NOT NULL COMMENT '应用id',
  `app_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'APP名字',
  `config_id` bigint NOT NULL COMMENT '配置id',
  `template_id` bigint NOT NULL COMMENT '部署配置模板id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部署配置';

alter table deploy_config  drop column config_name;

-- 子环境 2.0 名称
alter table deploy_metadata add column senv varchar(63) not null default '' comment '子环境 2.0 名称, 需要符合 kubernetes label value 命名规范';
