package testgin

import (
	"bytes"
	"encoding/json"
	"io"
)

func MarshalRequestBody(v any) (io.Reader, error) {
	jsonBody, err := json.Marshal(v)
	if err != nil {
		return nil, err
	}

	return bytes.<PERSON><PERSON><PERSON>er(jsonBody), nil
}

type Response struct {
	Code int    `json:"code"`
	Data any    `json:"data"`
	Msg  string `json:"msg"`
}

func UnmarshalResponse(data []byte) (*Response, error) {
	var result Response
	err := json.Unmarshal(data, &result)
	return &result, err
}

type PageResponse struct {
	//页码
	PageNum int `json:"page" mapstructure:"page"`
	//每页显示记录数量
	PageSize int `json:"pageSize"`
	//数据
	List any `json:"list"`
	//总记录数
	TotalRecord int64 `json:"totalRecord"`
}

func UnmarshalResponsePage(data *Response) (*PageResponse, error) {
	b := &bytes.Buffer{}
	err := json.NewEncoder(b).Encode(data.Data)
	if err != nil {
		return nil, err
	}

	var result PageResponse
	err = json.Unmarshal(b.Bytes(), &result)
	return &result, err
}

func UnmarshalResponseWithList(src, dst any) error {
	b := &bytes.Buffer{}
	err := json.NewEncoder(b).Encode(src)
	if err != nil {
		return err
	}

	return json.Unmarshal(b.Bytes(), dst)
}
