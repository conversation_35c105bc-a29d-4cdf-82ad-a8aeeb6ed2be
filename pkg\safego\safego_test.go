package safego_test

import (
	"52tt.com/cicd/pkg/safego"
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGo(t *testing.T) {
	t.Run("test go function without panic", func(t *testing.T) {
		logMessages := make(chan string)
		go func() {
			safego.Go(func() {
				// Some code that doesn't panic
				fmt.Println("not panic")
			})
			close(logMessages)
		}()

		for message := range logMessages {
			assert.Contains(t, message, "goroutine panicked with error")
		}
	})

	t.Run("test go function with panic", func(t *testing.T) {
		logMessages := make(chan string)
		go func() {
			safego.Go(func() {
				panic(errors.New("something went wrong"))
			})
			close(logMessages)
		}()

		for message := range logMessages {
			assert.Contains(t, message, "goroutine panicked with error: something went wrong")
		}
	})
}
