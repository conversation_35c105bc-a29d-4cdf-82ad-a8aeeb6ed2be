package constants

type PlanRunStatus string

const (
	PlanRunUnhand     PlanRunStatus = ""
	PlanRunInitial    PlanRunStatus = "INITIAL"    //未执行
	PlanRunWait       PlanRunStatus = "WAITING"    // 等待执行
	PlanRunRunning    PlanRunStatus = "RUNNING"    // 运行中
	PlanRunFail       PlanRunStatus = "FAILED"     // 失败
	PlanRunSuccessful PlanRunStatus = "SUCCESSFUL" // 成功
	PlanRunCancel     PlanRunStatus = "CANCEL"     // 终止
	PlanRunSkip       PlanRunStatus = "SKIPPED"    // 跳过
)

func (s PlanRunStatus) String() string {
	return string(s)
}
