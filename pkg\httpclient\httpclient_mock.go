// Code generated by MockGen. DO NOT EDIT.
// Source: ./pkg/httpclient/httpclient.go

// Package httpclient is a generated GoMock package.
package httpclient

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockRequester is a mock of Requester interface.
type MockRequester struct {
	ctrl     *gomock.Controller
	recorder *MockRequesterMockRecorder
}

// MockRequesterMockRecorder is the mock recorder for MockRequester.
type MockRequesterMockRecorder struct {
	mock *MockRequester
}

// NewMockRequester creates a new mock instance.
func NewMockRequester(ctrl *gomock.Controller) *MockRequester {
	mock := &MockRequester{ctrl: ctrl}
	mock.recorder = &MockRequesterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRequester) EXPECT() *MockRequesterMockRecorder {
	return m.recorder
}

// DoDelete mocks base method.
func (m *MockRequester) DoDelete(url string, value interface{}, opts ...Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{url, value}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoDelete", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoDelete indicates an expected call of DoDelete.
func (mr *MockRequesterMockRecorder) DoDelete(url, value interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{url, value}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoDelete", reflect.TypeOf((*MockRequester)(nil).DoDelete), varargs...)
}

// DoGet mocks base method.
func (m *MockRequester) DoGet(url string, value interface{}, opts ...Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{url, value}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoGet", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoGet indicates an expected call of DoGet.
func (mr *MockRequesterMockRecorder) DoGet(url, value interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{url, value}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoGet", reflect.TypeOf((*MockRequester)(nil).DoGet), varargs...)
}

// DoPatch mocks base method.
func (m *MockRequester) DoPatch(url string, value interface{}, opts ...Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{url, value}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoPatch", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoPatch indicates an expected call of DoPatch.
func (mr *MockRequesterMockRecorder) DoPatch(url, value interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{url, value}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoPatch", reflect.TypeOf((*MockRequester)(nil).DoPatch), varargs...)
}

// DoPost mocks base method.
func (m *MockRequester) DoPost(url string, value interface{}, opts ...Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{url, value}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoPost", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoPost indicates an expected call of DoPost.
func (mr *MockRequesterMockRecorder) DoPost(url, value interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{url, value}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoPost", reflect.TypeOf((*MockRequester)(nil).DoPost), varargs...)
}

// DoPut mocks base method.
func (m *MockRequester) DoPut(url string, value interface{}, opts ...Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{url, value}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoPut", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DoPut indicates an expected call of DoPut.
func (mr *MockRequesterMockRecorder) DoPut(url, value interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{url, value}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoPut", reflect.TypeOf((*MockRequester)(nil).DoPut), varargs...)
}
