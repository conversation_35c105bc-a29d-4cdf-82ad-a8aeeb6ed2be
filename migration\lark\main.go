package main

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

/*
对于配置飞书机器人消息卡片请求地址（回调本服务地址）

在迁移地址时可使用。

使用方法：运行本main函数后，在飞书机器人页面编辑回调地址并确定
	https://open.feishu.cn/app/cli_a21e8d6582fbd013/bot

配置成：
https://yw-cicd.ttyuyin.com:8087/tickets/lack/event/
http://*************:8087/tickets/lack/event
*/

func main() {
	type req struct {
		Challenge string `json:"challenge"`
		Token     string `json:"token"`
		Type      string `json:"type"`

		OpenId        string `json:"open_id"`
		UserId        string `json:"user_id"`
		OpenMessageId string `json:"open_message_id"`
		OpenChatId    string `json:"open_chat_id"`
		TenantKey     string `json:"tenant_key"`
	}

	type confirmCallbackAddress struct {
		Challenge string `json:"challenge"`
	}

	router := gin.Default()
	router.POST("/tickets/lark/event", func(ctx *gin.Context) {
		var req req
		if err := ctx.Bind(&req); err != nil {
			fmt.Errorf("已进入飞书工单审批，绑定参数错误：%v", err)
			ctx.AbortWithStatusJSON(http.StatusBadRequest, err)
			return
		}

		if req.Challenge != "" {
			confirmCallbackAddress := confirmCallbackAddress{
				Challenge: req.Challenge,
			}
			ctx.AbortWithStatusJSON(http.StatusOK, confirmCallbackAddress)
			return
		}
		/*
			回调业务逻辑
		*/
	})
	//运行服务器
	router.Run(":8080")
}

/*
飞书机器人应当申请的权限列表：
=================
以应用身份读取通讯录
获取部门基础信息
获取通讯录部门组织架构信息
获取用户邮箱信息
通过手机号或邮箱获取用户 ID
导出云文档
获取与更新群组信息

读取群信息
更新应用所创建群的群信息
获取群组信息
获取与发送单聊、群组消息
获取用户在群组中@机器人的消息
接收群聊中@机器人消息事件
获取群组中所有消息

更新应用创建群聊的信息
获取用户发给机器人的单聊消息
读取用户发给机器人的单聊消息
发送应用内加急消息
获取单聊、群组消息
以应用的身份发消息
给一个或多个部门的成员批量发消息
给多个用户批量发消息
获取与上传图片或文件资源
===================

新机器人发布版本时，注意可用范围是否为能通知到的范围
*/
