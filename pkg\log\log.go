package log

import (
	"context"

	"52tt.com/cicd/pkg/log/core"
	"52tt.com/cicd/pkg/log/logger"
)

func init() {
	defaultLogger = logger.New()
}

var defaultLogger core.LoggerManager

func Debug(format string) {
	defaultLogger.CurrentLogger().DebugWithCtx(context.Background(), format)
}

func Info(format string) {
	defaultLogger.CurrentLogger().InfoWithCtx(context.Background(), format)
}

func Warn(format string) {
	defaultLogger.CurrentLogger().WarnWithCtx(context.Background(), format)
}

func Error(format string) {
	defaultLogger.CurrentLogger().ErrorWithCtx(context.Background(), format)
}

func Panic(format string) {
	defaultLogger.CurrentLogger().PanicWithCtx(context.Background(), format)
}

func Fatal(format string) {
	defaultLogger.CurrentLogger().FatalWithCtx(context.Background(), format)
}

func Debugf(format string, args ...any) {
	defaultLogger.CurrentLogger().DebugWithCtx(context.Background(), format, args...)
}

func Infof(format string, args ...any) {
	defaultLogger.CurrentLogger().InfoWithCtx(context.Background(), format, args...)
}

func Warnf(format string, args ...any) {
	defaultLogger.CurrentLogger().WarnWithCtx(context.Background(), format, args...)
}

func Errorf(format string, args ...any) {
	defaultLogger.CurrentLogger().ErrorWithCtx(context.Background(), format, args...)
}

func Panicf(format string, args ...any) {
	defaultLogger.CurrentLogger().PanicWithCtx(context.Background(), format, args...)
}

func Fatalf(format string, args ...any) {
	defaultLogger.CurrentLogger().FatalWithCtx(context.Background(), format, args...)
}

func DebugWithCtx(ctx context.Context, format string, args ...any) {
	defaultLogger.CurrentLogger().DebugWithCtx(ctx, format, args...)
}

func InfoWithCtx(ctx context.Context, format string, args ...any) {
	defaultLogger.CurrentLogger().InfoWithCtx(ctx, format, args...)
}

func WarnWithCtx(ctx context.Context, format string, args ...any) {
	defaultLogger.CurrentLogger().WarnWithCtx(ctx, format, args...)
}

func ErrorWithCtx(ctx context.Context, format string, args ...any) {
	defaultLogger.CurrentLogger().ErrorWithCtx(ctx, format, args...)
}

func PanicWithCtx(ctx context.Context, format string, args ...any) {
	defaultLogger.CurrentLogger().PanicWithCtx(ctx, format, args...)
}

func FatalWithCtx(ctx context.Context, format string, args ...any) {
	defaultLogger.CurrentLogger().FatalWithCtx(ctx, format, args...)
}

func RegisterHook(hooks ...core.Hook) {
	defaultLogger.CurrentHookManager().RegisterHook(hooks...)
}

func SetLogger(l core.LoggerManager) {
	defaultLogger = l
}

func CurrentLevel() core.Level {
	return defaultLogger.CurrentLevelManager().CurrentLevel()
}

func SetLevel(lv core.Level) {
	defaultLogger.CurrentLevelManager().SetLevel(lv)
}

var ParseLevel = core.ParseLevel
