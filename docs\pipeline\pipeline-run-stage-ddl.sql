CREATE TABLE `pipeline_run_stage` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `pipeline_run_id` bigint NOT NULL COMMENT '关联流水线id',
  `name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '阶段名称',
  `type` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '阶段类型',
  `status` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '阶段运行状态',
  `started_time` timestamp NULL DEFAULT NULL COMMENT '阶段开始时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '阶段完成时间',
  `task_sequence` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '运行任务顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `pipeline_run_stage_pipeline_run_id_index` (`pipeline_run_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='流水线运行阶段表';

-- 是否实际运行的克隆记录
alter table `pipeline`.`pipeline_run_stage` add column `is_clone` tinyint not null default 0 comment '是否实际运行的克隆记录';