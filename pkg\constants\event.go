package constants

type SystemAnnotationKey string

func (s SystemAnnotationKey) String() string {
	return string(s)
}

const (
	ProjectIdKey            SystemAnnotationKey = "pipelinerun.cicd.work/projectId"
	AppIdKey                SystemAnnotationKey = "pipelinerun.cicd.work/appId"
	EnvKey                  SystemAnnotationKey = "pipelinerun.cicd.work/env"
	PipelineIdKey           SystemAnnotationKey = "pipelinerun.cicd.work/pipelineId"
	BuildNumberKey          SystemAnnotationKey = "pipelinerun.cicd.work/buildNumber"
	CsiVersionKey           SystemAnnotationKey = "pipelinerun.cicd.work/csiVersion"
	BranchKey               SystemAnnotationKey = "pipelinerun.cicd.work/branch"
	RepoAddressKey          SystemAnnotationKey = "pipelinerun.cicd.work/repoAddress"
	RequestDateKey          SystemAnnotationKey = "pipelinerun.cicd.work/requestDate"
	UserIdKey               SystemAnnotationKey = "pipelinerun.cicd.work/userId"
	TaskIdKey               SystemAnnotationKey = "pipelinerun.cicd.work/taskId"
	StageIdKey              SystemAnnotationKey = "pipelinerun.cicd.work/stageId"
	TemplateIdKey           SystemAnnotationKey = "pipelinerun.cicd.work/templateId"
	PipelineRunIdKey        SystemAnnotationKey = "pipelinerun.cicd.work/pipelineRunId"
	PipelineRunStageIdKey   SystemAnnotationKey = "pipelinerun.cicd.work/pipelineRunStageId"
	PipelineRunTaskIdKey    SystemAnnotationKey = "pipelinerun.cicd.work/pipelineRunTaskId"
	PipelineRunSubtaskIdKey SystemAnnotationKey = "pipelinerun.cicd.work/pipelineRunSubtaskId"
	RollbackFlagKey         SystemAnnotationKey = "pipelinerun.cicd.work/rollbackFlag"
	ChangeSetIdKey          SystemAnnotationKey = "pipelinerun.cicd.work/changeSetId"
	RequestIDKey            SystemAnnotationKey = "pipelinerun.cicd.work/X-Request-ID"
	DeployTypeKey           SystemAnnotationKey = "pipelinerun.cicd.work/deployType"
	ChangeLogIDKey          SystemAnnotationKey = "pipelinerun.cicd.work/changeLogId"
)

const (
	PipelineRunEvent           string = "cicd.pipeline.pipelinerun"
	StageRunEvent              string = "cicd.pipeline.stagerun"
	TaskRunEvent               string = "cicd.pipeline.taskrun"
	SubTaskRunEvent            string = "cicd.pipeline.subtaskrun"
	TaskRunLogEvent            string = "cicd.pipeline.taskrun.log"
	ChangeSetPipelineTaskEvent string = "cicd.changeset.pipeline.task"
	ChangeSetTaskEvent         string = "cicd.changeset.task"
	PipelineTemplateEvent      string = "cicd.pipeline.template"
	ChangeSetRunEvent          string = "cicd.changeset.run"
	PipelineCanaryEvent        string = "cicd.pipeline.canary"
	ProjectEvent               string = "cicd.app.project"
)

const (
	PipelineTaskCustomRunLabel string = "tekton.dev/pipelineTask"
)
const (
	ApprovalUpdateEvent string = "cicd.deploy.approval.update"
	ApprovalHandleEvent string = "cicd.deploy.approval.handle"
)

const (
	DeployChangeLogEvent string = "cicd.deploy.changelog"
)

const (
	DeployCfgChangeEvent = "cicd.deploy.template.change"
)

const (
	TicketUpdateEvent = "cicd.deploy.ticket.update"
)

// ResourceDeleteEvent 删除资源事件名
const (
	ResourceDeleteEvent = "cicd.resource.delete"
)

const (
	MigrateTaskAppUpdateEvent = "cicd.migrate.task.app.update"
)
