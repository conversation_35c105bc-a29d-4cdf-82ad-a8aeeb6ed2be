CREATE TABLE `pipeline_quality` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '质量门禁名称',
  `project_id` bigint NOT NULL COMMENT '所属项目id',
  `rule` json DEFAULT NULL COMMENT '质量门禁规则',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='质量门禁信息表';

CREATE TABLE `pipeline_quality_app` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `quality_id` bigint NOT NULL COMMENT '质量门禁id',
  `app_id` bigint NOT NULL COMMENT '服务id',
  `app_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务名称',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ui_quality_app` (`quality_id`,`app_name`,`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='质量门禁服务信息表';
