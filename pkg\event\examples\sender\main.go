package main

import (
	"context"
	"log"

	"52tt.com/cicd/pkg/event"

	"52tt.com/cicd/pkg/kafka"
)

const (
	count = 10
)

var (
	brokers = []string{"************:9092"}
	topic   = "pipeline-dev"
)

func main() {
	mySender, err := event.NewSender(kafka.NewConfig(kafka.Name("test-sender"), kafka.Brokers(brokers), kafka.ProducerTopic(topic)))
	if err != nil {
		log.Fatalf("failed %v", err)
	}

	defer mySender.Close(context.Background())

	for i := 0; i < count; i++ {
		e := event.NewPipelineRelatedEvent(map[string]interface{}{
			"id":      i,
			"message": "Hello my sender!",
		})

		if result := mySender.Send(
			// Set the producer message key
			context.Background(),
			e,
		); event.IsUndelivered(result) {
			log.Printf("failed to send: %v", result)
		} else {
			log.Printf("sent: %d, accepted: %t", i, event.IsACK(result))
		}
	}
}
