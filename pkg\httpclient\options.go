package httpclient

import "time"

type Options struct {
	// This is unrelated to the similarly named TCP keep-alives.
	DisableKeepAlives   bool
	DisableCompression  bool
	MaxIdleConns        int
	MaxIdleConnsPerHost int
	MaxConnsPerHost     int
	IdleConnTimeout     time.Duration
	InsecureSkipVerify  bool
	Timeout             time.Duration

	ResponseHeaderTimeout time.Duration
	ExpectContinueTimeout time.Duration
}

type Option func(*Options)

func Timeout(timeout time.Duration) Option {
	return func(opt *Options) {
		opt.Timeout = timeout
	}
}

func SkipVerify(skipVerify bool) Option {
	return func(opt *Options) {
		opt.InsecureSkipVerify = skipVerify
	}
}
