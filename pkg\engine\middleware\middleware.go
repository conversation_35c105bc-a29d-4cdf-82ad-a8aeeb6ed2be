package middleware

import (
	"52tt.com/cicd/pkg/context"
	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/requestid"
	"github.com/gin-gonic/gin"
)

func CORS() gin.HandlerFunc {
	return cors.Default()
}

func AuthContext() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		context.SetTrafficMark(ctx)
		context.SetRequestInfo(ctx, context.RequestInfoFromGinHeader(ctx))
		ctx.Next()
	}
}

func RequestID() gin.HandlerFunc {
	return requestid.New(requestid.WithCustomHeaderStrKey(context.RequestIDHeaderKey))
}
