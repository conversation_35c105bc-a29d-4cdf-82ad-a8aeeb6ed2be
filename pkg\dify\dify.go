//go:generate mockgen -destination=ttdi_mock.go -package=ttdi -source=ttdi.go
package dify

import (
	"context"
	"fmt"

	"52tt.com/cicd/pkg/httpclient"
	"52tt.com/cicd/pkg/log"
)

var _ Service = (*client)(nil)

type client struct {
	host    string
	token   string
	session httpclient.Session
}

func NewClient(host, token string) *client {
	session := httpclient.NewSession(&httpclient.SessionOption{})
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + token,
	}
	session.SetHeaders(headers)
	client := &client{
		host:    host,
		token:   token,
		session: session,
	}
	return client
}

type Service interface {
	Chat(ctx context.Context, req ChatRep) (resp ChatResp, err error)
}

func doPost[T any](ctx context.Context, c *client, urls string, req any) (respInfo T, err error) {
	httpReq, err := httpclient.NewRequest(urls, httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.ErrorWithCtx(ctx, "初始化Dify请求参数错误: %v", err)
		return
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "请求Dify平台出错: %v", err)
		return
	}
	if httpResp.StatusCode != 200 {
		err = fmt.Errorf("curl Dify Http  code [%d]", httpResp.StatusCode)
		return
	}
	if err = httpResp.JsonToStruct(&respInfo); err != nil {
		log.ErrorWithCtx(ctx, "结构化Dify平台 Response 失败: %v", err)
		return
	}
	return
}
