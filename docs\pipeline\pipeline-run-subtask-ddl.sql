CREATE TABLE `pipeline_run_subtask` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    `pipeline_run_task_id` bigint NOT NULL COMMENT '流水线运行任务id',
    `name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '子任务名称',
    `type` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子任务类型',
    `status` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '子任务运行状态',
    `started_time` timestamp NULL DEFAULT NULL COMMENT '子任务开始时间',
    `completed_time` timestamp NULL DEFAULT NULL COMMENT '子任务完成时间',
    `config` json NOT NULL COMMENT '子任务配置信息',
    `result` json DEFAULT NULL COMMENT '子任务结果',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tekton_name` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'tekton名称',
    `tekton_namespace` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'tekton命名空间',
    `pod_name` varchar(256) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'pod名称',
    `pipeline_run_id` bigint DEFAULT NULL COMMENT '流水线运行id',
    `prefix_id` int DEFAULT NULL COMMENT '重试过程中记录基于上个多云任务的id',
    `enabled` tinyint not null default 1 comment '是否有效，用于判断提取最新的任务',
    PRIMARY KEY (`id`),
    KEY `idx_pipeline_run_id` (`pipeline_run_id`) USING BTREE,
    KEY `idx_pipeline_task_run_id` (`pipeline_task_run_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='流水线运行子任务表';