// Code generated by MockGen. DO NOT EDIT.
// Source: apollo_admin.go

// Package apollo is a generated GoMock package.
package apollo

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// AddConfig mocks base method.
func (m *MockService) AddConfig(ctx context.Context, req *AddConfigReq) (*AddConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddConfig", ctx, req)
	ret0, _ := ret[0].(*AddConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddConfig indicates an expected call of AddConfig.
func (mr *MockServiceMockRecorder) AddConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddConfig", reflect.TypeOf((*MockService)(nil).AddConfig), ctx, req)
}

// AddNamespace mocks base method.
func (m *MockService) AddNamespace(ctx context.Context, req *AddNamespaceReq) (*AddNamespaceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNamespace", ctx, req)
	ret0, _ := ret[0].(*AddNamespaceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNamespace indicates an expected call of AddNamespace.
func (mr *MockServiceMockRecorder) AddNamespace(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNamespace", reflect.TypeOf((*MockService)(nil).AddNamespace), ctx, req)
}

// ModifyConfig mocks base method.
func (m *MockService) ModifyConfig(ctx context.Context, req *ModifyConfigReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyConfig", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyConfig indicates an expected call of ModifyConfig.
func (mr *MockServiceMockRecorder) ModifyConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyConfig", reflect.TypeOf((*MockService)(nil).ModifyConfig), ctx, req)
}

// ReleaseConfig mocks base method.
func (m *MockService) ReleaseConfig(ctx context.Context, req *ReleaseConfigReq) (*ReleaseConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseConfig", ctx, req)
	ret0, _ := ret[0].(*ReleaseConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReleaseConfig indicates an expected call of ReleaseConfig.
func (mr *MockServiceMockRecorder) ReleaseConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseConfig", reflect.TypeOf((*MockService)(nil).ReleaseConfig), ctx, req)
}
