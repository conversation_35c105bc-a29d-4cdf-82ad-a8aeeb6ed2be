package constants

import (
	"fmt"
	"strings"
)

type TemplateType string

const (
	SYSTEM TemplateType = "system"
	CUSTOM TemplateType = "custom"
)

func (t TemplateType) String() string {
	return string(t)
}

const ALREADY_UPDATE = "已更新"

type EnvType string

func (e EnvType) String() string {
	return string(e)
}

func (e EnvType) Value() int8 {
	return e.ToEnumEnv().Value()
}

var envTypeEnumMapping = map[EnvType]EnumEnv{
	DEV:        EnumEnvDev,
	TESTING:    EnumEnvTesting,
	PREVIEW:    EnumEnvPreview,
	PRODUCTION: EnumEnvProduction,
}

func (e EnvType) ToEnumEnv() EnumEnv {
	v, ok := envTypeEnumMapping[e]
	if ok {
		return v
	}
	return EnumEnvUnknown
}

func (e EnvType) LabelName() string {
	switch e {
	case DEV:
		return "DEVELOPMENT"
	case TESTING:
		return "TESTING"
	case PRODUCTION:
		return "PRODUCTION"
	case PREVIEW:
		return "STAGING"
	default:
		return ""
	}
}

func (e EnvType) Chinese() string {
	switch e {
	case TESTING:
		return "测试环境"
	case DEV:
		return "开发环境"
	case PRODUCTION:
		return "生产环境"
	case PREVIEW:
		return "灰度环境"
	default:
		return ""
	}
}

// TransferToEnvType 只用于 int8 类型转换为 EnvType 类型
// 如果字段本身是 EnumEnv，则直接调用 EnumEnv.ToEnvType 方法
func TransferToEnvType(value int8) EnvType {
	enumEnv := EnumEnv(value)
	return enumEnv.ToEnvType()
}

const (
	TESTING    EnvType = "testing"
	DEV        EnvType = "dev"
	PRODUCTION EnvType = "production"
	PREVIEW    EnvType = "preview"
)

type Stage string

const (
	STAGE_COMPILE_BUILD            Stage = "COMPILE_BUILD"
	STAGE_AUTOMATION_TEST          Stage = "AUTOMATION_TEST"
	STAGE_PARALLEL                 Stage = "PARALLEL" //并行阶段
	STAGE_STATIC_CODE_SCAN         Stage = "STATIC_CODE_SCAN"
	STAGE_SECURITY_SCAN            Stage = "SECURITY_SCAN"
	STAGE_ARTIFACT_MANAGEMENT      Stage = "ARTIFACT_MANAGEMENT"
	STAGE_DEPLOY_DEV_ENV           Stage = "DEPLOY_DEV_ENV"
	STAGE_DEPLOY_TEST_ENV          Stage = "DEPLOY_TEST_ENV"
	STAGE_BUSINESS_AUTOMATION_TEST Stage = "BUSINESS_AUTOMATION_TEST"
	STAGE_TEST_ACCEPTANCE          Stage = "TEST_ACCEPTANCE"
	STAGE_DEPLOY_GRAY_ENV          Stage = "DEPLOY_GRAY_ENV"
	STAGE_DEPLOY_PROD_ENV          Stage = "DEPLOY_PROD_ENV"
	STAGE_ENV_IMAGE_SYNC           Stage = "ENV_IMAGE_SYNC"
)

func (stage Stage) String() string {
	return string(stage)
}

func (stage Stage) IsDeployStage() bool {
	switch stage {
	case STAGE_DEPLOY_DEV_ENV, STAGE_DEPLOY_TEST_ENV, STAGE_DEPLOY_GRAY_ENV, STAGE_DEPLOY_PROD_ENV, STAGE_ENV_IMAGE_SYNC:
		return true
	}
	return false
}

func (stage Stage) IsImageSyncStage() bool {
	return stage == STAGE_ENV_IMAGE_SYNC || stage == STAGE_DEPLOY_DEV_ENV
}

type Task string

const (
	TASK_PULL_CODE              Task = "PULL_CODE"
	TASK_AUTOMATION_COMPILE     Task = "AUTOMATION_COMPILE"
	TASK_CUSTOM_SHELL           Task = "CUSTOM_SHELL"
	TASK_UNIT_TEST              Task = "UNIT_TEST"
	TASK_SONAR_SCAN             Task = "SONAR_SCAN"
	TASK_CHECKSTYLE             Task = "CHECKSTYLE"
	TASK_SCA_SCAN               Task = "SCA_SCAN"
	TASK_GENERATE_PUSH_IMAGE    Task = "GENERATE_PUSH_IMAGE"
	TASK_AUTOMATION_DEPLOY      Task = "AUTOMATION_DEPLOY" // 自动化部署
	TASK_API_AUTOMATION_TEST    Task = "API_AUTOMATION_TEST"
	TASK_TEST_ACCEPTANCE        Task = "TEST_ACCEPTANCE"       // 测试验收
	TASK_TEST_APPROVAL          Task = "TEST_APPROVAL"         // 提测审批
	TASK_GRAY_UPGRADE_APPROVAL  Task = "GRAY_UPGRADE_APPROVAL" // 灰度升级审批
	TASK_UPGRADE_APPROVAL       Task = "UPGRADE_APPROVAL"      // 升级审批
	TASK_TEST_ENV_IMAGE_SYNC    Task = "TEST_ENV_IMAGE_SYNC"
	TASK_DEV_ENV_IMAGE_SYNC     Task = "DEV_ENV_IMAGE_SYNC"
	TASK_VIRTUAL_NODE           Task = "VIRTUAL_NODE"
	TASK_AUTOMATION_DEPLOY_SENV Task = "AUTOMATION_DEPLOY_SENV" // TASK_AUTOMATION_DEPLOY_SENV 部署子环境 这个没用了，可以废弃掉
	TASK_DEPLOY_ORIGIN          Task = "DEPLOY_ORIGIN"          // 部署基准
	TASK_DEPLOY_SUB             Task = "DEPLOY_SUB"             // 部署子环境
	TASK_DEPLOY_STAGING         Task = "DEPLOY_STAGING"         // 部署预发布
	TASK_DEPLOY_CANARY          Task = "DEPLOY_CANARY"          // 部署金丝雀
	TASK_OFFLINE_CANARY         Task = "OFFLINE_CANARY"         // 下线子环境
	TASK_PAUSE                  Task = "PAUSE_TASK"             // 流水线暂停
)

func (t Task) String() string {
	return string(t)
}

func (t Task) CamelCase() string {
	// "DEPLOY_CANARY" -> "deployCanary"
	words := strings.Split(t.String(), "_")
	buf := strings.Builder{}
	for i, word := range words {
		if i == 0 {
			buf.WriteString(strings.ToLower(word))
		} else {
			buf.WriteString(strings.ToUpper(word[:1]))
			buf.WriteString(strings.ToLower(word[1:]))
		}
	}
	return buf.String()
}

// IsApiAutomationTestRelated 判断是否是API自动化测试相关的任务
func (t Task) IsApiAutomationTestRelated() bool {
	switch t {
	case TASK_AUTOMATION_DEPLOY, TASK_DEPLOY_ORIGIN, TASK_DEPLOY_SUB:
		return true
	}
	return false
}

// IsSimpleDeployTask 判断是否是短流程 fakeDeploy 部署任务
func (t Task) IsSimpleDeployTask() bool {
	if t.IsDeployTask() {
		return true
	} else if t.IsImageSyncTask() {
		return true
	} else {
		return false
	}
}

func (t Task) IsDeployTask() bool {
	switch t {
	case TASK_DEPLOY_ORIGIN, TASK_DEPLOY_SUB:
		return true
	case TASK_DEPLOY_STAGING, TASK_DEPLOY_CANARY:
		return true
	case TASK_AUTOMATION_DEPLOY, TASK_AUTOMATION_DEPLOY_SENV:
		return true
	}
	return false
}

func (t Task) IsApprovalTask() bool {
	switch t {
	case TASK_TEST_APPROVAL, TASK_TEST_ACCEPTANCE, TASK_GRAY_UPGRADE_APPROVAL, TASK_UPGRADE_APPROVAL:
		return true
	}
	return false
}

func (t Task) IsImageSyncTask() bool {
	switch t {
	case TASK_TEST_ENV_IMAGE_SYNC, TASK_DEV_ENV_IMAGE_SYNC:
		return true
	}
	return false
}

func OfTaskType(tkType string) Task {
	return Task(tkType)
}

type BuildImageWay string

const (
	GET_DOCKER_FILE_FROM_REPO BuildImageWay = "GET_DOCKER_FILE_FROM_REPO"
	CUSTOM_DOCKER_FILE        BuildImageWay = "CUSTOM_DOCKER_FILE"
)

type SonarScanRule string

const (
	EXCLUDE_RULE           SonarScanRule = "EXCLUDE_RULE"
	TEST_FILE_INCLUDE_RULE SonarScanRule = "TEST_FILE_INCLUDE_RULE"
)

type SonarScanThresholdMetric string

const (
	BUG           SonarScanThresholdMetric = "BUG"
	VULNERABILITY SonarScanThresholdMetric = "VULNERABILITY"
	BAD_SMELL     SonarScanThresholdMetric = "BAD_SMELL"
	TEST_COVERAGE SonarScanThresholdMetric = "TEST_COVERAGE"
)

type PipelineStatus string

func (ps PipelineStatus) String() string {
	return string(ps)
}

func (ps PipelineStatus) IsFinished() bool {
	return ps == FAILED || ps == SUCCESSFUL || ps == CANCEL
}

func (ps PipelineStatus) IsSuccess() bool {
	return ps == SUCCESSFUL
}

func (ps PipelineStatus) IsPending() bool {
	return ps == PENDING
}

func (ps PipelineStatus) IsFailed() bool {
	return ps == FAILED
}

func (ps PipelineStatus) IsCancel() bool {
	return ps == CANCEL
}

func (ps PipelineStatus) CanUpdateToRunning() bool {
	return ps == PENDING || ps == UNHANDLED || ps.IsFailed()
}

func (ps PipelineStatus) CanTransition(to PipelineStatus) bool {
	if ps == PENDING {
		return true
	} else if ps == UNHANDLED {
		return to != PENDING
	} else if ps == RUNNING {
		if to == PENDING || to == UNHANDLED {
			return false
		}
		return true
	} else if ps == FAILED {
		return to == CANCEL
	} else if ps == SUCCESSFUL {
		return to == CANCEL
	} else {
		// CANCEL
		return false
	}
}

func (ps PipelineStatus) DisplayName() string {
	v, ok := psMapping[ps]
	if ok {
		return v
	}
	return ""
}

func (ps PipelineStatus) IsBelongTo(taskStatus ...PipelineStatus) bool {
	for _, status := range taskStatus {
		if status == ps {
			return true
		}
	}
	return false
}

func (t Task) IsBelongTo(tasks ...Task) bool {
	for _, task := range tasks {
		if task == t {
			return true
		}
	}
	return false
}

const (
	PREPARING  PipelineStatus = "PREPARING"  // 准备中 集群资源不足
	PENDING    PipelineStatus = "PENDING"    // 未开始
	RUNNING    PipelineStatus = "RUNNING"    // 运行中
	FAILED     PipelineStatus = "FAILED"     // 失败
	SUCCESSFUL PipelineStatus = "SUCCESSFUL" // 成功
	CANCEL     PipelineStatus = "CANCEL"     // 终止
	UNHANDLED  PipelineStatus = "UNHANDLED"  // 待处理
	SKIPPED    PipelineStatus = "SKIPPED"    // 跳过
	UNKNOWN    PipelineStatus = "UNKNOWN"    // 未知 忽略的

)

func OfPipelineStatus(status string) PipelineStatus {
	return PipelineStatus(status)
}

var psMapping = map[PipelineStatus]string{
	PREPARING:  "准备中",
	PENDING:    "未开始",
	RUNNING:    "运行中",
	FAILED:     "失败",
	SUCCESSFUL: "成功",
	CANCEL:     "终止",
	UNHANDLED:  "待处理",
	SKIPPED:    "跳过",
}

type StreamName string

const (
	PIPELINE_LIST          StreamName = "PIPELINE_LIST"
	PIPELINE_RUN_LIST      StreamName = "PIPELINE_RUN_LIST"
	PIPELINE_RUN_DETAIL    StreamName = "PIPELINE_RUN_DETAIL"
	PIPELINE_RUN_STATUS    StreamName = "PIPELINE_RUN_STATUS"
	SUB_TASK_LIST          StreamName = "SUB_TASK_LIST"
	PIPELINE_TASK_LOG      StreamName = "PIPELINE_TASK_LOG"
	CHANGE_SET_DEPLOY_LIST StreamName = "CHANGE_SET_DEPLOY_LIST"
	PIPELINE_CANARY_STSTUS StreamName = "PIPELINE_CANARY_STSTUS"
)

func (name StreamName) String() string {
	return string(name)
}

type TriggerModeType string

func (t TriggerModeType) String() string {
	return string(t)
}

const (
	AUTO   TriggerModeType = "auto"
	MANUAL TriggerModeType = "manual"
)

type PipelineType string

const (
	FEATURE   PipelineType = "feature"   // 特性流水线
	RELEASE   PipelineType = "release"   // 发布流水线
	HOTFIX    PipelineType = "hotfix"    // hotfix流水线
	PRE_MERGE PipelineType = "pre_merge" // 预合并流水线
)

func IsPreMergePipeline(pType string) bool {
	return pType == PRE_MERGE.String()
}

func OfPipelineType(pType string) PipelineType {
	return PipelineType(pType)
}

func (pType PipelineType) String() string {
	return string(pType)
}

type TriggerType string

const (
	MERGE TriggerType = "merge_request" // 合并
	PUSH  TriggerType = "push"          // 提交
)

func (tType TriggerType) String() string {
	return string(tType)
}

type TriggerBranchType string

const (
	EXACT TriggerBranchType = "exact" // 单分支
	REGEX TriggerBranchType = "regex" // 正则表达式
)

func (tBType TriggerBranchType) String() string {
	return string(tBType)
}

type PipelineRunKind string

const (
	CIPipelineRun       PipelineRunKind = "ci"
	CDPipelineRun       PipelineRunKind = "cd"
	RollbackPipelineRun PipelineRunKind = "rollback"
	DeployPipelineRun   PipelineRunKind = "deploy"
)

func IsAutomationDeployTask(task Task) bool {
	switch task {
	case TASK_AUTOMATION_DEPLOY, TASK_AUTOMATION_DEPLOY_SENV, TASK_DEPLOY_STAGING, TASK_DEPLOY_CANARY:
		return true
	default:
		return false
	}
}
func IsImageSyncTask(task Task) bool {
	return task == TASK_TEST_ENV_IMAGE_SYNC || task == TASK_DEV_ENV_IMAGE_SYNC
}

const SKIP_PREFIX = "skip-"

func GenerateSkipParamName(taskID int64) string {
	return fmt.Sprintf("%s%d", SKIP_PREFIX, taskID)
}

type EnumPROpLogAction string

const (
	// OpLogActionPassCanary 通过金丝雀验证
	OpLogActionPassCanary EnumPROpLogAction = "PASS_CANARY"
)
